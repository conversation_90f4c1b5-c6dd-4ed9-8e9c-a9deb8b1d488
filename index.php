<?php

define ("ROOT_FOLDER",dirname(__DIR__).DIRECTORY_SEPARATOR);
define ("APP_FOLDER", ROOT_FOLDER . "core");
define ("CORE_FOLDER", ROOT_FOLDER . "core");

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
(file_exists($init_file)) ? include($init_file) : exit("No init $init_file");

/*
Had a report recently that a Sage Import file had not been emailed
The Cron had run fine and the file had been created so it seems to have been a temporary failure with SMTP
The below was a quick piece of code to list all previously created Sage Import files to allow me to
grab the specific one and resend it.
This could be expanded out to be a screen that L<PERSON>Y could use to peruse, examien and resend any Sage Import File.
*/

/*
# PurchaseTransaction::PeriodicImport(); # Use this to manually run a new Sage Import file
$sage = new Codebase\Sage();
#$sageFolder = "/var/www/html/core/codebase/Billing/csvfiles";
#$sageFile = '20230222230602.csv';
#$send = $sage->SendPurchaseTransactionFile($sageFolder.DIRECTORY_SEPARATOR.$sageFile);
$files = $sage->FetchFiles();
Tools::Dump($files);
exit(0);
*/


// Billing Stage 1 - Checking for any fixtures that are unbilled up to and including today
/*
$start = microtime(true);
$stage1 = StripePayment::BillingStage1();
echo "Stage 1 took " . (microtime(true) - $start) . " seconds<br>";
Tools::Dump($stage1);


// Billing Stage 2 - Loops through all Team balances, taking action for each
// Negative or zero balance = Set Aged Date for Team to today
// Positive balance (ie - customer owes money) 
// - perform some checks, 
// - check if they already have a billing effort open where the value matches currently owed value,
// - create a billing action where necessary
$start = microtime(true);
$stage2 = StripePayment::BillingStage2(true, true);
echo "Stage 2 took " . (microtime(true) - $start) . " seconds<br>";
Tools::Dump($stage2);
$stage3 = StripePayment::BillingStage3(true);
Tools::Dump($stage3);
StripePayment::BillingStage4();
StripePayment::BillingStage5();
*/

