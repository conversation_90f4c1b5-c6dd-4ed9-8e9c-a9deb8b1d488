(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerpolicy&&(r.referrerPolicy=o.referrerpolicy),o.crossorigin==="use-credentials"?r.credentials="include":o.crossorigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();function Hr(t,e){const n=Object.create(null),s=t.split(",");for(let o=0;o<s.length;o++)n[s[o]]=!0;return e?o=>!!n[o.toLowerCase()]:o=>!!n[o]}function Br(t){if(J(t)){const e={};for(let n=0;n<t.length;n++){const s=t[n],o=ke(s)?Mu(s):Br(s);if(o)for(const r in o)e[r]=o[r]}return e}else{if(ke(t))return t;if(Te(t))return t}}const Lu=/;(?![^(]*\))/g,ku=/:([^]+)/,Ru=/\/\*.*?\*\//gs;function Mu(t){const e={};return t.replace(Ru,"").split(Lu).forEach(n=>{if(n){const s=n.split(ku);s.length>1&&(e[s[0].trim()]=s[1].trim())}}),e}function bn(t){let e="";if(ke(t))e=t;else if(J(t))for(let n=0;n<t.length;n++){const s=bn(t[n]);s&&(e+=s+" ")}else if(Te(t))for(const n in t)t[n]&&(e+=n+" ");return e.trim()}const Vu="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ju=Hr(Vu);function _l(t){return!!t||t===""}function Fu(t,e){if(t.length!==e.length)return!1;let n=!0;for(let s=0;n&&s<t.length;s++)n=Ds(t[s],e[s]);return n}function Ds(t,e){if(t===e)return!0;let n=Di(t),s=Di(e);if(n||s)return n&&s?t.getTime()===e.getTime():!1;if(n=ys(t),s=ys(e),n||s)return t===e;if(n=J(t),s=J(e),n||s)return n&&s?Fu(t,e):!1;if(n=Te(t),s=Te(e),n||s){if(!n||!s)return!1;const o=Object.keys(t).length,r=Object.keys(e).length;if(o!==r)return!1;for(const i in t){const a=t.hasOwnProperty(i),l=e.hasOwnProperty(i);if(a&&!l||!a&&l||!Ds(t[i],e[i]))return!1}}return String(t)===String(e)}function Ur(t,e){return t.findIndex(n=>Ds(n,e))}const B=t=>ke(t)?t:t==null?"":J(t)||Te(t)&&(t.toString===bl||!ne(t.toString))?JSON.stringify(t,gl,2):String(t),gl=(t,e)=>e&&e.__v_isRef?gl(t,e.value):kn(e)?{[`Map(${e.size})`]:[...e.entries()].reduce((n,[s,o])=>(n[`${s} =>`]=o,n),{})}:Xn(e)?{[`Set(${e.size})`]:[...e.values()]}:Te(e)&&!J(e)&&!yl(e)?String(e):e,Ce={},Ln=[],pt=()=>{},Hu=()=>!1,Bu=/^on[^a-z]/,vo=t=>Bu.test(t),Kr=t=>t.startsWith("onUpdate:"),Fe=Object.assign,Wr=(t,e)=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)},Uu=Object.prototype.hasOwnProperty,pe=(t,e)=>Uu.call(t,e),J=Array.isArray,kn=t=>Is(t)==="[object Map]",Xn=t=>Is(t)==="[object Set]",Di=t=>Is(t)==="[object Date]",ne=t=>typeof t=="function",ke=t=>typeof t=="string",ys=t=>typeof t=="symbol",Te=t=>t!==null&&typeof t=="object",vl=t=>Te(t)&&ne(t.then)&&ne(t.catch),bl=Object.prototype.toString,Is=t=>bl.call(t),Ku=t=>Is(t).slice(8,-1),yl=t=>Is(t)==="[object Object]",zr=t=>ke(t)&&t!=="NaN"&&t[0]!=="-"&&""+parseInt(t,10)===t,eo=Hr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),bo=t=>{const e=Object.create(null);return n=>e[n]||(e[n]=t(n))},Wu=/-(\w)/g,St=bo(t=>t.replace(Wu,(e,n)=>n?n.toUpperCase():"")),zu=/\B([A-Z])/g,Qn=bo(t=>t.replace(zu,"-$1").toLowerCase()),yo=bo(t=>t.charAt(0).toUpperCase()+t.slice(1)),Wo=bo(t=>t?`on${yo(t)}`:""),Es=(t,e)=>!Object.is(t,e),to=(t,e)=>{for(let n=0;n<t.length;n++)t[n](e)},lo=(t,e,n)=>{Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value:n})},ws=t=>{const e=parseFloat(t);return isNaN(e)?t:e};let Ii;const Yu=()=>Ii||(Ii=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});let Ge;class El{constructor(e=!1){this.detached=e,this.active=!0,this.effects=[],this.cleanups=[],this.parent=Ge,!e&&Ge&&(this.index=(Ge.scopes||(Ge.scopes=[])).push(this)-1)}run(e){if(this.active){const n=Ge;try{return Ge=this,e()}finally{Ge=n}}}on(){Ge=this}off(){Ge=this.parent}stop(e){if(this.active){let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.scopes)for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!e){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0,this.active=!1}}}function wl(t){return new El(t)}function qu(t,e=Ge){e&&e.active&&e.effects.push(t)}function Gu(){return Ge}function Ju(t){Ge&&Ge.cleanups.push(t)}const Yr=t=>{const e=new Set(t);return e.w=0,e.n=0,e},Tl=t=>(t.w&Gt)>0,Al=t=>(t.n&Gt)>0,Xu=({deps:t})=>{if(t.length)for(let e=0;e<t.length;e++)t[e].w|=Gt},Qu=t=>{const{deps:e}=t;if(e.length){let n=0;for(let s=0;s<e.length;s++){const o=e[s];Tl(o)&&!Al(o)?o.delete(t):e[n++]=o,o.w&=~Gt,o.n&=~Gt}e.length=n}},pr=new WeakMap;let fs=0,Gt=1;const mr=30;let ft;const pn=Symbol(""),_r=Symbol("");class qr{constructor(e,n=null,s){this.fn=e,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,qu(this,s)}run(){if(!this.active)return this.fn();let e=ft,n=zt;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=ft,ft=this,zt=!0,Gt=1<<++fs,fs<=mr?Xu(this):Li(this),this.fn()}finally{fs<=mr&&Qu(this),Gt=1<<--fs,ft=this.parent,zt=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){ft===this?this.deferStop=!0:this.active&&(Li(this),this.onStop&&this.onStop(),this.active=!1)}}function Li(t){const{deps:e}=t;if(e.length){for(let n=0;n<e.length;n++)e[n].delete(t);e.length=0}}let zt=!0;const Sl=[];function Zn(){Sl.push(zt),zt=!1}function es(){const t=Sl.pop();zt=t===void 0?!0:t}function Xe(t,e,n){if(zt&&ft){let s=pr.get(t);s||pr.set(t,s=new Map);let o=s.get(n);o||s.set(n,o=Yr()),Cl(o)}}function Cl(t,e){let n=!1;fs<=mr?Al(t)||(t.n|=Gt,n=!Tl(t)):n=!t.has(ft),n&&(t.add(ft),ft.deps.push(t))}function Lt(t,e,n,s,o,r){const i=pr.get(t);if(!i)return;let a=[];if(e==="clear")a=[...i.values()];else if(n==="length"&&J(t)){const l=ws(s);i.forEach((d,u)=>{(u==="length"||u>=l)&&a.push(d)})}else switch(n!==void 0&&a.push(i.get(n)),e){case"add":J(t)?zr(n)&&a.push(i.get("length")):(a.push(i.get(pn)),kn(t)&&a.push(i.get(_r)));break;case"delete":J(t)||(a.push(i.get(pn)),kn(t)&&a.push(i.get(_r)));break;case"set":kn(t)&&a.push(i.get(pn));break}if(a.length===1)a[0]&&gr(a[0]);else{const l=[];for(const d of a)d&&l.push(...d);gr(Yr(l))}}function gr(t,e){const n=J(t)?t:[...t];for(const s of n)s.computed&&ki(s);for(const s of n)s.computed||ki(s)}function ki(t,e){(t!==ft||t.allowRecurse)&&(t.scheduler?t.scheduler():t.run())}const Zu=Hr("__proto__,__v_isRef,__isVue"),$l=new Set(Object.getOwnPropertyNames(Symbol).filter(t=>t!=="arguments"&&t!=="caller").map(t=>Symbol[t]).filter(ys)),ed=Gr(),td=Gr(!1,!0),nd=Gr(!0),Ri=sd();function sd(){const t={};return["includes","indexOf","lastIndexOf"].forEach(e=>{t[e]=function(...n){const s=me(this);for(let r=0,i=this.length;r<i;r++)Xe(s,"get",r+"");const o=s[e](...n);return o===-1||o===!1?s[e](...n.map(me)):o}}),["push","pop","shift","unshift","splice"].forEach(e=>{t[e]=function(...n){Zn();const s=me(this)[e].apply(this,n);return es(),s}}),t}function Gr(t=!1,e=!1){return function(s,o,r){if(o==="__v_isReactive")return!t;if(o==="__v_isReadonly")return t;if(o==="__v_isShallow")return e;if(o==="__v_raw"&&r===(t?e?bd:Dl:e?Nl:Pl).get(s))return s;const i=J(s);if(!t&&i&&pe(Ri,o))return Reflect.get(Ri,o,r);const a=Reflect.get(s,o,r);return(ys(o)?$l.has(o):Zu(o))||(t||Xe(s,"get",o),e)?a:xe(a)?i&&zr(o)?a:a.value:Te(a)?t?Il(a):ts(a):a}}const od=Ol(),rd=Ol(!0);function Ol(t=!1){return function(n,s,o,r){let i=n[s];if(Vn(i)&&xe(i)&&!xe(o))return!1;if(!t&&(!co(o)&&!Vn(o)&&(i=me(i),o=me(o)),!J(n)&&xe(i)&&!xe(o)))return i.value=o,!0;const a=J(n)&&zr(s)?Number(s)<n.length:pe(n,s),l=Reflect.set(n,s,o,r);return n===me(r)&&(a?Es(o,i)&&Lt(n,"set",s,o):Lt(n,"add",s,o)),l}}function id(t,e){const n=pe(t,e);t[e];const s=Reflect.deleteProperty(t,e);return s&&n&&Lt(t,"delete",e,void 0),s}function ad(t,e){const n=Reflect.has(t,e);return(!ys(e)||!$l.has(e))&&Xe(t,"has",e),n}function ld(t){return Xe(t,"iterate",J(t)?"length":pn),Reflect.ownKeys(t)}const xl={get:ed,set:od,deleteProperty:id,has:ad,ownKeys:ld},cd={get:nd,set(t,e){return!0},deleteProperty(t,e){return!0}},ud=Fe({},xl,{get:td,set:rd}),Jr=t=>t,Eo=t=>Reflect.getPrototypeOf(t);function Hs(t,e,n=!1,s=!1){t=t.__v_raw;const o=me(t),r=me(e);n||(e!==r&&Xe(o,"get",e),Xe(o,"get",r));const{has:i}=Eo(o),a=s?Jr:n?Zr:Ts;if(i.call(o,e))return a(t.get(e));if(i.call(o,r))return a(t.get(r));t!==o&&t.get(e)}function Bs(t,e=!1){const n=this.__v_raw,s=me(n),o=me(t);return e||(t!==o&&Xe(s,"has",t),Xe(s,"has",o)),t===o?n.has(t):n.has(t)||n.has(o)}function Us(t,e=!1){return t=t.__v_raw,!e&&Xe(me(t),"iterate",pn),Reflect.get(t,"size",t)}function Mi(t){t=me(t);const e=me(this);return Eo(e).has.call(e,t)||(e.add(t),Lt(e,"add",t,t)),this}function Vi(t,e){e=me(e);const n=me(this),{has:s,get:o}=Eo(n);let r=s.call(n,t);r||(t=me(t),r=s.call(n,t));const i=o.call(n,t);return n.set(t,e),r?Es(e,i)&&Lt(n,"set",t,e):Lt(n,"add",t,e),this}function ji(t){const e=me(this),{has:n,get:s}=Eo(e);let o=n.call(e,t);o||(t=me(t),o=n.call(e,t)),s&&s.call(e,t);const r=e.delete(t);return o&&Lt(e,"delete",t,void 0),r}function Fi(){const t=me(this),e=t.size!==0,n=t.clear();return e&&Lt(t,"clear",void 0,void 0),n}function Ks(t,e){return function(s,o){const r=this,i=r.__v_raw,a=me(i),l=e?Jr:t?Zr:Ts;return!t&&Xe(a,"iterate",pn),i.forEach((d,u)=>s.call(o,l(d),l(u),r))}}function Ws(t,e,n){return function(...s){const o=this.__v_raw,r=me(o),i=kn(r),a=t==="entries"||t===Symbol.iterator&&i,l=t==="keys"&&i,d=o[t](...s),u=n?Jr:e?Zr:Ts;return!e&&Xe(r,"iterate",l?_r:pn),{next(){const{value:f,done:h}=d.next();return h?{value:f,done:h}:{value:a?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function jt(t){return function(...e){return t==="delete"?!1:this}}function dd(){const t={get(r){return Hs(this,r)},get size(){return Us(this)},has:Bs,add:Mi,set:Vi,delete:ji,clear:Fi,forEach:Ks(!1,!1)},e={get(r){return Hs(this,r,!1,!0)},get size(){return Us(this)},has:Bs,add:Mi,set:Vi,delete:ji,clear:Fi,forEach:Ks(!1,!0)},n={get(r){return Hs(this,r,!0)},get size(){return Us(this,!0)},has(r){return Bs.call(this,r,!0)},add:jt("add"),set:jt("set"),delete:jt("delete"),clear:jt("clear"),forEach:Ks(!0,!1)},s={get(r){return Hs(this,r,!0,!0)},get size(){return Us(this,!0)},has(r){return Bs.call(this,r,!0)},add:jt("add"),set:jt("set"),delete:jt("delete"),clear:jt("clear"),forEach:Ks(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{t[r]=Ws(r,!1,!1),n[r]=Ws(r,!0,!1),e[r]=Ws(r,!1,!0),s[r]=Ws(r,!0,!0)}),[t,n,e,s]}const[fd,hd,pd,md]=dd();function Xr(t,e){const n=e?t?md:pd:t?hd:fd;return(s,o,r)=>o==="__v_isReactive"?!t:o==="__v_isReadonly"?t:o==="__v_raw"?s:Reflect.get(pe(n,o)&&o in s?n:s,o,r)}const _d={get:Xr(!1,!1)},gd={get:Xr(!1,!0)},vd={get:Xr(!0,!1)},Pl=new WeakMap,Nl=new WeakMap,Dl=new WeakMap,bd=new WeakMap;function yd(t){switch(t){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ed(t){return t.__v_skip||!Object.isExtensible(t)?0:yd(Ku(t))}function ts(t){return Vn(t)?t:Qr(t,!1,xl,_d,Pl)}function wd(t){return Qr(t,!1,ud,gd,Nl)}function Il(t){return Qr(t,!0,cd,vd,Dl)}function Qr(t,e,n,s,o){if(!Te(t)||t.__v_raw&&!(e&&t.__v_isReactive))return t;const r=o.get(t);if(r)return r;const i=Ed(t);if(i===0)return t;const a=new Proxy(t,i===2?s:n);return o.set(t,a),a}function Yt(t){return Vn(t)?Yt(t.__v_raw):!!(t&&t.__v_isReactive)}function Vn(t){return!!(t&&t.__v_isReadonly)}function co(t){return!!(t&&t.__v_isShallow)}function Ll(t){return Yt(t)||Vn(t)}function me(t){const e=t&&t.__v_raw;return e?me(e):t}function jn(t){return lo(t,"__v_skip",!0),t}const Ts=t=>Te(t)?ts(t):t,Zr=t=>Te(t)?Il(t):t;function kl(t){zt&&ft&&(t=me(t),Cl(t.dep||(t.dep=Yr())))}function Rl(t,e){t=me(t),t.dep&&gr(t.dep)}function xe(t){return!!(t&&t.__v_isRef===!0)}function oe(t){return Ml(t,!1)}function Td(t){return Ml(t,!0)}function Ml(t,e){return xe(t)?t:new Ad(t,e)}class Ad{constructor(e,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?e:me(e),this._value=n?e:Ts(e)}get value(){return kl(this),this._value}set value(e){const n=this.__v_isShallow||co(e)||Vn(e);e=n?e:me(e),Es(e,this._rawValue)&&(this._rawValue=e,this._value=n?e:Ts(e),Rl(this))}}function w(t){return xe(t)?t.value:t}const Sd={get:(t,e,n)=>w(Reflect.get(t,e,n)),set:(t,e,n,s)=>{const o=t[e];return xe(o)&&!xe(n)?(o.value=n,!0):Reflect.set(t,e,n,s)}};function Vl(t){return Yt(t)?t:new Proxy(t,Sd)}function Cd(t){const e=J(t)?new Array(t.length):{};for(const n in t)e[n]=Od(t,n);return e}class $d{constructor(e,n,s){this._object=e,this._key=n,this._defaultValue=s,this.__v_isRef=!0}get value(){const e=this._object[this._key];return e===void 0?this._defaultValue:e}set value(e){this._object[this._key]=e}}function Od(t,e,n){const s=t[e];return xe(s)?s:new $d(t,e,n)}var jl;class xd{constructor(e,n,s,o){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this[jl]=!1,this._dirty=!0,this.effect=new qr(e,()=>{this._dirty||(this._dirty=!0,Rl(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=s}get value(){const e=me(this);return kl(e),(e._dirty||!e._cacheable)&&(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}jl="__v_isReadonly";function Pd(t,e,n=!1){let s,o;const r=ne(t);return r?(s=t,o=pt):(s=t.get,o=t.set),new xd(s,o,r||!o,n)}function qt(t,e,n,s){let o;try{o=s?t(...s):t()}catch(r){wo(r,e,n)}return o}function st(t,e,n,s){if(ne(t)){const r=qt(t,e,n,s);return r&&vl(r)&&r.catch(i=>{wo(i,e,n)}),r}const o=[];for(let r=0;r<t.length;r++)o.push(st(t[r],e,n,s));return o}function wo(t,e,n,s=!0){const o=e?e.vnode:null;if(e){let r=e.parent;const i=e.proxy,a=n;for(;r;){const d=r.ec;if(d){for(let u=0;u<d.length;u++)if(d[u](t,i,a)===!1)return}r=r.parent}const l=e.appContext.config.errorHandler;if(l){qt(l,null,10,[t,i,a]);return}}Nd(t,n,o,s)}function Nd(t,e,n,s=!0){console.error(t)}let As=!1,vr=!1;const je=[];let wt=0;const Rn=[];let Ot=null,ln=0;const Fl=Promise.resolve();let ei=null;function ti(t){const e=ei||Fl;return t?e.then(this?t.bind(this):t):e}function Dd(t){let e=wt+1,n=je.length;for(;e<n;){const s=e+n>>>1;Ss(je[s])<t?e=s+1:n=s}return e}function ni(t){(!je.length||!je.includes(t,As&&t.allowRecurse?wt+1:wt))&&(t.id==null?je.push(t):je.splice(Dd(t.id),0,t),Hl())}function Hl(){!As&&!vr&&(vr=!0,ei=Fl.then(Ul))}function Id(t){const e=je.indexOf(t);e>wt&&je.splice(e,1)}function Ld(t){J(t)?Rn.push(...t):(!Ot||!Ot.includes(t,t.allowRecurse?ln+1:ln))&&Rn.push(t),Hl()}function Hi(t,e=As?wt+1:0){for(;e<je.length;e++){const n=je[e];n&&n.pre&&(je.splice(e,1),e--,n())}}function Bl(t){if(Rn.length){const e=[...new Set(Rn)];if(Rn.length=0,Ot){Ot.push(...e);return}for(Ot=e,Ot.sort((n,s)=>Ss(n)-Ss(s)),ln=0;ln<Ot.length;ln++)Ot[ln]();Ot=null,ln=0}}const Ss=t=>t.id==null?1/0:t.id,kd=(t,e)=>{const n=Ss(t)-Ss(e);if(n===0){if(t.pre&&!e.pre)return-1;if(e.pre&&!t.pre)return 1}return n};function Ul(t){vr=!1,As=!0,je.sort(kd);const e=pt;try{for(wt=0;wt<je.length;wt++){const n=je[wt];n&&n.active!==!1&&qt(n,null,14)}}finally{wt=0,je.length=0,Bl(),As=!1,ei=null,(je.length||Rn.length)&&Ul()}}function Rd(t,e,...n){if(t.isUnmounted)return;const s=t.vnode.props||Ce;let o=n;const r=e.startsWith("update:"),i=r&&e.slice(7);if(i&&i in s){const u=`${i==="modelValue"?"model":i}Modifiers`,{number:f,trim:h}=s[u]||Ce;h&&(o=n.map(m=>ke(m)?m.trim():m)),f&&(o=n.map(ws))}let a,l=s[a=Wo(e)]||s[a=Wo(St(e))];!l&&r&&(l=s[a=Wo(Qn(e))]),l&&st(l,t,6,o);const d=s[a+"Once"];if(d){if(!t.emitted)t.emitted={};else if(t.emitted[a])return;t.emitted[a]=!0,st(d,t,6,o)}}function Kl(t,e,n=!1){const s=e.emitsCache,o=s.get(t);if(o!==void 0)return o;const r=t.emits;let i={},a=!1;if(!ne(t)){const l=d=>{const u=Kl(d,e,!0);u&&(a=!0,Fe(i,u))};!n&&e.mixins.length&&e.mixins.forEach(l),t.extends&&l(t.extends),t.mixins&&t.mixins.forEach(l)}return!r&&!a?(Te(t)&&s.set(t,null),null):(J(r)?r.forEach(l=>i[l]=null):Fe(i,r),Te(t)&&s.set(t,i),i)}function To(t,e){return!t||!vo(e)?!1:(e=e.slice(2).replace(/Once$/,""),pe(t,e[0].toLowerCase()+e.slice(1))||pe(t,Qn(e))||pe(t,e))}let Je=null,Ao=null;function uo(t){const e=Je;return Je=t,Ao=t&&t.type.__scopeId||null,e}function Ls(t){Ao=t}function ks(){Ao=null}function ue(t,e=Je,n){if(!e||t._n)return t;const s=(...o)=>{s._d&&Xi(-1);const r=uo(e);let i;try{i=t(...o)}finally{uo(r),s._d&&Xi(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function zo(t){const{type:e,vnode:n,proxy:s,withProxy:o,props:r,propsOptions:[i],slots:a,attrs:l,emit:d,render:u,renderCache:f,data:h,setupState:m,ctx:g,inheritAttrs:v}=t;let D,S;const j=uo(t);try{if(n.shapeFlag&4){const C=o||s;D=Et(u.call(C,C,f,r,m,h,g)),S=l}else{const C=e;D=Et(C.length>1?C(r,{attrs:l,slots:a,emit:d}):C(r,null)),S=e.props?l:Md(l)}}catch(C){ps.length=0,wo(C,t,1),D=Q(mt)}let $=D;if(S&&v!==!1){const C=Object.keys(S),{shapeFlag:M}=$;C.length&&M&7&&(i&&C.some(Kr)&&(S=Vd(S,i)),$=Jt($,S))}return n.dirs&&($=Jt($),$.dirs=$.dirs?$.dirs.concat(n.dirs):n.dirs),n.transition&&($.transition=n.transition),D=$,uo(j),D}const Md=t=>{let e;for(const n in t)(n==="class"||n==="style"||vo(n))&&((e||(e={}))[n]=t[n]);return e},Vd=(t,e)=>{const n={};for(const s in t)(!Kr(s)||!(s.slice(9)in e))&&(n[s]=t[s]);return n};function jd(t,e,n){const{props:s,children:o,component:r}=t,{props:i,children:a,patchFlag:l}=e,d=r.emitsOptions;if(e.dirs||e.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?Bi(s,i,d):!!i;if(l&8){const u=e.dynamicProps;for(let f=0;f<u.length;f++){const h=u[f];if(i[h]!==s[h]&&!To(d,h))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:s===i?!1:s?i?Bi(s,i,d):!0:!!i;return!1}function Bi(t,e,n){const s=Object.keys(e);if(s.length!==Object.keys(t).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(e[r]!==t[r]&&!To(n,r))return!0}return!1}function Fd({vnode:t,parent:e},n){for(;e&&e.subTree===t;)(t=e.vnode).el=n,e=e.parent}const Hd=t=>t.__isSuspense;function Bd(t,e){e&&e.pendingBranch?J(t)?e.effects.push(...t):e.effects.push(t):Ld(t)}function no(t,e){if(Me){let n=Me.provides;const s=Me.parent&&Me.parent.provides;s===n&&(n=Me.provides=Object.create(s)),n[t]=e}}function ot(t,e,n=!1){const s=Me||Je;if(s){const o=s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides;if(o&&t in o)return o[t];if(arguments.length>1)return n&&ne(e)?e.call(s.proxy):e}}const zs={};function Pt(t,e,n){return Wl(t,e,n)}function Wl(t,e,{immediate:n,deep:s,flush:o,onTrack:r,onTrigger:i}=Ce){const a=Me;let l,d=!1,u=!1;if(xe(t)?(l=()=>t.value,d=co(t)):Yt(t)?(l=()=>t,s=!0):J(t)?(u=!0,d=t.some($=>Yt($)||co($)),l=()=>t.map($=>{if(xe($))return $.value;if(Yt($))return dn($);if(ne($))return qt($,a,2)})):ne(t)?e?l=()=>qt(t,a,2):l=()=>{if(!(a&&a.isUnmounted))return f&&f(),st(t,a,3,[h])}:l=pt,e&&s){const $=l;l=()=>dn($())}let f,h=$=>{f=S.onStop=()=>{qt($,a,4)}},m;if(Os)if(h=pt,e?n&&st(e,a,3,[l(),u?[]:void 0,h]):l(),o==="sync"){const $=Rf();m=$.__watcherHandles||($.__watcherHandles=[])}else return pt;let g=u?new Array(t.length).fill(zs):zs;const v=()=>{if(!!S.active)if(e){const $=S.run();(s||d||(u?$.some((C,M)=>Es(C,g[M])):Es($,g)))&&(f&&f(),st(e,a,3,[$,g===zs?void 0:u&&g[0]===zs?[]:g,h]),g=$)}else S.run()};v.allowRecurse=!!e;let D;o==="sync"?D=v:o==="post"?D=()=>We(v,a&&a.suspense):(v.pre=!0,a&&(v.id=a.uid),D=()=>ni(v));const S=new qr(l,D);e?n?v():g=S.run():o==="post"?We(S.run.bind(S),a&&a.suspense):S.run();const j=()=>{S.stop(),a&&a.scope&&Wr(a.scope.effects,S)};return m&&m.push(j),j}function Ud(t,e,n){const s=this.proxy,o=ke(t)?t.includes(".")?zl(s,t):()=>s[t]:t.bind(s,s);let r;ne(e)?r=e:(r=e.handler,n=e);const i=Me;Fn(this);const a=Wl(o,r.bind(s),n);return i?Fn(i):mn(),a}function zl(t,e){const n=e.split(".");return()=>{let s=t;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}function dn(t,e){if(!Te(t)||t.__v_skip||(e=e||new Set,e.has(t)))return t;if(e.add(t),xe(t))dn(t.value,e);else if(J(t))for(let n=0;n<t.length;n++)dn(t[n],e);else if(Xn(t)||kn(t))t.forEach(n=>{dn(n,e)});else if(yl(t))for(const n in t)dn(t[n],e);return t}function Kd(){const t={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Xl(()=>{t.isMounted=!0}),Ql(()=>{t.isUnmounting=!0}),t}const nt=[Function,Array],Wd={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:nt,onEnter:nt,onAfterEnter:nt,onEnterCancelled:nt,onBeforeLeave:nt,onLeave:nt,onAfterLeave:nt,onLeaveCancelled:nt,onBeforeAppear:nt,onAppear:nt,onAfterAppear:nt,onAppearCancelled:nt},setup(t,{slots:e}){const n=dc(),s=Kd();let o;return()=>{const r=e.default&&ql(e.default(),!0);if(!r||!r.length)return;let i=r[0];if(r.length>1){for(const v of r)if(v.type!==mt){i=v;break}}const a=me(t),{mode:l}=a;if(s.isLeaving)return Yo(i);const d=Ui(i);if(!d)return Yo(i);const u=br(d,a,s,n);yr(d,u);const f=n.subTree,h=f&&Ui(f);let m=!1;const{getTransitionKey:g}=d.type;if(g){const v=g();o===void 0?o=v:v!==o&&(o=v,m=!0)}if(h&&h.type!==mt&&(!cn(d,h)||m)){const v=br(h,a,s,n);if(yr(h,v),l==="out-in")return s.isLeaving=!0,v.afterLeave=()=>{s.isLeaving=!1,n.update.active!==!1&&n.update()},Yo(i);l==="in-out"&&d.type!==mt&&(v.delayLeave=(D,S,j)=>{const $=Yl(s,h);$[String(h.key)]=h,D._leaveCb=()=>{S(),D._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=j})}return i}}},zd=Wd;function Yl(t,e){const{leavingVNodes:n}=t;let s=n.get(e.type);return s||(s=Object.create(null),n.set(e.type,s)),s}function br(t,e,n,s){const{appear:o,mode:r,persisted:i=!1,onBeforeEnter:a,onEnter:l,onAfterEnter:d,onEnterCancelled:u,onBeforeLeave:f,onLeave:h,onAfterLeave:m,onLeaveCancelled:g,onBeforeAppear:v,onAppear:D,onAfterAppear:S,onAppearCancelled:j}=e,$=String(t.key),C=Yl(n,t),M=(L,X)=>{L&&st(L,s,9,X)},N=(L,X)=>{const G=X[1];M(L,X),J(L)?L.every(re=>re.length<=1)&&G():L.length<=1&&G()},U={mode:r,persisted:i,beforeEnter(L){let X=a;if(!n.isMounted)if(o)X=v||a;else return;L._leaveCb&&L._leaveCb(!0);const G=C[$];G&&cn(t,G)&&G.el._leaveCb&&G.el._leaveCb(),M(X,[L])},enter(L){let X=l,G=d,re=u;if(!n.isMounted)if(o)X=D||l,G=S||d,re=j||u;else return;let ge=!1;const ve=L._enterCb=$e=>{ge||(ge=!0,$e?M(re,[L]):M(G,[L]),U.delayedLeave&&U.delayedLeave(),L._enterCb=void 0)};X?N(X,[L,ve]):ve()},leave(L,X){const G=String(t.key);if(L._enterCb&&L._enterCb(!0),n.isUnmounting)return X();M(f,[L]);let re=!1;const ge=L._leaveCb=ve=>{re||(re=!0,X(),ve?M(g,[L]):M(m,[L]),L._leaveCb=void 0,C[G]===t&&delete C[G])};C[G]=t,h?N(h,[L,ge]):ge()},clone(L){return br(L,e,n,s)}};return U}function Yo(t){if(So(t))return t=Jt(t),t.children=null,t}function Ui(t){return So(t)?t.children?t.children[0]:void 0:t}function yr(t,e){t.shapeFlag&6&&t.component?yr(t.component.subTree,e):t.shapeFlag&128?(t.ssContent.transition=e.clone(t.ssContent),t.ssFallback.transition=e.clone(t.ssFallback)):t.transition=e}function ql(t,e=!1,n){let s=[],o=0;for(let r=0;r<t.length;r++){let i=t[r];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:r);i.type===Ee?(i.patchFlag&128&&o++,s=s.concat(ql(i.children,e,a))):(e||i.type!==mt)&&s.push(a!=null?Jt(i,{key:a}):i)}if(o>1)for(let r=0;r<s.length;r++)s[r].patchFlag=-2;return s}function Gl(t){return ne(t)?{setup:t,name:t.name}:t}const so=t=>!!t.type.__asyncLoader,So=t=>t.type.__isKeepAlive;function Yd(t,e){Jl(t,"a",e)}function qd(t,e){Jl(t,"da",e)}function Jl(t,e,n=Me){const s=t.__wdc||(t.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return t()});if(Co(e,s,n),n){let o=n.parent;for(;o&&o.parent;)So(o.parent.vnode)&&Gd(s,e,n,o),o=o.parent}}function Gd(t,e,n,s){const o=Co(e,t,s,!0);Zl(()=>{Wr(s[e],o)},n)}function Co(t,e,n=Me,s=!1){if(n){const o=n[t]||(n[t]=[]),r=e.__weh||(e.__weh=(...i)=>{if(n.isUnmounted)return;Zn(),Fn(n);const a=st(e,n,t,i);return mn(),es(),a});return s?o.unshift(r):o.push(r),r}}const Rt=t=>(e,n=Me)=>(!Os||t==="sp")&&Co(t,(...s)=>e(...s),n),ze=Rt("bm"),Xl=Rt("m"),Jd=Rt("bu"),Xd=Rt("u"),Ql=Rt("bum"),Zl=Rt("um"),Qd=Rt("sp"),Zd=Rt("rtg"),ef=Rt("rtc");function tf(t,e=Me){Co("ec",t,e)}function te(t,e){const n=Je;if(n===null)return t;const s=xo(n)||n.proxy,o=t.dirs||(t.dirs=[]);for(let r=0;r<e.length;r++){let[i,a,l,d=Ce]=e[r];i&&(ne(i)&&(i={mounted:i,updated:i}),i.deep&&dn(a),o.push({dir:i,instance:s,value:a,oldValue:void 0,arg:l,modifiers:d}))}return t}function on(t,e,n,s){const o=t.dirs,r=e&&e.dirs;for(let i=0;i<o.length;i++){const a=o[i];r&&(a.oldValue=r[i].value);let l=a.dir[s];l&&(Zn(),st(l,n,8,[t.el,a,t,e]),es())}}const ec="components";function Ye(t,e){return sf(ec,t,!0,e)||t}const nf=Symbol();function sf(t,e,n=!0,s=!1){const o=Je||Me;if(o){const r=o.type;if(t===ec){const a=If(r,!1);if(a&&(a===e||a===St(e)||a===yo(St(e))))return r}const i=Ki(o[t]||r[t],e)||Ki(o.appContext[t],e);return!i&&s?r:i}}function Ki(t,e){return t&&(t[e]||t[St(e)]||t[yo(St(e))])}function Re(t,e,n,s){let o;const r=n&&n[s];if(J(t)||ke(t)){o=new Array(t.length);for(let i=0,a=t.length;i<a;i++)o[i]=e(t[i],i,void 0,r&&r[i])}else if(typeof t=="number"){o=new Array(t);for(let i=0;i<t;i++)o[i]=e(i+1,i,void 0,r&&r[i])}else if(Te(t))if(t[Symbol.iterator])o=Array.from(t,(i,a)=>e(i,a,void 0,r&&r[a]));else{const i=Object.keys(t);o=new Array(i.length);for(let a=0,l=i.length;a<l;a++){const d=i[a];o[a]=e(t[d],d,a,r&&r[a])}}else o=[];return n&&(n[s]=o),o}const Er=t=>t?fc(t)?xo(t)||t.proxy:Er(t.parent):null,hs=Fe(Object.create(null),{$:t=>t,$el:t=>t.vnode.el,$data:t=>t.data,$props:t=>t.props,$attrs:t=>t.attrs,$slots:t=>t.slots,$refs:t=>t.refs,$parent:t=>Er(t.parent),$root:t=>Er(t.root),$emit:t=>t.emit,$options:t=>si(t),$forceUpdate:t=>t.f||(t.f=()=>ni(t.update)),$nextTick:t=>t.n||(t.n=ti.bind(t.proxy)),$watch:t=>Ud.bind(t)}),qo=(t,e)=>t!==Ce&&!t.__isScriptSetup&&pe(t,e),of={get({_:t},e){const{ctx:n,setupState:s,data:o,props:r,accessCache:i,type:a,appContext:l}=t;let d;if(e[0]!=="$"){const m=i[e];if(m!==void 0)switch(m){case 1:return s[e];case 2:return o[e];case 4:return n[e];case 3:return r[e]}else{if(qo(s,e))return i[e]=1,s[e];if(o!==Ce&&pe(o,e))return i[e]=2,o[e];if((d=t.propsOptions[0])&&pe(d,e))return i[e]=3,r[e];if(n!==Ce&&pe(n,e))return i[e]=4,n[e];wr&&(i[e]=0)}}const u=hs[e];let f,h;if(u)return e==="$attrs"&&Xe(t,"get",e),u(t);if((f=a.__cssModules)&&(f=f[e]))return f;if(n!==Ce&&pe(n,e))return i[e]=4,n[e];if(h=l.config.globalProperties,pe(h,e))return h[e]},set({_:t},e,n){const{data:s,setupState:o,ctx:r}=t;return qo(o,e)?(o[e]=n,!0):s!==Ce&&pe(s,e)?(s[e]=n,!0):pe(t.props,e)||e[0]==="$"&&e.slice(1)in t?!1:(r[e]=n,!0)},has({_:{data:t,setupState:e,accessCache:n,ctx:s,appContext:o,propsOptions:r}},i){let a;return!!n[i]||t!==Ce&&pe(t,i)||qo(e,i)||(a=r[0])&&pe(a,i)||pe(s,i)||pe(hs,i)||pe(o.config.globalProperties,i)},defineProperty(t,e,n){return n.get!=null?t._.accessCache[e]=0:pe(n,"value")&&this.set(t,e,n.value,null),Reflect.defineProperty(t,e,n)}};let wr=!0;function rf(t){const e=si(t),n=t.proxy,s=t.ctx;wr=!1,e.beforeCreate&&Wi(e.beforeCreate,t,"bc");const{data:o,computed:r,methods:i,watch:a,provide:l,inject:d,created:u,beforeMount:f,mounted:h,beforeUpdate:m,updated:g,activated:v,deactivated:D,beforeDestroy:S,beforeUnmount:j,destroyed:$,unmounted:C,render:M,renderTracked:N,renderTriggered:U,errorCaptured:L,serverPrefetch:X,expose:G,inheritAttrs:re,components:ge,directives:ve,filters:$e}=e;if(d&&af(d,s,null,t.appContext.config.unwrapInjectedRef),i)for(const ae in i){const he=i[ae];ne(he)&&(s[ae]=he.bind(n))}if(o){const ae=o.call(n,n);Te(ae)&&(t.data=ts(ae))}if(wr=!0,r)for(const ae in r){const he=r[ae],De=ne(he)?he.bind(n,n):ne(he.get)?he.get.bind(n,n):pt,tt=!ne(he)&&ne(he.set)?he.set.bind(n):pt,He=Oe({get:De,set:tt});Object.defineProperty(s,ae,{enumerable:!0,configurable:!0,get:()=>He.value,set:Pe=>He.value=Pe})}if(a)for(const ae in a)tc(a[ae],s,n,ae);if(l){const ae=ne(l)?l.call(n):l;Reflect.ownKeys(ae).forEach(he=>{no(he,ae[he])})}u&&Wi(u,t,"c");function de(ae,he){J(he)?he.forEach(De=>ae(De.bind(n))):he&&ae(he.bind(n))}if(de(ze,f),de(Xl,h),de(Jd,m),de(Xd,g),de(Yd,v),de(qd,D),de(tf,L),de(ef,N),de(Zd,U),de(Ql,j),de(Zl,C),de(Qd,X),J(G))if(G.length){const ae=t.exposed||(t.exposed={});G.forEach(he=>{Object.defineProperty(ae,he,{get:()=>n[he],set:De=>n[he]=De})})}else t.exposed||(t.exposed={});M&&t.render===pt&&(t.render=M),re!=null&&(t.inheritAttrs=re),ge&&(t.components=ge),ve&&(t.directives=ve)}function af(t,e,n=pt,s=!1){J(t)&&(t=Tr(t));for(const o in t){const r=t[o];let i;Te(r)?"default"in r?i=ot(r.from||o,r.default,!0):i=ot(r.from||o):i=ot(r),xe(i)&&s?Object.defineProperty(e,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):e[o]=i}}function Wi(t,e,n){st(J(t)?t.map(s=>s.bind(e.proxy)):t.bind(e.proxy),e,n)}function tc(t,e,n,s){const o=s.includes(".")?zl(n,s):()=>n[s];if(ke(t)){const r=e[t];ne(r)&&Pt(o,r)}else if(ne(t))Pt(o,t.bind(n));else if(Te(t))if(J(t))t.forEach(r=>tc(r,e,n,s));else{const r=ne(t.handler)?t.handler.bind(n):e[t.handler];ne(r)&&Pt(o,r,t)}}function si(t){const e=t.type,{mixins:n,extends:s}=e,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=t.appContext,a=r.get(e);let l;return a?l=a:!o.length&&!n&&!s?l=e:(l={},o.length&&o.forEach(d=>fo(l,d,i,!0)),fo(l,e,i)),Te(e)&&r.set(e,l),l}function fo(t,e,n,s=!1){const{mixins:o,extends:r}=e;r&&fo(t,r,n,!0),o&&o.forEach(i=>fo(t,i,n,!0));for(const i in e)if(!(s&&i==="expose")){const a=lf[i]||n&&n[i];t[i]=a?a(t[i],e[i]):e[i]}return t}const lf={data:zi,props:an,emits:an,methods:an,computed:an,beforeCreate:Be,created:Be,beforeMount:Be,mounted:Be,beforeUpdate:Be,updated:Be,beforeDestroy:Be,beforeUnmount:Be,destroyed:Be,unmounted:Be,activated:Be,deactivated:Be,errorCaptured:Be,serverPrefetch:Be,components:an,directives:an,watch:uf,provide:zi,inject:cf};function zi(t,e){return e?t?function(){return Fe(ne(t)?t.call(this,this):t,ne(e)?e.call(this,this):e)}:e:t}function cf(t,e){return an(Tr(t),Tr(e))}function Tr(t){if(J(t)){const e={};for(let n=0;n<t.length;n++)e[t[n]]=t[n];return e}return t}function Be(t,e){return t?[...new Set([].concat(t,e))]:e}function an(t,e){return t?Fe(Fe(Object.create(null),t),e):e}function uf(t,e){if(!t)return e;if(!e)return t;const n=Fe(Object.create(null),t);for(const s in e)n[s]=Be(t[s],e[s]);return n}function df(t,e,n,s=!1){const o={},r={};lo(r,Oo,1),t.propsDefaults=Object.create(null),nc(t,e,o,r);for(const i in t.propsOptions[0])i in o||(o[i]=void 0);n?t.props=s?o:wd(o):t.type.props?t.props=o:t.props=r,t.attrs=r}function ff(t,e,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=t,a=me(o),[l]=t.propsOptions;let d=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=t.vnode.dynamicProps;for(let f=0;f<u.length;f++){let h=u[f];if(To(t.emitsOptions,h))continue;const m=e[h];if(l)if(pe(r,h))m!==r[h]&&(r[h]=m,d=!0);else{const g=St(h);o[g]=Ar(l,a,g,m,t,!1)}else m!==r[h]&&(r[h]=m,d=!0)}}}else{nc(t,e,o,r)&&(d=!0);let u;for(const f in a)(!e||!pe(e,f)&&((u=Qn(f))===f||!pe(e,u)))&&(l?n&&(n[f]!==void 0||n[u]!==void 0)&&(o[f]=Ar(l,a,f,void 0,t,!0)):delete o[f]);if(r!==a)for(const f in r)(!e||!pe(e,f)&&!0)&&(delete r[f],d=!0)}d&&Lt(t,"set","$attrs")}function nc(t,e,n,s){const[o,r]=t.propsOptions;let i=!1,a;if(e)for(let l in e){if(eo(l))continue;const d=e[l];let u;o&&pe(o,u=St(l))?!r||!r.includes(u)?n[u]=d:(a||(a={}))[u]=d:To(t.emitsOptions,l)||(!(l in s)||d!==s[l])&&(s[l]=d,i=!0)}if(r){const l=me(n),d=a||Ce;for(let u=0;u<r.length;u++){const f=r[u];n[f]=Ar(o,l,f,d[f],t,!pe(d,f))}}return i}function Ar(t,e,n,s,o,r){const i=t[n];if(i!=null){const a=pe(i,"default");if(a&&s===void 0){const l=i.default;if(i.type!==Function&&ne(l)){const{propsDefaults:d}=o;n in d?s=d[n]:(Fn(o),s=d[n]=l.call(null,e),mn())}else s=l}i[0]&&(r&&!a?s=!1:i[1]&&(s===""||s===Qn(n))&&(s=!0))}return s}function sc(t,e,n=!1){const s=e.propsCache,o=s.get(t);if(o)return o;const r=t.props,i={},a=[];let l=!1;if(!ne(t)){const u=f=>{l=!0;const[h,m]=sc(f,e,!0);Fe(i,h),m&&a.push(...m)};!n&&e.mixins.length&&e.mixins.forEach(u),t.extends&&u(t.extends),t.mixins&&t.mixins.forEach(u)}if(!r&&!l)return Te(t)&&s.set(t,Ln),Ln;if(J(r))for(let u=0;u<r.length;u++){const f=St(r[u]);Yi(f)&&(i[f]=Ce)}else if(r)for(const u in r){const f=St(u);if(Yi(f)){const h=r[u],m=i[f]=J(h)||ne(h)?{type:h}:Object.assign({},h);if(m){const g=Ji(Boolean,m.type),v=Ji(String,m.type);m[0]=g>-1,m[1]=v<0||g<v,(g>-1||pe(m,"default"))&&a.push(f)}}}const d=[i,a];return Te(t)&&s.set(t,d),d}function Yi(t){return t[0]!=="$"}function qi(t){const e=t&&t.toString().match(/^\s*function (\w+)/);return e?e[1]:t===null?"null":""}function Gi(t,e){return qi(t)===qi(e)}function Ji(t,e){return J(e)?e.findIndex(n=>Gi(n,t)):ne(e)&&Gi(e,t)?0:-1}const oc=t=>t[0]==="_"||t==="$stable",oi=t=>J(t)?t.map(Et):[Et(t)],hf=(t,e,n)=>{if(e._n)return e;const s=ue((...o)=>oi(e(...o)),n);return s._c=!1,s},rc=(t,e,n)=>{const s=t._ctx;for(const o in t){if(oc(o))continue;const r=t[o];if(ne(r))e[o]=hf(o,r,s);else if(r!=null){const i=oi(r);e[o]=()=>i}}},ic=(t,e)=>{const n=oi(e);t.slots.default=()=>n},pf=(t,e)=>{if(t.vnode.shapeFlag&32){const n=e._;n?(t.slots=me(e),lo(e,"_",n)):rc(e,t.slots={})}else t.slots={},e&&ic(t,e);lo(t.slots,Oo,1)},mf=(t,e,n)=>{const{vnode:s,slots:o}=t;let r=!0,i=Ce;if(s.shapeFlag&32){const a=e._;a?n&&a===1?r=!1:(Fe(o,e),!n&&a===1&&delete o._):(r=!e.$stable,rc(e,o)),i=e}else e&&(ic(t,e),i={default:1});if(r)for(const a in o)!oc(a)&&!(a in i)&&delete o[a]};function ac(){return{app:null,config:{isNativeTag:Hu,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let _f=0;function gf(t,e){return function(s,o=null){ne(s)||(s=Object.assign({},s)),o!=null&&!Te(o)&&(o=null);const r=ac(),i=new Set;let a=!1;const l=r.app={_uid:_f++,_component:s,_props:o,_container:null,_context:r,_instance:null,version:Mf,get config(){return r.config},set config(d){},use(d,...u){return i.has(d)||(d&&ne(d.install)?(i.add(d),d.install(l,...u)):ne(d)&&(i.add(d),d(l,...u))),l},mixin(d){return r.mixins.includes(d)||r.mixins.push(d),l},component(d,u){return u?(r.components[d]=u,l):r.components[d]},directive(d,u){return u?(r.directives[d]=u,l):r.directives[d]},mount(d,u,f){if(!a){const h=Q(s,o);return h.appContext=r,u&&e?e(h,d):t(h,d,f),a=!0,l._container=d,d.__vue_app__=l,xo(h.component)||h.component.proxy}},unmount(){a&&(t(null,l._container),delete l._container.__vue_app__)},provide(d,u){return r.provides[d]=u,l}};return l}}function Sr(t,e,n,s,o=!1){if(J(t)){t.forEach((h,m)=>Sr(h,e&&(J(e)?e[m]:e),n,s,o));return}if(so(s)&&!o)return;const r=s.shapeFlag&4?xo(s.component)||s.component.proxy:s.el,i=o?null:r,{i:a,r:l}=t,d=e&&e.r,u=a.refs===Ce?a.refs={}:a.refs,f=a.setupState;if(d!=null&&d!==l&&(ke(d)?(u[d]=null,pe(f,d)&&(f[d]=null)):xe(d)&&(d.value=null)),ne(l))qt(l,a,12,[i,u]);else{const h=ke(l),m=xe(l);if(h||m){const g=()=>{if(t.f){const v=h?pe(f,l)?f[l]:u[l]:l.value;o?J(v)&&Wr(v,r):J(v)?v.includes(r)||v.push(r):h?(u[l]=[r],pe(f,l)&&(f[l]=u[l])):(l.value=[r],t.k&&(u[t.k]=l.value))}else h?(u[l]=i,pe(f,l)&&(f[l]=i)):m&&(l.value=i,t.k&&(u[t.k]=i))};i?(g.id=-1,We(g,n)):g()}}}const We=Bd;function vf(t){return bf(t)}function bf(t,e){const n=Yu();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:i,createText:a,createComment:l,setText:d,setElementText:u,parentNode:f,nextSibling:h,setScopeId:m=pt,insertStaticContent:g}=t,v=(p,_,b,E=null,O=null,k=null,H=!1,I=null,R=!!_.dynamicChildren)=>{if(p===_)return;p&&!cn(p,_)&&(E=V(p),Pe(p,O,k,!0),p=null),_.patchFlag===-2&&(R=!1,_.dynamicChildren=null);const{type:x,ref:Y,shapeFlag:K}=_;switch(x){case $o:D(p,_,b,E);break;case mt:S(p,_,b,E);break;case Go:p==null&&j(_,b,E,H);break;case Ee:ge(p,_,b,E,O,k,H,I,R);break;default:K&1?M(p,_,b,E,O,k,H,I,R):K&6?ve(p,_,b,E,O,k,H,I,R):(K&64||K&128)&&x.process(p,_,b,E,O,k,H,I,R,le)}Y!=null&&O&&Sr(Y,p&&p.ref,k,_||p,!_)},D=(p,_,b,E)=>{if(p==null)s(_.el=a(_.children),b,E);else{const O=_.el=p.el;_.children!==p.children&&d(O,_.children)}},S=(p,_,b,E)=>{p==null?s(_.el=l(_.children||""),b,E):_.el=p.el},j=(p,_,b,E)=>{[p.el,p.anchor]=g(p.children,_,b,E,p.el,p.anchor)},$=({el:p,anchor:_},b,E)=>{let O;for(;p&&p!==_;)O=h(p),s(p,b,E),p=O;s(_,b,E)},C=({el:p,anchor:_})=>{let b;for(;p&&p!==_;)b=h(p),o(p),p=b;o(_)},M=(p,_,b,E,O,k,H,I,R)=>{H=H||_.type==="svg",p==null?N(_,b,E,O,k,H,I,R):X(p,_,O,k,H,I,R)},N=(p,_,b,E,O,k,H,I)=>{let R,x;const{type:Y,props:K,shapeFlag:z,transition:Z,dirs:ie}=p;if(R=p.el=i(p.type,k,K&&K.is,K),z&8?u(R,p.children):z&16&&L(p.children,R,null,E,O,k&&Y!=="foreignObject",H,I),ie&&on(p,null,E,"created"),K){for(const _e in K)_e!=="value"&&!eo(_e)&&r(R,_e,null,K[_e],k,p.children,E,O,F);"value"in K&&r(R,"value",null,K.value),(x=K.onVnodeBeforeMount)&&yt(x,E,p)}U(R,p,p.scopeId,H,E),ie&&on(p,null,E,"beforeMount");const be=(!O||O&&!O.pendingBranch)&&Z&&!Z.persisted;be&&Z.beforeEnter(R),s(R,_,b),((x=K&&K.onVnodeMounted)||be||ie)&&We(()=>{x&&yt(x,E,p),be&&Z.enter(R),ie&&on(p,null,E,"mounted")},O)},U=(p,_,b,E,O)=>{if(b&&m(p,b),E)for(let k=0;k<E.length;k++)m(p,E[k]);if(O){let k=O.subTree;if(_===k){const H=O.vnode;U(p,H,H.scopeId,H.slotScopeIds,O.parent)}}},L=(p,_,b,E,O,k,H,I,R=0)=>{for(let x=R;x<p.length;x++){const Y=p[x]=I?Bt(p[x]):Et(p[x]);v(null,Y,_,b,E,O,k,H,I)}},X=(p,_,b,E,O,k,H)=>{const I=_.el=p.el;let{patchFlag:R,dynamicChildren:x,dirs:Y}=_;R|=p.patchFlag&16;const K=p.props||Ce,z=_.props||Ce;let Z;b&&rn(b,!1),(Z=z.onVnodeBeforeUpdate)&&yt(Z,b,_,p),Y&&on(_,p,b,"beforeUpdate"),b&&rn(b,!0);const ie=O&&_.type!=="foreignObject";if(x?G(p.dynamicChildren,x,I,b,E,ie,k):H||he(p,_,I,null,b,E,ie,k,!1),R>0){if(R&16)re(I,_,K,z,b,E,O);else if(R&2&&K.class!==z.class&&r(I,"class",null,z.class,O),R&4&&r(I,"style",K.style,z.style,O),R&8){const be=_.dynamicProps;for(let _e=0;_e<be.length;_e++){const Ne=be[_e],dt=K[Ne],An=z[Ne];(An!==dt||Ne==="value")&&r(I,Ne,dt,An,O,p.children,b,E,F)}}R&1&&p.children!==_.children&&u(I,_.children)}else!H&&x==null&&re(I,_,K,z,b,E,O);((Z=z.onVnodeUpdated)||Y)&&We(()=>{Z&&yt(Z,b,_,p),Y&&on(_,p,b,"updated")},E)},G=(p,_,b,E,O,k,H)=>{for(let I=0;I<_.length;I++){const R=p[I],x=_[I],Y=R.el&&(R.type===Ee||!cn(R,x)||R.shapeFlag&70)?f(R.el):b;v(R,x,Y,null,E,O,k,H,!0)}},re=(p,_,b,E,O,k,H)=>{if(b!==E){if(b!==Ce)for(const I in b)!eo(I)&&!(I in E)&&r(p,I,b[I],null,H,_.children,O,k,F);for(const I in E){if(eo(I))continue;const R=E[I],x=b[I];R!==x&&I!=="value"&&r(p,I,x,R,H,_.children,O,k,F)}"value"in E&&r(p,"value",b.value,E.value)}},ge=(p,_,b,E,O,k,H,I,R)=>{const x=_.el=p?p.el:a(""),Y=_.anchor=p?p.anchor:a("");let{patchFlag:K,dynamicChildren:z,slotScopeIds:Z}=_;Z&&(I=I?I.concat(Z):Z),p==null?(s(x,b,E),s(Y,b,E),L(_.children,b,Y,O,k,H,I,R)):K>0&&K&64&&z&&p.dynamicChildren?(G(p.dynamicChildren,z,b,O,k,H,I),(_.key!=null||O&&_===O.subTree)&&lc(p,_,!0)):he(p,_,b,Y,O,k,H,I,R)},ve=(p,_,b,E,O,k,H,I,R)=>{_.slotScopeIds=I,p==null?_.shapeFlag&512?O.ctx.activate(_,b,E,H,R):$e(_,b,E,O,k,H,R):we(p,_,R)},$e=(p,_,b,E,O,k,H)=>{const I=p.component=Of(p,E,O);if(So(p)&&(I.ctx.renderer=le),xf(I),I.asyncDep){if(O&&O.registerDep(I,de),!p.el){const R=I.subTree=Q(mt);S(null,R,_,b)}return}de(I,p,_,b,O,k,H)},we=(p,_,b)=>{const E=_.component=p.component;if(jd(p,_,b))if(E.asyncDep&&!E.asyncResolved){ae(E,_,b);return}else E.next=_,Id(E.update),E.update();else _.el=p.el,E.vnode=_},de=(p,_,b,E,O,k,H)=>{const I=()=>{if(p.isMounted){let{next:Y,bu:K,u:z,parent:Z,vnode:ie}=p,be=Y,_e;rn(p,!1),Y?(Y.el=ie.el,ae(p,Y,H)):Y=ie,K&&to(K),(_e=Y.props&&Y.props.onVnodeBeforeUpdate)&&yt(_e,Z,Y,ie),rn(p,!0);const Ne=zo(p),dt=p.subTree;p.subTree=Ne,v(dt,Ne,f(dt.el),V(dt),p,O,k),Y.el=Ne.el,be===null&&Fd(p,Ne.el),z&&We(z,O),(_e=Y.props&&Y.props.onVnodeUpdated)&&We(()=>yt(_e,Z,Y,ie),O)}else{let Y;const{el:K,props:z}=_,{bm:Z,m:ie,parent:be}=p,_e=so(_);if(rn(p,!1),Z&&to(Z),!_e&&(Y=z&&z.onVnodeBeforeMount)&&yt(Y,be,_),rn(p,!0),K&&ee){const Ne=()=>{p.subTree=zo(p),ee(K,p.subTree,p,O,null)};_e?_.type.__asyncLoader().then(()=>!p.isUnmounted&&Ne()):Ne()}else{const Ne=p.subTree=zo(p);v(null,Ne,b,E,p,O,k),_.el=Ne.el}if(ie&&We(ie,O),!_e&&(Y=z&&z.onVnodeMounted)){const Ne=_;We(()=>yt(Y,be,Ne),O)}(_.shapeFlag&256||be&&so(be.vnode)&&be.vnode.shapeFlag&256)&&p.a&&We(p.a,O),p.isMounted=!0,_=b=E=null}},R=p.effect=new qr(I,()=>ni(x),p.scope),x=p.update=()=>R.run();x.id=p.uid,rn(p,!0),x()},ae=(p,_,b)=>{_.component=p;const E=p.vnode.props;p.vnode=_,p.next=null,ff(p,_.props,E,b),mf(p,_.children,b),Zn(),Hi(),es()},he=(p,_,b,E,O,k,H,I,R=!1)=>{const x=p&&p.children,Y=p?p.shapeFlag:0,K=_.children,{patchFlag:z,shapeFlag:Z}=_;if(z>0){if(z&128){tt(x,K,b,E,O,k,H,I,R);return}else if(z&256){De(x,K,b,E,O,k,H,I,R);return}}Z&8?(Y&16&&F(x,O,k),K!==x&&u(b,K)):Y&16?Z&16?tt(x,K,b,E,O,k,H,I,R):F(x,O,k,!0):(Y&8&&u(b,""),Z&16&&L(K,b,E,O,k,H,I,R))},De=(p,_,b,E,O,k,H,I,R)=>{p=p||Ln,_=_||Ln;const x=p.length,Y=_.length,K=Math.min(x,Y);let z;for(z=0;z<K;z++){const Z=_[z]=R?Bt(_[z]):Et(_[z]);v(p[z],Z,b,null,O,k,H,I,R)}x>Y?F(p,O,k,!0,!1,K):L(_,b,E,O,k,H,I,R,K)},tt=(p,_,b,E,O,k,H,I,R)=>{let x=0;const Y=_.length;let K=p.length-1,z=Y-1;for(;x<=K&&x<=z;){const Z=p[x],ie=_[x]=R?Bt(_[x]):Et(_[x]);if(cn(Z,ie))v(Z,ie,b,null,O,k,H,I,R);else break;x++}for(;x<=K&&x<=z;){const Z=p[K],ie=_[z]=R?Bt(_[z]):Et(_[z]);if(cn(Z,ie))v(Z,ie,b,null,O,k,H,I,R);else break;K--,z--}if(x>K){if(x<=z){const Z=z+1,ie=Z<Y?_[Z].el:E;for(;x<=z;)v(null,_[x]=R?Bt(_[x]):Et(_[x]),b,ie,O,k,H,I,R),x++}}else if(x>z)for(;x<=K;)Pe(p[x],O,k,!0),x++;else{const Z=x,ie=x,be=new Map;for(x=ie;x<=z;x++){const qe=_[x]=R?Bt(_[x]):Et(_[x]);qe.key!=null&&be.set(qe.key,x)}let _e,Ne=0;const dt=z-ie+1;let An=!1,xi=0;const ls=new Array(dt);for(x=0;x<dt;x++)ls[x]=0;for(x=Z;x<=K;x++){const qe=p[x];if(Ne>=dt){Pe(qe,O,k,!0);continue}let bt;if(qe.key!=null)bt=be.get(qe.key);else for(_e=ie;_e<=z;_e++)if(ls[_e-ie]===0&&cn(qe,_[_e])){bt=_e;break}bt===void 0?Pe(qe,O,k,!0):(ls[bt-ie]=x+1,bt>=xi?xi=bt:An=!0,v(qe,_[bt],b,null,O,k,H,I,R),Ne++)}const Pi=An?yf(ls):Ln;for(_e=Pi.length-1,x=dt-1;x>=0;x--){const qe=ie+x,bt=_[qe],Ni=qe+1<Y?_[qe+1].el:E;ls[x]===0?v(null,bt,b,Ni,O,k,H,I,R):An&&(_e<0||x!==Pi[_e]?He(bt,b,Ni,2):_e--)}}},He=(p,_,b,E,O=null)=>{const{el:k,type:H,transition:I,children:R,shapeFlag:x}=p;if(x&6){He(p.component.subTree,_,b,E);return}if(x&128){p.suspense.move(_,b,E);return}if(x&64){H.move(p,_,b,le);return}if(H===Ee){s(k,_,b);for(let K=0;K<R.length;K++)He(R[K],_,b,E);s(p.anchor,_,b);return}if(H===Go){$(p,_,b);return}if(E!==2&&x&1&&I)if(E===0)I.beforeEnter(k),s(k,_,b),We(()=>I.enter(k),O);else{const{leave:K,delayLeave:z,afterLeave:Z}=I,ie=()=>s(k,_,b),be=()=>{K(k,()=>{ie(),Z&&Z()})};z?z(k,ie,be):be()}else s(k,_,b)},Pe=(p,_,b,E=!1,O=!1)=>{const{type:k,props:H,ref:I,children:R,dynamicChildren:x,shapeFlag:Y,patchFlag:K,dirs:z}=p;if(I!=null&&Sr(I,null,b,p,!0),Y&256){_.ctx.deactivate(p);return}const Z=Y&1&&z,ie=!so(p);let be;if(ie&&(be=H&&H.onVnodeBeforeUnmount)&&yt(be,_,p),Y&6)A(p.component,b,E);else{if(Y&128){p.suspense.unmount(b,E);return}Z&&on(p,null,_,"beforeUnmount"),Y&64?p.type.remove(p,_,b,O,le,E):x&&(k!==Ee||K>0&&K&64)?F(x,_,b,!1,!0):(k===Ee&&K&384||!O&&Y&16)&&F(R,_,b),E&&ct(p)}(ie&&(be=H&&H.onVnodeUnmounted)||Z)&&We(()=>{be&&yt(be,_,p),Z&&on(p,null,_,"unmounted")},b)},ct=p=>{const{type:_,el:b,anchor:E,transition:O}=p;if(_===Ee){ut(b,E);return}if(_===Go){C(p);return}const k=()=>{o(b),O&&!O.persisted&&O.afterLeave&&O.afterLeave()};if(p.shapeFlag&1&&O&&!O.persisted){const{leave:H,delayLeave:I}=O,R=()=>H(b,k);I?I(p.el,k,R):R()}else k()},ut=(p,_)=>{let b;for(;p!==_;)b=h(p),o(p),p=b;o(_)},A=(p,_,b)=>{const{bum:E,scope:O,update:k,subTree:H,um:I}=p;E&&to(E),O.stop(),k&&(k.active=!1,Pe(H,p,_,b)),I&&We(I,_),We(()=>{p.isUnmounted=!0},_),_&&_.pendingBranch&&!_.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===_.pendingId&&(_.deps--,_.deps===0&&_.resolve())},F=(p,_,b,E=!1,O=!1,k=0)=>{for(let H=k;H<p.length;H++)Pe(p[H],_,b,E,O)},V=p=>p.shapeFlag&6?V(p.component.subTree):p.shapeFlag&128?p.suspense.next():h(p.anchor||p.el),W=(p,_,b)=>{p==null?_._vnode&&Pe(_._vnode,null,null,!0):v(_._vnode||null,p,_,null,null,null,b),Hi(),Bl(),_._vnode=p},le={p:v,um:Pe,m:He,r:ct,mt:$e,mc:L,pc:he,pbc:G,n:V,o:t};let Se,ee;return e&&([Se,ee]=e(le)),{render:W,hydrate:Se,createApp:gf(W,Se)}}function rn({effect:t,update:e},n){t.allowRecurse=e.allowRecurse=n}function lc(t,e,n=!1){const s=t.children,o=e.children;if(J(s)&&J(o))for(let r=0;r<s.length;r++){const i=s[r];let a=o[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[r]=Bt(o[r]),a.el=i.el),n||lc(i,a)),a.type===$o&&(a.el=i.el)}}function yf(t){const e=t.slice(),n=[0];let s,o,r,i,a;const l=t.length;for(s=0;s<l;s++){const d=t[s];if(d!==0){if(o=n[n.length-1],t[o]<d){e[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)a=r+i>>1,t[n[a]]<d?r=a+1:i=a;d<t[n[r]]&&(r>0&&(e[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=e[i];return n}const Ef=t=>t.__isTeleport,Ee=Symbol(void 0),$o=Symbol(void 0),mt=Symbol(void 0),Go=Symbol(void 0),ps=[];let ht=null;function y(t=!1){ps.push(ht=t?null:[])}function wf(){ps.pop(),ht=ps[ps.length-1]||null}let Cs=1;function Xi(t){Cs+=t}function cc(t){return t.dynamicChildren=Cs>0?ht||Ln:null,wf(),Cs>0&&ht&&ht.push(t),t}function T(t,e,n,s,o,r){return cc(c(t,e,n,s,o,r,!0))}function $s(t,e,n,s,o){return cc(Q(t,e,n,s,o,!0))}function Cr(t){return t?t.__v_isVNode===!0:!1}function cn(t,e){return t.type===e.type&&t.key===e.key}const Oo="__vInternal",uc=({key:t})=>t!=null?t:null,oo=({ref:t,ref_key:e,ref_for:n})=>t!=null?ke(t)||xe(t)||ne(t)?{i:Je,r:t,k:e,f:!!n}:t:null;function c(t,e=null,n=null,s=0,o=null,r=t===Ee?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:t,props:e,key:e&&uc(e),ref:e&&oo(e),scopeId:Ao,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Je};return a?(ri(l,n),r&128&&t.normalize(l)):n&&(l.shapeFlag|=ke(n)?8:16),Cs>0&&!i&&ht&&(l.patchFlag>0||r&6)&&l.patchFlag!==32&&ht.push(l),l}const Q=Tf;function Tf(t,e=null,n=null,s=0,o=null,r=!1){if((!t||t===nf)&&(t=mt),Cr(t)){const a=Jt(t,e,!0);return n&&ri(a,n),Cs>0&&!r&&ht&&(a.shapeFlag&6?ht[ht.indexOf(t)]=a:ht.push(a)),a.patchFlag|=-2,a}if(Lf(t)&&(t=t.__vccOpts),e){e=Af(e);let{class:a,style:l}=e;a&&!ke(a)&&(e.class=bn(a)),Te(l)&&(Ll(l)&&!J(l)&&(l=Fe({},l)),e.style=Br(l))}const i=ke(t)?1:Hd(t)?128:Ef(t)?64:Te(t)?4:ne(t)?2:0;return c(t,e,n,s,o,i,r,!0)}function Af(t){return t?Ll(t)||Oo in t?Fe({},t):t:null}function Jt(t,e,n=!1){const{props:s,ref:o,patchFlag:r,children:i}=t,a=e?Sf(s||{},e):s;return{__v_isVNode:!0,__v_skip:!0,type:t.type,props:a,key:a&&uc(a),ref:e&&e.ref?n&&o?J(o)?o.concat(oo(e)):[o,oo(e)]:oo(e):o,scopeId:t.scopeId,slotScopeIds:t.slotScopeIds,children:i,target:t.target,targetAnchor:t.targetAnchor,staticCount:t.staticCount,shapeFlag:t.shapeFlag,patchFlag:e&&t.type!==Ee?r===-1?16:r|16:r,dynamicProps:t.dynamicProps,dynamicChildren:t.dynamicChildren,appContext:t.appContext,dirs:t.dirs,transition:t.transition,component:t.component,suspense:t.suspense,ssContent:t.ssContent&&Jt(t.ssContent),ssFallback:t.ssFallback&&Jt(t.ssFallback),el:t.el,anchor:t.anchor,ctx:t.ctx}}function q(t=" ",e=0){return Q($o,null,t,e)}function Ie(t="",e=!1){return e?(y(),$s(mt,null,t)):Q(mt,null,t)}function Et(t){return t==null||typeof t=="boolean"?Q(mt):J(t)?Q(Ee,null,t.slice()):typeof t=="object"?Bt(t):Q($o,null,String(t))}function Bt(t){return t.el===null&&t.patchFlag!==-1||t.memo?t:Jt(t)}function ri(t,e){let n=0;const{shapeFlag:s}=t;if(e==null)e=null;else if(J(e))n=16;else if(typeof e=="object")if(s&65){const o=e.default;o&&(o._c&&(o._d=!1),ri(t,o()),o._c&&(o._d=!0));return}else{n=32;const o=e._;!o&&!(Oo in e)?e._ctx=Je:o===3&&Je&&(Je.slots._===1?e._=1:(e._=2,t.patchFlag|=1024))}else ne(e)?(e={default:e,_ctx:Je},n=32):(e=String(e),s&64?(n=16,e=[q(e)]):n=8);t.children=e,t.shapeFlag|=n}function Sf(...t){const e={};for(let n=0;n<t.length;n++){const s=t[n];for(const o in s)if(o==="class")e.class!==s.class&&(e.class=bn([e.class,s.class]));else if(o==="style")e.style=Br([e.style,s.style]);else if(vo(o)){const r=e[o],i=s[o];i&&r!==i&&!(J(r)&&r.includes(i))&&(e[o]=r?[].concat(r,i):i)}else o!==""&&(e[o]=s[o])}return e}function yt(t,e,n,s=null){st(t,e,7,[n,s])}const Cf=ac();let $f=0;function Of(t,e,n){const s=t.type,o=(e?e.appContext:t.appContext)||Cf,r={uid:$f++,vnode:t,type:s,parent:e,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,scope:new El(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:e?e.provides:Object.create(o.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:sc(s,o),emitsOptions:Kl(s,o),emit:null,emitted:null,propsDefaults:Ce,inheritAttrs:s.inheritAttrs,ctx:Ce,data:Ce,props:Ce,attrs:Ce,slots:Ce,refs:Ce,setupState:Ce,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=e?e.root:r,r.emit=Rd.bind(null,r),t.ce&&t.ce(r),r}let Me=null;const dc=()=>Me||Je,Fn=t=>{Me=t,t.scope.on()},mn=()=>{Me&&Me.scope.off(),Me=null};function fc(t){return t.vnode.shapeFlag&4}let Os=!1;function xf(t,e=!1){Os=e;const{props:n,children:s}=t.vnode,o=fc(t);df(t,n,o,e),pf(t,s);const r=o?Pf(t,e):void 0;return Os=!1,r}function Pf(t,e){const n=t.type;t.accessCache=Object.create(null),t.proxy=jn(new Proxy(t.ctx,of));const{setup:s}=n;if(s){const o=t.setupContext=s.length>1?Df(t):null;Fn(t),Zn();const r=qt(s,t,0,[t.props,o]);if(es(),mn(),vl(r)){if(r.then(mn,mn),e)return r.then(i=>{Qi(t,i,e)}).catch(i=>{wo(i,t,0)});t.asyncDep=r}else Qi(t,r,e)}else hc(t,e)}function Qi(t,e,n){ne(e)?t.type.__ssrInlineRender?t.ssrRender=e:t.render=e:Te(e)&&(t.setupState=Vl(e)),hc(t,n)}let Zi;function hc(t,e,n){const s=t.type;if(!t.render){if(!e&&Zi&&!s.render){const o=s.template||si(t).template;if(o){const{isCustomElement:r,compilerOptions:i}=t.appContext.config,{delimiters:a,compilerOptions:l}=s,d=Fe(Fe({isCustomElement:r,delimiters:a},i),l);s.render=Zi(o,d)}}t.render=s.render||pt}Fn(t),Zn(),rf(t),es(),mn()}function Nf(t){return new Proxy(t.attrs,{get(e,n){return Xe(t,"get","$attrs"),e[n]}})}function Df(t){const e=s=>{t.exposed=s||{}};let n;return{get attrs(){return n||(n=Nf(t))},slots:t.slots,emit:t.emit,expose:e}}function xo(t){if(t.exposed)return t.exposeProxy||(t.exposeProxy=new Proxy(Vl(jn(t.exposed)),{get(e,n){if(n in e)return e[n];if(n in hs)return hs[n](t)},has(e,n){return n in e||n in hs}}))}function If(t,e=!0){return ne(t)?t.displayName||t.name:t.name||e&&t.__name}function Lf(t){return ne(t)&&"__vccOpts"in t}const Oe=(t,e)=>Pd(t,e,Os);function pc(t,e,n){const s=arguments.length;return s===2?Te(e)&&!J(e)?Cr(e)?Q(t,null,[e]):Q(t,e):Q(t,null,e):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Cr(n)&&(n=[n]),Q(t,e,n))}const kf=Symbol(""),Rf=()=>ot(kf),Mf="3.2.45",Vf="http://www.w3.org/2000/svg",un=typeof document<"u"?document:null,ea=un&&un.createElement("template"),jf={insert:(t,e,n)=>{e.insertBefore(t,n||null)},remove:t=>{const e=t.parentNode;e&&e.removeChild(t)},createElement:(t,e,n,s)=>{const o=e?un.createElementNS(Vf,t):un.createElement(t,n?{is:n}:void 0);return t==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:t=>un.createTextNode(t),createComment:t=>un.createComment(t),setText:(t,e)=>{t.nodeValue=e},setElementText:(t,e)=>{t.textContent=e},parentNode:t=>t.parentNode,nextSibling:t=>t.nextSibling,querySelector:t=>un.querySelector(t),setScopeId(t,e){t.setAttribute(e,"")},insertStaticContent(t,e,n,s,o,r){const i=n?n.previousSibling:e.lastChild;if(o&&(o===r||o.nextSibling))for(;e.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{ea.innerHTML=s?`<svg>${t}</svg>`:t;const a=ea.content;if(s){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}e.insertBefore(a,n)}return[i?i.nextSibling:e.firstChild,n?n.previousSibling:e.lastChild]}};function Ff(t,e,n){const s=t._vtc;s&&(e=(e?[e,...s]:[...s]).join(" ")),e==null?t.removeAttribute("class"):n?t.setAttribute("class",e):t.className=e}function Hf(t,e,n){const s=t.style,o=ke(n);if(n&&!o){for(const r in n)$r(s,r,n[r]);if(e&&!ke(e))for(const r in e)n[r]==null&&$r(s,r,"")}else{const r=s.display;o?e!==n&&(s.cssText=n):e&&t.removeAttribute("style"),"_vod"in t&&(s.display=r)}}const ta=/\s*!important$/;function $r(t,e,n){if(J(n))n.forEach(s=>$r(t,e,s));else if(n==null&&(n=""),e.startsWith("--"))t.setProperty(e,n);else{const s=Bf(t,e);ta.test(n)?t.setProperty(Qn(s),n.replace(ta,""),"important"):t[s]=n}}const na=["Webkit","Moz","ms"],Jo={};function Bf(t,e){const n=Jo[e];if(n)return n;let s=St(e);if(s!=="filter"&&s in t)return Jo[e]=s;s=yo(s);for(let o=0;o<na.length;o++){const r=na[o]+s;if(r in t)return Jo[e]=r}return e}const sa="http://www.w3.org/1999/xlink";function Uf(t,e,n,s,o){if(s&&e.startsWith("xlink:"))n==null?t.removeAttributeNS(sa,e.slice(6,e.length)):t.setAttributeNS(sa,e,n);else{const r=ju(e);n==null||r&&!_l(n)?t.removeAttribute(e):t.setAttribute(e,r?"":n)}}function Kf(t,e,n,s,o,r,i){if(e==="innerHTML"||e==="textContent"){s&&i(s,o,r),t[e]=n==null?"":n;return}if(e==="value"&&t.tagName!=="PROGRESS"&&!t.tagName.includes("-")){t._value=n;const l=n==null?"":n;(t.value!==l||t.tagName==="OPTION")&&(t.value=l),n==null&&t.removeAttribute(e);return}let a=!1;if(n===""||n==null){const l=typeof t[e];l==="boolean"?n=_l(n):n==null&&l==="string"?(n="",a=!0):l==="number"&&(n=0,a=!0)}try{t[e]=n}catch{}a&&t.removeAttribute(e)}function Wt(t,e,n,s){t.addEventListener(e,n,s)}function Wf(t,e,n,s){t.removeEventListener(e,n,s)}function zf(t,e,n,s,o=null){const r=t._vei||(t._vei={}),i=r[e];if(s&&i)i.value=s;else{const[a,l]=Yf(e);if(s){const d=r[e]=Jf(s,o);Wt(t,a,d,l)}else i&&(Wf(t,a,i,l),r[e]=void 0)}}const oa=/(?:Once|Passive|Capture)$/;function Yf(t){let e;if(oa.test(t)){e={};let s;for(;s=t.match(oa);)t=t.slice(0,t.length-s[0].length),e[s[0].toLowerCase()]=!0}return[t[2]===":"?t.slice(3):Qn(t.slice(2)),e]}let Xo=0;const qf=Promise.resolve(),Gf=()=>Xo||(qf.then(()=>Xo=0),Xo=Date.now());function Jf(t,e){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;st(Xf(s,n.value),e,5,[s])};return n.value=t,n.attached=Gf(),n}function Xf(t,e){if(J(e)){const n=t.stopImmediatePropagation;return t.stopImmediatePropagation=()=>{n.call(t),t._stopped=!0},e.map(s=>o=>!o._stopped&&s&&s(o))}else return e}const ra=/^on[a-z]/,Qf=(t,e,n,s,o=!1,r,i,a,l)=>{e==="class"?Ff(t,s,o):e==="style"?Hf(t,n,s):vo(e)?Kr(e)||zf(t,e,n,s,i):(e[0]==="."?(e=e.slice(1),!0):e[0]==="^"?(e=e.slice(1),!1):Zf(t,e,s,o))?Kf(t,e,s,r,i,a,l):(e==="true-value"?t._trueValue=s:e==="false-value"&&(t._falseValue=s),Uf(t,e,s,o))};function Zf(t,e,n,s){return s?!!(e==="innerHTML"||e==="textContent"||e in t&&ra.test(e)&&ne(n)):e==="spellcheck"||e==="draggable"||e==="translate"||e==="form"||e==="list"&&t.tagName==="INPUT"||e==="type"&&t.tagName==="TEXTAREA"||ra.test(e)&&ke(n)?!1:e in t}const eh={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};zd.props;const Hn=t=>{const e=t.props["onUpdate:modelValue"]||!1;return J(e)?n=>to(e,n):e};function th(t){t.target.composing=!0}function ia(t){const e=t.target;e.composing&&(e.composing=!1,e.dispatchEvent(new Event("input")))}const fe={created(t,{modifiers:{lazy:e,trim:n,number:s}},o){t._assign=Hn(o);const r=s||o.props&&o.props.type==="number";Wt(t,e?"change":"input",i=>{if(i.target.composing)return;let a=t.value;n&&(a=a.trim()),r&&(a=ws(a)),t._assign(a)}),n&&Wt(t,"change",()=>{t.value=t.value.trim()}),e||(Wt(t,"compositionstart",th),Wt(t,"compositionend",ia),Wt(t,"change",ia))},mounted(t,{value:e}){t.value=e==null?"":e},beforeUpdate(t,{value:e,modifiers:{lazy:n,trim:s,number:o}},r){if(t._assign=Hn(r),t.composing||document.activeElement===t&&t.type!=="range"&&(n||s&&t.value.trim()===e||(o||t.type==="number")&&ws(t.value)===e))return;const i=e==null?"":e;t.value!==i&&(t.value=i)}},nh={deep:!0,created(t,e,n){t._assign=Hn(n),Wt(t,"change",()=>{const s=t._modelValue,o=xs(t),r=t.checked,i=t._assign;if(J(s)){const a=Ur(s,o),l=a!==-1;if(r&&!l)i(s.concat(o));else if(!r&&l){const d=[...s];d.splice(a,1),i(d)}}else if(Xn(s)){const a=new Set(s);r?a.add(o):a.delete(o),i(a)}else i(mc(t,r))})},mounted:aa,beforeUpdate(t,e,n){t._assign=Hn(n),aa(t,e,n)}};function aa(t,{value:e,oldValue:n},s){t._modelValue=e,J(e)?t.checked=Ur(e,s.props.value)>-1:Xn(e)?t.checked=e.has(s.props.value):e!==n&&(t.checked=Ds(e,mc(t,!0)))}const Mn={deep:!0,created(t,{value:e,modifiers:{number:n}},s){const o=Xn(e);Wt(t,"change",()=>{const r=Array.prototype.filter.call(t.options,i=>i.selected).map(i=>n?ws(xs(i)):xs(i));t._assign(t.multiple?o?new Set(r):r:r[0])}),t._assign=Hn(s)},mounted(t,{value:e}){la(t,e)},beforeUpdate(t,e,n){t._assign=Hn(n)},updated(t,{value:e}){la(t,e)}};function la(t,e){const n=t.multiple;if(!(n&&!J(e)&&!Xn(e))){for(let s=0,o=t.options.length;s<o;s++){const r=t.options[s],i=xs(r);if(n)J(e)?r.selected=Ur(e,i)>-1:r.selected=e.has(i);else if(Ds(xs(r),e)){t.selectedIndex!==s&&(t.selectedIndex=s);return}}!n&&t.selectedIndex!==-1&&(t.selectedIndex=-1)}}function xs(t){return"_value"in t?t._value:t.value}function mc(t,e){const n=e?"_trueValue":"_falseValue";return n in t?t[n]:e}const sh=["ctrl","shift","alt","meta"],oh={stop:t=>t.stopPropagation(),prevent:t=>t.preventDefault(),self:t=>t.target!==t.currentTarget,ctrl:t=>!t.ctrlKey,shift:t=>!t.shiftKey,alt:t=>!t.altKey,meta:t=>!t.metaKey,left:t=>"button"in t&&t.button!==0,middle:t=>"button"in t&&t.button!==1,right:t=>"button"in t&&t.button!==2,exact:(t,e)=>sh.some(n=>t[`${n}Key`]&&!e.includes(n))},Le=(t,e)=>(n,...s)=>{for(let o=0;o<e.length;o++){const r=oh[e[o]];if(r&&r(n,e))return}return t(n,...s)},rh=Fe({patchProp:Qf},jf);let ca;function ih(){return ca||(ca=vf(rh))}const ah=(...t)=>{const e=ih().createApp(...t),{mount:n}=e;return e.mount=s=>{const o=lh(s);if(!o)return;const r=e._component;!ne(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},e};function lh(t){return ke(t)?document.querySelector(t):t}var ch=!1;/*!
  * pinia v2.0.27
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */let _c;const Po=t=>_c=t,gc=Symbol();function Or(t){return t&&typeof t=="object"&&Object.prototype.toString.call(t)==="[object Object]"&&typeof t.toJSON!="function"}var ms;(function(t){t.direct="direct",t.patchObject="patch object",t.patchFunction="patch function"})(ms||(ms={}));function uh(){const t=wl(!0),e=t.run(()=>oe({}));let n=[],s=[];const o=jn({install(r){Po(o),o._a=r,r.provide(gc,o),r.config.globalProperties.$pinia=o,s.forEach(i=>n.push(i)),s=[]},use(r){return!this._a&&!ch?s.push(r):n.push(r),this},_p:n,_a:null,_e:t,_s:new Map,state:e});return o}const vc=()=>{};function ua(t,e,n,s=vc){t.push(e);const o=()=>{const r=t.indexOf(e);r>-1&&(t.splice(r,1),s())};return!n&&Gu()&&Ju(o),o}function Sn(t,...e){t.slice().forEach(n=>{n(...e)})}function xr(t,e){t instanceof Map&&e instanceof Map&&e.forEach((n,s)=>t.set(s,n)),t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const n in e){if(!e.hasOwnProperty(n))continue;const s=e[n],o=t[n];Or(o)&&Or(s)&&t.hasOwnProperty(n)&&!xe(s)&&!Yt(s)?t[n]=xr(o,s):t[n]=s}return t}const dh=Symbol();function fh(t){return!Or(t)||!t.hasOwnProperty(dh)}const{assign:Ut}=Object;function hh(t){return!!(xe(t)&&t.effect)}function ph(t,e,n,s){const{state:o,actions:r,getters:i}=e,a=n.state.value[t];let l;function d(){a||(n.state.value[t]=o?o():{});const u=Cd(n.state.value[t]);return Ut(u,r,Object.keys(i||{}).reduce((f,h)=>(f[h]=jn(Oe(()=>{Po(n);const m=n._s.get(t);return i[h].call(m,m)})),f),{}))}return l=bc(t,d,e,n,s,!0),l.$reset=function(){const f=o?o():{};this.$patch(h=>{Ut(h,f)})},l}function bc(t,e,n={},s,o,r){let i;const a=Ut({actions:{}},n),l={deep:!0};let d,u,f=jn([]),h=jn([]),m;const g=s.state.value[t];!r&&!g&&(s.state.value[t]={}),oe({});let v;function D(U){let L;d=u=!1,typeof U=="function"?(U(s.state.value[t]),L={type:ms.patchFunction,storeId:t,events:m}):(xr(s.state.value[t],U),L={type:ms.patchObject,payload:U,storeId:t,events:m});const X=v=Symbol();ti().then(()=>{v===X&&(d=!0)}),u=!0,Sn(f,L,s.state.value[t])}const S=vc;function j(){i.stop(),f=[],h=[],s._s.delete(t)}function $(U,L){return function(){Po(s);const X=Array.from(arguments),G=[],re=[];function ge(we){G.push(we)}function ve(we){re.push(we)}Sn(h,{args:X,name:U,store:M,after:ge,onError:ve});let $e;try{$e=L.apply(this&&this.$id===t?this:M,X)}catch(we){throw Sn(re,we),we}return $e instanceof Promise?$e.then(we=>(Sn(G,we),we)).catch(we=>(Sn(re,we),Promise.reject(we))):(Sn(G,$e),$e)}}const C={_p:s,$id:t,$onAction:ua.bind(null,h),$patch:D,$reset:S,$subscribe(U,L={}){const X=ua(f,U,L.detached,()=>G()),G=i.run(()=>Pt(()=>s.state.value[t],re=>{(L.flush==="sync"?u:d)&&U({storeId:t,type:ms.direct,events:m},re)},Ut({},l,L)));return X},$dispose:j},M=ts(C);s._s.set(t,M);const N=s._e.run(()=>(i=wl(),i.run(()=>e())));for(const U in N){const L=N[U];if(xe(L)&&!hh(L)||Yt(L))r||(g&&fh(L)&&(xe(L)?L.value=g[U]:xr(L,g[U])),s.state.value[t][U]=L);else if(typeof L=="function"){const X=$(U,L);N[U]=X,a.actions[U]=L}}return Ut(M,N),Ut(me(M),N),Object.defineProperty(M,"$state",{get:()=>s.state.value[t],set:U=>{D(L=>{Ut(L,U)})}}),s._p.forEach(U=>{Ut(M,i.run(()=>U({store:M,app:s._a,pinia:s,options:a})))}),g&&r&&n.hydrate&&n.hydrate(M.$state,g),d=!0,u=!0,M}function yn(t,e,n){let s,o;const r=typeof e=="function";typeof t=="string"?(s=t,o=r?n:e):(o=t,s=t.id);function i(a,l){const d=dc();return a=a||d&&ot(gc),a&&Po(a),a=_c,a._s.has(s)||(r?bc(s,e,o,a):ph(s,o,a)),a._s.get(s)}return i.$id=s,i}var mh=Object.defineProperty,da=Object.getOwnPropertySymbols,_h=Object.prototype.hasOwnProperty,gh=Object.prototype.propertyIsEnumerable,fa=(t,e,n)=>e in t?mh(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,ha=(t,e)=>{for(var n in e||(e={}))_h.call(e,n)&&fa(t,n,e[n]);if(da)for(var n of da(e))gh.call(e,n)&&fa(t,n,e[n]);return t};function vh(t){return typeof t=="object"&&t!==null}function bh(t){return t}function pa(t,e){return t=vh(t)?t:Object.create(null),new Proxy(t,{get(n,s,o){var r;return s==="key"?((r=e.key)!=null?r:bh)(Reflect.get(n,s,o)):Reflect.get(n,s,o)||Reflect.get(e,s,o)}})}function ma(t){return t!==null&&typeof t=="object"}function Pr(t,e){const n=Array.isArray(t)&&Array.isArray(e),s=ma(t)&&ma(e);if(!n&&!s)throw new Error("Can only merge object with object or array with array");const o=n?[]:{};return[...Object.keys(t),...Object.keys(e)].forEach(i=>{Array.isArray(t[i])&&Array.isArray(e[i])?o[i]=[...Object.values(Pr(t[i],e[i]))]:e[i]!==null&&typeof e[i]=="object"&&typeof t[i]=="object"?o[i]=Pr(t[i],e[i]):t[i]!==void 0&&e[i]===void 0?o[i]=t[i]:t[i]===void 0&&e[i]!==void 0&&(o[i]=e[i])}),o}function _a(t,e){return e.reduce((n,s)=>s==="[]"&&Array.isArray(n)?n:n==null?void 0:n[s],t)}function ga(t,e,n){const s=e.slice(0,-1).reduce((o,r)=>/^(__proto__)$/.test(r)?{}:o[r]=o[r]||{},t);if(Array.isArray(s[e[e.length-1]])&&Array.isArray(n)){const o=s[e[e.length-1]].map((r,i)=>Array.isArray(r)&&typeof r!="object"?[...r,...n[i]]:typeof r=="object"&&r!==null&&Object.keys(r).some(a=>Array.isArray(r[a]))?Pr(r,n[i]):ha(ha({},r),n[i]));s[e[e.length-1]]=o}else e[e.length-1]===void 0&&Array.isArray(s)&&Array.isArray(n)?s.push(...n):s[e[e.length-1]]=n;return t}function yc(t,e){return e.reduce((n,s)=>{const o=s.split(".");if(!o.includes("[]"))return ga(n,o,_a(t,o));const r=o.indexOf("[]"),i=o.slice(0,r),a=o.slice(0,r+1),l=o.slice(r+1),d=_a(t,a),u=[];for(const f of d)l.length!==0&&(Array.isArray(f)||typeof f=="object")?u.push(yc(f,[l.join(".")])):u.push(f);return ga(n,i,u)},Array.isArray(t)?[]:{})}function va(t,e,n,s,o){try{const r=e==null?void 0:e.getItem(s);r&&t.$patch(n==null?void 0:n.deserialize(r))}catch(r){o&&console.error(r)}}function yh(t={}){return e=>{const{options:{persist:n},store:s}=e;if(!n)return;const o=(Array.isArray(n)?n.map(r=>pa(r,t)):[pa(n,t)]).map(({storage:r=localStorage,beforeRestore:i=null,afterRestore:a=null,serializer:l={serialize:JSON.stringify,deserialize:JSON.parse},key:d=s.$id,paths:u=null,debug:f=!1})=>({storage:r,beforeRestore:i,afterRestore:a,serializer:l,key:d,paths:u,debug:f}));o.forEach(r=>{const{storage:i,serializer:a,key:l,paths:d,beforeRestore:u,afterRestore:f,debug:h}=r;u==null||u(e),va(s,i,a,l,h),f==null||f(e),s.$subscribe((m,g)=>{try{const v=Array.isArray(d)?yc(g,d):g;i.setItem(l,a.serialize(v))}catch(v){h&&console.error(v)}},{detached:!0})}),s.$hydrate=({runHooks:r=!0}={})=>{o.forEach(i=>{const{beforeRestore:a,afterRestore:l,storage:d,serializer:u,key:f,debug:h}=i;r&&(a==null||a(e)),va(s,d,u,f,h),r&&(l==null||l(e))})}}}var Eh=yh();/*!
  * vue-router v4.1.6
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */const xn=typeof window<"u";function wh(t){return t.__esModule||t[Symbol.toStringTag]==="Module"}const ye=Object.assign;function Qo(t,e){const n={};for(const s in e){const o=e[s];n[s]=_t(o)?o.map(t):t(o)}return n}const _s=()=>{},_t=Array.isArray,Th=/\/$/,Ah=t=>t.replace(Th,"");function Zo(t,e,n="/"){let s,o={},r="",i="";const a=e.indexOf("#");let l=e.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=e.slice(0,l),r=e.slice(l+1,a>-1?a:e.length),o=t(r)),a>-1&&(s=s||e.slice(0,a),i=e.slice(a,e.length)),s=Oh(s!=null?s:e,n),{fullPath:s+(r&&"?")+r+i,path:s,query:o,hash:i}}function Sh(t,e){const n=e.query?t(e.query):"";return e.path+(n&&"?")+n+(e.hash||"")}function ba(t,e){return!e||!t.toLowerCase().startsWith(e.toLowerCase())?t:t.slice(e.length)||"/"}function Ch(t,e,n){const s=e.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&Bn(e.matched[s],n.matched[o])&&Ec(e.params,n.params)&&t(e.query)===t(n.query)&&e.hash===n.hash}function Bn(t,e){return(t.aliasOf||t)===(e.aliasOf||e)}function Ec(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t)if(!$h(t[n],e[n]))return!1;return!0}function $h(t,e){return _t(t)?ya(t,e):_t(e)?ya(e,t):t===e}function ya(t,e){return _t(e)?t.length===e.length&&t.every((n,s)=>n===e[s]):t.length===1&&t[0]===e}function Oh(t,e){if(t.startsWith("/"))return t;if(!t)return e;const n=e.split("/"),s=t.split("/");let o=n.length-1,r,i;for(r=0;r<s.length;r++)if(i=s[r],i!==".")if(i==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(r-(r===s.length?1:0)).join("/")}var Ps;(function(t){t.pop="pop",t.push="push"})(Ps||(Ps={}));var gs;(function(t){t.back="back",t.forward="forward",t.unknown=""})(gs||(gs={}));function xh(t){if(!t)if(xn){const e=document.querySelector("base");t=e&&e.getAttribute("href")||"/",t=t.replace(/^\w+:\/\/[^\/]+/,"")}else t="/";return t[0]!=="/"&&t[0]!=="#"&&(t="/"+t),Ah(t)}const Ph=/^[^#]+#/;function Nh(t,e){return t.replace(Ph,"#")+e}function Dh(t,e){const n=document.documentElement.getBoundingClientRect(),s=t.getBoundingClientRect();return{behavior:e.behavior,left:s.left-n.left-(e.left||0),top:s.top-n.top-(e.top||0)}}const No=()=>({left:window.pageXOffset,top:window.pageYOffset});function Ih(t){let e;if("el"in t){const n=t.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;e=Dh(o,t)}else e=t;"scrollBehavior"in document.documentElement.style?window.scrollTo(e):window.scrollTo(e.left!=null?e.left:window.pageXOffset,e.top!=null?e.top:window.pageYOffset)}function Ea(t,e){return(history.state?history.state.position-e:-1)+t}const Nr=new Map;function Lh(t,e){Nr.set(t,e)}function kh(t){const e=Nr.get(t);return Nr.delete(t),e}let Rh=()=>location.protocol+"//"+location.host;function wc(t,e){const{pathname:n,search:s,hash:o}=e,r=t.indexOf("#");if(r>-1){let a=o.includes(t.slice(r))?t.slice(r).length:1,l=o.slice(a);return l[0]!=="/"&&(l="/"+l),ba(l,"")}return ba(n,t)+s+o}function Mh(t,e,n,s){let o=[],r=[],i=null;const a=({state:h})=>{const m=wc(t,location),g=n.value,v=e.value;let D=0;if(h){if(n.value=m,e.value=h,i&&i===g){i=null;return}D=v?h.position-v.position:0}else s(m);o.forEach(S=>{S(n.value,g,{delta:D,type:Ps.pop,direction:D?D>0?gs.forward:gs.back:gs.unknown})})};function l(){i=n.value}function d(h){o.push(h);const m=()=>{const g=o.indexOf(h);g>-1&&o.splice(g,1)};return r.push(m),m}function u(){const{history:h}=window;!h.state||h.replaceState(ye({},h.state,{scroll:No()}),"")}function f(){for(const h of r)h();r=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u),{pauseListeners:l,listen:d,destroy:f}}function wa(t,e,n,s=!1,o=!1){return{back:t,current:e,forward:n,replaced:s,position:window.history.length,scroll:o?No():null}}function Vh(t){const{history:e,location:n}=window,s={value:wc(t,n)},o={value:e.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:e.length-1,replaced:!0,scroll:null},!0);function r(l,d,u){const f=t.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?t:t.slice(f))+l:Rh()+t+l;try{e[u?"replaceState":"pushState"](d,"",h),o.value=d}catch(m){console.error(m),n[u?"replace":"assign"](h)}}function i(l,d){const u=ye({},e.state,wa(o.value.back,l,o.value.forward,!0),d,{position:o.value.position});r(l,u,!0),s.value=l}function a(l,d){const u=ye({},o.value,e.state,{forward:l,scroll:No()});r(u.current,u,!0);const f=ye({},wa(s.value,l,null),{position:u.position+1},d);r(l,f,!1),s.value=l}return{location:s,state:o,push:a,replace:i}}function jh(t){t=xh(t);const e=Vh(t),n=Mh(t,e.state,e.location,e.replace);function s(r,i=!0){i||n.pauseListeners(),history.go(r)}const o=ye({location:"",base:t,go:s,createHref:Nh.bind(null,t)},e,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>e.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>e.state.value}),o}function Fh(t){return typeof t=="string"||t&&typeof t=="object"}function Tc(t){return typeof t=="string"||typeof t=="symbol"}const Ft={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},Ac=Symbol("");var Ta;(function(t){t[t.aborted=4]="aborted",t[t.cancelled=8]="cancelled",t[t.duplicated=16]="duplicated"})(Ta||(Ta={}));function Un(t,e){return ye(new Error,{type:t,[Ac]:!0},e)}function $t(t,e){return t instanceof Error&&Ac in t&&(e==null||!!(t.type&e))}const Aa="[^/]+?",Hh={sensitive:!1,strict:!1,start:!0,end:!0},Bh=/[.+*?^${}()[\]/\\]/g;function Uh(t,e){const n=ye({},Hh,e),s=[];let o=n.start?"^":"";const r=[];for(const d of t){const u=d.length?[]:[90];n.strict&&!d.length&&(o+="/");for(let f=0;f<d.length;f++){const h=d[f];let m=40+(n.sensitive?.25:0);if(h.type===0)f||(o+="/"),o+=h.value.replace(Bh,"\\$&"),m+=40;else if(h.type===1){const{value:g,repeatable:v,optional:D,regexp:S}=h;r.push({name:g,repeatable:v,optional:D});const j=S||Aa;if(j!==Aa){m+=10;try{new RegExp(`(${j})`)}catch(C){throw new Error(`Invalid custom RegExp for param "${g}" (${j}): `+C.message)}}let $=v?`((?:${j})(?:/(?:${j}))*)`:`(${j})`;f||($=D&&d.length<2?`(?:/${$})`:"/"+$),D&&($+="?"),o+=$,m+=20,D&&(m+=-8),v&&(m+=-20),j===".*"&&(m+=-50)}u.push(m)}s.push(u)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function a(d){const u=d.match(i),f={};if(!u)return null;for(let h=1;h<u.length;h++){const m=u[h]||"",g=r[h-1];f[g.name]=m&&g.repeatable?m.split("/"):m}return f}function l(d){let u="",f=!1;for(const h of t){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const m of h)if(m.type===0)u+=m.value;else if(m.type===1){const{value:g,repeatable:v,optional:D}=m,S=g in d?d[g]:"";if(_t(S)&&!v)throw new Error(`Provided param "${g}" is an array but it is not repeatable (* or + modifiers)`);const j=_t(S)?S.join("/"):S;if(!j)if(D)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${g}"`);u+=j}}return u||"/"}return{re:i,score:s,keys:r,parse:a,stringify:l}}function Kh(t,e){let n=0;for(;n<t.length&&n<e.length;){const s=e[n]-t[n];if(s)return s;n++}return t.length<e.length?t.length===1&&t[0]===40+40?-1:1:t.length>e.length?e.length===1&&e[0]===40+40?1:-1:0}function Wh(t,e){let n=0;const s=t.score,o=e.score;for(;n<s.length&&n<o.length;){const r=Kh(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(Sa(s))return 1;if(Sa(o))return-1}return o.length-s.length}function Sa(t){const e=t[t.length-1];return t.length>0&&e[e.length-1]<0}const zh={type:0,value:""},Yh=/[a-zA-Z0-9_]/;function qh(t){if(!t)return[[]];if(t==="/")return[[zh]];if(!t.startsWith("/"))throw new Error(`Invalid path "${t}"`);function e(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const o=[];let r;function i(){r&&o.push(r),r=[]}let a=0,l,d="",u="";function f(){!d||(n===0?r.push({type:0,value:d}):n===1||n===2||n===3?(r.length>1&&(l==="*"||l==="+")&&e(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:d,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):e("Invalid state to consume buffer"),d="")}function h(){d+=l}for(;a<t.length;){if(l=t[a++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(d&&f(),i()):l===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:l==="("?n=2:Yh.test(l)?h():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:e("Unknown state");break}}return n===2&&e(`Unfinished custom RegExp for param "${d}"`),f(),i(),o}function Gh(t,e,n){const s=Uh(qh(t.path),n),o=ye(s,{record:t,parent:e,children:[],alias:[]});return e&&!o.record.aliasOf==!e.record.aliasOf&&e.children.push(o),o}function Jh(t,e){const n=[],s=new Map;e=Oa({strict:!1,end:!0,sensitive:!1},e);function o(u){return s.get(u)}function r(u,f,h){const m=!h,g=Xh(u);g.aliasOf=h&&h.record;const v=Oa(e,u),D=[g];if("alias"in u){const $=typeof u.alias=="string"?[u.alias]:u.alias;for(const C of $)D.push(ye({},g,{components:h?h.record.components:g.components,path:C,aliasOf:h?h.record:g}))}let S,j;for(const $ of D){const{path:C}=$;if(f&&C[0]!=="/"){const M=f.record.path,N=M[M.length-1]==="/"?"":"/";$.path=f.record.path+(C&&N+C)}if(S=Gh($,f,v),h?h.alias.push(S):(j=j||S,j!==S&&j.alias.push(S),m&&u.name&&!$a(S)&&i(u.name)),g.children){const M=g.children;for(let N=0;N<M.length;N++)r(M[N],S,h&&h.children[N])}h=h||S,(S.record.components&&Object.keys(S.record.components).length||S.record.name||S.record.redirect)&&l(S)}return j?()=>{i(j)}:_s}function i(u){if(Tc(u)){const f=s.get(u);f&&(s.delete(u),n.splice(n.indexOf(f),1),f.children.forEach(i),f.alias.forEach(i))}else{const f=n.indexOf(u);f>-1&&(n.splice(f,1),u.record.name&&s.delete(u.record.name),u.children.forEach(i),u.alias.forEach(i))}}function a(){return n}function l(u){let f=0;for(;f<n.length&&Wh(u,n[f])>=0&&(u.record.path!==n[f].record.path||!Sc(u,n[f]));)f++;n.splice(f,0,u),u.record.name&&!$a(u)&&s.set(u.record.name,u)}function d(u,f){let h,m={},g,v;if("name"in u&&u.name){if(h=s.get(u.name),!h)throw Un(1,{location:u});v=h.record.name,m=ye(Ca(f.params,h.keys.filter(j=>!j.optional).map(j=>j.name)),u.params&&Ca(u.params,h.keys.map(j=>j.name))),g=h.stringify(m)}else if("path"in u)g=u.path,h=n.find(j=>j.re.test(g)),h&&(m=h.parse(g),v=h.record.name);else{if(h=f.name?s.get(f.name):n.find(j=>j.re.test(f.path)),!h)throw Un(1,{location:u,currentLocation:f});v=h.record.name,m=ye({},f.params,u.params),g=h.stringify(m)}const D=[];let S=h;for(;S;)D.unshift(S.record),S=S.parent;return{name:v,path:g,params:m,matched:D,meta:Zh(D)}}return t.forEach(u=>r(u)),{addRoute:r,resolve:d,removeRoute:i,getRoutes:a,getRecordMatcher:o}}function Ca(t,e){const n={};for(const s of e)s in t&&(n[s]=t[s]);return n}function Xh(t){return{path:t.path,redirect:t.redirect,name:t.name,meta:t.meta||{},aliasOf:void 0,beforeEnter:t.beforeEnter,props:Qh(t),children:t.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in t?t.components||null:t.component&&{default:t.component}}}function Qh(t){const e={},n=t.props||!1;if("component"in t)e.default=n;else for(const s in t.components)e[s]=typeof n=="boolean"?n:n[s];return e}function $a(t){for(;t;){if(t.record.aliasOf)return!0;t=t.parent}return!1}function Zh(t){return t.reduce((e,n)=>ye(e,n.meta),{})}function Oa(t,e){const n={};for(const s in t)n[s]=s in e?e[s]:t[s];return n}function Sc(t,e){return e.children.some(n=>n===t||Sc(t,n))}const Cc=/#/g,ep=/&/g,tp=/\//g,np=/=/g,sp=/\?/g,$c=/\+/g,op=/%5B/g,rp=/%5D/g,Oc=/%5E/g,ip=/%60/g,xc=/%7B/g,ap=/%7C/g,Pc=/%7D/g,lp=/%20/g;function ii(t){return encodeURI(""+t).replace(ap,"|").replace(op,"[").replace(rp,"]")}function cp(t){return ii(t).replace(xc,"{").replace(Pc,"}").replace(Oc,"^")}function Dr(t){return ii(t).replace($c,"%2B").replace(lp,"+").replace(Cc,"%23").replace(ep,"%26").replace(ip,"`").replace(xc,"{").replace(Pc,"}").replace(Oc,"^")}function up(t){return Dr(t).replace(np,"%3D")}function dp(t){return ii(t).replace(Cc,"%23").replace(sp,"%3F")}function fp(t){return t==null?"":dp(t).replace(tp,"%2F")}function ho(t){try{return decodeURIComponent(""+t)}catch{}return""+t}function hp(t){const e={};if(t===""||t==="?")return e;const s=(t[0]==="?"?t.slice(1):t).split("&");for(let o=0;o<s.length;++o){const r=s[o].replace($c," "),i=r.indexOf("="),a=ho(i<0?r:r.slice(0,i)),l=i<0?null:ho(r.slice(i+1));if(a in e){let d=e[a];_t(d)||(d=e[a]=[d]),d.push(l)}else e[a]=l}return e}function xa(t){let e="";for(let n in t){const s=t[n];if(n=up(n),s==null){s!==void 0&&(e+=(e.length?"&":"")+n);continue}(_t(s)?s.map(r=>r&&Dr(r)):[s&&Dr(s)]).forEach(r=>{r!==void 0&&(e+=(e.length?"&":"")+n,r!=null&&(e+="="+r))})}return e}function pp(t){const e={};for(const n in t){const s=t[n];s!==void 0&&(e[n]=_t(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return e}const mp=Symbol(""),Pa=Symbol(""),Do=Symbol(""),ai=Symbol(""),Ir=Symbol("");function cs(){let t=[];function e(s){return t.push(s),()=>{const o=t.indexOf(s);o>-1&&t.splice(o,1)}}function n(){t=[]}return{add:e,list:()=>t,reset:n}}function Kt(t,e,n,s,o){const r=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((i,a)=>{const l=f=>{f===!1?a(Un(4,{from:n,to:e})):f instanceof Error?a(f):Fh(f)?a(Un(2,{from:e,to:f})):(r&&s.enterCallbacks[o]===r&&typeof f=="function"&&r.push(f),i())},d=t.call(s&&s.instances[o],e,n,l);let u=Promise.resolve(d);t.length<3&&(u=u.then(l)),u.catch(f=>a(f))})}function er(t,e,n,s){const o=[];for(const r of t)for(const i in r.components){let a=r.components[i];if(!(e!=="beforeRouteEnter"&&!r.instances[i]))if(_p(a)){const d=(a.__vccOpts||a)[e];d&&o.push(Kt(d,n,s,r,i))}else{let l=a();o.push(()=>l.then(d=>{if(!d)return Promise.reject(new Error(`Couldn't resolve component "${i}" at "${r.path}"`));const u=wh(d)?d.default:d;r.components[i]=u;const h=(u.__vccOpts||u)[e];return h&&Kt(h,n,s,r,i)()}))}}return o}function _p(t){return typeof t=="object"||"displayName"in t||"props"in t||"__vccOpts"in t}function Na(t){const e=ot(Do),n=ot(ai),s=Oe(()=>e.resolve(w(t.to))),o=Oe(()=>{const{matched:l}=s.value,{length:d}=l,u=l[d-1],f=n.matched;if(!u||!f.length)return-1;const h=f.findIndex(Bn.bind(null,u));if(h>-1)return h;const m=Da(l[d-2]);return d>1&&Da(u)===m&&f[f.length-1].path!==m?f.findIndex(Bn.bind(null,l[d-2])):h}),r=Oe(()=>o.value>-1&&yp(n.params,s.value.params)),i=Oe(()=>o.value>-1&&o.value===n.matched.length-1&&Ec(n.params,s.value.params));function a(l={}){return bp(l)?e[w(t.replace)?"replace":"push"](w(t.to)).catch(_s):Promise.resolve()}return{route:s,href:Oe(()=>s.value.href),isActive:r,isExactActive:i,navigate:a}}const gp=Gl({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Na,setup(t,{slots:e}){const n=ts(Na(t)),{options:s}=ot(Do),o=Oe(()=>({[Ia(t.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Ia(t.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=e.default&&e.default(n);return t.custom?r:pc("a",{"aria-current":n.isExactActive?t.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),vp=gp;function bp(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)&&!t.defaultPrevented&&!(t.button!==void 0&&t.button!==0)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}function yp(t,e){for(const n in e){const s=e[n],o=t[n];if(typeof s=="string"){if(s!==o)return!1}else if(!_t(o)||o.length!==s.length||s.some((r,i)=>r!==o[i]))return!1}return!0}function Da(t){return t?t.aliasOf?t.aliasOf.path:t.path:""}const Ia=(t,e,n)=>t!=null?t:e!=null?e:n,Ep=Gl({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(t,{attrs:e,slots:n}){const s=ot(Ir),o=Oe(()=>t.route||s.value),r=ot(Pa,0),i=Oe(()=>{let d=w(r);const{matched:u}=o.value;let f;for(;(f=u[d])&&!f.components;)d++;return d}),a=Oe(()=>o.value.matched[i.value]);no(Pa,Oe(()=>i.value+1)),no(mp,a),no(Ir,o);const l=oe();return Pt(()=>[l.value,a.value,t.name],([d,u,f],[h,m,g])=>{u&&(u.instances[f]=d,m&&m!==u&&d&&d===h&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),d&&u&&(!m||!Bn(u,m)||!h)&&(u.enterCallbacks[f]||[]).forEach(v=>v(d))},{flush:"post"}),()=>{const d=o.value,u=t.name,f=a.value,h=f&&f.components[u];if(!h)return La(n.default,{Component:h,route:d});const m=f.props[u],g=m?m===!0?d.params:typeof m=="function"?m(d):m:null,D=pc(h,ye({},g,e,{onVnodeUnmounted:S=>{S.component.isUnmounted&&(f.instances[u]=null)},ref:l}));return La(n.default,{Component:D,route:d})||D}}});function La(t,e){if(!t)return null;const n=t(e);return n.length===1?n[0]:n}const Nc=Ep;function wp(t){const e=Jh(t.routes,t),n=t.parseQuery||hp,s=t.stringifyQuery||xa,o=t.history,r=cs(),i=cs(),a=cs(),l=Td(Ft);let d=Ft;xn&&t.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Qo.bind(null,A=>""+A),f=Qo.bind(null,fp),h=Qo.bind(null,ho);function m(A,F){let V,W;return Tc(A)?(V=e.getRecordMatcher(A),W=F):W=A,e.addRoute(W,V)}function g(A){const F=e.getRecordMatcher(A);F&&e.removeRoute(F)}function v(){return e.getRoutes().map(A=>A.record)}function D(A){return!!e.getRecordMatcher(A)}function S(A,F){if(F=ye({},F||l.value),typeof A=="string"){const p=Zo(n,A,F.path),_=e.resolve({path:p.path},F),b=o.createHref(p.fullPath);return ye(p,_,{params:h(_.params),hash:ho(p.hash),redirectedFrom:void 0,href:b})}let V;if("path"in A)V=ye({},A,{path:Zo(n,A.path,F.path).path});else{const p=ye({},A.params);for(const _ in p)p[_]==null&&delete p[_];V=ye({},A,{params:f(A.params)}),F.params=f(F.params)}const W=e.resolve(V,F),le=A.hash||"";W.params=u(h(W.params));const Se=Sh(s,ye({},A,{hash:cp(le),path:W.path})),ee=o.createHref(Se);return ye({fullPath:Se,hash:le,query:s===xa?pp(A.query):A.query||{}},W,{redirectedFrom:void 0,href:ee})}function j(A){return typeof A=="string"?Zo(n,A,l.value.path):ye({},A)}function $(A,F){if(d!==A)return Un(8,{from:F,to:A})}function C(A){return U(A)}function M(A){return C(ye(j(A),{replace:!0}))}function N(A){const F=A.matched[A.matched.length-1];if(F&&F.redirect){const{redirect:V}=F;let W=typeof V=="function"?V(A):V;return typeof W=="string"&&(W=W.includes("?")||W.includes("#")?W=j(W):{path:W},W.params={}),ye({query:A.query,hash:A.hash,params:"path"in W?{}:A.params},W)}}function U(A,F){const V=d=S(A),W=l.value,le=A.state,Se=A.force,ee=A.replace===!0,p=N(V);if(p)return U(ye(j(p),{state:typeof p=="object"?ye({},le,p.state):le,force:Se,replace:ee}),F||V);const _=V;_.redirectedFrom=F;let b;return!Se&&Ch(s,W,V)&&(b=Un(16,{to:_,from:W}),tt(W,W,!0,!1)),(b?Promise.resolve(b):X(_,W)).catch(E=>$t(E)?$t(E,2)?E:De(E):ae(E,_,W)).then(E=>{if(E){if($t(E,2))return U(ye({replace:ee},j(E.to),{state:typeof E.to=="object"?ye({},le,E.to.state):le,force:Se}),F||_)}else E=re(_,W,!0,ee,le);return G(_,W,E),E})}function L(A,F){const V=$(A,F);return V?Promise.reject(V):Promise.resolve()}function X(A,F){let V;const[W,le,Se]=Tp(A,F);V=er(W.reverse(),"beforeRouteLeave",A,F);for(const p of W)p.leaveGuards.forEach(_=>{V.push(Kt(_,A,F))});const ee=L.bind(null,A,F);return V.push(ee),Cn(V).then(()=>{V=[];for(const p of r.list())V.push(Kt(p,A,F));return V.push(ee),Cn(V)}).then(()=>{V=er(le,"beforeRouteUpdate",A,F);for(const p of le)p.updateGuards.forEach(_=>{V.push(Kt(_,A,F))});return V.push(ee),Cn(V)}).then(()=>{V=[];for(const p of A.matched)if(p.beforeEnter&&!F.matched.includes(p))if(_t(p.beforeEnter))for(const _ of p.beforeEnter)V.push(Kt(_,A,F));else V.push(Kt(p.beforeEnter,A,F));return V.push(ee),Cn(V)}).then(()=>(A.matched.forEach(p=>p.enterCallbacks={}),V=er(Se,"beforeRouteEnter",A,F),V.push(ee),Cn(V))).then(()=>{V=[];for(const p of i.list())V.push(Kt(p,A,F));return V.push(ee),Cn(V)}).catch(p=>$t(p,8)?p:Promise.reject(p))}function G(A,F,V){for(const W of a.list())W(A,F,V)}function re(A,F,V,W,le){const Se=$(A,F);if(Se)return Se;const ee=F===Ft,p=xn?history.state:{};V&&(W||ee?o.replace(A.fullPath,ye({scroll:ee&&p&&p.scroll},le)):o.push(A.fullPath,le)),l.value=A,tt(A,F,V,ee),De()}let ge;function ve(){ge||(ge=o.listen((A,F,V)=>{if(!ut.listening)return;const W=S(A),le=N(W);if(le){U(ye(le,{replace:!0}),W).catch(_s);return}d=W;const Se=l.value;xn&&Lh(Ea(Se.fullPath,V.delta),No()),X(W,Se).catch(ee=>$t(ee,12)?ee:$t(ee,2)?(U(ee.to,W).then(p=>{$t(p,20)&&!V.delta&&V.type===Ps.pop&&o.go(-1,!1)}).catch(_s),Promise.reject()):(V.delta&&o.go(-V.delta,!1),ae(ee,W,Se))).then(ee=>{ee=ee||re(W,Se,!1),ee&&(V.delta&&!$t(ee,8)?o.go(-V.delta,!1):V.type===Ps.pop&&$t(ee,20)&&o.go(-1,!1)),G(W,Se,ee)}).catch(_s)}))}let $e=cs(),we=cs(),de;function ae(A,F,V){De(A);const W=we.list();return W.length?W.forEach(le=>le(A,F,V)):console.error(A),Promise.reject(A)}function he(){return de&&l.value!==Ft?Promise.resolve():new Promise((A,F)=>{$e.add([A,F])})}function De(A){return de||(de=!A,ve(),$e.list().forEach(([F,V])=>A?V(A):F()),$e.reset()),A}function tt(A,F,V,W){const{scrollBehavior:le}=t;if(!xn||!le)return Promise.resolve();const Se=!V&&kh(Ea(A.fullPath,0))||(W||!V)&&history.state&&history.state.scroll||null;return ti().then(()=>le(A,F,Se)).then(ee=>ee&&Ih(ee)).catch(ee=>ae(ee,A,F))}const He=A=>o.go(A);let Pe;const ct=new Set,ut={currentRoute:l,listening:!0,addRoute:m,removeRoute:g,hasRoute:D,getRoutes:v,resolve:S,options:t,push:C,replace:M,go:He,back:()=>He(-1),forward:()=>He(1),beforeEach:r.add,beforeResolve:i.add,afterEach:a.add,onError:we.add,isReady:he,install(A){const F=this;A.component("RouterLink",vp),A.component("RouterView",Nc),A.config.globalProperties.$router=F,Object.defineProperty(A.config.globalProperties,"$route",{enumerable:!0,get:()=>w(l)}),xn&&!Pe&&l.value===Ft&&(Pe=!0,C(o.location).catch(le=>{}));const V={};for(const le in Ft)V[le]=Oe(()=>l.value[le]);A.provide(Do,F),A.provide(ai,ts(V)),A.provide(Ir,l);const W=A.unmount;ct.add(A),A.unmount=function(){ct.delete(A),ct.size<1&&(d=Ft,ge&&ge(),ge=null,l.value=Ft,Pe=!1,de=!1),W()}}};return ut}function Cn(t){return t.reduce((e,n)=>e.then(()=>n()),Promise.resolve())}function Tp(t,e){const n=[],s=[],o=[],r=Math.max(e.matched.length,t.matched.length);for(let i=0;i<r;i++){const a=e.matched[i];a&&(t.matched.find(d=>Bn(d,a))?s.push(a):n.push(a));const l=t.matched[i];l&&(e.matched.find(d=>Bn(d,l))||o.push(l))}return[n,s,o]}function Mt(){return ot(Do)}function Io(){return ot(ai)}const en=(t,e)=>{const n=t.__vccOpts||t;for(const[s,o]of e)n[s]=o;return n},Ap={},Sp={class:"navbar navbar-expand-lg bg-light"},Cp={class:"container-fluid"},$p=c("button",{class:"navbar-toggler",type:"button","data-bs-toggle":"collapse","data-bs-target":"#navbarSupportedContent","aria-controls":"navbarSupportedContent","aria-expanded":"false","aria-label":"Toggle navigation"},[c("span",{class:"navbar-toggler-icon"})],-1),Op={class:"collapse navbar-collapse",id:"navbarSupportedContent"},xp={class:"navbar-nav me-auto mb-2 mb-lg-0"},Pp={class:"nav-item"},Np={class:"nav-item"},Dp={class:"nav-item"},Ip={class:"nav-item"};function Lp(t,e){const n=Ye("RouterLink");return y(),T("nav",Sp,[c("div",Cp,[Q(n,{to:"/",class:"navbar-brand"},{default:ue(()=>[q("L4Y Dash")]),_:1}),$p,c("div",Op,[c("ul",xp,[c("li",Pp,[Q(n,{to:"/",class:"nav-link active","aria-current":"page"},{default:ue(()=>[q("Home")]),_:1})]),c("li",Np,[Q(n,{to:"/login",class:"nav-link"},{default:ue(()=>[q("Login")]),_:1})]),c("li",Dp,[Q(n,{to:"/remind",class:"nav-link"},{default:ue(()=>[q("Remind")]),_:1})]),c("li",Ip,[Q(n,{to:"/activate",class:"nav-link"},{default:ue(()=>[q("Activate")]),_:1})])])])])])}const kp=en(Ap,[["render",Lp]]),ce=yn("profileStore",{state:()=>({endPoints:{reminder:"/reminder",login:"/authenticate",activate:"/activation",logout:"/logout",password:"/password",profile:"/profile"},production:!0,system:{name:"L4Y Dash"},jwt:null,auth:{},remind:{},registration:{},activation:{},changePassword:{},profile:{},cookie:{path:"/"},reference:{},logging:[],captureLogging:!1}),getters:{rootDomain(){return(this.production==!1||location.host.substring(0,9)=="127.0.0.1"||location.host.substring(0,9)=="localhost"||location.host.substring(0,27)=="dashboard.leagues4you.local")&&(this.production=!1),"leagues4you.co.uk"},rootProtocol(){return window.location.href.indexOf("staging")>-1?"https://staging.":window.location.href.indexOf("local")>-1?"http://local.":"https://"},apiEndpoint(){return this.rootProtocol+"//q."+this.rootDomain}},actions:{loggingAdd(t){this.captureLogging===!0&&this.logging.push(t)},async login(t,e){const n=this.rootProtocol+"public.v2.api."+this.rootDomain+"/authenticate";try{let o=await(await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:e})})).json();return(o==null?void 0:o.success)===!0?"2FA_REQUIRED":(console.log("Login Failed",n,{email:t,password:e}),o)}catch(s){return console.log("Login Failed",n,{email:t,password:e}),s}},logout(){return this.rootProtocol+""+this.rootDomain+this.endPoints.logout,this.jwt=null,this.profile={},this.storeJWT(),!0},async register(){const t=this.rootApi.public+"register";let e;try{let n=await fetch(t,{method:"POST",body:JSON.stringify(this.registration)});return e=n.status,e!=200?await n.json():(this.activation.email=this.registration.email,this.registration={},!0)}catch(n){console.log(n)}},async activate(){const t=this.rootProtocol+"public.v3.api."+this.rootDomain+this.endPoints.activate;let e="POST",n=JSON.stringify({email:this.activation.email,code:this.activation.code});try{let s=await fetch(t,{method:e,body:n}),o=await s.json();s.status==200&&(this.jwt=o.token,this.storeJWT(o))}catch(s){console.log(s)}},async verify2FA(t,e){var s;const n=this.rootProtocol+"public.v2.api."+this.rootDomain+this.endPoints.activate;try{const r=await(await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,code:t})})).json();if(((s=r==null?void 0:r.success)==null?void 0:s.token)&&r.success.token.length>0){const i=r.success.token;return this.jwt=i,this.storeJWT(i),!0}else return console.log("2FA Verification failed:",r),!1}catch(o){return console.error("2FA Verification error:",o),!1}},async reminder(){const t=this.rootProtocol+"public.v3.api."+this.rootDomain+this.endPoints.reminder;let e;try{let n=await fetch(t,{method:"POST",body:JSON.stringify(this.remind)});return e=n.status,e!=200?await n.json():(this.activation.email=this.remind.email,!0)}catch(n){console.log(n)}},async savePassword(){const t=this.rootProtocol+"user.v3.api."+this.rootDomain+this.endPoints.password;try{let e=await fetch(t,{headers:new Headers({Authorization:"Bearer "+this.jwt}),method:"PATCH",body:JSON.stringify(this.changePassword)}),n=e.status,s=await e.json();return n==200?(this.changePassword={},!0):s}catch(e){return e}},async fetchProfile(){if(this.profile={},!this.jwt)return;const t=this.rootProtocol+"admin.v3.api."+this.rootDomain+this.endPoints.profile;let e=new Headers({Authorization:"Bearer "+this.jwt}),n;try{if(n=await fetch(t,{method:"GET",headers:e}),!n.ok||n.status!=200)return"Connection Failed";let s=await n.json();return this.profile=s,!0}catch(s){return this.jwt=null,this.storeJWT(),s}},saveProfile(){const t=this.rootApi.user+"profile",e="PATCH";let n;fetch(t,{method:e,headers:new Headers({Authorization:"Bearer "+this.jwt}),body:JSON.stringify(this.profile)}).then(s=>{n=s.status}).catch(s=>console.error("Status",n,"Error",s))},fetchJwt(){if(this.jwt)return this.jwt;let e="jwt"+"=",s=decodeURIComponent(document.cookie).split(";");for(let r=0;r<s.length;r++){let i=s[r];for(;i.charAt(0)==" ";)i=i.substring(1);if(i.indexOf(e)==0){var o=i.substring(e.length,i.length);return this.jwt=o,o}}},fetchReferenceData(){let t=this.rootApi.user+"reference";fetch(t,{method:"GET",headers:new Headers({Authorization:"Bearer "+this.jwt})}).then(e=>e.json()).then(e=>this.reference=e).catch(e=>console.error(e))},storeJWT(t){if(t&&t.token){let e="jwt="+t.token+"; path=/; domain=."+this.rootDomain+"; expires="+t.expires;document.cookie=e}else document.cookie="jwt=;path=/; domain=."+this.rootDomain+";expires=Thu, 01 Jan 1970 00:00:01 GMT; Max-Age=-99999999;"}},persist:!0}),Rp={class:"navbar navbar-expand-sm bg-light"},Mp={class:"container-fluid"},Vp=c("button",{class:"navbar-toggler",type:"button","data-bs-toggle":"collapse","data-bs-target":"#navbarSupportedContent","aria-controls":"navbarSupportedContent","aria-expanded":"false","aria-label":"Toggle navigation"},[c("span",{class:"navbar-toggler-icon"})],-1),jp={class:"collapse navbar-collapse",id:"navbarSupportedContent"},Fp={class:"navbar-nav ms-auto mb-2 mb-lg-0"},Hp={class:"nav-item"},Bp={class:"nav-item"},Up={class:"nav-item dropdown"},Kp=c("a",{class:"nav-link dropdown-toggle",href:"#",role:"button","data-bs-toggle":"dropdown","aria-expanded":"false"}," Teams ",-1),Wp={class:"dropdown-menu"},zp={class:"nav-item"},Yp={class:"nav-item"},qp={class:"nav-item dropdown"},Gp=c("a",{class:"nav-link dropdown-toggle",href:"#",role:"button","data-bs-toggle":"dropdown","aria-expanded":"false"}," Finance ",-1),Jp={class:"dropdown-menu"},Xp={class:"nav-item"},Qp={class:"nav-item dropdown"},Zp=c("a",{class:"nav-link dropdown-toggle",href:"#",role:"button","data-bs-toggle":"dropdown","aria-expanded":"false"}," Me ",-1),em={class:"dropdown-menu"},tm={class:""},nm={class:""},sm=["onClick"],om={class:"align-self-center"},rm=["href"],im={__name:"User.Nav",setup(t){const e=ce(),n=Mt(),s=async()=>{await e.logout()===!0&&n.replace("/login")};return(o,r)=>{const i=Ye("RouterLink");return y(),T("nav",Rp,[c("div",Mp,[Q(i,{to:"/",class:"navbar-brand"},{default:ue(()=>[q("Dashboard")]),_:1}),Vp,c("div",jp,[c("ul",Fp,[c("li",Hp,[Q(i,{to:"/",class:"nav-link active","aria-current":"page"},{default:ue(()=>[q("Home")]),_:1})]),c("li",Bp,[Q(i,{to:"/venues",class:"nav-link"},{default:ue(()=>[q("Venues")]),_:1})]),c("li",Up,[Kp,c("ul",Wp,[c("li",zp,[Q(i,{to:"/teams",class:"nav-link"},{default:ue(()=>[q("Listing")]),_:1})]),c("li",Yp,[Q(i,{to:"/team-management",class:"nav-link"},{default:ue(()=>[q("Management")]),_:1})])])]),c("li",qp,[Gp,c("ul",Jp,[c("li",Xp,[Q(i,{to:"/venues-transaction-report",class:"nav-link"},{default:ue(()=>[q("Venue Report")]),_:1}),Q(i,{to:"/purchase-transactions",class:"nav-link"},{default:ue(()=>[q("Venue Invoices")]),_:1}),Q(i,{to:"/venues-prepay-transactions",class:"nav-link"},{default:ue(()=>[q("Venue Cards")]),_:1}),Q(i,{to:"/purchase-transaction-files",class:"nav-link"},{default:ue(()=>[q("Import Files")]),_:1})])])]),c("li",Qp,[Zp,c("ul",em,[c("li",tm,[Q(i,{to:"/password",class:"dropdown-item"},{default:ue(()=>[q("Password")]),_:1})]),c("li",nm,[c("a",{class:"dropdown-item",href:"#",onClick:Le(s,["prevent"])},"Logout",8,sm)])])]),c("li",om,[c("a",{href:w(e).rootProtocol+"//hub."+w(e).rootDomain,class:"btn btn-sm btn-warning"},"Hub",8,rm)])])])])])}}},Lo=yn("userStore",{state:()=>({users:[],user:{},coordinators:[],fetchCriteria:{start:1,limit:20}}),actions:{async getUsers(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/users";let n="GET",s=new Headers({Authorization:"Bearer "+t.jwt});try{let o=await fetch(e,{method:n,headers:s}),r=await o.json();return o.status==200?(this.users=r,!0):r}catch(o){return o}},async fetchUser(t){return t},getUser(t){for(let e in this.users)if(this.users[e].id==t)return this.user=this.users[e],this.users[e]},async saveUser(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/user";let n=this.user.id?"PATCH":"POST",s=new Headers({Authorization:"Bearer "+t.jwt}),o=JSON.stringify(this.user);try{let r=await fetch(e,{method:n,headers:s,body:o}),i=await r.json();return r.status==200?(this.user=i,!0):i}catch(r){return r}},async searchUsers(t){if(t&&(this.searchText=t),this.user={},!this.searchText||this.searchText.length==0)return await this.getUsers(),!0;const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/user-search/"+this.fetchCriteria.limit+"/"+(this.fetchCriteria.start-1);let s="POST",o=new Headers({Authorization:"Bearer "+e.jwt}),r=JSON.stringify({searchText:this.searchText});try{let i=await fetch(n,{method:s,headers:o,body:r}),a=await i.json();return console.log(a),i.status==200?(this.users=a.data,this.total=a.total,this.count=a.count,!0):a}catch(i){return i}},async fetchCoordinators(){const t=ce(),e=t.rootProtocol+"admin.v3.api."+t.rootDomain+"/coordinators";let n="GET",s=new Headers({Authorization:"Bearer "+t.jwt});try{let o=await fetch(e,{method:n,headers:s}),r=await o.json();return o.status==200?(this.coordinators=r,!0):r}catch(o){return o}}},persist:!0}),et=yn("alertStore",{state:()=>({messages:[],interval:null}),actions:{add(t,e,n){this.interval&&clearInterval(this.interval),this.messages.push({type:t,title:e,text:n}),this.interval=setTimeout(()=>{this.messages=[]},3500)}}});const am={class:"alerts",style:{opacity:".9"}},lm={class:"p-0"},cm={__name:"Alerts",setup(t){const e=et();return(n,s)=>(y(),T("div",am,[(y(!0),T(Ee,null,Re(w(e).messages,(o,r)=>(y(),T("div",{key:r,class:bn(["alert text-right","alert-"+o.type]),role:"alert"},[c("p",lm,[c("b",null,B(o.title),1),q(". "+B(o.text),1)])],2))),128))]))}},um=en(cm,[["__scopeId","data-v-5944e52e"]]);const dm=c("footer",null,null,-1),fm={__name:"App",setup(t){const e=ce(),n=Lo(),s=Mt();return ze(async()=>{await e.fetchJwt()||s.replace("/login"),await e.fetchProfile()!==!0&&s.replace("/login"),n.fetchCoordinators()}),(o,r)=>(y(),T(Ee,null,[c("header",null,[w(e).jwt?(y(),$s(im,{key:0})):(y(),$s(kp,{key:1}))]),Q(w(Nc)),Q(um),dm],64))}},hm={},pm={class:"container-fluid"},mm=c("div",{class:"container"},[c("h1",null,"hello")],-1),_m=[mm];function gm(t,e){return y(),T("main",pm,_m)}const vm=en(hm,[["render",gm]]),bm={class:"login container"},ym=c("h1",{class:"mt-4"},"Login",-1),Em=["onSubmit"],wm=c("label",{for:"email",class:"sr-only"},"Email address",-1),Tm=c("label",{for:"password",class:"sr-only"},"Password",-1),Am=["disabled"],Sm={key:0,class:"spinner-border spinner-border-sm text-light"},Cm={key:1},$m=["onSubmit"],Om=c("label",{for:"code",class:"sr-only"},"Enter 2FA Code",-1),xm=["disabled"],Pm={key:0,class:"spinner-border spinner-border-sm text-light"},Nm={key:1},Dm={class:"mt-4"},Im={__name:"LoginView",setup(t){const e=oe(""),n=oe(""),s=oe(""),o=oe(!1),r=oe(!1),i=Mt(),a=ce(),l=et(),d=async()=>{o.value=!0;const f=await a.login(e.value,n.value);f===!0?(l.add("success","Success","You are logged in"),i.push("/dashboard")):f==="2FA_REQUIRED"?r.value=!0:l.add("danger","Oops","Login failed."),o.value=!1},u=async()=>{o.value=!0,await a.verify2FA(s.value,e.value)?(l.add("success","Success","You are fully authenticated!"),i.push("/dashboard")):l.add("danger","Oops","Invalid 2FA code."),o.value=!1};return(f,h)=>{const m=Ye("router-link");return y(),T("div",bm,[ym,r.value?(y(),T("form",{key:1,onSubmit:Le(u,["prevent"]),class:"form-signin"},[Om,te(c("input",{type:"text",id:"code","onUpdate:modelValue":h[2]||(h[2]=g=>s.value=g),class:"form-control",placeholder:"Enter the verification code",required:"",autofocus:""},null,512),[[fe,s.value]]),c("button",{id:"verifyBtn",class:"btn btn-sm btn-success btn-block mt-2",type:"submit",disabled:o.value},[o.value?(y(),T("div",Pm)):(y(),T("span",Nm,"Verify"))],8,xm)],40,$m)):(y(),T("form",{key:0,onSubmit:Le(d,["prevent"]),class:"form-signin"},[wm,te(c("input",{type:"email",id:"email","onUpdate:modelValue":h[0]||(h[0]=g=>e.value=g),class:"form-control",placeholder:"Email address",required:"",autofocus:""},null,512),[[fe,e.value]]),Tm,te(c("input",{type:"password",id:"password","onUpdate:modelValue":h[1]||(h[1]=g=>n.value=g),class:"form-control",placeholder:"Password",required:""},null,512),[[fe,n.value]]),c("button",{id:"loginBtn",class:"btn btn-sm btn-primary btn-block mt-2",type:"submit",disabled:o.value},[o.value?(y(),T("div",Sm)):(y(),T("span",Cm,"Sign in"))],8,Am)],40,Em)),c("p",Dm,[q(" Forgotten password? Click "),Q(m,{to:"/remind"},{default:ue(()=>[q("here")]),_:1}),q(" to get a reminder. ")])])}}},Lm={class:"reminder container mt-4"},km=c("h1",null,"Reminder",-1),Rm=["onSubmit"],Mm=c("label",{for:"profile.remind.email",class:"sr-only"},"Email address",-1),Vm={class:"btn btn-primary btn-block mt-2",type:"submit"},jm={key:0,class:"spinner-border spinner-border-sm text-light",style:{width:"1.5rem",height:"1.5rem"},role:"status"},Fm=c("span",{class:"visually-hidden"},"Loading...",-1),Hm=[Fm],Bm={key:1},Um={class:"mt-4"},Km={__name:"RemindView",setup(t){const e=oe(!1),n=Mt(),s=ce(),o=et(),r=async()=>{e.value=!0,await s.reminder()===!0?(o.add("success","Success","We have sent you a password reminder"),n.push("/activate")):(o.add("danger","Oops","Could not send a password reminder. Are you definitely registered with us?"),e.value=!1)};return(i,a)=>{const l=Ye("router-link");return y(),T("div",Lm,[km,c("form",{onSubmit:Le(r,["prevent"]),class:"form-signin"},[Mm,te(c("input",{type:"email",id:"profile.remind.email","onUpdate:modelValue":a[0]||(a[0]=d=>w(s).remind.email=d),class:"form-control",placeholder:"Email address",required:"",autofocus:""},null,512),[[fe,w(s).remind.email]]),c("button",Vm,[e.value?(y(),T("div",jm,Hm)):(y(),T("span",Bm,"Send Reminder"))])],40,Rm),c("p",Um,[q(" Remembered your password? Click "),Q(l,{to:"/login"},{default:ue(()=>[q("here")]),_:1}),q(" to login. ")])])}}},Wm={class:"activate container"},zm=c("h1",{class:"mt-4"},"Activate",-1),Ym=["onSubmit"],qm=c("label",{for:"profile.activation.email",class:"sr-only"},"Email address",-1),Gm=c("label",{for:"profile.activation.code",class:"sr-only"},"Code",-1),Jm=["disabled"],Xm={key:0,class:"spinner-border spinner-border-sm text-light",style:{width:"1.5rem",height:"1.5rem"},role:"status"},Qm=c("span",{class:"visually-hidden"},"Loading...",-1),Zm=[Qm],e_={key:1},t_={class:"mt-4"},n_={__name:"ActivateView",setup(t){const e=oe(!1),n=Mt(),s=Io(),o=ce(),r=et(),i=async()=>{e.value=!0,await o.activate(),o.jwt?(r.add("success","Success","You are activated and logged in"),r.add("info","Hint","Change your password now"),n.push("/dashboard")):(r.add("danger","Oops","Activation failed. Try again or use the Password Reminder option"),e.value=!1)};return ze(()=>{s.params.email&&(o.activation.email=s.params.email),s.params.code&&(o.activation.code=s.params.code),o.activation.email&&o.activation.code&&i()}),(a,l)=>{const d=Ye("router-link");return y(),T("div",Wm,[zm,c("form",{onSubmit:Le(i,["prevent"]),class:"form-signin"},[qm,te(c("input",{type:"email",id:"profile.activation.email","onUpdate:modelValue":l[0]||(l[0]=u=>w(o).activation.email=u),class:"form-control",placeholder:"Email address",required:"",autofocus:""},null,512),[[fe,w(o).activation.email]]),Gm,te(c("input",{type:"number",id:"profile.activation.code","onUpdate:modelValue":l[1]||(l[1]=u=>w(o).activation.code=u),class:"form-control",placeholder:"Code",required:""},null,512),[[fe,w(o).activation.code]]),c("button",{id:"activateBtn",class:"btn btn-primary btn-block mt-2",type:"submit",disabled:e.value},[e.value?(y(),T("div",Xm,Zm)):(y(),T("span",e_,"Activate"))],8,Jm)],40,Ym),c("p",t_,[q(" Not arrived? Click "),Q(d,{to:"/remind"},{default:ue(()=>[q("here")]),_:1}),q(" to request again. ")])])}}},ko=yn("leagueStore",{state:()=>({leagues:[],league:{}}),actions:{async getLeagues(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/leagues";let n="GET",s=new Headers({Authorization:"Bearer "+t.jwt});try{let o=await fetch(e,{method:n,headers:s}),r=await o.json();return o.status==200?(this.leagues=r,!0):r}catch(o){return o}},getLeague(t){for(let e in this.leagues)if(this.leagues[e].id==t)return this.league=this.leagues[e],this.leagues[e]},getLeagueName(t){let e=this.getLeague(t);return e&&e.name?e.name:null},async saveLeague(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/league";let n=this.venue.id?"PATCH":"POST",s=new Headers({Authorization:"Bearer "+t.jwt}),o=JSON.stringify(this.league);try{let r=await fetch(e,{method:n,headers:s,body:o}),i=await r.json();return r.status==200?(this.league=i,!0):i}catch(r){return r}}},persist:!0}),ns=yn("venueStore",{state:()=>({venues:[],venue:{},purchaseTerms:[{id:2,name:"Card Payment",description:"Payment is taken from card on account"},{id:3,name:"Pay on Invoice",description:"Payment required for full invoice value"},{id:4,name:"Pre-pay",description:"Pay prior to booking commencement"}]}),actions:{async getVenues(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/venues";let n="GET",s=new Headers({Authorization:"Bearer "+t.jwt});try{let o=await fetch(e,{method:n,headers:s}),r=await o.json();return o.status==200?(this.venues=r,!0):(console.log(r),r)}catch(o){return console.log(o),o}},getVenue(t){for(let e in this.venues)if(this.venues[e].id==t)return this.venue=this.venues[e];return t},async saveVenue(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/venue";let n=this.venue.id?"PATCH":"POST",s=new Headers({Authorization:"Bearer "+t.jwt}),o=JSON.stringify(this.venue);try{let r=await fetch(e,{method:n,headers:s,body:o}),i=await r.json();return console.log("Status",r.status),r.status==200?(this.venue=i,!0):i}catch(r){return r}},async fetchAccrual(t,e,n){const s=ce(),o=s.rootProtocol+"admin.v3.api."+s.rootDomain+"/venue-monthly-accrual/"+t+"/"+n+"/"+e;let r="GET",i=new Headers({Authorization:"Bearer "+s.jwt}),a=await fetch(o,{headers:i,method:r}),l=await a.json();return a.status==200?parseFloat(l.subTotal):l},async venuesReport(t,e){const n=ce(),s=n.rootProtocol+"admin.v3.api."+n.rootDomain+"/venues-monthly-accrual/"+t+"/"+e;let o="GET",r=new Headers({Authorization:"Bearer "+n.jwt}),i=await fetch(s,{headers:r,method:o}),a=await i.json();if(console.log(a),i.status==200)return a},coordinatorVenues(t){return this.venues.filter(e=>e.coordinatorID==t)},isCoordinatorVenue(t,e){if(!t||!e)return!1;console.log("Does Venue",t,"belong to",e);let n=this.coordinatorVenues(e);console.log("Checking",n);for(let s in n)if(n[s].id==t)return!0;return!1}}}),s_={class:"dashboardView"},o_={__name:"DashboardView",setup(t){const e=ce(),n=ko(),s=ns();return ze(async()=>{await n.getLeagues(),await s.getVenues(),await e.fetchProfile()}),(o,r)=>(y(),T("div",s_))}},r_={class:"passwordView"},i_={class:"container mt-4"},a_=c("h3",null,"Change Password",-1),l_=["onSubmit"],c_=c("label",{for:"profile.changePassword.password1"},"Enter Password",-1),u_=c("label",{for:"profile.changePassword.password2"},"Repeat Password",-1),d_=["disabled"],f_={key:0,class:"spinner-border spinner-border-sm text-light",style:{width:"1.5rem",height:"1.5rem"},role:"status"},h_=c("span",{class:"visually-hidden"},"Loading...",-1),p_=[h_],m_={key:1},__={__name:"PasswordView",setup(t){const e=Mt(),n=ce(),s=et(),o=oe(!1),r=async()=>{o.value=!0;let i=await n.savePassword();i===!0?(e.push("/"),s.add("success","Super Secure!","Password updated")):s.add("warning","Ooops",i),o.value=!1};return(i,a)=>(y(),T("div",r_,[c("div",i_,[a_,c("form",{onSubmit:Le(r,["prevent"])},[c_,te(c("input",{type:"password",id:"profile.changePassword.password1","onUpdate:modelValue":a[0]||(a[0]=l=>w(n).changePassword.password1=l),class:"form-control form-control-sm"},null,512),[[fe,w(n).changePassword.password1]]),u_,te(c("input",{type:"password",id:"profile.changePassword.password2","onUpdate:modelValue":a[1]||(a[1]=l=>w(n).changePassword.password2=l),class:"form-control form-control-sm"},null,512),[[fe,w(n).changePassword.password2]]),c("button",{id:"loginBtn",class:"btn btn-sm btn-primary mt-2",type:"submit",disabled:o.value},[o.value?(y(),T("div",f_,p_)):(y(),T("span",m_,"Save"))],8,d_)],40,l_)])]))}},g_={class:"venues"},v_={class:"container"},b_={key:0,class:"spinner-border text-success",role:"status"},y_=c("span",{class:"visually-hidden"},"Loading...",-1),E_=[y_],w_={key:1,class:"tables-responsive"},T_={class:"table table-sm"},A_=c("thead",null,[c("tr",null,[c("th",null,"Name"),c("th",null,"Address"),c("th",null,"Town"),c("th",null,"Postcode"),c("th",null,"Acc"),c("th",null,"...")])],-1),S_=c("i",{class:"bi bi-pencil"},null,-1),C_={__name:"VenuesView",setup(t){const e=ns(),n=et(),s=oe(!1),o=oe(),r=Oe(()=>o.value?e.venues.filter(i=>i.name.toLowerCase().includes(o.value.toLowerCase())||i.sageAccount&&i.sageAccount.toLowerCase().includes(o.value.toLowerCase())||i.town.toLowerCase().includes(o.value.toLowerCase())):e.venues);return ze(async()=>{s.value=!0;let i=await e.getVenues();i!==!0&&n.add("warning","Ooops",i),s.value=!1}),(i,a)=>{const l=Ye("RouterLink");return y(),T("div",g_,[c("div",v_,[c("h3",null,[q("Venues | "),Q(l,{to:"/venue",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("+")]),_:1})]),s.value?(y(),T("div",b_,E_)):(y(),T("div",w_,[te(c("input",{type:"search","onUpdate:modelValue":a[0]||(a[0]=d=>o.value=d),id:"search",class:"form-control",placeholder:"Find..."},null,512),[[fe,o.value]]),c("table",T_,[A_,c("tbody",null,[(y(!0),T(Ee,null,Re(w(r),d=>(y(),T("tr",{key:d.id},[c("td",null,B(d.name),1),c("td",null,B(d.address1),1),c("td",null,B(d.town),1),c("td",null,B(d.postcode),1),c("td",null,B(d.sageAccount),1),c("td",null,[Q(l,{to:"/venue/"+d.id},{default:ue(()=>[S_]),_:2},1032,["to"])])]))),128))])])]))])])}}};const Ve=t=>(Ls("data-v-becdca4c"),t=t(),ks(),t),$_={class:"venues"},O_={class:"container"},x_={key:0,class:"spinner-grow spinner-grow-sm text-success",role:"status"},P_=Ve(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),N_=[P_],D_=Ve(()=>c("ul",{class:"nav nav-tabs",id:"myTab",role:"tablist"},[c("li",{class:"nav-item",role:"presentation"},[c("button",{class:"nav-link active",id:"home-tab","data-bs-toggle":"tab","data-bs-target":"#home-tab-pane",type:"button",role:"tab","aria-controls":"home-tab-pane","aria-selected":"true"}," Main ")])],-1)),I_={class:"tab-content mt-2",id:"myTabContent"},L_={class:"tab-pane fade show active",id:"home-tab-pane",role:"tabpanel","aria-labelledby":"home-tab",tabindex:"0"},k_=["onSubmit"],R_={class:"d-flex flex-column flex-sm-row"},M_={class:"d-flex flex-column w-100 pe-1"},V_=Ve(()=>c("label",{for:"venue.name"},"Name",-1)),j_={class:"d-flex flex-column flex-sm-row"},F_={class:"d-flex flex-column w-100 ps-1"},H_=Ve(()=>c("label",{for:"venue.coordinatorID"},"Coordinator",-1)),B_=["value"],U_={class:"d-flex flex-column flex-sm-row"},K_={class:"d-flex flex-column w-100 pe-1"},W_=Ve(()=>c("label",{for:"venue.address1"},"Address 1",-1)),z_={class:"d-flex flex-column w-100 ps-1"},Y_=Ve(()=>c("label",{for:"venue.address2"},"Address Line 2",-1)),q_={class:"d-flex flex-column flex-sm-row"},G_={class:"d-flex flex-column w-100 pe-1"},J_=Ve(()=>c("label",{for:"venue.town"},"Town",-1)),X_={class:"d-flex flex-column w-100 ps-1"},Q_=Ve(()=>c("label",{for:"venue.postcode"},"Postcode",-1)),Z_={class:"d-flex flex-column flex-sm-row"},eg={class:"d-flex flex-column w-100 pe-1"},tg=Ve(()=>c("label",{for:"venue.lat"},"Latitude",-1)),ng={class:"d-flex flex-column w-100 ps-1"},sg=Ve(()=>c("label",{for:"venue.lng"},"Longitude",-1)),og={class:"d-flex flex-column flex-sm-row"},rg={class:"d-flex flex-column w-100 pe-1"},ig=Ve(()=>c("label",{for:"venue.sageAccount"},"Sage Acc/No",-1)),ag={class:"d-flex flex-column w-100 ps-1"},lg=Ve(()=>c("label",{for:"venue.purchaseTerms"},"Purchase Terms",-1)),cg=["title","value"],ug={class:"d-flex flex-column w-100 ps-1"},dg=Ve(()=>c("label",{for:"venue.status"},"Status",-1)),fg=Ve(()=>c("option",{value:1},"Active",-1)),hg=Ve(()=>c("option",{value:0},"Inactive",-1)),pg=[fg,hg],mg=Ve(()=>c("label",{for:"venue.notes"},"Notes",-1)),_g=Ve(()=>c("button",{class:"btn btn-sm btn-success mt-2"},"Save",-1)),gg={class:"tab-pane fade",id:"profile-tab-pane",role:"tabpanel","aria-labelledby":"profile-tab",tabindex:"0"},vg={class:"table"},bg=Ve(()=>c("thead",null,[c("tr",null,[c("th",null,"Sport"),c("th",null,"Date"),c("th",null,"Time"),c("th",null,"Charge")])],-1)),yg={__name:"VenueView",setup(t){const e=Mt(),n=et(),s=ns(),o=Lo(),r=oe(!1),i=async()=>{r.value=!0;let a=await s.saveVenue();a===!0?(n.add("success","Saved",s.venue.name),e.push("/venues")):(n.add("warning","Ooops",a),r.value=!1)};return ze(async()=>{const a=Io();a.params.id&&(s.venues.length==0&&await s.getVenues(),s.getVenue(a.params.id))}),(a,l)=>{const d=Ye("RouterLink");return y(),T("div",$_,[c("div",O_,[c("h3",null,[q(" Venue "),r.value?(y(),T("div",x_,N_)):(y(),$s(d,{key:1,to:"/venues",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("Back")]),_:1}))]),D_,c("div",I_,[c("div",L_,[c("form",{onSubmit:Le(i,["prevent"])},[c("div",R_,[c("div",M_,[V_,te(c("input",{type:"text","onUpdate:modelValue":l[0]||(l[0]=u=>w(s).venue.name=u),id:"venue.name",class:"form-control",placeholder:"Venue Name",required:""},null,512),[[fe,w(s).venue.name]])])]),c("div",j_,[c("div",F_,[H_,te(c("select",{"onUpdate:modelValue":l[1]||(l[1]=u=>w(s).venue.coordinatorID=u),id:"venue.coordinatorID",class:"form-control"},[(y(!0),T(Ee,null,Re(w(o).coordinators,u=>(y(),T("option",{value:u.id},B(u.name),9,B_))),256))],512),[[Mn,w(s).venue.coordinatorID]])])]),c("div",U_,[c("div",K_,[W_,te(c("input",{type:"text","onUpdate:modelValue":l[2]||(l[2]=u=>w(s).venue.address1=u),id:"venue.address1",class:"form-control",placeholder:"Venue Address Line 1",required:""},null,512),[[fe,w(s).venue.address1]])]),c("div",z_,[Y_,te(c("input",{type:"text","onUpdate:modelValue":l[3]||(l[3]=u=>w(s).venue.address2=u),id:"venue.address2",class:"form-control",placeholder:"Venue Address Line 2"},null,512),[[fe,w(s).venue.address2]])])]),c("div",q_,[c("div",G_,[J_,te(c("input",{type:"text","onUpdate:modelValue":l[4]||(l[4]=u=>w(s).venue.town=u),id:"venue.town",class:"form-control",placeholder:"Venue Town",required:""},null,512),[[fe,w(s).venue.town]])]),c("div",X_,[Q_,te(c("input",{type:"text","onUpdate:modelValue":l[5]||(l[5]=u=>w(s).venue.postcode=u),id:"venue.postcode",class:"form-control",placeholder:"Venue Postcode",required:""},null,512),[[fe,w(s).venue.postcode]])])]),c("div",Z_,[c("div",eg,[tg,te(c("input",{type:"text","onUpdate:modelValue":l[6]||(l[6]=u=>w(s).venue.lat=u),id:"venue.lat",class:"form-control",placeholder:"Latitude"},null,512),[[fe,w(s).venue.lat]])]),c("div",ng,[sg,te(c("input",{type:"text","onUpdate:modelValue":l[7]||(l[7]=u=>w(s).venue.lng=u),id:"venue.lng",class:"form-control",placeholder:"Longitude"},null,512),[[fe,w(s).venue.lng]])])]),c("div",og,[c("div",rg,[ig,te(c("input",{type:"text","onUpdate:modelValue":l[8]||(l[8]=u=>w(s).venue.sageAccount=u),id:"venue.sageAccount",class:"form-control",placeholder:"Sage Account Code"},null,512),[[fe,w(s).venue.sageAccount]])]),c("div",ag,[lg,te(c("select",{"onUpdate:modelValue":l[9]||(l[9]=u=>w(s).venue.purchaseTerms=u),id:"venue.purchaseTerms",class:"form-control"},[(y(!0),T(Ee,null,Re(w(s).purchaseTerms,u=>(y(),T("option",{key:u.id,title:u.description,value:u.id},B(u.name),9,cg))),128))],512),[[Mn,w(s).venue.purchaseTerms]])]),c("div",ug,[dg,te(c("select",{"onUpdate:modelValue":l[10]||(l[10]=u=>w(s).venue.status=u),id:"venue.status",class:"form-control"},pg,512),[[Mn,w(s).venue.status,void 0,{number:!0}]])])]),mg,te(c("textarea",{"onUpdate:modelValue":l[11]||(l[11]=u=>w(s).venue.notes=u),id:"venue.notes",cols:"30",rows:"5",class:"form-control"},null,512),[[fe,w(s).venue.notes]]),_g],40,k_)]),c("div",gg,[c("table",vg,[bg,c("tbody",null,[(y(!0),T(Ee,null,Re(w(s).venue.tasters,u=>(y(),T("tr",{key:u.id},[c("td",null,B(u.sportID),1),c("td",null,B(u.date),1),c("td",null,B(u.time),1),c("td",null,B(u.charge),1)]))),128))])])])])])])}}},Eg=en(yg,[["__scopeId","data-v-becdca4c"]]),li=yn("teamStore",{state:()=>({teams:[],team:{},total:0,count:0,fetchCriteria:{start:1,limit:20},searchText:null,searchPage:1}),actions:{async getTeams(){this.team={};const t=ko();t.leagues.length==0&&await t.getLeagues();const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/teams/"+this.fetchCriteria.limit+"/"+(this.fetchCriteria.start-1);let s="GET",o=new Headers({Authorization:"Bearer "+e.jwt});try{let r=await fetch(n,{method:s,headers:o}),i=await r.json();return r.status==200?(this.teams=i.data,this.total=i.total,this.count=i.count,!0):i}catch(r){return r}},async searchTeams(t){if(t&&(this.searchText=t),this.team={},!this.searchText||this.searchText.length==0)return await this.getTeams(),!0;const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/team-search/"+this.fetchCriteria.limit+"/"+(this.fetchCriteria.start-1);let s="POST",o=new Headers({Authorization:"Bearer "+e.jwt}),r=JSON.stringify({searchText:this.searchText});try{let i=await fetch(n,{method:s,headers:o,body:r}),a=await i.json();return i.status==200?(this.teams=a.data,this.total=a.total,this.count=a.count,!0):a}catch(i){return i}},getTeam(t){for(let e in this.teams)this.teams[e].id==t&&(this.team=this.teams[e])},async saveTeam(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/team";let n=this.team.id?"PATCH":"POST",s=new Headers({Authorization:"Bearer "+t.jwt}),o=JSON.stringify(this.team);try{let r=await fetch(e,{method:n,headers:s,body:o}),i=await r.json();return r.status==200?(this.team={},!0):i}catch(r){return r}},async teamManagement(t){const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/team-management",s="POST",o=JSON.stringify({management:t});let r=new Headers({Authorization:"Bearer "+e.jwt}),i=await fetch(n,{headers:r,method:s,body:o}),a=await i.json();return i.status!==200?a:!0}},persist:!0}),wg={class:"venues"},Tg={class:"container"},Ag={key:0,class:"spinner-border text-success",role:"status"},Sg=c("span",{class:"visually-hidden"},"Loading...",-1),Cg=[Sg],$g={key:1,class:"table-responsive"},Og=["onSubmit"],xg=c("button",{class:"btn btn-sm btn-info"},"Go",-1),Pg={key:0,"aria-label":"Page Navigation for Teams",class:"mt-2"},Ng={class:"pagination flex-wrap"},Dg=["aria-current"],Ig=["onClick"],Lg={class:"table table-sm"},kg=c("thead",null,[c("tr",null,[c("th",null,"Name"),c("th",null,"League"),c("th",null,"Capt."),c("th",null,"Trsr."),c("th",null,"...")])],-1),Rg={key:0},Mg={key:0},Vg=c("i",{class:"bi bi-pencil"},null,-1),jg={__name:"TeamsView",setup(t){const e=li(),n=ko(),s=et(),o=oe(!1);oe();const r=u=>{for(let f in n.leagues)if(n.leagues[f].id==u)return n.leagues[f].name},i=Oe(()=>e.total>0&&e.fetchCriteria.limit>0?Math.ceil(e.total/e.fetchCriteria.limit):null),a=u=>u==e.searchPage,l=async()=>{o.value=!0,e.fetchCriteria.start=1,await e.searchTeams(),o.value=!1},d=async u=>{o.value=!0,e.searchPage=e.fetchCriteria.start=u,await e.searchTeams(),o.value=!1};return ze(async()=>{o.value=!0,e.searchText=null;let u=await e.searchTeams();u!==!0&&s.add("warning","Ooops",u),o.value=!1}),(u,f)=>{const h=Ye("RouterLink");return y(),T("div",wg,[c("div",Tg,[c("h3",null,[q("Teams | "),Q(h,{to:"/team",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("+")]),_:1})]),o.value?(y(),T("div",Ag,Cg)):(y(),T("div",$g,[c("form",{onSubmit:Le(l,["prevent"]),class:"search d-flex"},[te(c("input",{type:"search","onUpdate:modelValue":f[0]||(f[0]=m=>w(e).searchText=m),id:"searchText",class:"form-control",placeholder:"Find..."},null,512),[[fe,w(e).searchText]]),xg],40,Og),w(i)>1?(y(),T("nav",Pg,[c("ul",Ng,[(y(!0),T(Ee,null,Re(w(i),m=>(y(),T("li",{key:m,class:bn(["page-item",{active:m==w(e).fetchCriteria.start}]),"aria-current":a(m)?"page":null},[c("a",{class:"page-link",href:"#",onClick:g=>d(m)},B(m),9,Ig)],10,Dg))),128))])])):Ie("",!0),c("table",Lg,[kg,c("tbody",null,[(y(!0),T(Ee,null,Re(w(e).teams,m=>(y(),T("tr",{key:m.id},[c("td",null,B(m.name),1),c("td",null,B(r(m.leagueID)),1),c("td",null,[m.managers.captain?(y(),T("span",Rg,B(m.managers.captain.email),1)):Ie("",!0)]),c("td",null,[m.managers.treasurer?(y(),T("span",Mg,B(m.managers.treasurer.email),1)):Ie("",!0)]),c("td",null,[Q(h,{to:"/team/"+m.id},{default:ue(()=>[Vg]),_:2},1032,["to"])])]))),128))])])]))])])}}},Fg={class:"team"},Hg={class:"container"},Bg={key:0,class:"spinner-grow spinner-grow-sm text-success",role:"status"},Ug=c("span",{class:"visually-hidden"},"Loading...",-1),Kg=[Ug],Wg=["onSubmit"],zg={class:"table"},Yg=c("th",null,[c("label",{for:"team.name"},"Name")],-1),qg=c("th",null,[c("label",{for:"leagueName"},"League")],-1),Gg={for:"captain"},Jg=c("i",{class:"bi bi-pencil"},null,-1),Xg={for:"treasurer"},Qg=c("i",{class:"bi bi-pencil"},null,-1),Zg={key:0,class:"d-flex flex-column"},ev={key:0,class:"d-flex flex-column"},tv=c("button",{class:"btn btn-sm btn-success mt-2"},"Save",-1),nv={__name:"TeamView",setup(t){const e=Mt(),n=et(),s=li(),o=ko();Lo();const r=oe(!1),i=async()=>{r.value=!0;let l=await s.saveTeam();l===!0?(n.add("success","Saved",s.team.name),e.push("/teams")):(n.add("warning","Ooops",l),r.value=!1)},a=async()=>{const l=s.team.id,d=s.team.name;let u={};if(u.id=s.team.leagueID,console.log(s.team.leagueID),l&&u){const f=ce(),h=f.rootProtocol+"public.v2.api."+f.rootDomain+"/team-name-checker";let m="POST",g=new Headers({Authorization:"Bearer "+f.jwt,"Content-Type":"application/json"}),v=JSON.stringify({teamId:l,league:u,name:d});try{const S=await(await fetch(h,{method:m,headers:g,body:v})).json();S.success&&(s.team.name=S.success),S.error&&n.add("warning","Oops",S.error)}catch(D){console.error("Error checking team name:",D)}}};return ze(async()=>{const l=Io();l.params.id?(s.teams.length==0&&await s.getTeams(),s.getTeam(l.params.id)):s.team={}}),(l,d)=>{const u=Ye("RouterLink");return y(),T("div",Fg,[c("div",Hg,[c("h3",null,[q("Team "),r.value?(y(),T("div",Bg,Kg)):(y(),$s(u,{key:1,to:"/teams",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("<")]),_:1}))]),c("form",{onSubmit:Le(i,["prevent"])},[c("table",zg,[c("tbody",null,[c("tr",null,[Yg,c("td",null,[te(c("input",{type:"text","onUpdate:modelValue":d[0]||(d[0]=f=>w(s).team.name=f),id:"team.name",class:"form-control",placeholder:"Team Name",required:"",onKeyup:a},null,544),[[fe,w(s).team.name]])])]),c("tr",null,[qg,c("td",null,B(w(o).getLeagueName(w(s).team.leagueID)),1)]),c("tr",null,[c("th",null,[c("label",Gg,[q("Captain "),Q(u,{to:"/team-management"},{default:ue(()=>[Jg]),_:1})])]),c("th",null,[c("label",Xg,[q("Treasurer "),Q(u,{to:"/team-management"},{default:ue(()=>[Qg]),_:1})])])]),c("tr",null,[c("td",null,[w(s).team.managers.captain?(y(),T("div",Zg,[c("span",null,B(w(s).team.managers.captain.name),1),c("span",null,B(w(s).team.managers.captain.email),1),c("span",null,B(w(s).team.managers.captain.mobile),1),c("span",null,B(w(s).team.managers.captain.dob),1),c("span",null,B(w(s).team.managers.captain.line1),1),c("span",null,B(w(s).team.managers.captain.town)+" "+B(w(s).team.managers.captain.postcode),1)])):Ie("",!0)]),c("td",null,[w(s).team.managers.treasurer?(y(),T("div",ev,[c("span",null,B(w(s).team.managers.treasurer.name),1),c("span",null,B(w(s).team.managers.treasurer.email),1),c("span",null,B(w(s).team.managers.treasurer.mobile),1),c("span",null,B(w(s).team.managers.treasurer.dob),1),c("span",null,B(w(s).team.managers.treasurer.line1),1),c("span",null,B(w(s).team.managers.treasurer.town)+" "+B(w(s).team.managers.captain.postcode),1)])):Ie("",!0)])])])]),tv],40,Wg)])])}}};const En=t=>(Ls("data-v-fc52fa62"),t=t(),ks(),t),sv={class:"teamManagement"},ov={class:"container"},rv=["onSubmit"],iv=En(()=>c("p",null,"Appoint a new Captain",-1)),av=En(()=>c("label",{for:"management.teamID"},"Team",-1)),lv={class:"teamSearch d-flex flex-column position-relative"},cv={key:0,class:"spinner-border spinner-border-sm position-absolute ms-2 mt-2",role:"status"},uv=En(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),dv=[uv],fv={key:1,class:"bi bi-search position-absolute ms-2 mt-2"},hv={class:"teamSearchResults d-flex flex-column"},pv={class:"teamListing d-flex flex-column"},mv=["onClick"],_v=En(()=>c("label",{for:"management.userID"},"User",-1)),gv={class:"teamSearch d-flex flex-column position-relative"},vv={key:0,class:"spinner-border spinner-border-sm position-absolute ms-2 mt-2",role:"status"},bv=En(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),yv=[bv],Ev={key:1,class:"bi bi-search position-absolute ms-2 mt-2"},wv={class:"userSearchResults d-flex flex-column"},Tv={class:"userListing d-flex flex-column"},Av=["onClick"],Sv={class:"form-check form-switch"},Cv=En(()=>c("label",{class:"form-check-label",for:"management.isSuppressed"},"Supress Alert?",-1)),$v=["disabled"],Ov={key:0,class:"spinner-border spinner-border-sm",role:"status"},xv=En(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),Pv=[xv],Nv={key:1},Dv={__name:"TeamManagementView",setup(t){const e=et(),n=li(),s=Lo(),o=oe({appointID:1}),r=oe(!1),i=oe(!1),a=oe(null),l=oe(null),d=oe(!1),u=oe(!1),f=g=>{a.value=g.fullName,o.value.teamID=g.id},h=g=>{l.value=g.fullName,o.value.userID=g.id},m=async()=>{u.value=!0;let g=await n.teamManagement(o.value);g===!0?(e.add("success","Success","Team Updated"),o.value={},a.value=null,l.value=null):e.add("warning","Failed",g),i.value=!1,r.value=!1,u.value=!1};return Pt(a,async g=>{g.length==0?(n.teams=[],o.value.teamID=null):(i.value=!0,await n.searchTeams(g)),i.value=!1}),Pt(l,async g=>{g.length==0?(s.users=[],o.value.userID=null):(r.value=!0,await s.searchUsers(g)),r.value=!1}),Pt(o.value,g=>{d.value=!1,g.teamID&&g.userID&&g.appointID&&(d.value=!0)}),ze(async()=>{a.value=null,n.teams=[],l.value=null,s.users=[]}),(g,v)=>{const D=Ye("RouterLink");return y(),T("div",sv,[c("div",ov,[c("h3",null,[q("Team Management "),Q(D,{to:"/teams",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("Back")]),_:1})]),c("form",{onSubmit:Le(m,["prevent"])},[iv,av,c("div",lv,[i.value?(y(),T("div",cv,dv)):(y(),T("i",fv)),te(c("input",{type:"search","onUpdate:modelValue":v[0]||(v[0]=S=>a.value=S),id:"teamSearch",class:"form-control rounded-4",placeholder:"Team Search...",autocomplete:"off"},null,512),[[fe,a.value]]),c("div",hv,[c("div",pv,[(y(!0),T(Ee,null,Re(w(n).teams,S=>(y(),T("span",{key:S.id,onClick:Le(j=>f(S),["prevent"]),class:"dropdownOption"},B(S.fullName),9,mv))),128))])])]),_v,c("div",gv,[r.value?(y(),T("div",vv,yv)):(y(),T("i",Ev)),q(),te(c("input",{type:"search","onUpdate:modelValue":v[1]||(v[1]=S=>l.value=S),id:"userSearch",class:"form-control rounded-4",placeholder:"User Search...",autocomplete:"off"},null,512),[[fe,l.value]]),c("div",wv,[c("div",Tv,[(y(!0),T(Ee,null,Re(w(s).users,S=>(y(),T("span",{key:S.id,onClick:Le(j=>h(S),["prevent"]),class:"dropdownOption"},B(S.email),9,Av))),128))])])]),c("div",Sv,[te(c("input",{class:"form-check-input",type:"checkbox",role:"switch","onUpdate:modelValue":v[2]||(v[2]=S=>o.value.isSuppressed=S),id:"management.isSuppressed"},null,512),[[nh,o.value.isSuppressed]]),Cv]),c("button",{class:"btn btn-success mt-2 updateTeamCaptainBtn",disabled:!d.value},[u.value?(y(),T("div",Ov,Pv)):(y(),T("span",Nv," Submit "))],8,$v)],40,rv)])])}}},Iv=en(Dv,[["__scopeId","data-v-fc52fa62"]]),Ro=yn("financeStore",{state:()=>({purchaseTransactions:[],purchaseTransaction:{},transactionTypes:[{id:1,ledger:"Sales",name:"Invoice"},{id:2,ledger:"Sales",name:"Credit"},{id:3,ledger:"Purchase",name:"Invoice"},{id:4,ledger:"Purchase",name:"Credit"}],vatCodes:[{id:1,code:"T0",name:"T0 (0% VAT)"},{id:2,code:"T1",name:"T1 (20% VAT)"},{id:3,code:"T2",name:"T2 (VAT Exempt)"}],purchaseTransactionImportFiles:[],prepays:[]}),getters:{purchaseTransactionTypes(){return this.transactionTypes.filter(t=>t.ledger=="Purchase")},salesTransactionTypes(){return this.transactionTypes.filter(t=>t.ledger=="Sales")},purchaseItemsTotal(){let t=0;for(let e in this.purchaseTransaction.items)t+=this.purchaseTransaction.items[e].total;return t}},actions:{async fetchPurchaseTransactions(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/purchase-transactions";let n="GET",s=new Headers({Authorization:"Bearer "+t.jwt});try{let o=await fetch(e,{method:n,headers:s}),r=await o.json();return o.status==200?(this.purchaseTransactions=r,!0):r}catch(o){return o}},async savePurchaseTransaction(){const t=ce(),e=t.rootProtocol+"user.v3.api."+t.rootDomain+"/purchase-transaction",n=new FormData;n.append("purchaseInvoice",JSON.stringify(this.purchaseTransaction));const s=new Headers({Authorization:"Bearer "+t.jwt});let r=await fetch(e,{headers:s,method:"POST",body:n}),i=await r.json();return r.status==200?(this.purchaseTransaction=i,!0):i},async savePurchaseDocument(t){if(t.size>20971520)return"File is too large to upload";const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/purchase-transaction-document/"+this.purchaseTransaction.id,s=new FormData;s.append("purchaseTransaction",t);const o=new Headers({Authorization:"Bearer "+e.jwt});let i=await fetch(n,{headers:o,method:"POST",body:s}),a=await i.json();return i.status==200?(this.purchaseTransaction=a,!0):a},async getPurchaseTransaction(t){const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/purchase-transaction/"+t;let s="GET",o=new Headers({Authorization:"Bearer "+e.jwt});try{let r=await fetch(n,{method:s,headers:o}),i=await r.json();if(r.status==200){for(let a in this.purchaseTransactions)this.purchaseTransactions[a].id==i.id&&(this.purchaseTransactions[a]=i);return this.purchaseTransaction=i,!0}return i}catch(r){return r}},async postPurchaseTransaction(t){const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/purchase-transaction-post/"+t;let s="GET",o=new Headers({Authorization:"Bearer "+e.jwt});try{let r=await fetch(n,{method:s,headers:o}),i=await r.json();if(r.status==200){for(let a in this.purchaseTransactions)this.purchaseTransactions[a].id==i.id&&(this.purchaseTransactions[a]=i);return!0}}catch(r){return r}},async unpostPurchaseTransaction(t){const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/purchase-transaction-unpost/"+t;let s="GET",o=new Headers({Authorization:"Bearer "+e.jwt});try{let r=await fetch(n,{method:s,headers:o}),i=await r.json();if(r.status==200){for(let a in this.purchaseTransactions)this.purchaseTransactions[a].id==i.id&&(this.purchaseTransactions[a]=i);return!0}}catch(r){return r}},async addNewPurchaseItem(t){if(!t.month)return"No month specified";if(!t.year)return"No year specified";if(!t.total)return"No total specified";if(!t.mainID)return"No Purchase Transaction ID specified";if(!t.vatCode)return"No VAT Code specified";const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/purchase-transaction-item",s=new Headers({Authorization:"Bearer "+e.jwt}),o="POST",r=JSON.stringify(t);let i=await fetch(n,{headers:s,method:o,body:r}),a=await i.json();if(i.status==200){for(let l in this.purchaseTransactions)this.purchaseTransactions[l].id==a.id&&(this.purchaseTransactions[l]=a);return this.purchaseTransaction=a,!0}return a},async deletePurchaseItem(t){const e=ce(),n=e.rootProtocol+"user.v3.api."+e.rootDomain+"/purchase-transaction-item/"+t,s=new Headers({Authorization:"Bearer "+e.jwt});let r=await fetch(n,{headers:s,method:"DELETE"}),i=await r.json();if(r.status==200){for(let a in this.purchaseTransactions)this.purchaseTransactions[a].id==i.id&&(this.purchaseTransactions[a]=i);return this.purchaseTransaction=i,!0}return i},async fetchPrePays(){const t=ce(),e=t.rootProtocol+"admin.v3.api."+t.rootDomain+"/venue-prepay-transactions",n=new Headers({Authorization:"Bearer "+t.jwt});let o=await fetch(e,{headers:n,method:"GET"}),r=await o.json();return o.status==200?(this.prepays=r,!0):r},async fetchPurchaseTransactionFiles(){const t=ce(),e=t.rootProtocol+"admin.v3.api."+t.rootDomain+"/transaction-import-files",n=new Headers({Authorization:"Bearer "+t.jwt});let o=await fetch(e,{headers:n,method:"GET"}),r=await o.json();return o.status==200?(this.purchaseTransactionImportFiles=r,!0):r},async resendPurhaseTransactionImportFile(t){const e=ce(),n=e.rootProtocol+"admin.v3.api."+e.rootDomain+"/resend-transaction-import-file/"+t,s=new Headers({Authorization:"Bearer "+e.jwt});let r=await fetch(n,{headers:s,method:"GET"});return r.status==200?!0:await r.json()},getTransactionType(t){for(let e in this.purchaseTransactionTypes)if(this.purchaseTransactionTypes[e].id==t)return this.purchaseTransactionTypes[e]}}});const Ae=t=>(Ls("data-v-aeeeaf8a"),t=t(),ks(),t),Lv={class:"purchaseTransaction container-fluid"},kv={class:"nav nav-tabs",id:"myTab",role:"tablist"},Rv=Ae(()=>c("li",{class:"nav-item",role:"presentation"},[c("button",{class:"nav-link",id:"home-tab","data-bs-toggle":"tab","data-bs-target":"#home-tab-pane",type:"button",role:"tab","aria-controls":"home-tab-pane","aria-selected":"true"},"Main")],-1)),Mv={key:0,class:"nav-item",role:"presentation"},Vv={class:"nav-link",id:"profile-tab","data-bs-toggle":"tab","data-bs-target":"#profile-tab-pane",type:"button",role:"tab","aria-controls":"profile-tab-pane","aria-selected":"false"},jv={key:0,class:"badge text-bg-secondary"},Fv={key:1,class:"nav-item",role:"presentation"},Hv=Ae(()=>c("button",{class:"nav-link",id:"contact-tab","data-bs-toggle":"tab","data-bs-target":"#contact-tab-pane",type:"button",role:"tab","aria-controls":"contact-tab-pane","aria-selected":"false"},"Docs",-1)),Bv=[Hv],Uv={class:"tab-content mt-4",id:"myTabContent"},Kv={class:"tab-pane fade show active",id:"home-tab-pane",role:"tabpanel","aria-labelledby":"home-tab",tabindex:"0"},Wv=["onSubmit"],zv=Ae(()=>c("input",{type:"hidden",name:"MAX_FILE_SIZE",value:"20971520"},null,-1)),Yv={class:"row"},qv={class:"com-12 col-sm-6"},Gv=Ae(()=>c("label",{for:"purchaseTransaction.venueID"},"Venue",-1)),Jv=["value"],Xv={class:"com-12 col-sm-6"},Qv=Ae(()=>c("label",{for:"purchaseTransaction.typeID"},"Type",-1)),Zv=["value"],eb={class:"row"},tb={class:"col-12 col-sm-6"},nb=Ae(()=>c("label",{for:"purchaseTransaction.reference"},"Reference",-1)),sb={class:"col-12 col-sm-6"},ob=Ae(()=>c("label",{for:"purchaseTransaction.taxDate"},"Date",-1)),rb={class:"row"},ib={class:"col-12 col-sm-6"},ab=Ae(()=>c("label",{for:"purchaseTransaction.total"},"Total",-1)),lb=Ae(()=>c("div",{class:"col col-sm-6"},null,-1)),cb={key:0,class:"btn btn-sm btn-primary mt-2"},ub={class:"tab-pane fade",id:"profile-tab-pane",role:"tabpanel","aria-labelledby":"profile-tab",tabindex:"0"},db={key:0,class:"addNew"},fb=["onSubmit"],hb={class:"row"},pb={class:"col-12 col-sm-6"},mb=Ae(()=>c("label",{for:"newitem.month"},"Month",-1)),_b={class:"col-12 col-sm-6"},gb=Ae(()=>c("label",{for:"newitem.year"},"Year",-1)),vb=["max"],bb={class:"row"},yb={class:"col-12 col-sm-6"},Eb={for:"newitem.total"},wb={key:0},Tb={class:"totalInput position-relative"},Ab={key:0,class:"spinner-grow spinner-grow-sm text-primary position-absolute top-50 end-0 translate-middle",role:"status"},Sb=Ae(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),Cb=[Sb],$b={class:"col-12 col-sm-6"},Ob=Ae(()=>c("label",{for:"newitem.vatCode"},"VAT Code",-1)),xb=["value"],Pb=Ae(()=>c("button",{class:"btn btn-sm btn-info mt-2"},"Add",-1)),Nb={class:"table"},Db=Ae(()=>c("thead",null,[c("tr",null,[c("th",null,"Period"),c("th",null,"VatCode"),c("th",{class:"text-end"},"Total"),c("th",null,"...")])],-1)),Ib={class:"text-end"},Lb=["onClick"],kb=Ae(()=>c("th",null,"Item Total",-1)),Rb={class:"text-end",colspan:"2"},Mb=Ae(()=>c("th",null,"\xA0",-1)),Vb=Ae(()=>c("th",null,"Transaction Total",-1)),jb={class:"text-end",colspan:"2"},Fb={key:0},Hb=Ae(()=>c("th",null,"\xA0",-1)),Bb=Ae(()=>c("th",null,"Balance to Allocate",-1)),Ub={class:"text-end",colspan:"2"},Kb=Ae(()=>c("th",null,"\xA0",-1)),Wb={class:"tab-pane fade",id:"contact-tab-pane",role:"tabpanel","aria-labelledby":"contact-tab",tabindex:"0"},zb=["onSubmit"],Yb=Ae(()=>c("label",{for:"purchaseTransactionFile"},"Add a Doc",-1)),qb=Ae(()=>c("input",{id:"purchaseTransactionFile",class:"form-control",type:"file",required:""},null,-1)),Gb={class:"btn btn-sm btn-info mt-2 transactionDocUploadBtn"},Jb={key:0,class:"spinner-border spinner-border-sm",role:"status"},Xb=Ae(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),Qb=[Xb],Zb={key:1},ey=Ae(()=>c("i",{class:"bi bi-send"},null,-1)),ty={class:"table table-sm"},ny=Ae(()=>c("thead",null,[c("tr",null,[c("th",null,"Name")])],-1)),sy=["onClick"],oy={__name:"PurchaseTransaction",setup(t){const e=Io(),n=Mt(),s=Oe(()=>{let $=new Date;return parseInt($.getFullYear())+1}),o=oe(!1),r=oe(!1),i=oe(!1),a=oe(!1),l=oe({}),d=oe(null),u=ce(),f=et(),h=ns(),m=Ro(),g=async $=>{const C=u.rootProtocol+"//admin.v3.api."+u.rootDomain+"/download-venue-transaction-document/"+m.purchaseTransaction.id+"/"+$;let M="get",N=new Headers({Authorization:"Bearer "+u.jwt});try{let L=await fetch(C,{method:M,headers:N});if(L.status==200){var U=document.createElement("a");U.href=window.URL.createObjectURL(await L.blob()),U.download=$,U.click();return}else f.add("warning","Failed","Could not download "+$)}catch(L){f.add("danger","Error",L)}console.log("Download",$)};Pt(l.value,async($,C)=>{if(a.value=!0,$.month>=1&&$.month<=12&&$.year>=2016&&$.year<=2022)try{let M=await h.fetchAccrual(m.purchaseTransaction.venueID,$.month,$.year);isNaN(M)?f.add("warning","Problem",M):d.value=M}catch(M){f.add("warning","Problem",M)}a.value=!1});const v=async()=>{if(m.purchaseTransaction.posted){f.add("warning","Cannot Save","Transaction already Posted");return}l.value.mainID=m.purchaseTransaction.id;let $=await m.addNewPurchaseItem(l.value);$===!0?(f.add("success","Added",l.value.total+" to "+l.value.month+"/"+l.value.year),l.value={}):f.add("warning","Failed",$)},D=async()=>{if(m.purchaseTransaction.posted){f.add("warning","Cannot Save","Transaction already Posted");return}o.value=!0;let $=await m.savePurchaseTransaction();$!==!0?f.add("warning","Problem",$):f.add("success","Purchase Transaction Saved",$),o.value=!1},S=async()=>{i.value=!0;let $=document.getElementById("purchaseTransactionFile").files[0];try{let C=await m.savePurchaseDocument($);C!==!0?f.add("warning","Problem",C):f.add("success","Success","Document uploaded")}catch(C){f.add("warning","Problem",C)}i.value=!1},j=async $=>{if(m.purchaseTransaction.posted){f.add("warning","Cannot Remove Items","Transaction already Posted");return}let C=await m.deletePurchaseItem($.id);C!==!0?f.add("warning","Problem",C):f.add("success","Success","Purchase Item Removed")};return ze(async()=>{e.params.id?(await m.getPurchaseTransaction(e.params.id),(!m.purchaseTransaction||!m.purchaseTransaction.id)&&(f.add("warning","Failed","Could not load Transaction ID "+e.params.id),n.replace("/purchase-transactions"))):m.purchaseTransaction={},document.querySelector("#myTab").querySelectorAll(".nav-link").forEach(M=>{M.id=="home-tab"?M.classList.add("active"):M.classList.remove("active")})}),($,C)=>{const M=Ye("RouterLink");return y(),T("div",Lv,[c("h3",null,[q("Venue Invoice | "),Q(M,{to:"/venues-transaction-report",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("\u2190 Report ")]),_:1}),q(" | "),Q(M,{to:"/purchase-transactions",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("\u2190 Invoices ")]),_:1})]),c("ul",kv,[Rv,w(m).purchaseTransaction.id?(y(),T("li",Mv,[c("button",Vv,[q("Items "),w(m).purchaseTransaction.items?(y(),T("span",jv,B(w(m).purchaseTransaction.items.length),1)):Ie("",!0)])])):Ie("",!0),w(m).purchaseTransaction.id?(y(),T("li",Fv,Bv)):Ie("",!0)]),c("div",Uv,[c("div",Kv,[c("form",{onSubmit:Le(D,["prevent"])},[zv,c("div",Yv,[c("div",qv,[Gv,te(c("select",{"onUpdate:modelValue":C[0]||(C[0]=N=>w(m).purchaseTransaction.venueID=N),id:"purchaseTransaction.venueID",class:"form-control",required:""},[(y(!0),T(Ee,null,Re(w(h).venues,N=>(y(),T("option",{key:N.id,value:N.id},B(N.name),9,Jv))),128))],512),[[Mn,w(m).purchaseTransaction.venueID]])]),c("div",Xv,[Qv,te(c("select",{"onUpdate:modelValue":C[1]||(C[1]=N=>w(m).purchaseTransaction.typeID=N),id:"purchaseTransaction.typeID",class:"form-control",required:""},[(y(!0),T(Ee,null,Re(w(m).purchaseTransactionTypes,N=>(y(),T("option",{key:N.id,value:N.id},B(N.name),9,Zv))),128))],512),[[Mn,w(m).purchaseTransaction.typeID]])])]),c("div",eb,[c("div",tb,[nb,te(c("input",{type:"text","onUpdate:modelValue":C[2]||(C[2]=N=>w(m).purchaseTransaction.reference=N),id:"purchaseTransaction.reference",class:"form-control",placeholder:"eg Invoice Number",required:""},null,512),[[fe,w(m).purchaseTransaction.reference]])]),c("div",sb,[ob,te(c("input",{type:"date","onUpdate:modelValue":C[3]||(C[3]=N=>w(m).purchaseTransaction.taxDate=N),id:"purchaseTransaction.taxDate",class:"form-control",required:""},null,512),[[fe,w(m).purchaseTransaction.taxDate]])])]),c("div",rb,[c("div",ib,[ab,te(c("input",{type:"text","onUpdate:modelValue":C[4]||(C[4]=N=>w(m).purchaseTransaction.total=N),id:"purchaseTransaction.total",class:"form-control",required:""},null,512),[[fe,w(m).purchaseTransaction.total]])]),lb]),w(m).purchaseTransaction.posted?Ie("",!0):(y(),T("button",cb,"Save"))],40,Wv)]),c("div",ub,[c("h4",null,[q("Items "),w(m).purchaseTransaction.posted?Ie("",!0):(y(),T("span",db,[q(" | "),c("button",{class:"btn btn-sm btn-warning",onClick:C[5]||(C[5]=N=>r.value=!r.value)},"+")]))]),r.value?(y(),T("form",{key:0,onSubmit:Le(v,["prevent"])},[c("div",hb,[c("div",pb,[mb,te(c("input",{type:"number",min:"1",max:"12","onUpdate:modelValue":C[6]||(C[6]=N=>l.value.month=N),id:"newitem.month",class:"form-control mx-1"},null,512),[[fe,l.value.month]])]),c("div",_b,[gb,te(c("input",{type:"number",min:"2016",max:w(s),"onUpdate:modelValue":C[7]||(C[7]=N=>l.value.year=N),id:"newitem.year",class:"form-control mx-1"},null,8,vb),[[fe,l.value.year]])])]),c("div",bb,[c("div",yb,[c("label",Eb,[q("Total "),d.value?(y(),T("span",wb,"Max "+B(d.value),1)):Ie("",!0)]),c("div",Tb,[te(c("input",{type:"text","onUpdate:modelValue":C[8]||(C[8]=N=>l.value.total=N),id:"newitem.total",class:"form-control mx-1"},null,512),[[fe,l.value.total]]),a.value?(y(),T("div",Ab,Cb)):Ie("",!0)])]),c("div",$b,[Ob,te(c("select",{"onUpdate:modelValue":C[9]||(C[9]=N=>l.value.vatCode=N),id:"newitem.vatCode",class:"form-control"},[(y(!0),T(Ee,null,Re(w(m).vatCodes,N=>(y(),T("option",{value:N.code},B(N.name),9,xb))),256))],512),[[Mn,l.value.vatCode]])])]),Pb],40,fb)):Ie("",!0),c("table",Nb,[Db,c("tbody",null,[(y(!0),T(Ee,null,Re(w(m).purchaseTransaction.items,N=>(y(),T("tr",{key:N.id},[c("td",null,B(N.month)+" / "+B(N.year),1),c("td",null,B(N.vatCode),1),c("td",Ib,B(Math.abs(N.total.toFixed(2))),1),c("td",null,[c("i",{class:"bi bi-trash text-danger",onClick:U=>j(N)},null,8,Lb)])]))),128))]),c("tfoot",null,[c("tr",null,[kb,c("th",Rb,B(Math.abs(w(m).purchaseItemsTotal.toFixed(2))),1),Mb]),c("tr",null,[Vb,c("th",jb,[w(m).purchaseTransaction?(y(),T("span",Fb,B(Math.abs(parseFloat(w(m).purchaseTransaction.total).toFixed(2)).toFixed(2)),1)):Ie("",!0)]),Hb]),c("tr",null,[Bb,c("th",Ub,B((Math.abs(parseFloat(w(m).purchaseTransaction.total))-Math.abs(w(m).purchaseItemsTotal)).toFixed(2)),1),Kb])])])]),c("div",Wb,[c("form",{onSubmit:Le(S,["prevent"])},[Yb,qb,c("button",Gb,[i.value?(y(),T("div",Jb,Qb)):(y(),T("span",Zb,[ey,q(" Upload ")]))])],40,zb),c("table",ty,[ny,c("tbody",null,[(y(!0),T(Ee,null,Re(w(m).purchaseTransaction.files,N=>(y(),T("tr",{key:N},[c("td",null,[c("span",{class:"fileDownloadLink",onClick:U=>g(N)},B(N),9,sy)])]))),128))])])])])])}}},ry=en(oy,[["__scopeId","data-v-aeeeaf8a"]]),iy={class:"purchaseInvoices container-fluid"},ay={key:0,class:"spinner-border",role:"status"},ly=c("span",{class:"visually-hidden"},"Loading...",-1),cy=[ly],uy={key:1,class:"table-responsive"},dy=c("label",{for:"searchText"},"Search",-1),fy={class:"table"},hy=c("thead",null,[c("tr",null,[c("th",null,"Date"),c("th",null,"Type"),c("th",null,"Venue"),c("th",null,"Coordinator"),c("th",null,"Ref"),c("th",{class:"text-end"},"Total"),c("th",null,"...")])],-1),py=["title"],my={class:"text-end"},_y={key:0},gy={style:{cursor:"pointer"}},vy={key:0,class:"spinner-grow spinner-grow-sm",role:"status"},by=c("span",{class:"visually-hidden"},"Loading...",-1),yy=[by],Ey={key:1},wy={key:0,class:"bi bi-pencil me-1"},Ty={key:1,class:"bi bi-eye me-1"},Ay={key:0,class:"bi bi-check-square text-success",title:"Imported"},Sy=["onClick"],Cy=["onClick"],$y={__name:"PurchaseTransactions",setup(t){const e=ce(),n=ns(),s=Ro(),o=oe(!1),r=oe(null),i=oe(null),a=Oe(()=>{let h=[];if(e.profile.isManager||e.profile.isAuthor)h=s.purchaseTransactions;else return s.purchaseTransactions.filter(m=>n.isCoordinatorVenue(m.venueID,e.profile.id));return i.value&&(h=h.filter(m=>m.venueName.toLowerCase().includes(i.value.toLowerCase())||m.reference.toLowerCase().includes(i.value.toLowerCase())||m.coordinatorName.toLowerCase().includes(i.value.toLowerCase()))),h}),l=h=>{let m=new Date(h),g="";return g+=m.getDate()<=9?"0"+m.getDate():m.getDate(),g+="/",g+=m.getMonth()<9?"0"+(m.getMonth()+1):m.getMonth()+1,g+="/"+m.getFullYear(),g},d=h=>{let m=s.getTransactionType(h);return m?m.name:h},u=async h=>{r.value=h.id,await s.postPurchaseTransaction(h.id),r.value=null},f=async h=>{r.value=h.id,await s.unpostPurchaseTransaction(h.id),r.value=null};return ze(async()=>{o.value=!0,await s.fetchPurchaseTransactions(),console.log(s.purchaseTransactions),o.value=!1}),(h,m)=>{const g=Ye("RouterLink");return y(),T("div",iy,[c("h3",null,[q("Venue Invoices | "),Q(g,{to:"/purchase-transaction",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("+")]),_:1})]),o.value?(y(),T("div",ay,cy)):(y(),T("div",uy,[c("form",{onSubmit:m[1]||(m[1]=Le(()=>{},["prevent"]))},[dy,te(c("input",{type:"search",id:"searchText","onUpdate:modelValue":m[0]||(m[0]=v=>i.value=v),class:"form-control"},null,512),[[fe,i.value]])],32),c("table",fy,[hy,c("tbody",null,[(y(!0),T(Ee,null,Re(w(a),v=>(y(),T("tr",{class:bn({"text-danger":v.typeID==4})},[c("td",{title:v.taxDate},B(l(v.taxDate)),9,py),c("td",null,B(d(v.typeID)),1),c("td",null,[Q(g,{to:"/venue/"+v.venueID},{default:ue(()=>[q(B(v.venueName),1)]),_:2},1032,["to"])]),c("td",null,B(v.coordinatorName),1),c("td",null,B(v.reference),1),c("td",my,[v.total?(y(),T("span",_y,B(v.total),1)):Ie("",!0)]),c("td",gy,[r.value===v.id?(y(),T("div",vy,yy)):(y(),T("div",Ey,[Q(g,{to:"/purchase-transaction/"+v.id,title:v.canPost},{default:ue(()=>[v.posted?(y(),T("i",Ty)):(y(),T("i",wy))]),_:2},1032,["to","title"]),v.imported?(y(),T("i",Ay)):v.posted?(y(),T("i",{key:1,class:"bi bi-check-square text-warning",title:"Posted",onClick:D=>f(v)},null,8,Sy)):v.canPost===!0?(y(),T("i",{key:2,class:"bi bi-send",onClick:D=>u(v),title:"Ready to Post"},null,8,Cy)):Ie("",!0)]))])],2))),256))])])]))])}}},Oy={class:"venuesTransactionReport container-fluid"},xy=["onSubmit"],Py=c("label",{for:"formFilter.month"},"Month",-1),Ny=c("label",{for:"formFilter.year"},"Year",-1),Dy=["max"],Iy=c("button",{class:"btn btn-sm btn-info mt-2"},"Go",-1),Ly=c("label",{for:"searchtext"},"Search",-1),ky={key:0,class:"spinner-border text-success",role:"status"},Ry=c("span",{class:"visually-hidden"},"Loading...",-1),My=[Ry],Vy={key:1,class:"table-responsive"},jy={class:"table"},Fy=c("thead",null,[c("tr",null,[c("th",null,"Venue"),c("th",null,"Coordinator"),c("th",{class:"text-end"},"Bookings"),c("th",{class:"text-end"},"Invoices"),c("th",{class:"text-end"},"Balance")])],-1),Hy={key:0},By={class:"text-end"},Uy={key:0},Ky={class:"text-end"},Wy={key:0},zy=c("th",{colspan:"2"},"Totals",-1),Yy={class:"text-end"},qy={class:"text-end"},Gy={class:"text-end"},Jy={__name:"VenuesTransactionReport",setup(t){const e=ns(),n=ce(),s=oe({}),o=oe(null),r=oe(!1),i=oe([]),a=Oe(()=>{let f=new Date;return parseInt(f.getFullYear())+1}),l=Oe(()=>{let f=[];if(n.profile.isManager||n.profile.isAuthor)f=i.value;else for(let h in i.value)i.value[h].coordinatorID==n.profile.id&&f.push(i.value[h]);return o.value&&(f=f.filter(h=>h.venueName.toLowerCase().includes(o.value.toLowerCase())||h.coordinator.name.toLowerCase().includes(o.value.toLowerCase()))),f}),d=Oe(()=>{let f={bookings:0,transactions:0};return l.value.forEach(h=>{isNaN(f.bookings)||(f.bookings+=parseFloat(h.bookings)),isNaN(f.transactions)||(f.transactions+=parseFloat(h.transactions))}),f}),u=async()=>{r.value=!0;let f=new Date;if(!s.value.month){let h=f.getMonth();h==0&&(h=12),s.value.month=h}s.value.year||(s.value.year=s.value.year=parseInt(f.getFullYear())),i.value=await e.venuesReport(s.value.year,s.value.month),r.value=!1};return ze(async()=>{await u()}),(f,h)=>{const m=Ye("RouterLink");return y(),T("div",Oy,[c("h3",null,[q("Venue Invoice Report | "),Q(m,{to:"/purchase-transaction",class:"btn btn-sm btn-warning"},{default:ue(()=>[q("+ ")]),_:1})]),c("form",{class:"d-flex align-items-center",onSubmit:Le(u,["prevent"])},[Py,te(c("input",{type:"number",min:"1",max:"12","onUpdate:modelValue":h[0]||(h[0]=g=>s.value.month=g),id:"formFilter.month",class:"form-control"},null,512),[[fe,s.value.month]]),Ny,te(c("input",{type:"number",min:"2016",max:w(a),"onUpdate:modelValue":h[1]||(h[1]=g=>s.value.year=g),id:"formFilter.year",class:"form-control"},null,8,Dy),[[fe,s.value.year]]),Iy],40,xy),c("form",{onSubmit:h[3]||(h[3]=Le(()=>{},["prevent"]))},[Ly,te(c("input",{type:"search",id:"searchtext","onUpdate:modelValue":h[2]||(h[2]=g=>o.value=g),class:"form-control"},null,512),[[fe,o.value]])],32),r.value?(y(),T("div",ky,My)):(y(),T("div",Vy,[c("table",jy,[Fy,c("tbody",null,[(y(!0),T(Ee,null,Re(w(l),g=>(y(),T("tr",null,[c("td",null,[Q(m,{to:"/venue/"+g.venueID},{default:ue(()=>[q(B(g.venueName),1)]),_:2},1032,["to"])]),c("td",null,[g.coordinator?(y(),T("span",Hy,B(g.coordinator.name),1)):Ie("",!0)]),c("td",By,[g.bookings&&!isNaN(g.bookings)?(y(),T("span",Uy,B(g.bookings.toFixed(2)),1)):Ie("",!0)]),c("td",Ky,[g.transactions&&!isNaN(g.transactions)?(y(),T("span",Wy,B(g.transactions.toFixed(2)),1)):Ie("",!0)]),c("td",{class:bn(["text-end",{"text-danger":g.balance<0}])},B(g.balance.toFixed(2)),3)]))),256))]),c("tfoot",null,[c("tr",null,[zy,c("th",Yy,B(w(d).bookings.toFixed(2)),1),c("th",qy,B(w(d).transactions.toFixed(2)),1),c("th",Gy,B((w(d).bookings-w(d).transactions).toFixed(2)),1)])])])]))])}}};const ss=t=>(Ls("data-v-3ca45af7"),t=t(),ks(),t),Xy={class:"purchaseTransaction container-fluid"},Qy=ss(()=>c("h3",null,"Card Payment Transactions",-1)),Zy={key:0,class:"spinner-grow",role:"status"},eE=ss(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),tE=[eE],nE={key:1,class:"prepay-transactions-list"},sE={class:"d-flex align-items-center"},oE=ss(()=>c("label",{for:"formFilter.month"},"Month",-1)),rE=ss(()=>c("label",{for:"formFilter.year"},"Year",-1)),iE=["max"],aE={class:"table-responsive"},lE={class:"table"},cE=ss(()=>c("thead",null,[c("tr",null,[c("th",null,"Date"),c("th",null,"Venue"),c("th",null,"Sage Code"),c("th",null,"Inv. No."),c("th",null,"Period"),c("th",{class:"text-end"},"Amount")])],-1)),uE={class:"text-end"},dE=ss(()=>c("th",{colspan:"5"},"Total",-1)),fE={class:"text-end"},hE={__name:"VenuePrePayTransactions",setup(t){const e=new Date,n=oe(!1),s=oe({month:parseInt(e.getMonth())+1,year:parseInt(e.getFullYear())}),o=Ro(),r=Oe(()=>parseInt(e.getFullYear())+1),i=Oe(()=>{if(!s.value.month||!s.value.year||s.value.month<1||s.value.month>12||s.value.year<2016||s.value.year>r.value)return o.prepays;let d=[];for(let u in o.prepays)o.prepays[u].month==parseInt(s.value.month)&&o.prepays[u].year==parseInt(s.value.year)&&d.push(o.prepays[u]);return d}),a=Oe(()=>{let d=0;return i.value.forEach(u=>d+=parseFloat(u.total)),d}),l=d=>{let u=new Date(d),f="";return f+=u.getDate()<=9?"0"+u.getDate():u.getDate(),f+="/",f+=u.getMonth()<9?"0"+(u.getMonth()+1):u.getMonth()+1,f+="/"+u.getFullYear(),f};return ze(async()=>{n.value=!0,await o.fetchPrePays(),n.value=!1}),(d,u)=>{const f=Ye("RouterLink");return y(),T("div",Xy,[Qy,n.value?(y(),T("div",Zy,tE)):(y(),T("div",nE,[c("form",sE,[oE,te(c("input",{type:"number",min:"1",max:"12","onUpdate:modelValue":u[0]||(u[0]=h=>s.value.month=h),id:"formFilter.month",class:"form-control"},null,512),[[fe,s.value.month]]),rE,te(c("input",{type:"number",min:"2016",max:w(r),"onUpdate:modelValue":u[1]||(u[1]=h=>s.value.year=h),id:"formFilter.year",class:"form-control"},null,8,iE),[[fe,s.value.year]])]),c("div",aE,[c("table",lE,[cE,c("tbody",null,[(y(!0),T(Ee,null,Re(w(i),h=>(y(),T("tr",{key:h.id},[c("td",null,B(l(h.taxDate)),1),c("td",null,[Q(f,{to:"/venue/"+h.venueID},{default:ue(()=>[q(B(h.venueName),1)]),_:2},1032,["to"])]),c("td",null,B(h.sageAccount),1),c("td",null,B(h.reference),1),c("td",null,B(h.month)+"/"+B(h.year),1),c("td",uE,B(parseFloat(h.total).toFixed(2)),1)]))),128))]),c("tfoot",null,[dE,c("th",fE,B(w(a)),1)])])])]))])}}},pE=en(hE,[["__scopeId","data-v-3ca45af7"]]);const ci=t=>(Ls("data-v-58a99d4b"),t=t(),ks(),t),mE={class:"purchaseTransaction container-fluid"},_E=ci(()=>c("h3",null,"Purchase Transaction Files",-1)),gE={key:0,class:"spinner-border",role:"status"},vE=ci(()=>c("span",{class:"visually-hidden"},"Loading...",-1)),bE=[vE],yE={key:1,class:"table-responsive"},EE={class:"table"},wE=ci(()=>c("thead",null,[c("tr",null,[c("th",null,"Name"),c("th",{class:"text-center"},"...")])],-1)),TE=["onClick"],AE={__name:"PurchaseTransactionFiles",setup(t){const e=oe(!1),n=Ro(),s=et(),o=async()=>{e.value=!0,await n.fetchPurchaseTransactionFiles(),e.value=!1},r=async i=>{e.value=!0,await n.resendPurhaseTransactionImportFile(i.filename)===!0&&s.add("success","Success","Import file resent"),e.value=!1};return ze(async()=>{o()}),(i,a)=>(y(),T("div",mE,[_E,e.value?(y(),T("div",gE,bE)):(y(),T("div",yE,[c("table",EE,[wE,c("tbody",null,[(y(!0),T(Ee,null,Re(w(n).purchaseTransactionImportFiles,l=>(y(),T("tr",null,[c("td",null,B(l.timestamp),1),c("td",null,[c("i",{onClick:Le(d=>r(l),["prevent"]),class:"bi bi-send",style:{cursor:"pointer"}},null,8,TE)])]))),256))])])]))]))}},SE=en(AE,[["__scopeId","data-v-58a99d4b"]]),CE=[{path:"/",name:"home",component:vm},{path:"/login",name:"login",component:Im},{path:"/remind",name:"remind",component:Km},{path:"/activate/:email?/:code?",name:"activate",component:n_},{path:"/dashboard",name:"dashboard",component:o_},{path:"/password",name:"password",component:__},{path:"/venues",name:"VenuesView",component:C_},{path:"/venue/:id?",name:"VenueView",component:Eg},{path:"/teams",name:"TeamsView",component:jg},{path:"/team/:id?",name:"TeamView",component:nv},{path:"/team-management/",name:"TeamManagementView",component:Iv},{path:"/purchase-transaction/:id?",name:"PurchaseTransactionView",component:ry},{path:"/purchase-transactions",name:"PurchaseTransactionsView",component:$y},{path:"/venues-transaction-report",name:"VenuesTransactionReportView",component:Jy},{path:"/venues-prepay-transactions",name:"VenuePrePayTransactionsView",component:pE},{path:"/purchase-transaction-files",name:"PurchaseTransactionFiles",component:SE}],Dc=wp({history:jh(),routes:CE});Dc.beforeEach(t=>{const e=document.querySelector(".navbar-toggler"),n=document.querySelector(".navbar-collapse");e&&e.classList.add("collapsed"),n&&(e.setAttribute("aria-expanded",!1),n.classList.remove("show"));const s=ce(),o=["/","/activate","/login","/remind"];return s.jwt&&o.includes(t.path)?{path:"/dashboard"}:o.includes(t.path)||s.jwt?!0:(et().add("warning","Security","You must be logged in"),{path:"/login"})});var Ue="top",Qe="bottom",Ze="right",Ke="left",Mo="auto",os=[Ue,Qe,Ze,Ke],gn="start",Kn="end",Ic="clippingParents",ui="viewport",Pn="popper",Lc="reference",Lr=os.reduce(function(t,e){return t.concat([e+"-"+gn,e+"-"+Kn])},[]),di=[].concat(os,[Mo]).reduce(function(t,e){return t.concat([e,e+"-"+gn,e+"-"+Kn])},[]),kc="beforeRead",Rc="read",Mc="afterRead",Vc="beforeMain",jc="main",Fc="afterMain",Hc="beforeWrite",Bc="write",Uc="afterWrite",Kc=[kc,Rc,Mc,Vc,jc,Fc,Hc,Bc,Uc];function Ct(t){return t?(t.nodeName||"").toLowerCase():null}function at(t){if(t==null)return window;if(t.toString()!=="[object Window]"){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function vn(t){var e=at(t).Element;return t instanceof e||t instanceof Element}function rt(t){var e=at(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function fi(t){if(typeof ShadowRoot>"u")return!1;var e=at(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}function $E(t){var e=t.state;Object.keys(e.elements).forEach(function(n){var s=e.styles[n]||{},o=e.attributes[n]||{},r=e.elements[n];!rt(r)||!Ct(r)||(Object.assign(r.style,s),Object.keys(o).forEach(function(i){var a=o[i];a===!1?r.removeAttribute(i):r.setAttribute(i,a===!0?"":a)}))})}function OE(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow),function(){Object.keys(e.elements).forEach(function(s){var o=e.elements[s],r=e.attributes[s]||{},i=Object.keys(e.styles.hasOwnProperty(s)?e.styles[s]:n[s]),a=i.reduce(function(l,d){return l[d]="",l},{});!rt(o)||!Ct(o)||(Object.assign(o.style,a),Object.keys(r).forEach(function(l){o.removeAttribute(l)}))})}}const hi={name:"applyStyles",enabled:!0,phase:"write",fn:$E,effect:OE,requires:["computeStyles"]};function Tt(t){return t.split("-")[0]}var _n=Math.max,po=Math.min,Wn=Math.round;function kr(){var t=navigator.userAgentData;return t!=null&&t.brands?t.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function Wc(){return!/^((?!chrome|android).)*safari/i.test(kr())}function zn(t,e,n){e===void 0&&(e=!1),n===void 0&&(n=!1);var s=t.getBoundingClientRect(),o=1,r=1;e&&rt(t)&&(o=t.offsetWidth>0&&Wn(s.width)/t.offsetWidth||1,r=t.offsetHeight>0&&Wn(s.height)/t.offsetHeight||1);var i=vn(t)?at(t):window,a=i.visualViewport,l=!Wc()&&n,d=(s.left+(l&&a?a.offsetLeft:0))/o,u=(s.top+(l&&a?a.offsetTop:0))/r,f=s.width/o,h=s.height/r;return{width:f,height:h,top:u,right:d+f,bottom:u+h,left:d,x:d,y:u}}function pi(t){var e=zn(t),n=t.offsetWidth,s=t.offsetHeight;return Math.abs(e.width-n)<=1&&(n=e.width),Math.abs(e.height-s)<=1&&(s=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:s}}function zc(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&fi(n)){var s=e;do{if(s&&t.isSameNode(s))return!0;s=s.parentNode||s.host}while(s)}return!1}function kt(t){return at(t).getComputedStyle(t)}function xE(t){return["table","td","th"].indexOf(Ct(t))>=0}function tn(t){return((vn(t)?t.ownerDocument:t.document)||window.document).documentElement}function Vo(t){return Ct(t)==="html"?t:t.assignedSlot||t.parentNode||(fi(t)?t.host:null)||tn(t)}function ka(t){return!rt(t)||kt(t).position==="fixed"?null:t.offsetParent}function PE(t){var e=/firefox/i.test(kr()),n=/Trident/i.test(kr());if(n&&rt(t)){var s=kt(t);if(s.position==="fixed")return null}var o=Vo(t);for(fi(o)&&(o=o.host);rt(o)&&["html","body"].indexOf(Ct(o))<0;){var r=kt(o);if(r.transform!=="none"||r.perspective!=="none"||r.contain==="paint"||["transform","perspective"].indexOf(r.willChange)!==-1||e&&r.willChange==="filter"||e&&r.filter&&r.filter!=="none")return o;o=o.parentNode}return null}function Rs(t){for(var e=at(t),n=ka(t);n&&xE(n)&&kt(n).position==="static";)n=ka(n);return n&&(Ct(n)==="html"||Ct(n)==="body"&&kt(n).position==="static")?e:n||PE(t)||e}function mi(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function vs(t,e,n){return _n(t,po(e,n))}function NE(t,e,n){var s=vs(t,e,n);return s>n?n:s}function Yc(){return{top:0,right:0,bottom:0,left:0}}function qc(t){return Object.assign({},Yc(),t)}function Gc(t,e){return e.reduce(function(n,s){return n[s]=t,n},{})}var DE=function(e,n){return e=typeof e=="function"?e(Object.assign({},n.rects,{placement:n.placement})):e,qc(typeof e!="number"?e:Gc(e,os))};function IE(t){var e,n=t.state,s=t.name,o=t.options,r=n.elements.arrow,i=n.modifiersData.popperOffsets,a=Tt(n.placement),l=mi(a),d=[Ke,Ze].indexOf(a)>=0,u=d?"height":"width";if(!(!r||!i)){var f=DE(o.padding,n),h=pi(r),m=l==="y"?Ue:Ke,g=l==="y"?Qe:Ze,v=n.rects.reference[u]+n.rects.reference[l]-i[l]-n.rects.popper[u],D=i[l]-n.rects.reference[l],S=Rs(r),j=S?l==="y"?S.clientHeight||0:S.clientWidth||0:0,$=v/2-D/2,C=f[m],M=j-h[u]-f[g],N=j/2-h[u]/2+$,U=vs(C,N,M),L=l;n.modifiersData[s]=(e={},e[L]=U,e.centerOffset=U-N,e)}}function LE(t){var e=t.state,n=t.options,s=n.element,o=s===void 0?"[data-popper-arrow]":s;o!=null&&(typeof o=="string"&&(o=e.elements.popper.querySelector(o),!o)||!zc(e.elements.popper,o)||(e.elements.arrow=o))}const Jc={name:"arrow",enabled:!0,phase:"main",fn:IE,effect:LE,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Yn(t){return t.split("-")[1]}var kE={top:"auto",right:"auto",bottom:"auto",left:"auto"};function RE(t){var e=t.x,n=t.y,s=window,o=s.devicePixelRatio||1;return{x:Wn(e*o)/o||0,y:Wn(n*o)/o||0}}function Ra(t){var e,n=t.popper,s=t.popperRect,o=t.placement,r=t.variation,i=t.offsets,a=t.position,l=t.gpuAcceleration,d=t.adaptive,u=t.roundOffsets,f=t.isFixed,h=i.x,m=h===void 0?0:h,g=i.y,v=g===void 0?0:g,D=typeof u=="function"?u({x:m,y:v}):{x:m,y:v};m=D.x,v=D.y;var S=i.hasOwnProperty("x"),j=i.hasOwnProperty("y"),$=Ke,C=Ue,M=window;if(d){var N=Rs(n),U="clientHeight",L="clientWidth";if(N===at(n)&&(N=tn(n),kt(N).position!=="static"&&a==="absolute"&&(U="scrollHeight",L="scrollWidth")),N=N,o===Ue||(o===Ke||o===Ze)&&r===Kn){C=Qe;var X=f&&N===M&&M.visualViewport?M.visualViewport.height:N[U];v-=X-s.height,v*=l?1:-1}if(o===Ke||(o===Ue||o===Qe)&&r===Kn){$=Ze;var G=f&&N===M&&M.visualViewport?M.visualViewport.width:N[L];m-=G-s.width,m*=l?1:-1}}var re=Object.assign({position:a},d&&kE),ge=u===!0?RE({x:m,y:v}):{x:m,y:v};if(m=ge.x,v=ge.y,l){var ve;return Object.assign({},re,(ve={},ve[C]=j?"0":"",ve[$]=S?"0":"",ve.transform=(M.devicePixelRatio||1)<=1?"translate("+m+"px, "+v+"px)":"translate3d("+m+"px, "+v+"px, 0)",ve))}return Object.assign({},re,(e={},e[C]=j?v+"px":"",e[$]=S?m+"px":"",e.transform="",e))}function ME(t){var e=t.state,n=t.options,s=n.gpuAcceleration,o=s===void 0?!0:s,r=n.adaptive,i=r===void 0?!0:r,a=n.roundOffsets,l=a===void 0?!0:a,d={placement:Tt(e.placement),variation:Yn(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:o,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Ra(Object.assign({},d,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:i,roundOffsets:l})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Ra(Object.assign({},d,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}const _i={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:ME,data:{}};var Ys={passive:!0};function VE(t){var e=t.state,n=t.instance,s=t.options,o=s.scroll,r=o===void 0?!0:o,i=s.resize,a=i===void 0?!0:i,l=at(e.elements.popper),d=[].concat(e.scrollParents.reference,e.scrollParents.popper);return r&&d.forEach(function(u){u.addEventListener("scroll",n.update,Ys)}),a&&l.addEventListener("resize",n.update,Ys),function(){r&&d.forEach(function(u){u.removeEventListener("scroll",n.update,Ys)}),a&&l.removeEventListener("resize",n.update,Ys)}}const gi={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:VE,data:{}};var jE={left:"right",right:"left",bottom:"top",top:"bottom"};function ro(t){return t.replace(/left|right|bottom|top/g,function(e){return jE[e]})}var FE={start:"end",end:"start"};function Ma(t){return t.replace(/start|end/g,function(e){return FE[e]})}function vi(t){var e=at(t),n=e.pageXOffset,s=e.pageYOffset;return{scrollLeft:n,scrollTop:s}}function bi(t){return zn(tn(t)).left+vi(t).scrollLeft}function HE(t,e){var n=at(t),s=tn(t),o=n.visualViewport,r=s.clientWidth,i=s.clientHeight,a=0,l=0;if(o){r=o.width,i=o.height;var d=Wc();(d||!d&&e==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:r,height:i,x:a+bi(t),y:l}}function BE(t){var e,n=tn(t),s=vi(t),o=(e=t.ownerDocument)==null?void 0:e.body,r=_n(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=_n(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),a=-s.scrollLeft+bi(t),l=-s.scrollTop;return kt(o||n).direction==="rtl"&&(a+=_n(n.clientWidth,o?o.clientWidth:0)-r),{width:r,height:i,x:a,y:l}}function yi(t){var e=kt(t),n=e.overflow,s=e.overflowX,o=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+s)}function Xc(t){return["html","body","#document"].indexOf(Ct(t))>=0?t.ownerDocument.body:rt(t)&&yi(t)?t:Xc(Vo(t))}function bs(t,e){var n;e===void 0&&(e=[]);var s=Xc(t),o=s===((n=t.ownerDocument)==null?void 0:n.body),r=at(s),i=o?[r].concat(r.visualViewport||[],yi(s)?s:[]):s,a=e.concat(i);return o?a:a.concat(bs(Vo(i)))}function Rr(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function UE(t,e){var n=zn(t,!1,e==="fixed");return n.top=n.top+t.clientTop,n.left=n.left+t.clientLeft,n.bottom=n.top+t.clientHeight,n.right=n.left+t.clientWidth,n.width=t.clientWidth,n.height=t.clientHeight,n.x=n.left,n.y=n.top,n}function Va(t,e,n){return e===ui?Rr(HE(t,n)):vn(e)?UE(e,n):Rr(BE(tn(t)))}function KE(t){var e=bs(Vo(t)),n=["absolute","fixed"].indexOf(kt(t).position)>=0,s=n&&rt(t)?Rs(t):t;return vn(s)?e.filter(function(o){return vn(o)&&zc(o,s)&&Ct(o)!=="body"}):[]}function WE(t,e,n,s){var o=e==="clippingParents"?KE(t):[].concat(e),r=[].concat(o,[n]),i=r[0],a=r.reduce(function(l,d){var u=Va(t,d,s);return l.top=_n(u.top,l.top),l.right=po(u.right,l.right),l.bottom=po(u.bottom,l.bottom),l.left=_n(u.left,l.left),l},Va(t,i,s));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function Qc(t){var e=t.reference,n=t.element,s=t.placement,o=s?Tt(s):null,r=s?Yn(s):null,i=e.x+e.width/2-n.width/2,a=e.y+e.height/2-n.height/2,l;switch(o){case Ue:l={x:i,y:e.y-n.height};break;case Qe:l={x:i,y:e.y+e.height};break;case Ze:l={x:e.x+e.width,y:a};break;case Ke:l={x:e.x-n.width,y:a};break;default:l={x:e.x,y:e.y}}var d=o?mi(o):null;if(d!=null){var u=d==="y"?"height":"width";switch(r){case gn:l[d]=l[d]-(e[u]/2-n[u]/2);break;case Kn:l[d]=l[d]+(e[u]/2-n[u]/2);break}}return l}function qn(t,e){e===void 0&&(e={});var n=e,s=n.placement,o=s===void 0?t.placement:s,r=n.strategy,i=r===void 0?t.strategy:r,a=n.boundary,l=a===void 0?Ic:a,d=n.rootBoundary,u=d===void 0?ui:d,f=n.elementContext,h=f===void 0?Pn:f,m=n.altBoundary,g=m===void 0?!1:m,v=n.padding,D=v===void 0?0:v,S=qc(typeof D!="number"?D:Gc(D,os)),j=h===Pn?Lc:Pn,$=t.rects.popper,C=t.elements[g?j:h],M=WE(vn(C)?C:C.contextElement||tn(t.elements.popper),l,u,i),N=zn(t.elements.reference),U=Qc({reference:N,element:$,strategy:"absolute",placement:o}),L=Rr(Object.assign({},$,U)),X=h===Pn?L:N,G={top:M.top-X.top+S.top,bottom:X.bottom-M.bottom+S.bottom,left:M.left-X.left+S.left,right:X.right-M.right+S.right},re=t.modifiersData.offset;if(h===Pn&&re){var ge=re[o];Object.keys(G).forEach(function(ve){var $e=[Ze,Qe].indexOf(ve)>=0?1:-1,we=[Ue,Qe].indexOf(ve)>=0?"y":"x";G[ve]+=ge[we]*$e})}return G}function zE(t,e){e===void 0&&(e={});var n=e,s=n.placement,o=n.boundary,r=n.rootBoundary,i=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,d=l===void 0?di:l,u=Yn(s),f=u?a?Lr:Lr.filter(function(g){return Yn(g)===u}):os,h=f.filter(function(g){return d.indexOf(g)>=0});h.length===0&&(h=f);var m=h.reduce(function(g,v){return g[v]=qn(t,{placement:v,boundary:o,rootBoundary:r,padding:i})[Tt(v)],g},{});return Object.keys(m).sort(function(g,v){return m[g]-m[v]})}function YE(t){if(Tt(t)===Mo)return[];var e=ro(t);return[Ma(t),e,Ma(e)]}function qE(t){var e=t.state,n=t.options,s=t.name;if(!e.modifiersData[s]._skip){for(var o=n.mainAxis,r=o===void 0?!0:o,i=n.altAxis,a=i===void 0?!0:i,l=n.fallbackPlacements,d=n.padding,u=n.boundary,f=n.rootBoundary,h=n.altBoundary,m=n.flipVariations,g=m===void 0?!0:m,v=n.allowedAutoPlacements,D=e.options.placement,S=Tt(D),j=S===D,$=l||(j||!g?[ro(D)]:YE(D)),C=[D].concat($).reduce(function(ut,A){return ut.concat(Tt(A)===Mo?zE(e,{placement:A,boundary:u,rootBoundary:f,padding:d,flipVariations:g,allowedAutoPlacements:v}):A)},[]),M=e.rects.reference,N=e.rects.popper,U=new Map,L=!0,X=C[0],G=0;G<C.length;G++){var re=C[G],ge=Tt(re),ve=Yn(re)===gn,$e=[Ue,Qe].indexOf(ge)>=0,we=$e?"width":"height",de=qn(e,{placement:re,boundary:u,rootBoundary:f,altBoundary:h,padding:d}),ae=$e?ve?Ze:Ke:ve?Qe:Ue;M[we]>N[we]&&(ae=ro(ae));var he=ro(ae),De=[];if(r&&De.push(de[ge]<=0),a&&De.push(de[ae]<=0,de[he]<=0),De.every(function(ut){return ut})){X=re,L=!1;break}U.set(re,De)}if(L)for(var tt=g?3:1,He=function(A){var F=C.find(function(V){var W=U.get(V);if(W)return W.slice(0,A).every(function(le){return le})});if(F)return X=F,"break"},Pe=tt;Pe>0;Pe--){var ct=He(Pe);if(ct==="break")break}e.placement!==X&&(e.modifiersData[s]._skip=!0,e.placement=X,e.reset=!0)}}const Zc={name:"flip",enabled:!0,phase:"main",fn:qE,requiresIfExists:["offset"],data:{_skip:!1}};function ja(t,e,n){return n===void 0&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function Fa(t){return[Ue,Ze,Qe,Ke].some(function(e){return t[e]>=0})}function GE(t){var e=t.state,n=t.name,s=e.rects.reference,o=e.rects.popper,r=e.modifiersData.preventOverflow,i=qn(e,{elementContext:"reference"}),a=qn(e,{altBoundary:!0}),l=ja(i,s),d=ja(a,o,r),u=Fa(l),f=Fa(d);e.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:d,isReferenceHidden:u,hasPopperEscaped:f},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":f})}const eu={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:GE};function JE(t,e,n){var s=Tt(t),o=[Ke,Ue].indexOf(s)>=0?-1:1,r=typeof n=="function"?n(Object.assign({},e,{placement:t})):n,i=r[0],a=r[1];return i=i||0,a=(a||0)*o,[Ke,Ze].indexOf(s)>=0?{x:a,y:i}:{x:i,y:a}}function XE(t){var e=t.state,n=t.options,s=t.name,o=n.offset,r=o===void 0?[0,0]:o,i=di.reduce(function(u,f){return u[f]=JE(f,e.rects,r),u},{}),a=i[e.placement],l=a.x,d=a.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=d),e.modifiersData[s]=i}const tu={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:XE};function QE(t){var e=t.state,n=t.name;e.modifiersData[n]=Qc({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}const Ei={name:"popperOffsets",enabled:!0,phase:"read",fn:QE,data:{}};function ZE(t){return t==="x"?"y":"x"}function ew(t){var e=t.state,n=t.options,s=t.name,o=n.mainAxis,r=o===void 0?!0:o,i=n.altAxis,a=i===void 0?!1:i,l=n.boundary,d=n.rootBoundary,u=n.altBoundary,f=n.padding,h=n.tether,m=h===void 0?!0:h,g=n.tetherOffset,v=g===void 0?0:g,D=qn(e,{boundary:l,rootBoundary:d,padding:f,altBoundary:u}),S=Tt(e.placement),j=Yn(e.placement),$=!j,C=mi(S),M=ZE(C),N=e.modifiersData.popperOffsets,U=e.rects.reference,L=e.rects.popper,X=typeof v=="function"?v(Object.assign({},e.rects,{placement:e.placement})):v,G=typeof X=="number"?{mainAxis:X,altAxis:X}:Object.assign({mainAxis:0,altAxis:0},X),re=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,ge={x:0,y:0};if(!!N){if(r){var ve,$e=C==="y"?Ue:Ke,we=C==="y"?Qe:Ze,de=C==="y"?"height":"width",ae=N[C],he=ae+D[$e],De=ae-D[we],tt=m?-L[de]/2:0,He=j===gn?U[de]:L[de],Pe=j===gn?-L[de]:-U[de],ct=e.elements.arrow,ut=m&&ct?pi(ct):{width:0,height:0},A=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:Yc(),F=A[$e],V=A[we],W=vs(0,U[de],ut[de]),le=$?U[de]/2-tt-W-F-G.mainAxis:He-W-F-G.mainAxis,Se=$?-U[de]/2+tt+W+V+G.mainAxis:Pe+W+V+G.mainAxis,ee=e.elements.arrow&&Rs(e.elements.arrow),p=ee?C==="y"?ee.clientTop||0:ee.clientLeft||0:0,_=(ve=re==null?void 0:re[C])!=null?ve:0,b=ae+le-_-p,E=ae+Se-_,O=vs(m?po(he,b):he,ae,m?_n(De,E):De);N[C]=O,ge[C]=O-ae}if(a){var k,H=C==="x"?Ue:Ke,I=C==="x"?Qe:Ze,R=N[M],x=M==="y"?"height":"width",Y=R+D[H],K=R-D[I],z=[Ue,Ke].indexOf(S)!==-1,Z=(k=re==null?void 0:re[M])!=null?k:0,ie=z?Y:R-U[x]-L[x]-Z+G.altAxis,be=z?R+U[x]+L[x]-Z-G.altAxis:K,_e=m&&z?NE(ie,R,be):vs(m?ie:Y,R,m?be:K);N[M]=_e,ge[M]=_e-R}e.modifiersData[s]=ge}}const nu={name:"preventOverflow",enabled:!0,phase:"main",fn:ew,requiresIfExists:["offset"]};function tw(t){return{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}}function nw(t){return t===at(t)||!rt(t)?vi(t):tw(t)}function sw(t){var e=t.getBoundingClientRect(),n=Wn(e.width)/t.offsetWidth||1,s=Wn(e.height)/t.offsetHeight||1;return n!==1||s!==1}function ow(t,e,n){n===void 0&&(n=!1);var s=rt(e),o=rt(e)&&sw(e),r=tn(e),i=zn(t,o,n),a={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(s||!s&&!n)&&((Ct(e)!=="body"||yi(r))&&(a=nw(e)),rt(e)?(l=zn(e,!0),l.x+=e.clientLeft,l.y+=e.clientTop):r&&(l.x=bi(r))),{x:i.left+a.scrollLeft-l.x,y:i.top+a.scrollTop-l.y,width:i.width,height:i.height}}function rw(t){var e=new Map,n=new Set,s=[];t.forEach(function(r){e.set(r.name,r)});function o(r){n.add(r.name);var i=[].concat(r.requires||[],r.requiresIfExists||[]);i.forEach(function(a){if(!n.has(a)){var l=e.get(a);l&&o(l)}}),s.push(r)}return t.forEach(function(r){n.has(r.name)||o(r)}),s}function iw(t){var e=rw(t);return Kc.reduce(function(n,s){return n.concat(e.filter(function(o){return o.phase===s}))},[])}function aw(t){var e;return function(){return e||(e=new Promise(function(n){Promise.resolve().then(function(){e=void 0,n(t())})})),e}}function lw(t){var e=t.reduce(function(n,s){var o=n[s.name];return n[s.name]=o?Object.assign({},o,s,{options:Object.assign({},o.options,s.options),data:Object.assign({},o.data,s.data)}):s,n},{});return Object.keys(e).map(function(n){return e[n]})}var Ha={placement:"bottom",modifiers:[],strategy:"absolute"};function Ba(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some(function(s){return!(s&&typeof s.getBoundingClientRect=="function")})}function jo(t){t===void 0&&(t={});var e=t,n=e.defaultModifiers,s=n===void 0?[]:n,o=e.defaultOptions,r=o===void 0?Ha:o;return function(a,l,d){d===void 0&&(d=r);var u={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ha,r),modifiersData:{},elements:{reference:a,popper:l},attributes:{},styles:{}},f=[],h=!1,m={state:u,setOptions:function(S){var j=typeof S=="function"?S(u.options):S;v(),u.options=Object.assign({},r,u.options,j),u.scrollParents={reference:vn(a)?bs(a):a.contextElement?bs(a.contextElement):[],popper:bs(l)};var $=iw(lw([].concat(s,u.options.modifiers)));return u.orderedModifiers=$.filter(function(C){return C.enabled}),g(),m.update()},forceUpdate:function(){if(!h){var S=u.elements,j=S.reference,$=S.popper;if(!!Ba(j,$)){u.rects={reference:ow(j,Rs($),u.options.strategy==="fixed"),popper:pi($)},u.reset=!1,u.placement=u.options.placement,u.orderedModifiers.forEach(function(G){return u.modifiersData[G.name]=Object.assign({},G.data)});for(var C=0;C<u.orderedModifiers.length;C++){if(u.reset===!0){u.reset=!1,C=-1;continue}var M=u.orderedModifiers[C],N=M.fn,U=M.options,L=U===void 0?{}:U,X=M.name;typeof N=="function"&&(u=N({state:u,options:L,name:X,instance:m})||u)}}}},update:aw(function(){return new Promise(function(D){m.forceUpdate(),D(u)})}),destroy:function(){v(),h=!0}};if(!Ba(a,l))return m;m.setOptions(d).then(function(D){!h&&d.onFirstUpdate&&d.onFirstUpdate(D)});function g(){u.orderedModifiers.forEach(function(D){var S=D.name,j=D.options,$=j===void 0?{}:j,C=D.effect;if(typeof C=="function"){var M=C({state:u,name:S,instance:m,options:$}),N=function(){};f.push(M||N)}})}function v(){f.forEach(function(D){return D()}),f=[]}return m}}var cw=jo(),uw=[gi,Ei,_i,hi],dw=jo({defaultModifiers:uw}),fw=[gi,Ei,_i,hi,tu,Zc,nu,Jc,eu],wi=jo({defaultModifiers:fw});const su=Object.freeze(Object.defineProperty({__proto__:null,popperGenerator:jo,detectOverflow:qn,createPopperBase:cw,createPopper:wi,createPopperLite:dw,top:Ue,bottom:Qe,right:Ze,left:Ke,auto:Mo,basePlacements:os,start:gn,end:Kn,clippingParents:Ic,viewport:ui,popper:Pn,reference:Lc,variationPlacements:Lr,placements:di,beforeRead:kc,read:Rc,afterRead:Mc,beforeMain:Vc,main:jc,afterMain:Fc,beforeWrite:Hc,write:Bc,afterWrite:Uc,modifierPhases:Kc,applyStyles:hi,arrow:Jc,computeStyles:_i,eventListeners:gi,flip:Zc,hide:eu,offset:tu,popperOffsets:Ei,preventOverflow:nu},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.2.3 (https://getbootstrap.com/)
  * Copyright 2011-2022 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const hw=1e6,pw=1e3,Mr="transitionend",mw=t=>t==null?`${t}`:Object.prototype.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase(),_w=t=>{do t+=Math.floor(Math.random()*hw);while(document.getElementById(t));return t},ou=t=>{let e=t.getAttribute("data-bs-target");if(!e||e==="#"){let n=t.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),e=n&&n!=="#"?n.trim():null}return e},ru=t=>{const e=ou(t);return e&&document.querySelector(e)?e:null},Nt=t=>{const e=ou(t);return e?document.querySelector(e):null},gw=t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:n}=window.getComputedStyle(t);const s=Number.parseFloat(e),o=Number.parseFloat(n);return!s&&!o?0:(e=e.split(",")[0],n=n.split(",")[0],(Number.parseFloat(e)+Number.parseFloat(n))*pw)},iu=t=>{t.dispatchEvent(new Event(Mr))},Dt=t=>!t||typeof t!="object"?!1:(typeof t.jquery<"u"&&(t=t[0]),typeof t.nodeType<"u"),Xt=t=>Dt(t)?t.jquery?t[0]:t:typeof t=="string"&&t.length>0?document.querySelector(t):null,rs=t=>{if(!Dt(t)||t.getClientRects().length===0)return!1;const e=getComputedStyle(t).getPropertyValue("visibility")==="visible",n=t.closest("details:not([open])");if(!n)return e;if(n!==t){const s=t.closest("summary");if(s&&s.parentNode!==n||s===null)return!1}return e},Qt=t=>!t||t.nodeType!==Node.ELEMENT_NODE||t.classList.contains("disabled")?!0:typeof t.disabled<"u"?t.disabled:t.hasAttribute("disabled")&&t.getAttribute("disabled")!=="false",au=t=>{if(!document.documentElement.attachShadow)return null;if(typeof t.getRootNode=="function"){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?au(t.parentNode):null},mo=()=>{},Ms=t=>{t.offsetHeight},lu=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,tr=[],vw=t=>{document.readyState==="loading"?(tr.length||document.addEventListener("DOMContentLoaded",()=>{for(const e of tr)e()}),tr.push(t)):t()},it=()=>document.documentElement.dir==="rtl",lt=t=>{vw(()=>{const e=lu();if(e){const n=t.NAME,s=e.fn[n];e.fn[n]=t.jQueryInterface,e.fn[n].Constructor=t,e.fn[n].noConflict=()=>(e.fn[n]=s,t.jQueryInterface)}})},xt=t=>{typeof t=="function"&&t()},cu=(t,e,n=!0)=>{if(!n){xt(t);return}const s=5,o=gw(e)+s;let r=!1;const i=({target:a})=>{a===e&&(r=!0,e.removeEventListener(Mr,i),xt(t))};e.addEventListener(Mr,i),setTimeout(()=>{r||iu(e)},o)},Ti=(t,e,n,s)=>{const o=t.length;let r=t.indexOf(e);return r===-1?!n&&s?t[o-1]:t[0]:(r+=n?1:-1,s&&(r=(r+o)%o),t[Math.max(0,Math.min(r,o-1))])},bw=/[^.]*(?=\..*)\.|.*/,yw=/\..*/,Ew=/::\d+$/,nr={};let Ua=1;const uu={mouseenter:"mouseover",mouseleave:"mouseout"},ww=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function du(t,e){return e&&`${e}::${Ua++}`||t.uidEvent||Ua++}function fu(t){const e=du(t);return t.uidEvent=e,nr[e]=nr[e]||{},nr[e]}function Tw(t,e){return function n(s){return Ai(s,{delegateTarget:t}),n.oneOff&&P.off(t,s.type,e),e.apply(t,[s])}}function Aw(t,e,n){return function s(o){const r=t.querySelectorAll(e);for(let{target:i}=o;i&&i!==this;i=i.parentNode)for(const a of r)if(a===i)return Ai(o,{delegateTarget:i}),s.oneOff&&P.off(t,o.type,e,n),n.apply(i,[o])}}function hu(t,e,n=null){return Object.values(t).find(s=>s.callable===e&&s.delegationSelector===n)}function pu(t,e,n){const s=typeof e=="string",o=s?n:e||n;let r=mu(t);return ww.has(r)||(r=t),[s,o,r]}function Ka(t,e,n,s,o){if(typeof e!="string"||!t)return;let[r,i,a]=pu(e,n,s);e in uu&&(i=(g=>function(v){if(!v.relatedTarget||v.relatedTarget!==v.delegateTarget&&!v.delegateTarget.contains(v.relatedTarget))return g.call(this,v)})(i));const l=fu(t),d=l[a]||(l[a]={}),u=hu(d,i,r?n:null);if(u){u.oneOff=u.oneOff&&o;return}const f=du(i,e.replace(bw,"")),h=r?Aw(t,n,i):Tw(t,i);h.delegationSelector=r?n:null,h.callable=i,h.oneOff=o,h.uidEvent=f,d[f]=h,t.addEventListener(a,h,r)}function Vr(t,e,n,s,o){const r=hu(e[n],s,o);!r||(t.removeEventListener(n,r,Boolean(o)),delete e[n][r.uidEvent])}function Sw(t,e,n,s){const o=e[n]||{};for(const r of Object.keys(o))if(r.includes(s)){const i=o[r];Vr(t,e,n,i.callable,i.delegationSelector)}}function mu(t){return t=t.replace(yw,""),uu[t]||t}const P={on(t,e,n,s){Ka(t,e,n,s,!1)},one(t,e,n,s){Ka(t,e,n,s,!0)},off(t,e,n,s){if(typeof e!="string"||!t)return;const[o,r,i]=pu(e,n,s),a=i!==e,l=fu(t),d=l[i]||{},u=e.startsWith(".");if(typeof r<"u"){if(!Object.keys(d).length)return;Vr(t,l,i,r,o?n:null);return}if(u)for(const f of Object.keys(l))Sw(t,l,f,e.slice(1));for(const f of Object.keys(d)){const h=f.replace(Ew,"");if(!a||e.includes(h)){const m=d[f];Vr(t,l,i,m.callable,m.delegationSelector)}}},trigger(t,e,n){if(typeof e!="string"||!t)return null;const s=lu(),o=mu(e),r=e!==o;let i=null,a=!0,l=!0,d=!1;r&&s&&(i=s.Event(e,n),s(t).trigger(i),a=!i.isPropagationStopped(),l=!i.isImmediatePropagationStopped(),d=i.isDefaultPrevented());let u=new Event(e,{bubbles:a,cancelable:!0});return u=Ai(u,n),d&&u.preventDefault(),l&&t.dispatchEvent(u),u.defaultPrevented&&i&&i.preventDefault(),u}};function Ai(t,e){for(const[n,s]of Object.entries(e||{}))try{t[n]=s}catch{Object.defineProperty(t,n,{configurable:!0,get(){return s}})}return t}const Ht=new Map,sr={set(t,e,n){Ht.has(t)||Ht.set(t,new Map);const s=Ht.get(t);if(!s.has(e)&&s.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`);return}s.set(e,n)},get(t,e){return Ht.has(t)&&Ht.get(t).get(e)||null},remove(t,e){if(!Ht.has(t))return;const n=Ht.get(t);n.delete(e),n.size===0&&Ht.delete(t)}};function Wa(t){if(t==="true")return!0;if(t==="false")return!1;if(t===Number(t).toString())return Number(t);if(t===""||t==="null")return null;if(typeof t!="string")return t;try{return JSON.parse(decodeURIComponent(t))}catch{return t}}function or(t){return t.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}const It={setDataAttribute(t,e,n){t.setAttribute(`data-bs-${or(e)}`,n)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${or(e)}`)},getDataAttributes(t){if(!t)return{};const e={},n=Object.keys(t.dataset).filter(s=>s.startsWith("bs")&&!s.startsWith("bsConfig"));for(const s of n){let o=s.replace(/^bs/,"");o=o.charAt(0).toLowerCase()+o.slice(1,o.length),e[o]=Wa(t.dataset[s])}return e},getDataAttribute(t,e){return Wa(t.getAttribute(`data-bs-${or(e)}`))}};class Vs{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,n){const s=Dt(n)?It.getDataAttribute(n,"config"):{};return{...this.constructor.Default,...typeof s=="object"?s:{},...Dt(n)?It.getDataAttributes(n):{},...typeof e=="object"?e:{}}}_typeCheckConfig(e,n=this.constructor.DefaultType){for(const s of Object.keys(n)){const o=n[s],r=e[s],i=Dt(r)?"element":mw(r);if(!new RegExp(o).test(i))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${i}" but expected type "${o}".`)}}}const Cw="5.2.3";class gt extends Vs{constructor(e,n){super(),e=Xt(e),e&&(this._element=e,this._config=this._getConfig(n),sr.set(this._element,this.constructor.DATA_KEY,this))}dispose(){sr.remove(this._element,this.constructor.DATA_KEY),P.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,n,s=!0){cu(e,n,s)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return sr.get(Xt(e),this.DATA_KEY)}static getOrCreateInstance(e,n={}){return this.getInstance(e)||new this(e,typeof n=="object"?n:null)}static get VERSION(){return Cw}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const Fo=(t,e="hide")=>{const n=`click.dismiss${t.EVENT_KEY}`,s=t.NAME;P.on(document,n,`[data-bs-dismiss="${s}"]`,function(o){if(["A","AREA"].includes(this.tagName)&&o.preventDefault(),Qt(this))return;const r=Nt(this)||this.closest(`.${s}`);t.getOrCreateInstance(r)[e]()})},$w="alert",Ow="bs.alert",_u=`.${Ow}`,xw=`close${_u}`,Pw=`closed${_u}`,Nw="fade",Dw="show";class Ho extends gt{static get NAME(){return $w}close(){if(P.trigger(this._element,xw).defaultPrevented)return;this._element.classList.remove(Dw);const n=this._element.classList.contains(Nw);this._queueCallback(()=>this._destroyElement(),this._element,n)}_destroyElement(){this._element.remove(),P.trigger(this._element,Pw),this.dispose()}static jQueryInterface(e){return this.each(function(){const n=Ho.getOrCreateInstance(this);if(typeof e=="string"){if(n[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);n[e](this)}})}}Fo(Ho,"close");lt(Ho);const Iw="button",Lw="bs.button",kw=`.${Lw}`,Rw=".data-api",Mw="active",za='[data-bs-toggle="button"]',Vw=`click${kw}${Rw}`;class Bo extends gt{static get NAME(){return Iw}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(Mw))}static jQueryInterface(e){return this.each(function(){const n=Bo.getOrCreateInstance(this);e==="toggle"&&n[e]()})}}P.on(document,Vw,za,t=>{t.preventDefault();const e=t.target.closest(za);Bo.getOrCreateInstance(e).toggle()});lt(Bo);const se={find(t,e=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(e,t))},findOne(t,e=document.documentElement){return Element.prototype.querySelector.call(e,t)},children(t,e){return[].concat(...t.children).filter(n=>n.matches(e))},parents(t,e){const n=[];let s=t.parentNode.closest(e);for(;s;)n.push(s),s=s.parentNode.closest(e);return n},prev(t,e){let n=t.previousElementSibling;for(;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next(t,e){let n=t.nextElementSibling;for(;n;){if(n.matches(e))return[n];n=n.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(n=>`${n}:not([tabindex^="-"])`).join(",");return this.find(e,t).filter(n=>!Qt(n)&&rs(n))}},jw="swipe",is=".bs.swipe",Fw=`touchstart${is}`,Hw=`touchmove${is}`,Bw=`touchend${is}`,Uw=`pointerdown${is}`,Kw=`pointerup${is}`,Ww="touch",zw="pen",Yw="pointer-event",qw=40,Gw={endCallback:null,leftCallback:null,rightCallback:null},Jw={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class _o extends Vs{constructor(e,n){super(),this._element=e,!(!e||!_o.isSupported())&&(this._config=this._getConfig(n),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Gw}static get DefaultType(){return Jw}static get NAME(){return jw}dispose(){P.off(this._element,is)}_start(e){if(!this._supportPointerEvents){this._deltaX=e.touches[0].clientX;return}this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX)}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),xt(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=qw)return;const n=e/this._deltaX;this._deltaX=0,n&&xt(n>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(P.on(this._element,Uw,e=>this._start(e)),P.on(this._element,Kw,e=>this._end(e)),this._element.classList.add(Yw)):(P.on(this._element,Fw,e=>this._start(e)),P.on(this._element,Hw,e=>this._move(e)),P.on(this._element,Bw,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&(e.pointerType===zw||e.pointerType===Ww)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const Xw="carousel",Qw="bs.carousel",nn=`.${Qw}`,gu=".data-api",Zw="ArrowLeft",eT="ArrowRight",tT=500,us="next",$n="prev",Nn="left",io="right",nT=`slide${nn}`,rr=`slid${nn}`,sT=`keydown${nn}`,oT=`mouseenter${nn}`,rT=`mouseleave${nn}`,iT=`dragstart${nn}`,aT=`load${nn}${gu}`,lT=`click${nn}${gu}`,vu="carousel",qs="active",cT="slide",uT="carousel-item-end",dT="carousel-item-start",fT="carousel-item-next",hT="carousel-item-prev",bu=".active",yu=".carousel-item",pT=bu+yu,mT=".carousel-item img",_T=".carousel-indicators",gT="[data-bs-slide], [data-bs-slide-to]",vT='[data-bs-ride="carousel"]',bT={[Zw]:io,[eT]:Nn},yT={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},ET={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class js extends gt{constructor(e,n){super(e,n),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=se.findOne(_T,this._element),this._addEventListeners(),this._config.ride===vu&&this.cycle()}static get Default(){return yT}static get DefaultType(){return ET}static get NAME(){return Xw}next(){this._slide(us)}nextWhenVisible(){!document.hidden&&rs(this._element)&&this.next()}prev(){this._slide($n)}pause(){this._isSliding&&iu(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(!!this._config.ride){if(this._isSliding){P.one(this._element,rr,()=>this.cycle());return}this.cycle()}}to(e){const n=this._getItems();if(e>n.length-1||e<0)return;if(this._isSliding){P.one(this._element,rr,()=>this.to(e));return}const s=this._getItemIndex(this._getActive());if(s===e)return;const o=e>s?us:$n;this._slide(o,n[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&P.on(this._element,sT,e=>this._keydown(e)),this._config.pause==="hover"&&(P.on(this._element,oT,()=>this.pause()),P.on(this._element,rT,()=>this._maybeEnableCycle())),this._config.touch&&_o.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const s of se.find(mT,this._element))P.on(s,iT,o=>o.preventDefault());const n={leftCallback:()=>this._slide(this._directionToOrder(Nn)),rightCallback:()=>this._slide(this._directionToOrder(io)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),tT+this._config.interval))}};this._swipeHelper=new _o(this._element,n)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const n=bT[e.key];n&&(e.preventDefault(),this._slide(this._directionToOrder(n)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const n=se.findOne(bu,this._indicatorsElement);n.classList.remove(qs),n.removeAttribute("aria-current");const s=se.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);s&&(s.classList.add(qs),s.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const n=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=n||this._config.defaultInterval}_slide(e,n=null){if(this._isSliding)return;const s=this._getActive(),o=e===us,r=n||Ti(this._getItems(),s,o,this._config.wrap);if(r===s)return;const i=this._getItemIndex(r),a=m=>P.trigger(this._element,m,{relatedTarget:r,direction:this._orderToDirection(e),from:this._getItemIndex(s),to:i});if(a(nT).defaultPrevented||!s||!r)return;const d=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(i),this._activeElement=r;const u=o?dT:uT,f=o?fT:hT;r.classList.add(f),Ms(r),s.classList.add(u),r.classList.add(u);const h=()=>{r.classList.remove(u,f),r.classList.add(qs),s.classList.remove(qs,f,u),this._isSliding=!1,a(rr)};this._queueCallback(h,s,this._isAnimated()),d&&this.cycle()}_isAnimated(){return this._element.classList.contains(cT)}_getActive(){return se.findOne(pT,this._element)}_getItems(){return se.find(yu,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return it()?e===Nn?$n:us:e===Nn?us:$n}_orderToDirection(e){return it()?e===$n?Nn:io:e===$n?io:Nn}static jQueryInterface(e){return this.each(function(){const n=js.getOrCreateInstance(this,e);if(typeof e=="number"){n.to(e);return}if(typeof e=="string"){if(n[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);n[e]()}})}}P.on(document,lT,gT,function(t){const e=Nt(this);if(!e||!e.classList.contains(vu))return;t.preventDefault();const n=js.getOrCreateInstance(e),s=this.getAttribute("data-bs-slide-to");if(s){n.to(s),n._maybeEnableCycle();return}if(It.getDataAttribute(this,"slide")==="next"){n.next(),n._maybeEnableCycle();return}n.prev(),n._maybeEnableCycle()});P.on(window,aT,()=>{const t=se.find(vT);for(const e of t)js.getOrCreateInstance(e)});lt(js);const wT="collapse",TT="bs.collapse",Fs=`.${TT}`,AT=".data-api",ST=`show${Fs}`,CT=`shown${Fs}`,$T=`hide${Fs}`,OT=`hidden${Fs}`,xT=`click${Fs}${AT}`,ir="show",In="collapse",Gs="collapsing",PT="collapsed",NT=`:scope .${In} .${In}`,DT="collapse-horizontal",IT="width",LT="height",kT=".collapse.show, .collapse.collapsing",jr='[data-bs-toggle="collapse"]',RT={parent:null,toggle:!0},MT={parent:"(null|element)",toggle:"boolean"};class Ns extends gt{constructor(e,n){super(e,n),this._isTransitioning=!1,this._triggerArray=[];const s=se.find(jr);for(const o of s){const r=ru(o),i=se.find(r).filter(a=>a===this._element);r!==null&&i.length&&this._triggerArray.push(o)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return RT}static get DefaultType(){return MT}static get NAME(){return wT}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(kT).filter(a=>a!==this._element).map(a=>Ns.getOrCreateInstance(a,{toggle:!1}))),e.length&&e[0]._isTransitioning||P.trigger(this._element,ST).defaultPrevented)return;for(const a of e)a.hide();const s=this._getDimension();this._element.classList.remove(In),this._element.classList.add(Gs),this._element.style[s]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const o=()=>{this._isTransitioning=!1,this._element.classList.remove(Gs),this._element.classList.add(In,ir),this._element.style[s]="",P.trigger(this._element,CT)},i=`scroll${s[0].toUpperCase()+s.slice(1)}`;this._queueCallback(o,this._element,!0),this._element.style[s]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown()||P.trigger(this._element,$T).defaultPrevented)return;const n=this._getDimension();this._element.style[n]=`${this._element.getBoundingClientRect()[n]}px`,Ms(this._element),this._element.classList.add(Gs),this._element.classList.remove(In,ir);for(const o of this._triggerArray){const r=Nt(o);r&&!this._isShown(r)&&this._addAriaAndCollapsedClass([o],!1)}this._isTransitioning=!0;const s=()=>{this._isTransitioning=!1,this._element.classList.remove(Gs),this._element.classList.add(In),P.trigger(this._element,OT)};this._element.style[n]="",this._queueCallback(s,this._element,!0)}_isShown(e=this._element){return e.classList.contains(ir)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=Xt(e.parent),e}_getDimension(){return this._element.classList.contains(DT)?IT:LT}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(jr);for(const n of e){const s=Nt(n);s&&this._addAriaAndCollapsedClass([n],this._isShown(s))}}_getFirstLevelChildren(e){const n=se.find(NT,this._config.parent);return se.find(e,this._config.parent).filter(s=>!n.includes(s))}_addAriaAndCollapsedClass(e,n){if(!!e.length)for(const s of e)s.classList.toggle(PT,!n),s.setAttribute("aria-expanded",n)}static jQueryInterface(e){const n={};return typeof e=="string"&&/show|hide/.test(e)&&(n.toggle=!1),this.each(function(){const s=Ns.getOrCreateInstance(this,n);if(typeof e=="string"){if(typeof s[e]>"u")throw new TypeError(`No method named "${e}"`);s[e]()}})}}P.on(document,xT,jr,function(t){(t.target.tagName==="A"||t.delegateTarget&&t.delegateTarget.tagName==="A")&&t.preventDefault();const e=ru(this),n=se.find(e);for(const s of n)Ns.getOrCreateInstance(s,{toggle:!1}).toggle()});lt(Ns);const Ya="dropdown",VT="bs.dropdown",wn=`.${VT}`,Si=".data-api",jT="Escape",qa="Tab",FT="ArrowUp",Ga="ArrowDown",HT=2,BT=`hide${wn}`,UT=`hidden${wn}`,KT=`show${wn}`,WT=`shown${wn}`,Eu=`click${wn}${Si}`,wu=`keydown${wn}${Si}`,zT=`keyup${wn}${Si}`,Dn="show",YT="dropup",qT="dropend",GT="dropstart",JT="dropup-center",XT="dropdown-center",fn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',QT=`${fn}.${Dn}`,ao=".dropdown-menu",ZT=".navbar",eA=".navbar-nav",tA=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",nA=it()?"top-end":"top-start",sA=it()?"top-start":"top-end",oA=it()?"bottom-end":"bottom-start",rA=it()?"bottom-start":"bottom-end",iA=it()?"left-start":"right-start",aA=it()?"right-start":"left-start",lA="top",cA="bottom",uA={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},dA={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class At extends gt{constructor(e,n){super(e,n),this._popper=null,this._parent=this._element.parentNode,this._menu=se.next(this._element,ao)[0]||se.prev(this._element,ao)[0]||se.findOne(ao,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return uA}static get DefaultType(){return dA}static get NAME(){return Ya}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Qt(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!P.trigger(this._element,KT,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(eA))for(const s of[].concat(...document.body.children))P.on(s,"mouseover",mo);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Dn),this._element.classList.add(Dn),P.trigger(this._element,WT,e)}}hide(){if(Qt(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!P.trigger(this._element,BT,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const s of[].concat(...document.body.children))P.off(s,"mouseover",mo);this._popper&&this._popper.destroy(),this._menu.classList.remove(Dn),this._element.classList.remove(Dn),this._element.setAttribute("aria-expanded","false"),It.removeDataAttribute(this._menu,"popper"),P.trigger(this._element,UT,e)}}_getConfig(e){if(e=super._getConfig(e),typeof e.reference=="object"&&!Dt(e.reference)&&typeof e.reference.getBoundingClientRect!="function")throw new TypeError(`${Ya.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(typeof su>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;this._config.reference==="parent"?e=this._parent:Dt(this._config.reference)?e=Xt(this._config.reference):typeof this._config.reference=="object"&&(e=this._config.reference);const n=this._getPopperConfig();this._popper=wi(e,this._menu,n)}_isShown(){return this._menu.classList.contains(Dn)}_getPlacement(){const e=this._parent;if(e.classList.contains(qT))return iA;if(e.classList.contains(GT))return aA;if(e.classList.contains(JT))return lA;if(e.classList.contains(XT))return cA;const n=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return e.classList.contains(YT)?n?sA:nA:n?rA:oA}_detectNavbar(){return this._element.closest(ZT)!==null}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(n=>Number.parseInt(n,10)):typeof e=="function"?n=>e(n,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(It.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...typeof this._config.popperConfig=="function"?this._config.popperConfig(e):this._config.popperConfig}}_selectMenuItem({key:e,target:n}){const s=se.find(tA,this._menu).filter(o=>rs(o));!s.length||Ti(s,n,e===Ga,!s.includes(n)).focus()}static jQueryInterface(e){return this.each(function(){const n=At.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof n[e]>"u")throw new TypeError(`No method named "${e}"`);n[e]()}})}static clearMenus(e){if(e.button===HT||e.type==="keyup"&&e.key!==qa)return;const n=se.find(QT);for(const s of n){const o=At.getInstance(s);if(!o||o._config.autoClose===!1)continue;const r=e.composedPath(),i=r.includes(o._menu);if(r.includes(o._element)||o._config.autoClose==="inside"&&!i||o._config.autoClose==="outside"&&i||o._menu.contains(e.target)&&(e.type==="keyup"&&e.key===qa||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const a={relatedTarget:o._element};e.type==="click"&&(a.clickEvent=e),o._completeHide(a)}}static dataApiKeydownHandler(e){const n=/input|textarea/i.test(e.target.tagName),s=e.key===jT,o=[FT,Ga].includes(e.key);if(!o&&!s||n&&!s)return;e.preventDefault();const r=this.matches(fn)?this:se.prev(this,fn)[0]||se.next(this,fn)[0]||se.findOne(fn,e.delegateTarget.parentNode),i=At.getOrCreateInstance(r);if(o){e.stopPropagation(),i.show(),i._selectMenuItem(e);return}i._isShown()&&(e.stopPropagation(),i.hide(),r.focus())}}P.on(document,wu,fn,At.dataApiKeydownHandler);P.on(document,wu,ao,At.dataApiKeydownHandler);P.on(document,Eu,At.clearMenus);P.on(document,zT,At.clearMenus);P.on(document,Eu,fn,function(t){t.preventDefault(),At.getOrCreateInstance(this).toggle()});lt(At);const Ja=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Xa=".sticky-top",Js="padding-right",Qa="margin-right";class Fr{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,Js,n=>n+e),this._setElementAttributes(Ja,Js,n=>n+e),this._setElementAttributes(Xa,Qa,n=>n-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,Js),this._resetElementAttributes(Ja,Js),this._resetElementAttributes(Xa,Qa)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,n,s){const o=this.getWidth(),r=i=>{if(i!==this._element&&window.innerWidth>i.clientWidth+o)return;this._saveInitialAttribute(i,n);const a=window.getComputedStyle(i).getPropertyValue(n);i.style.setProperty(n,`${s(Number.parseFloat(a))}px`)};this._applyManipulationCallback(e,r)}_saveInitialAttribute(e,n){const s=e.style.getPropertyValue(n);s&&It.setDataAttribute(e,n,s)}_resetElementAttributes(e,n){const s=o=>{const r=It.getDataAttribute(o,n);if(r===null){o.style.removeProperty(n);return}It.removeDataAttribute(o,n),o.style.setProperty(n,r)};this._applyManipulationCallback(e,s)}_applyManipulationCallback(e,n){if(Dt(e)){n(e);return}for(const s of se.find(e,this._element))n(s)}}const Tu="backdrop",fA="fade",Za="show",el=`mousedown.bs.${Tu}`,hA={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},pA={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Au extends Vs{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return hA}static get DefaultType(){return pA}static get NAME(){return Tu}show(e){if(!this._config.isVisible){xt(e);return}this._append();const n=this._getElement();this._config.isAnimated&&Ms(n),n.classList.add(Za),this._emulateAnimation(()=>{xt(e)})}hide(e){if(!this._config.isVisible){xt(e);return}this._getElement().classList.remove(Za),this._emulateAnimation(()=>{this.dispose(),xt(e)})}dispose(){!this._isAppended||(P.off(this._element,el),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add(fA),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=Xt(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),P.on(e,el,()=>{xt(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){cu(e,this._getElement(),this._config.isAnimated)}}const mA="focustrap",_A="bs.focustrap",go=`.${_A}`,gA=`focusin${go}`,vA=`keydown.tab${go}`,bA="Tab",yA="forward",tl="backward",EA={autofocus:!0,trapElement:null},wA={autofocus:"boolean",trapElement:"element"};class Su extends Vs{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return EA}static get DefaultType(){return wA}static get NAME(){return mA}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),P.off(document,go),P.on(document,gA,e=>this._handleFocusin(e)),P.on(document,vA,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){!this._isActive||(this._isActive=!1,P.off(document,go))}_handleFocusin(e){const{trapElement:n}=this._config;if(e.target===document||e.target===n||n.contains(e.target))return;const s=se.focusableChildren(n);s.length===0?n.focus():this._lastTabNavDirection===tl?s[s.length-1].focus():s[0].focus()}_handleKeydown(e){e.key===bA&&(this._lastTabNavDirection=e.shiftKey?tl:yA)}}const TA="modal",AA="bs.modal",vt=`.${AA}`,SA=".data-api",CA="Escape",$A=`hide${vt}`,OA=`hidePrevented${vt}`,Cu=`hidden${vt}`,$u=`show${vt}`,xA=`shown${vt}`,PA=`resize${vt}`,NA=`click.dismiss${vt}`,DA=`mousedown.dismiss${vt}`,IA=`keydown.dismiss${vt}`,LA=`click${vt}${SA}`,nl="modal-open",kA="fade",sl="show",ar="modal-static",RA=".modal.show",MA=".modal-dialog",VA=".modal-body",jA='[data-bs-toggle="modal"]',FA={backdrop:!0,focus:!0,keyboard:!0},HA={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Gn extends gt{constructor(e,n){super(e,n),this._dialog=se.findOne(MA,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Fr,this._addEventListeners()}static get Default(){return FA}static get DefaultType(){return HA}static get NAME(){return TA}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||P.trigger(this._element,$u,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(nl),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){!this._isShown||this._isTransitioning||P.trigger(this._element,$A).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(sl),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){for(const e of[window,this._dialog])P.off(e,vt);this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Au({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Su({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const n=se.findOne(VA,this._dialog);n&&(n.scrollTop=0),Ms(this._element),this._element.classList.add(sl);const s=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,P.trigger(this._element,xA,{relatedTarget:e})};this._queueCallback(s,this._dialog,this._isAnimated())}_addEventListeners(){P.on(this._element,IA,e=>{if(e.key===CA){if(this._config.keyboard){e.preventDefault(),this.hide();return}this._triggerBackdropTransition()}}),P.on(window,PA,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),P.on(this._element,DA,e=>{P.one(this._element,NA,n=>{if(!(this._element!==e.target||this._element!==n.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(nl),this._resetAdjustments(),this._scrollBar.reset(),P.trigger(this._element,Cu)})}_isAnimated(){return this._element.classList.contains(kA)}_triggerBackdropTransition(){if(P.trigger(this._element,OA).defaultPrevented)return;const n=this._element.scrollHeight>document.documentElement.clientHeight,s=this._element.style.overflowY;s==="hidden"||this._element.classList.contains(ar)||(n||(this._element.style.overflowY="hidden"),this._element.classList.add(ar),this._queueCallback(()=>{this._element.classList.remove(ar),this._queueCallback(()=>{this._element.style.overflowY=s},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,n=this._scrollBar.getWidth(),s=n>0;if(s&&!e){const o=it()?"paddingLeft":"paddingRight";this._element.style[o]=`${n}px`}if(!s&&e){const o=it()?"paddingRight":"paddingLeft";this._element.style[o]=`${n}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,n){return this.each(function(){const s=Gn.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof s[e]>"u")throw new TypeError(`No method named "${e}"`);s[e](n)}})}}P.on(document,LA,jA,function(t){const e=Nt(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),P.one(e,$u,o=>{o.defaultPrevented||P.one(e,Cu,()=>{rs(this)&&this.focus()})});const n=se.findOne(RA);n&&Gn.getInstance(n).hide(),Gn.getOrCreateInstance(e).toggle(this)});Fo(Gn);lt(Gn);const BA="offcanvas",UA="bs.offcanvas",Vt=`.${UA}`,Ou=".data-api",KA=`load${Vt}${Ou}`,WA="Escape",ol="show",rl="showing",il="hiding",zA="offcanvas-backdrop",xu=".offcanvas.show",YA=`show${Vt}`,qA=`shown${Vt}`,GA=`hide${Vt}`,al=`hidePrevented${Vt}`,Pu=`hidden${Vt}`,JA=`resize${Vt}`,XA=`click${Vt}${Ou}`,QA=`keydown.dismiss${Vt}`,ZA='[data-bs-toggle="offcanvas"]',e1={backdrop:!0,keyboard:!0,scroll:!1},t1={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Zt extends gt{constructor(e,n){super(e,n),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return e1}static get DefaultType(){return t1}static get NAME(){return BA}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown||P.trigger(this._element,YA,{relatedTarget:e}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Fr().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(rl);const s=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(ol),this._element.classList.remove(rl),P.trigger(this._element,qA,{relatedTarget:e})};this._queueCallback(s,this._element,!0)}hide(){if(!this._isShown||P.trigger(this._element,GA).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(il),this._backdrop.hide();const n=()=>{this._element.classList.remove(ol,il),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Fr().reset(),P.trigger(this._element,Pu)};this._queueCallback(n,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=()=>{if(this._config.backdrop==="static"){P.trigger(this._element,al);return}this.hide()},n=Boolean(this._config.backdrop);return new Au({className:zA,isVisible:n,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:n?e:null})}_initializeFocusTrap(){return new Su({trapElement:this._element})}_addEventListeners(){P.on(this._element,QA,e=>{if(e.key===WA){if(!this._config.keyboard){P.trigger(this._element,al);return}this.hide()}})}static jQueryInterface(e){return this.each(function(){const n=Zt.getOrCreateInstance(this,e);if(typeof e=="string"){if(n[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);n[e](this)}})}}P.on(document,XA,ZA,function(t){const e=Nt(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),Qt(this))return;P.one(e,Pu,()=>{rs(this)&&this.focus()});const n=se.findOne(xu);n&&n!==e&&Zt.getInstance(n).hide(),Zt.getOrCreateInstance(e).toggle(this)});P.on(window,KA,()=>{for(const t of se.find(xu))Zt.getOrCreateInstance(t).show()});P.on(window,JA,()=>{for(const t of se.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(t).position!=="fixed"&&Zt.getOrCreateInstance(t).hide()});Fo(Zt);lt(Zt);const n1=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),s1=/^aria-[\w-]*$/i,o1=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,r1=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,i1=(t,e)=>{const n=t.nodeName.toLowerCase();return e.includes(n)?n1.has(n)?Boolean(o1.test(t.nodeValue)||r1.test(t.nodeValue)):!0:e.filter(s=>s instanceof RegExp).some(s=>s.test(n))},Nu={"*":["class","dir","id","lang","role",s1],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function a1(t,e,n){if(!t.length)return t;if(n&&typeof n=="function")return n(t);const o=new window.DOMParser().parseFromString(t,"text/html"),r=[].concat(...o.body.querySelectorAll("*"));for(const i of r){const a=i.nodeName.toLowerCase();if(!Object.keys(e).includes(a)){i.remove();continue}const l=[].concat(...i.attributes),d=[].concat(e["*"]||[],e[a]||[]);for(const u of l)i1(u,d)||i.removeAttribute(u.nodeName)}return o.body.innerHTML}const l1="TemplateFactory",c1={allowList:Nu,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},u1={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},d1={entry:"(string|element|function|null)",selector:"(string|element)"};class f1 extends Vs{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return c1}static get DefaultType(){return u1}static get NAME(){return l1}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[o,r]of Object.entries(this._config.content))this._setContent(e,r,o);const n=e.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&n.classList.add(...s.split(" ")),n}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[n,s]of Object.entries(e))super._typeCheckConfig({selector:n,entry:s},d1)}_setContent(e,n,s){const o=se.findOne(s,e);if(!!o){if(n=this._resolvePossibleFunction(n),!n){o.remove();return}if(Dt(n)){this._putElementInTemplate(Xt(n),o);return}if(this._config.html){o.innerHTML=this._maybeSanitize(n);return}o.textContent=n}}_maybeSanitize(e){return this._config.sanitize?a1(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return typeof e=="function"?e(this):e}_putElementInTemplate(e,n){if(this._config.html){n.innerHTML="",n.append(e);return}n.textContent=e.textContent}}const h1="tooltip",p1=new Set(["sanitize","allowList","sanitizeFn"]),lr="fade",m1="modal",Xs="show",_1=".tooltip-inner",ll=`.${m1}`,cl="hide.bs.modal",ds="hover",cr="focus",g1="click",v1="manual",b1="hide",y1="hidden",E1="show",w1="shown",T1="inserted",A1="click",S1="focusin",C1="focusout",$1="mouseenter",O1="mouseleave",x1={AUTO:"auto",TOP:"top",RIGHT:it()?"left":"right",BOTTOM:"bottom",LEFT:it()?"right":"left"},P1={allowList:Nu,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,0],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},N1={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class as extends gt{constructor(e,n){if(typeof su>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e,n),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return P1}static get DefaultType(){return N1}static get NAME(){return h1}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(!!this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),P.off(this._element.closest(ll),cl,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const e=P.trigger(this._element,this.constructor.eventName(E1)),s=(au(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!s)return;this._disposePopper();const o=this._getTipElement();this._element.setAttribute("aria-describedby",o.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(o),P.trigger(this._element,this.constructor.eventName(T1))),this._popper=this._createPopper(o),o.classList.add(Xs),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))P.on(a,"mouseover",mo);const i=()=>{P.trigger(this._element,this.constructor.eventName(w1)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(i,this.tip,this._isAnimated())}hide(){if(!this._isShown()||P.trigger(this._element,this.constructor.eventName(b1)).defaultPrevented)return;if(this._getTipElement().classList.remove(Xs),"ontouchstart"in document.documentElement)for(const o of[].concat(...document.body.children))P.off(o,"mouseover",mo);this._activeTrigger[g1]=!1,this._activeTrigger[cr]=!1,this._activeTrigger[ds]=!1,this._isHovered=null;const s=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),P.trigger(this._element,this.constructor.eventName(y1)))};this._queueCallback(s,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const n=this._getTemplateFactory(e).toHtml();if(!n)return null;n.classList.remove(lr,Xs),n.classList.add(`bs-${this.constructor.NAME}-auto`);const s=_w(this.constructor.NAME).toString();return n.setAttribute("id",s),this._isAnimated()&&n.classList.add(lr),n}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new f1({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[_1]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(lr)}_isShown(){return this.tip&&this.tip.classList.contains(Xs)}_createPopper(e){const n=typeof this._config.placement=="function"?this._config.placement.call(this,e,this._element):this._config.placement,s=x1[n.toUpperCase()];return wi(this._element,e,this._getPopperConfig(s))}_getOffset(){const{offset:e}=this._config;return typeof e=="string"?e.split(",").map(n=>Number.parseInt(n,10)):typeof e=="function"?n=>e(n,this._element):e}_resolvePossibleFunction(e){return typeof e=="function"?e.call(this._element):e}_getPopperConfig(e){const n={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:s=>{this._getTipElement().setAttribute("data-popper-placement",s.state.placement)}}]};return{...n,...typeof this._config.popperConfig=="function"?this._config.popperConfig(n):this._config.popperConfig}}_setListeners(){const e=this._config.trigger.split(" ");for(const n of e)if(n==="click")P.on(this._element,this.constructor.eventName(A1),this._config.selector,s=>{this._initializeOnDelegatedTarget(s).toggle()});else if(n!==v1){const s=n===ds?this.constructor.eventName($1):this.constructor.eventName(S1),o=n===ds?this.constructor.eventName(O1):this.constructor.eventName(C1);P.on(this._element,s,this._config.selector,r=>{const i=this._initializeOnDelegatedTarget(r);i._activeTrigger[r.type==="focusin"?cr:ds]=!0,i._enter()}),P.on(this._element,o,this._config.selector,r=>{const i=this._initializeOnDelegatedTarget(r);i._activeTrigger[r.type==="focusout"?cr:ds]=i._element.contains(r.relatedTarget),i._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},P.on(this._element.closest(ll),cl,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");!e||(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,n){clearTimeout(this._timeout),this._timeout=setTimeout(e,n)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const n=It.getDataAttributes(this._element);for(const s of Object.keys(n))p1.has(s)&&delete n[s];return e={...n,...typeof e=="object"&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=e.container===!1?document.body:Xt(e.container),typeof e.delay=="number"&&(e.delay={show:e.delay,hide:e.delay}),typeof e.title=="number"&&(e.title=e.title.toString()),typeof e.content=="number"&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const n in this._config)this.constructor.Default[n]!==this._config[n]&&(e[n]=this._config[n]);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){const n=as.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof n[e]>"u")throw new TypeError(`No method named "${e}"`);n[e]()}})}}lt(as);const D1="popover",I1=".popover-header",L1=".popover-body",k1={...as.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},R1={...as.DefaultType,content:"(null|string|element|function)"};class Ci extends as{static get Default(){return k1}static get DefaultType(){return R1}static get NAME(){return D1}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[I1]:this._getTitle(),[L1]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){const n=Ci.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof n[e]>"u")throw new TypeError(`No method named "${e}"`);n[e]()}})}}lt(Ci);const M1="scrollspy",V1="bs.scrollspy",$i=`.${V1}`,j1=".data-api",F1=`activate${$i}`,ul=`click${$i}`,H1=`load${$i}${j1}`,B1="dropdown-item",On="active",U1='[data-bs-spy="scroll"]',ur="[href]",K1=".nav, .list-group",dl=".nav-link",W1=".nav-item",z1=".list-group-item",Y1=`${dl}, ${W1} > ${dl}, ${z1}`,q1=".dropdown",G1=".dropdown-toggle",J1={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},X1={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Uo extends gt{constructor(e,n){super(e,n),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return J1}static get DefaultType(){return X1}static get NAME(){return M1}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=Xt(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,typeof e.threshold=="string"&&(e.threshold=e.threshold.split(",").map(n=>Number.parseFloat(n))),e}_maybeEnableSmoothScroll(){!this._config.smoothScroll||(P.off(this._config.target,ul),P.on(this._config.target,ul,ur,e=>{const n=this._observableSections.get(e.target.hash);if(n){e.preventDefault();const s=this._rootElement||window,o=n.offsetTop-this._element.offsetTop;if(s.scrollTo){s.scrollTo({top:o,behavior:"smooth"});return}s.scrollTop=o}}))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(n=>this._observerCallback(n),e)}_observerCallback(e){const n=i=>this._targetLinks.get(`#${i.target.id}`),s=i=>{this._previousScrollData.visibleEntryTop=i.target.offsetTop,this._process(n(i))},o=(this._rootElement||document.documentElement).scrollTop,r=o>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=o;for(const i of e){if(!i.isIntersecting){this._activeTarget=null,this._clearActiveClass(n(i));continue}const a=i.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(r&&a){if(s(i),!o)return;continue}!r&&!a&&s(i)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=se.find(ur,this._config.target);for(const n of e){if(!n.hash||Qt(n))continue;const s=se.findOne(n.hash,this._element);rs(s)&&(this._targetLinks.set(n.hash,n),this._observableSections.set(n.hash,s))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(On),this._activateParents(e),P.trigger(this._element,F1,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains(B1)){se.findOne(G1,e.closest(q1)).classList.add(On);return}for(const n of se.parents(e,K1))for(const s of se.prev(n,Y1))s.classList.add(On)}_clearActiveClass(e){e.classList.remove(On);const n=se.find(`${ur}.${On}`,e);for(const s of n)s.classList.remove(On)}static jQueryInterface(e){return this.each(function(){const n=Uo.getOrCreateInstance(this,e);if(typeof e=="string"){if(n[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);n[e]()}})}}P.on(window,H1,()=>{for(const t of se.find(U1))Uo.getOrCreateInstance(t)});lt(Uo);const Q1="tab",Z1="bs.tab",Tn=`.${Z1}`,eS=`hide${Tn}`,tS=`hidden${Tn}`,nS=`show${Tn}`,sS=`shown${Tn}`,oS=`click${Tn}`,rS=`keydown${Tn}`,iS=`load${Tn}`,aS="ArrowLeft",fl="ArrowRight",lS="ArrowUp",hl="ArrowDown",hn="active",pl="fade",dr="show",cS="dropdown",uS=".dropdown-toggle",dS=".dropdown-menu",fr=":not(.dropdown-toggle)",fS='.list-group, .nav, [role="tablist"]',hS=".nav-item, .list-group-item",pS=`.nav-link${fr}, .list-group-item${fr}, [role="tab"]${fr}`,Du='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',hr=`${pS}, ${Du}`,mS=`.${hn}[data-bs-toggle="tab"], .${hn}[data-bs-toggle="pill"], .${hn}[data-bs-toggle="list"]`;class Jn extends gt{constructor(e){super(e),this._parent=this._element.closest(fS),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),P.on(this._element,rS,n=>this._keydown(n)))}static get NAME(){return Q1}show(){const e=this._element;if(this._elemIsActive(e))return;const n=this._getActiveElem(),s=n?P.trigger(n,eS,{relatedTarget:e}):null;P.trigger(e,nS,{relatedTarget:n}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(n,e),this._activate(e,n))}_activate(e,n){if(!e)return;e.classList.add(hn),this._activate(Nt(e));const s=()=>{if(e.getAttribute("role")!=="tab"){e.classList.add(dr);return}e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),P.trigger(e,sS,{relatedTarget:n})};this._queueCallback(s,e,e.classList.contains(pl))}_deactivate(e,n){if(!e)return;e.classList.remove(hn),e.blur(),this._deactivate(Nt(e));const s=()=>{if(e.getAttribute("role")!=="tab"){e.classList.remove(dr);return}e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),P.trigger(e,tS,{relatedTarget:n})};this._queueCallback(s,e,e.classList.contains(pl))}_keydown(e){if(![aS,fl,lS,hl].includes(e.key))return;e.stopPropagation(),e.preventDefault();const n=[fl,hl].includes(e.key),s=Ti(this._getChildren().filter(o=>!Qt(o)),e.target,n,!0);s&&(s.focus({preventScroll:!0}),Jn.getOrCreateInstance(s).show())}_getChildren(){return se.find(hr,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,n){this._setAttributeIfNotExists(e,"role","tablist");for(const s of n)this._setInitialAttributesOnChild(s)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const n=this._elemIsActive(e),s=this._getOuterElement(e);e.setAttribute("aria-selected",n),s!==e&&this._setAttributeIfNotExists(s,"role","presentation"),n||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const n=Nt(e);!n||(this._setAttributeIfNotExists(n,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(n,"aria-labelledby",`#${e.id}`))}_toggleDropDown(e,n){const s=this._getOuterElement(e);if(!s.classList.contains(cS))return;const o=(r,i)=>{const a=se.findOne(r,s);a&&a.classList.toggle(i,n)};o(uS,hn),o(dS,dr),s.setAttribute("aria-expanded",n)}_setAttributeIfNotExists(e,n,s){e.hasAttribute(n)||e.setAttribute(n,s)}_elemIsActive(e){return e.classList.contains(hn)}_getInnerElement(e){return e.matches(hr)?e:se.findOne(hr,e)}_getOuterElement(e){return e.closest(hS)||e}static jQueryInterface(e){return this.each(function(){const n=Jn.getOrCreateInstance(this);if(typeof e=="string"){if(n[e]===void 0||e.startsWith("_")||e==="constructor")throw new TypeError(`No method named "${e}"`);n[e]()}})}}P.on(document,oS,Du,function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),!Qt(this)&&Jn.getOrCreateInstance(this).show()});P.on(window,iS,()=>{for(const t of se.find(mS))Jn.getOrCreateInstance(t)});lt(Jn);const _S="toast",gS="bs.toast",sn=`.${gS}`,vS=`mouseover${sn}`,bS=`mouseout${sn}`,yS=`focusin${sn}`,ES=`focusout${sn}`,wS=`hide${sn}`,TS=`hidden${sn}`,AS=`show${sn}`,SS=`shown${sn}`,CS="fade",ml="hide",Qs="show",Zs="showing",$S={animation:"boolean",autohide:"boolean",delay:"number"},OS={animation:!0,autohide:!0,delay:5e3};class Ko extends gt{constructor(e,n){super(e,n),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return OS}static get DefaultType(){return $S}static get NAME(){return _S}show(){if(P.trigger(this._element,AS).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(CS);const n=()=>{this._element.classList.remove(Zs),P.trigger(this._element,SS),this._maybeScheduleHide()};this._element.classList.remove(ml),Ms(this._element),this._element.classList.add(Qs,Zs),this._queueCallback(n,this._element,this._config.animation)}hide(){if(!this.isShown()||P.trigger(this._element,wS).defaultPrevented)return;const n=()=>{this._element.classList.add(ml),this._element.classList.remove(Zs,Qs),P.trigger(this._element,TS)};this._element.classList.add(Zs),this._queueCallback(n,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Qs),super.dispose()}isShown(){return this._element.classList.contains(Qs)}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(e,n){switch(e.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=n;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=n;break}}if(n){this._clearTimeout();return}const s=e.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){P.on(this._element,vS,e=>this._onInteraction(e,!0)),P.on(this._element,bS,e=>this._onInteraction(e,!1)),P.on(this._element,yS,e=>this._onInteraction(e,!0)),P.on(this._element,ES,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){const n=Ko.getOrCreateInstance(this,e);if(typeof e=="string"){if(typeof n[e]>"u")throw new TypeError(`No method named "${e}"`);n[e](this)}})}}Fo(Ko);lt(Ko);const Iu=uh();Iu.use(Eh);const Oi=ah(fm);Oi.use(Iu);Oi.use(Dc);Oi.mount("#app");
