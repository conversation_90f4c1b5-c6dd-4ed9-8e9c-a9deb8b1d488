<script setup>
    import { ref, computed, onBeforeMount } from 'vue';
    import { useRoute, useRouter } from 'vue-router';
    import { useTeamStore } from '@/stores/teamStore';
    import { useAlertStore } from '@/stores/alertStore';
    import { useLeagueStore } from '@/stores/leagueStore';
    import { useUserStore } from '@/stores/userStore';
    import { useProfileStore } from '@/stores/profileStore'
    const router = useRouter();
    const alertStore = useAlertStore();
    const teamStore = useTeamStore();
    const leagueStore = useLeagueStore();
    const userStore = useUserStore();
    const loading = ref(false);
    
    const saveTeam = async () => {
        loading.value = true;
        let result = await teamStore.saveTeam();
        if (result === true) {
            alertStore.add('success','Saved',teamStore.team.name);
            router.push('/teams');
        } else {
            alertStore.add('warning','Ooops',result);
            loading.value = false;
        }
    }
    const checkTeamName = async () => {
        const teamId = teamStore.team.id;
        const teamName = teamStore.team.name;
        let leagueId = {};
        leagueId['id'] = teamStore.team.leagueID;
        console.log(teamStore.team.leagueID);
        

        if (teamId && leagueId) {
            const profileStore = useProfileStore();
            const url = profileStore.rootProtocol + 'public.v2.api.' + profileStore.rootDomain + '/team-name-checker';
            
            let method = 'POST';
            let headers = new Headers({
                'Authorization': 'Bearer ' + profileStore.jwt,
                'Content-Type': 'application/json',
            });
            
            let body = JSON.stringify({
                teamId: teamId,
                league: leagueId,
                name: teamName  
            });
            
            try {
                const response = await fetch(url, {
                    method,
                    headers,
                    body,
                });
                const data = await response.json();
                if(data.success){
                    teamStore.team.name = data.success
                }
                if(data.error){
                    alertStore.add('warning','Oops',data.error);
                }
            } catch (error) {
                console.error('Error checking team name:', error);
                // Handle network errors or other unexpected errors
            }
        }
    };

    onBeforeMount( async () => {
        const route = useRoute();
        if (route.params.id) {
            if (teamStore.teams.length == 0) await teamStore.getTeams();
            teamStore.getTeam(route.params.id);
        } else teamStore.team = {}
    })
</script>
<template>
    <div class="team">
        <div class="container">
            <h3>Team <div v-if="loading" class="spinner-grow spinner-grow-sm text-success" role="status">
                <span class="visually-hidden">Loading...</span>
                </div><RouterLink v-else to="/teams" class="btn btn-sm btn-warning">&lt;</RouterLink>
            </h3>
            <form @submit.prevent="saveTeam">
                <table class="table">
                    <tbody>
                        <tr>
                            <th>
                                <label for="team.name">Name</label>
                            </th>
                            <td>
                                <input type="text" 
                                v-model="teamStore.team.name" 
                                id="team.name" 
                                class="form-control" 
                                placeholder="Team Name" 
                                required
                                @keyup="checkTeamName">
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="leagueName">League</label>
                            </th>
                            <td>
                                {{ leagueStore.getLeagueName(teamStore.team.leagueID) }}
                            </td>
                        </tr>
                        <tr>
                            <th>
                                <label for="captain">Captain <RouterLink to="/team-management"><i class="bi bi-pencil"></i></RouterLink></label>
                            </th>
                            <th>
                                <label for="treasurer">Treasurer <RouterLink to="/team-management"><i class="bi bi-pencil"></i></RouterLink></label>
                            </th>
                        </tr>
                        <tr>
                            <td>
                                <div v-if="teamStore.team.managers.captain" class="d-flex flex-column">
                                    <span>{{ teamStore.team.managers.captain.name }}</span>
                                    <span>{{ teamStore.team.managers.captain.email }}</span>
                                    <span>{{ teamStore.team.managers.captain.mobile }}</span>
                                    <span>{{ teamStore.team.managers.captain.dob }}</span>
                                    <span>{{ teamStore.team.managers.captain.line1 }}</span>
                                    <span>{{ teamStore.team.managers.captain.town }} {{ teamStore.team.managers.captain.postcode }}</span>
                                </div>
                            </td>
                            <td>
                                <div v-if="teamStore.team.managers.treasurer" class="d-flex flex-column">
                                    <span>{{ teamStore.team.managers.treasurer.name }}</span>
                                    <span>{{ teamStore.team.managers.treasurer.email }}</span>
                                    <span>{{ teamStore.team.managers.treasurer.mobile }}</span>
                                    <span>{{ teamStore.team.managers.treasurer.dob }}</span>
                                    <span>{{ teamStore.team.managers.treasurer.line1 }}</span>
                                    <span>{{ teamStore.team.managers.treasurer.town }} {{ teamStore.team.managers.captain.postcode }}</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button class="btn btn-sm btn-success mt-2">Save</button>
            </form>
        </div>
    </div>
</template>