<script setup>
  import { ref, onBeforeMount } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useVenueStore } from '@/stores/venueStore'
  import { useAlertStore } from '@/stores/alertStore'
  import { useUserStore } from '@/stores/userStore'
  const router = useRouter()
  const alertStore = useAlertStore()
  const venueStore = useVenueStore()
  const userStore = useUserStore()
  const loading = ref(false)
  const saveVenue = async () => {
    loading.value = true
    let result = await venueStore.saveVenue()
    if (result === true) {
      alertStore.add('success', 'Saved', venueStore.venue.name)
      router.push('/venues')
    } else {
      alertStore.add('warning', 'Ooops', result)
      loading.value = false
    }
  }
  onBeforeMount(async () => {
    const route = useRoute()
    if (route.params.id) {
      if (venueStore.venues.length == 0) await venueStore.getVenues()
      venueStore.getVenue(route.params.id)
    }
  })
</script>
<template>
  <div class="venues">
    <div class="container">
      <h3>
        Venue
        <div v-if="loading" class="spinner-grow spinner-grow-sm text-success" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <RouterLink v-else to="/venues" class="btn btn-sm btn-warning">Back</RouterLink>
      </h3>
      <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button
            class="nav-link active"
            id="home-tab"
            data-bs-toggle="tab"
            data-bs-target="#home-tab-pane"
            type="button"
            role="tab"
            aria-controls="home-tab-pane"
            aria-selected="true"
          >
            Main
          </button>
        </li>
        <!-- <li class="nav-item" role="presentation">
                    <button class="nav-link" id="tasters-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
                        type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">Tasters</button>
                </li> -->
      </ul>
      <div class="tab-content mt-2" id="myTabContent">
        <div
          class="tab-pane fade show active"
          id="home-tab-pane"
          role="tabpanel"
          aria-labelledby="home-tab"
          tabindex="0"
        >
          <form @submit.prevent="saveVenue">
            <div class="d-flex flex-column flex-sm-row">
              <div class="d-flex flex-column w-100 pe-1">
                <label for="venue.name">Name</label>
                <input
                  type="text"
                  v-model="venueStore.venue.name"
                  id="venue.name"
                  class="form-control"
                  placeholder="Venue Name"
                  required
                />
              </div>
            </div>
            <div class="d-flex flex-column flex-sm-row">
              <div class="d-flex flex-column w-100 ps-1">
                <label for="venue.coordinatorID">Coordinator</label>
                <select
                  v-model="venueStore.venue.coordinatorID"
                  id="venue.coordinatorID"
                  class="form-control"
                >
                  <option v-for="coordinator in userStore.coordinators" :value="coordinator.id">
                    {{ coordinator.name }}
                  </option>
                </select>
              </div>
            </div>
            <div class="d-flex flex-column flex-sm-row">
              <div class="d-flex flex-column w-100 pe-1">
                <label for="venue.address1">Address 1</label>
                <input
                  type="text"
                  v-model="venueStore.venue.address1"
                  id="venue.address1"
                  class="form-control"
                  placeholder="Venue Address Line 1"
                  required
                />
              </div>
              <div class="d-flex flex-column w-100 ps-1">
                <label for="venue.address2">Address Line 2</label>
                <input
                  type="text"
                  v-model="venueStore.venue.address2"
                  id="venue.address2"
                  class="form-control"
                  placeholder="Venue Address Line 2"
                />
              </div>
            </div>
            <div class="d-flex flex-column flex-sm-row">
              <div class="d-flex flex-column w-100 pe-1">
                <label for="venue.town">Town</label>
                <input
                  type="text"
                  v-model="venueStore.venue.town"
                  id="venue.town"
                  class="form-control"
                  placeholder="Venue Town"
                  required
                />
              </div>
              <div class="d-flex flex-column w-100 ps-1">
                <label for="venue.postcode">Postcode</label>
                <input
                  type="text"
                  v-model="venueStore.venue.postcode"
                  id="venue.postcode"
                  class="form-control"
                  placeholder="Venue Postcode"
                  required
                />
              </div>
            </div>
            <div class="d-flex flex-column flex-sm-row">
              <div class="d-flex flex-column w-100 pe-1">
                <label for="venue.lat">Latitude</label>
                <input
                  type="text"
                  v-model="venueStore.venue.lat"
                  id="venue.lat"
                  class="form-control"
                  placeholder="Latitude"
                />
              </div>
              <div class="d-flex flex-column w-100 ps-1">
                <label for="venue.lng">Longitude</label>
                <input
                  type="text"
                  v-model="venueStore.venue.lng"
                  id="venue.lng"
                  class="form-control"
                  placeholder="Longitude"
                />
              </div>
            </div>
            <div class="d-flex flex-column flex-sm-row">
              <div class="d-flex flex-column w-100 pe-1">
                <label for="venue.sageAccount">Sage Acc/No</label>
                <input
                  type="text"
                  v-model="venueStore.venue.sageAccount"
                  id="venue.sageAccount"
                  class="form-control"
                  placeholder="Sage Account Code"
                />
              </div>
              <div class="d-flex flex-column w-100 ps-1">
                <label for="venue.purchaseTerms">Purchase Terms</label>
                <select
                  v-model="venueStore.venue.purchaseTerms"
                  id="venue.purchaseTerms"
                  class="form-control"
                >
                  <option
                    v-for="purchaseTerm in venueStore.purchaseTerms"
                    :key="purchaseTerm.id"
                    :title="purchaseTerm.description"
                    :value="purchaseTerm.id"
                  >
                    {{ purchaseTerm.name }}
                  </option>
                </select>
              </div>
              <div class="d-flex flex-column w-100 ps-1">
                <label for="venue.status">Status</label>
                <select
                  v-model.number="venueStore.venue.status"
                  id="venue.status"
                  class="form-control"
                >
                  <option :value="1">Active</option>
                  <option :value="0">Inactive</option>
                </select>
              </div>
            </div>
            <label for="venue.notes">Notes</label>
            <textarea
              v-model="venueStore.venue.notes"
              id="venue.notes"
              cols="30"
              rows="5"
              class="form-control"
            ></textarea>
            <button class="btn btn-sm btn-success mt-2">Save</button>
          </form>
        </div>
        <div
          class="tab-pane fade"
          id="profile-tab-pane"
          role="tabpanel"
          aria-labelledby="profile-tab"
          tabindex="0"
        >
          <table class="table">
            <thead>
              <tr>
                <th>Sport</th>
                <th>Date</th>
                <th>Time</th>
                <th>Charge</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="taster in venueStore.venue.tasters" :key="taster.id">
                <td>{{ taster.sportID }}</td>
                <td>{{ taster.date }}</td>
                <td>{{ taster.time }}</td>
                <td>{{ taster.charge }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
  .backButton {
    text-decoration: none;
  }
</style>
