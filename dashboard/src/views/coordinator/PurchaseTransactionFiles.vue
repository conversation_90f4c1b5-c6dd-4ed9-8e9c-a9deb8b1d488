<script setup>
import { ref, onBeforeMount } from 'vue';
// import { useRoute, useRouter } from 'vue-router';
// import { useProfileStore } from '@/stores/profileStore';
// import { useVenueStore } from '@/stores/venueStore';
import { useFinanceStore } from '@/stores/financeStore';
import { useAlertStore } from '@/stores/alertStore';
// const route = useRoute();
// const router = useRouter();
// const nextYear = computed(() => {
//     let thisDate = new Date();
//     return parseInt(thisDate.getFullYear()) + 1;
// })
const loading = ref(false);
const financeStore = useFinanceStore();

// const uploading = ref(false);
// const newitemTargetLoading = ref(false);
// const newitem = ref({});
// const newitemTargetTotal = ref(null);
// const profileStore = useProfileStore();
const alertStore = useAlertStore();
// const venueStore = useVenueStore();
const fetchPurchaseTransactionFiles = async () => {
    loading.value = true;
    let result = await financeStore.fetchPurchaseTransactionFiles();
    loading.value = false;
}

const resendPurhaseTransactionImportFile = async (file) => {
    loading.value = true;
    let result = await financeStore.resendPurhaseTransactionImportFile(file.filename);
    if (result === true) alertStore.add('success', 'Success', 'Import file resent');
    loading.value = false;
}
// const downloadFile = async (downloadFile) => {
//     const url = profileStore.rootProtocol + "//admin.v3.api." + profileStore.rootDomain + "/download-venue-transaction-document/" + financeStore.purchaseTransaction.id + "/" + downloadFile;
//     let method = 'get';
//     let headers = new Headers({
//         Authorization: "Bearer " + profileStore.jwt,
//     });
//     try {
//         let response = await fetch(url, {
//             method,
//             headers
//         });
//         if (response.status == 200) {
//             var a = document.createElement("a");
//             a.href = window.URL.createObjectURL(await response.blob());
//             a.download = downloadFile;
//             a.click();
//             return;
//         } else alertStore.add('warning', 'Failed', 'Could not download ' + downloadFile);
//     } catch (err) {
//         alertStore.add('danger', 'Error', err);
//     } console.log("Download", downloadFile);
// }
// watch(newitem.value, async (newValue, oldValue) => {
//     newitemTargetLoading.value = true;
//     if (newValue.month >= 1 && newValue.month <= 12 && newValue.year >= 2016 && newValue.year <= 2022) {
//         try {
//             let result = await venueStore.fetchAccrual(financeStore.purchaseTransaction.venueID, newValue.month, newValue.year);
//             if (isNaN(result)) {
//                 alertStore.add('warning', 'Problem', result);
//             } else {
//                 newitemTargetTotal.value = result;
//             }
//         } catch (err) {
//             alertStore.add('warning', 'Problem', err);
//         }
//     }
//     newitemTargetLoading.value = false;
// })
// const addNewItem = async () => {
//     if (financeStore.purchaseTransaction.posted) {
//         alertStore.add('warning', 'Cannot Save', 'Transaction already Posted');
//         return;
//     }
//     newitem.value.mainID = financeStore.purchaseTransaction.id;
//     let result = await financeStore.addNewPurchaseItem(newitem.value);
//     if (result === true) {
//         alertStore.add('success', 'Added', newitem.value.total + " to " + newitem.value.month + "/" + newitem.value.year);
//         newitem.value = {};
//     } else alertStore.add('warning', 'Failed', result);
// }

// const savePurchaseTransaction = async () => {
//     if (financeStore.purchaseTransaction.posted) {
//         alertStore.add('warning', 'Cannot Save', 'Transaction already Posted');
//         return;
//     }
//     loading.value = true;
//     let result = await financeStore.savePurchaseTransaction();
//     if (result !== true) {
//         alertStore.add('warning', 'Problem', result);
//     } else {
//         alertStore.add('success', 'Purchase Transaction Saved', result);
//         // router.push('/purchase-transactions');
//     }
//     loading.value = false;
// }

// const savePurchaseDocument = async () => {
//     uploading.value = true;
//     let filename = document.getElementById("purchaseTransactionFile").files[0];
//     try {
//         let result = await financeStore.savePurchaseDocument(filename);
//         if (result !== true) {
//             alertStore.add('warning', 'Problem', result);
//         } else {
//             alertStore.add('success', 'Success', 'Document uploaded');
//         }
//     } catch (err) {
//         alertStore.add('warning', 'Problem', err);
//     }
//     uploading.value = false;
// }

// const deletePurchaseItem = async (item) => {
//     if (financeStore.purchaseTransaction.posted) {
//         alertStore.add('warning', 'Cannot Remove Items', 'Transaction already Posted');
//         return;
//     }
//     let result = await financeStore.deletePurchaseItem(item.id);
//     if (result !== true) {
//         alertStore.add('warning', 'Problem', result);
//     } else alertStore.add('success', 'Success', 'Purchase Item Removed');
// }

onBeforeMount(async () => {
    fetchPurchaseTransactionFiles();
})
</script>
<template>
    <div class="purchaseTransaction container-fluid">
        <h3>Purchase Transaction Files</h3>
        <div v-if="loading" class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div v-else class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th class="text-center">...</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="file in financeStore.purchaseTransactionImportFiles">
                        <td>{{ file.timestamp }}</td>
                        <td><i @click.prevent="resendPurhaseTransactionImportFile(file)" class="bi bi-send"
                                style="cursor: pointer;"></i></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>
<style scoped>
.transactionDocUploadBtn {
    width: 70px;
}

.fileDownloadLink {
    cursor: pointer;
}
</style>