<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useVenueStore } from '@/stores/venueStore';
import { useAlertStore } from '@/stores/alertStore';

const venueStore = useVenueStore();
const alertStore = useAlertStore();
const loading = ref(false);
const search = ref();
const venues = computed(() => {
    if (!search.value) return venueStore.venues;
    return venueStore.venues.filter((venue) => {
        return (
            venue.name.toLowerCase().includes(search.value.toLowerCase()) || (venue.sageAccount && venue.sageAccount.toLowerCase().includes(search.value.toLowerCase())) ||
            venue.town.toLowerCase().includes(search.value.toLowerCase())
        );
    })
});
onBeforeMount(async () => {
    // if (!venueStore.venues) {
    loading.value = true;
    let result = await venueStore.getVenues();
    if (result !== true) alertStore.add('warning', 'Ooops', result);
    loading.value = false;
    // }
})
</script>
<template>
    <div class="venues">
        <div class="container">
            <h3>Venues | <RouterLink to="/venue" class="btn btn-sm btn-warning">&plus;</RouterLink>
            </h3>
            <div v-if="loading" class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div v-else class="tables-responsive">
                <input type="search" v-model="search" id="search" class="form-control" placeholder="Find...">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Address</th>
                            <th>Town</th>
                            <th>Postcode</th>
                            <th>Acc</th>
                            <th>...</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="venue in venues" :key="venue.id">
                            <td>{{ venue.name }}</td>
                            <td>{{ venue.address1 }}</td>
                            <td>{{ venue.town }}</td>
                            <td>{{ venue.postcode }}</td>
                            <td>{{ venue.sageAccount }}</td>
                            <td>
                                <RouterLink :to="'/venue/' + venue.id"><i class="bi bi-pencil"></i></RouterLink>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>