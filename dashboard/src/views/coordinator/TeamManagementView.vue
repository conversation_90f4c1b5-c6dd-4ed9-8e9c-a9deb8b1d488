<script setup>
import { ref, watch, onBeforeMount } from 'vue';
// import { useRoute } from 'vue-router';
import { useTeamStore } from '@/stores/teamStore';
import { useProfileStore } from '@/stores/profileStore';
import { useAlertStore } from '@/stores/alertStore';
// import { useLeagueStore } from '@/stores/leagueStore';
import { useUserStore } from '@/stores/userStore';
// const router = useRouter();
const alertStore = useAlertStore();
const teamStore = useTeamStore();
// const profileStore = useProfileStore();
// const leagueStore = useLeagueStore();
const userStore = useUserStore();
const management = ref({
    appointID: 1,
});
const userLoading = ref(false);
const teamLoading = ref(false);
const teamSearch = ref(null);
const userSearch = ref(null);
const canSubmit = ref(false);
const managementUpdating = ref(false);

const selectTeam = (team) => {
    teamSearch.value = team.fullName;
    management.value.teamID = team.id;
}

const selectUser = (user) => {
    userSearch.value = user.fullName;
    management.value.userID = user.id;
}

const updateTeamManagement = async () => {
    managementUpdating.value = true;
    let result = await teamStore.teamManagement(management.value);
    if (result === true) {
        alertStore.add('success', 'Success', 'Team Updated');
        management.value = {};
        teamSearch.value = null;
        userSearch.value = null;
    } else alertStore.add('warning', 'Failed', result);
    teamLoading.value = false;
    userLoading.value = false;
    managementUpdating.value = false;

}

watch(teamSearch, async (newValue) => {
    if (newValue.length == 0) {
        teamStore.teams = [];
        management.value.teamID = null;
    } else {
        teamLoading.value = true;
        await teamStore.searchTeams(newValue);
    }
    teamLoading.value = false;
})

watch(userSearch, async (newValue) => {
    if (newValue.length == 0) {
        userStore.users = [];
        management.value.userID = null;
    } else {
        userLoading.value = true;
        await userStore.searchUsers(newValue);
    }
    userLoading.value = false;
})

watch(management.value, (newVal) => {
    canSubmit.value = false;
    if (newVal.teamID && newVal.userID && newVal.appointID) canSubmit.value = true;
})

onBeforeMount(async () => {
    teamSearch.value = null;
    teamStore.teams = [];
    userSearch.value = null;
    userStore.users = [];
})
</script>
<template>
    <div class="teamManagement">
        <div class="container">
            <h3>Team Management <RouterLink to="/teams" class="btn btn-sm btn-warning">Back</RouterLink>
            </h3>
            <form @submit.prevent="updateTeamManagement">
                <p>Appoint a new Captain</p>
                <label for="management.teamID">Team</label>
                <div class="teamSearch d-flex flex-column position-relative">
                    <div v-if="teamLoading" class="spinner-border spinner-border-sm position-absolute ms-2 mt-2"
                        role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <i v-else class="bi bi-search position-absolute ms-2 mt-2"></i>

                    <input type="search" v-model="teamSearch" id="teamSearch" class="form-control rounded-4"
                        placeholder="Team Search..." autocomplete="off">

                    <div class="teamSearchResults d-flex flex-column">
                        <div class="teamListing d-flex flex-column">
                            <span v-for="t in teamStore.teams" :key="t.id" @click.prevent="selectTeam(t)"
                                class="dropdownOption">{{ t.fullName }}</span>
                        </div>
                    </div>
                </div>

                <label for="management.userID">User</label>
                <div class="teamSearch d-flex flex-column position-relative">
                    <div v-if="userLoading" class="spinner-border spinner-border-sm position-absolute ms-2 mt-2"
                        role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <i v-else class="bi bi-search position-absolute ms-2 mt-2"></i> <input type="search"
                        v-model="userSearch" id="userSearch" class="form-control rounded-4"
                        placeholder="User Search..." autocomplete="off">
                    <div class="userSearchResults d-flex flex-column">
                        <div class="userListing d-flex flex-column">
                            <span v-for="user in userStore.users" :key="user.id" @click.prevent="selectUser(user)"
                                class="dropdownOption">{{ user.email }}</span>
                        </div>
                    </div>
                </div>

                <!-- <label for="management.appointID">Appoint as</label>
                <select v-model="management.appointID" id="management.appointID" class="form-control rounded-4">
                    <option value="1">Captain & Treasurer</option>
                    <option value="2">Captain</option>
                    <option value="3">Treasurer</option>
                </select> -->

                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" role="switch" v-model="management.isSuppressed"
                        id="management.isSuppressed">
                    <label class="form-check-label" for="management.isSuppressed">Supress Alert?</label>
                </div>

                <button class="btn btn-success mt-2 updateTeamCaptainBtn" :disabled="!canSubmit">
                    <div v-if="managementUpdating" class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span v-else>
                        Submit
                    </span>

                </button>

            </form>
        </div>
    </div>
</template>
<style scoped>
input::placeholder {
    font-style: italic;
    font-size: .9em;
}

input {
    padding-left: 2em;
}

.dropdownOption {
    padding: .5em;
    cursor: pointer;
}

.dropdownOption:hover {
    background-color: #eee;
}

.updateTeamCaptainBtn {
    width: 70px;
}
</style>