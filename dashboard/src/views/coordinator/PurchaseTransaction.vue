<script setup>
import { ref, watch, computed, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useProfileStore } from '@/stores/profileStore';
import { useVenueStore } from '@/stores/venueStore';
import { useFinanceStore } from '@/stores/financeStore';
import { useAlertStore } from '@/stores/alertStore';
const route = useRoute();
const router = useRouter();

const nextYear = computed(() => {
    let thisDate = new Date();
    return parseInt(thisDate.getFullYear()) + 1;
})

const loading = ref(false);
const addNew = ref(false);
const uploading = ref(false);
const newitemTargetLoading = ref(false);
const newitem = ref({});
const newitemTargetTotal = ref(null);
const profileStore = useProfileStore();
const alertStore = useAlertStore();
const venueStore = useVenueStore();
const financeStore = useFinanceStore();

const downloadFile = async (downloadFile) => {
    const url = profileStore.rootProtocol + "//admin.v3.api." + profileStore.rootDomain + "/download-venue-transaction-document/" + financeStore.purchaseTransaction.id + "/" + downloadFile;
    let method = 'get';
    let headers = new Headers({
        Authorization: "Bearer " + profileStore.jwt,
    });
    try {
        let response = await fetch(url, {
            method,
            headers
        });
        if (response.status == 200) {
            var a = document.createElement("a");
            a.href = window.URL.createObjectURL(await response.blob());
            a.download = downloadFile;
            a.click();
            return;
        } else alertStore.add('warning', 'Failed', 'Could not download ' + downloadFile);
    } catch (err) {
        alertStore.add('danger', 'Error', err);
    } console.log("Download", downloadFile);
}

watch(newitem.value, async (newValue, oldValue) => {
    newitemTargetLoading.value = true;
    if (newValue.month >= 1 && newValue.month <= 12 && newValue.year >= 2016 && newValue.year <= 2022) {
        try {
            let result = await venueStore.fetchAccrual(financeStore.purchaseTransaction.venueID, newValue.month, newValue.year);
            if (isNaN(result)) {
                alertStore.add('warning', 'Problem', result);
            } else {
                newitemTargetTotal.value = result;
            }
        } catch (err) {
            alertStore.add('warning', 'Problem', err);
        }
    }
    newitemTargetLoading.value = false;
})

const addNewItem = async () => {
    if (financeStore.purchaseTransaction.posted) {
        alertStore.add('warning', 'Cannot Save', 'Transaction already Posted');
        return;
    }
    newitem.value.mainID = financeStore.purchaseTransaction.id;
    let result = await financeStore.addNewPurchaseItem(newitem.value);
    if (result === true) {
        alertStore.add('success', 'Added', newitem.value.total + " to " + newitem.value.month + "/" + newitem.value.year);
        newitem.value = {};
    } else alertStore.add('warning', 'Failed', result);
}

const savePurchaseTransaction = async () => {
    if (financeStore.purchaseTransaction.posted) {
        alertStore.add('warning', 'Cannot Save', 'Transaction already Posted');
        return;
    }
    loading.value = true;
    let result = await financeStore.savePurchaseTransaction();
    if (result !== true) {
        alertStore.add('warning', 'Problem', result);
    } else {
        alertStore.add('success', 'Purchase Transaction Saved', result);
        // router.push('/purchase-transactions');
    }
    loading.value = false;
}

const savePurchaseDocument = async () => {
    uploading.value = true;
    let filename = document.getElementById("purchaseTransactionFile").files[0];
    try {
        let result = await financeStore.savePurchaseDocument(filename);
        if (result !== true) {
            alertStore.add('warning', 'Problem', result);
        } else {
            alertStore.add('success', 'Success', 'Document uploaded');
        }
    } catch (err) {
        alertStore.add('warning', 'Problem', err);
    }
    uploading.value = false;
}

const deletePurchaseItem = async (item) => {
    if (financeStore.purchaseTransaction.posted) {
        alertStore.add('warning', 'Cannot Remove Items', 'Transaction already Posted');
        return;
    }
    let result = await financeStore.deletePurchaseItem(item.id);
    if (result !== true) {
        alertStore.add('warning', 'Problem', result);
    } else alertStore.add('success', 'Success', 'Purchase Item Removed');
}

onBeforeMount(async () => {
    if (route.params.id) {
        await financeStore.getPurchaseTransaction(route.params.id);
        if (!financeStore.purchaseTransaction || !financeStore.purchaseTransaction.id) {
            alertStore.add('warning', 'Failed', 'Could not load Transaction ID ' + route.params.id);
            router.replace('/purchase-transactions');
        }
        //  else {
        //     let myTab = document.querySelector("#myTab");
        //     let tabButtons = myTab.querySelectorAll(".nav-link");
        //     tabButtons.forEach((tabButton) => {
        //         tabButton.classList.remove("active");
        //     });
        // }
    } else financeStore.purchaseTransaction = {};
    let myTab = document.querySelector("#myTab");
    let tabButtons = myTab.querySelectorAll(".nav-link");
    tabButtons.forEach((tabButton) => {
        (tabButton.id == "home-tab") ? tabButton.classList.add("active") : tabButton.classList.remove("active");
    });
})
</script>

<template>
    <div class="purchaseTransaction container-fluid">

        <h3>Venue Invoice | <RouterLink to="/venues-transaction-report" class="btn btn-sm btn-warning">&larr; Report
            </RouterLink> | <RouterLink to="/purchase-transactions" class="btn btn-sm btn-warning">&larr; Invoices
            </RouterLink>
        </h3>

        <ul class="nav nav-tabs" id="myTab" role="tablist">

            <li class="nav-item" role="presentation">
                <button class="nav-link" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane"
                    type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">Main</button>
            </li>

            <li v-if="financeStore.purchaseTransaction.id" class="nav-item" role="presentation">
                <button class="nav-link" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-tab-pane"
                    type="button" role="tab" aria-controls="profile-tab-pane" aria-selected="false">Items <span
                        v-if="financeStore.purchaseTransaction.items"
                        class="badge text-bg-secondary">{{
                                financeStore.purchaseTransaction.items.length
                        }}</span></button>
            </li>

            <li v-if="financeStore.purchaseTransaction.id" class="nav-item" role="presentation">
                <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact-tab-pane"
                    type="button" role="tab" aria-controls="contact-tab-pane" aria-selected="false">Docs</button>
            </li>

        </ul>

        <div class="tab-content mt-4" id="myTabContent">

            <div class="tab-pane fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab"
                tabindex="0">
                <form @submit.prevent="savePurchaseTransaction">
                    <input type="hidden" name="MAX_FILE_SIZE" value="20971520" />
                    <div class="row">

                        <div class="com-12 col-sm-6">
                            <label for="purchaseTransaction.venueID">Venue</label>
                            <select v-model="financeStore.purchaseTransaction.venueID" id="purchaseTransaction.venueID"
                                class="form-control" required>
                                <option v-for="venue in venueStore.venues" :key="venue.id" :value="venue.id">{{
                                        venue.name
                                }}
                                </option>
                            </select>
                        </div>

                        <div class="com-12 col-sm-6">
                            <label for="purchaseTransaction.typeID">Type</label>
                            <select v-model="financeStore.purchaseTransaction.typeID" id="purchaseTransaction.typeID"
                                class="form-control" required>
                                <option v-for="purchaseType in financeStore.purchaseTransactionTypes"
                                    :key="purchaseType.id"
                                    :value="purchaseType.id">{{ purchaseType.name
                                    }}
                                </option>
                            </select>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-12 col-sm-6">
                            <label for="purchaseTransaction.reference">Reference</label>
                            <input type="text" v-model="financeStore.purchaseTransaction.reference"
                                id="purchaseTransaction.reference"
                                class="form-control" placeholder="eg Invoice Number" required>
                        </div>
                        <div class="col-12 col-sm-6">
                            <label for="purchaseTransaction.taxDate">Date</label>
                            <input type="date" v-model="financeStore.purchaseTransaction.taxDate"
                                id="purchaseTransaction.taxDate"
                                class="form-control" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 col-sm-6">
                            <label for="purchaseTransaction.total">Total</label>
                            <input type="text" v-model="financeStore.purchaseTransaction.total"
                                id="purchaseTransaction.total"
                                class="form-control" required>
                        </div>
                        <div class="col col-sm-6">

                        </div>
                    </div>

                    <button v-if="!financeStore.purchaseTransaction.posted"
                        class="btn btn-sm btn-primary mt-2">Save</button>

                </form>
            </div>

            <div class="tab-pane fade" id="profile-tab-pane" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
                <h4>Items <span v-if="!financeStore.purchaseTransaction.posted" class="addNew">
                        | <button class="btn btn-sm btn-warning"
                            @click="addNew = !addNew">&plus;</button>
                    </span>
                </h4>
                <form v-if="addNew" @submit.prevent="addNewItem">
                    <div class="row">
                        <div class="col-12 col-sm-6">
                            <label for="newitem.month">Month</label>
                            <input type="number" min="1" max="12" v-model="newitem.month" id="newitem.month"
                                class="form-control mx-1">
                        </div>
                        <div class="col-12 col-sm-6">
                            <label for="newitem.year">Year</label>
                            <input type="number" min="2016" :max="nextYear" v-model="newitem.year"
                                id="newitem.year" class="form-control mx-1">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12 col-sm-6">
                            <label for="newitem.total">Total <span v-if="newitemTargetTotal">Max {{
                                    newitemTargetTotal
                            }}</span></label>

                            <div class="totalInput position-relative">
                                <input type="text" v-model="newitem.total" id="newitem.total"
                                    class="form-control mx-1">
                                <div v-if="newitemTargetLoading"
                                    class="spinner-grow spinner-grow-sm text-primary position-absolute top-50 end-0 translate-middle"
                                    role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>

                        </div>
                        <div class="col-12 col-sm-6">
                            <label for="newitem.vatCode">VAT Code</label>
                            <select v-model="newitem.vatCode" id="newitem.vatCode" class="form-control">
                                <option v-for="vatCode in financeStore.vatCodes" :value="vatCode.code">{{ vatCode.name
                                }}
                                </option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-sm btn-info mt-2">Add</button>
                </form>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Period</th>
                            <th>VatCode</th>
                            <th class="text-end">Total</th>
                            <th>...</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in financeStore.purchaseTransaction.items" :key="item.id">
                            <td>{{ item.month }} / {{ item.year }}</td>
                            <td>{{ item.vatCode }}</td>
                            <td class="text-end">{{ Math.abs(item.total.toFixed(2)) }}</td>
                            <td><i class="bi bi-trash text-danger" @click="deletePurchaseItem(item)"></i></td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th>Item Total</th>
                            <th class="text-end" colspan="2">{{ Math.abs(financeStore.purchaseItemsTotal.toFixed(2)) }}
                            </th>
                            <th>&nbsp;</th>
                        </tr>
                        <tr>
                            <th>Transaction Total</th>
                            <th class="text-end" colspan="2">
                                <span v-if="financeStore.purchaseTransaction">
                                    {{
                                            Math.abs(parseFloat(financeStore.purchaseTransaction.total).toFixed(2)).toFixed(2)
                                    }}
                                </span>
                            </th>
                            <th>&nbsp;</th>
                        </tr>
                        <tr>
                            <th>Balance to Allocate</th>
                            <th class="text-end" colspan="2">
                                {{ (Math.abs(parseFloat(financeStore.purchaseTransaction.total)) -
                                        Math.abs(financeStore.purchaseItemsTotal)).toFixed(2)
                                }}
                            </th>
                            <th>&nbsp;</th>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="tab-pane fade" id="contact-tab-pane" role="tabpanel" aria-labelledby="contact-tab" tabindex="0">
                <form @submit.prevent="savePurchaseDocument">
                    <label for="purchaseTransactionFile">Add a Doc</label>
                    <input id="purchaseTransactionFile"
                        class="form-control" type="file" required />
                    <button class="btn btn-sm btn-info mt-2 transactionDocUploadBtn">
                        <div v-if="uploading" class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span v-else>
                            <i class="bi bi-send"></i>
                            Upload
                        </span>
                    </button>
                </form>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Name</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="fileName in financeStore.purchaseTransaction.files" :key="fileName">
                            <td>
                                <span class="fileDownloadLink" @click="downloadFile(fileName)">{{ fileName }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

        </div>

    </div>
</template>
<style scoped>
.transactionDocUploadBtn {
    width: 70px;
}

.fileDownloadLink {
    cursor: pointer;
}
</style>