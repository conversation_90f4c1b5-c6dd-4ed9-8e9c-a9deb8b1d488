<script setup>
    import { ref, computed, onBeforeMount } from 'vue';
    import { useTeamStore } from '@/stores/teamStore';
    import { useLeagueStore } from '@/stores/leagueStore';
    import { useAlertStore } from '@/stores/alertStore';
    
    const teamStore = useTeamStore();
    const leagueStore = useLeagueStore();
    const alertStore = useAlertStore();
    const loading = ref(false);
    const search = ref();
    const leagueName = (leagueID) => {
        for (let l in leagueStore.leagues) {
            if (leagueStore.leagues[l].id == leagueID) return leagueStore.leagues[l].name;
        }
    }
    // const teams = computed( () => {
    //     if (!search.value) return teamStore.teams;
    //     return teamStore.teams.filter( (team) => {
    //         return (team.name.includes(search.value) );
    //     })
    // });
    const pageCount = computed( () => {
        return (teamStore.total > 0 && teamStore.fetchCriteria.limit > 0) ? Math.ceil(teamStore.total / teamStore.fetchCriteria.limit) : null;
    })
    const isCurrentSearchPage = (p) => p == teamStore.searchPage;
    const teamSearch = async () => {
        loading.value = true;
        teamStore.fetchCriteria.start = 1;
        await teamStore.searchTeams();
        loading.value = false;
    }
    const paginateSearch = async (pageNo) => {
        loading.value = true;
        teamStore.searchPage = teamStore.fetchCriteria.start = pageNo;
        await teamStore.searchTeams();
        loading.value = false;
    }
    onBeforeMount( async () => {
        loading.value = true;
        teamStore.searchText = null;
        let result = await teamStore.searchTeams();
        if (result !== true) alertStore.add('warning','Ooops',result);
        loading.value = false;
    })
</script>
<template>
    <div class="venues">
        <div class="container">
            <h3>Teams | <RouterLink to="/team" class="btn btn-sm btn-warning">&plus;</RouterLink></h3>
            <div v-if="loading" class="spinner-border text-success" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div v-else class="table-responsive">
                <form @submit.prevent="teamSearch" class="search d-flex">
                    <input type="search" v-model="teamStore.searchText" id="searchText" class="form-control" placeholder="Find...">
                    <button class="btn btn-sm btn-info">Go</button>
                </form>
                <nav v-if="pageCount > 1" aria-label="Page Navigation for Teams" class="mt-2">
                    <ul class="pagination flex-wrap">
                        
                        <li v-for="p in pageCount" :key="p" class="page-item" :class="{active: p==teamStore.fetchCriteria.start}" :aria-current="isCurrentSearchPage(p)? 'page':null"><a class="page-link" href="#" @click="paginateSearch(p)">{{ p }}</a></li>
                        
                    </ul>
                </nav>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>League</th>
                            <th>Capt.</th>
                            <th>Trsr.</th>
                            <th>...</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="team in teamStore.teams" :key="team.id">
                            <td>{{ team.name }}</td>
                            <td>{{ leagueName(team.leagueID) }}</td>
                            <td><span v-if="team.managers.captain">{{ team.managers.captain.email }}</span></td>
                            <td><span v-if="team.managers.treasurer">{{ team.managers.treasurer.email }}</span></td>
                            <td><RouterLink :to="'/team/'+team.id"><i class="bi bi-pencil"></i></RouterLink></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>