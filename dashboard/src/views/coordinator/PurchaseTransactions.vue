<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useProfileStore } from '@/stores/profileStore';
import { useVenueStore } from '@/stores/venueStore';
import { useFinanceStore } from '@/stores/financeStore';
const profileStore = useProfileStore();
const venueStore = useVenueStore();
const financeStore = useFinanceStore();
const loading = ref(false);
const amendingStatus = ref(null);
const searchText = ref(null);

const filteredPurchases = computed(() => {
    let returnVals = [];
    if (profileStore.profile.isManager || profileStore.profile.isAuthor) {
        returnVals = financeStore.purchaseTransactions;
    } else {
        return financeStore.purchaseTransactions.filter((p) => venueStore.isCoordinatorVenue(p.venueID, profileStore.profile.id))
    }
    if (searchText.value) {
        returnVals = returnVals.filter((venue) => {
            return (venue.venueName.toLowerCase().includes(searchText.value.toLowerCase()) || venue.reference.toLowerCase().includes(searchText.value.toLowerCase()) || venue.coordinatorName.toLowerCase().includes(searchText.value.toLowerCase()));
        })
    }
    return returnVals;
})

const formatDate = (date) => {
    let thisDate = new Date(date);
    let returnDate = '';
    returnDate += (thisDate.getDate() <= 9) ? "0" + thisDate.getDate() : thisDate.getDate();
    returnDate += "/";
    returnDate += (thisDate.getMonth() < 9) ? "0" + (thisDate.getMonth() + 1) : thisDate.getMonth() + 1;
    returnDate += "/" + thisDate.getFullYear();
    return returnDate;
}

const getVenueName = (venueID) => {
    let venue = venueStore.getVenue(venueID);
    return (venue) ? venue.name : venueID;
}

const getTransactionTypeName = (transactionTypeID) => {
    let transactionType = financeStore.getTransactionType(transactionTypeID);
    return (transactionType) ? transactionType.name : transactionTypeID;
}

const postTransaction = async (purchaseTransaction) => {
    amendingStatus.value = purchaseTransaction.id;
    await financeStore.postPurchaseTransaction(purchaseTransaction.id);
    amendingStatus.value = null;
}

const unpostTransaction = async (purchaseTransaction) => {
    amendingStatus.value = purchaseTransaction.id;
    await financeStore.unpostPurchaseTransaction(purchaseTransaction.id);
    amendingStatus.value = null;
}

onBeforeMount(async () => {
    loading.value = true;
    await financeStore.fetchPurchaseTransactions();
    console.log(financeStore.purchaseTransactions);
    loading.value = false;
})
</script>

<template>
    <div class="purchaseInvoices container-fluid">
        <h3>Venue Invoices | <RouterLink to="/purchase-transaction" class="btn btn-sm btn-warning">&plus;</RouterLink>
        </h3>

        <div v-if="loading" class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div v-else class="table-responsive">
            <form @submit.prevent="">
                <label for="searchText">Search</label>
                <input type="search" id="searchText" v-model="searchText" class="form-control">
            </form>
            <table class="table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Venue</th>
                        <th>Coordinator</th>
                        <th>Ref</th>
                        <th class="text-end">Total</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="purchaseTransaction in filteredPurchases"
                        :class="{ 'text-danger': purchaseTransaction.typeID == 4 }">

                        <td :title="purchaseTransaction.taxDate">{{ formatDate(purchaseTransaction.taxDate) }}</td>

                        <td>{{ getTransactionTypeName(purchaseTransaction.typeID) }}</td>

                        <td>
                            <RouterLink :to="'/venue/' + purchaseTransaction.venueID">{{
                                    purchaseTransaction.venueName
                            }}</RouterLink>
                        </td>

                        <td>{{ purchaseTransaction.coordinatorName }}</td>

                        <td>{{ purchaseTransaction.reference }}</td>

                        <td class="text-end">
                            <span v-if="purchaseTransaction.total">
                                {{ purchaseTransaction.total }}
                            </span>
                        </td>

                        <td style="cursor:pointer;">
                            <div v-if="amendingStatus === purchaseTransaction.id" class="spinner-grow spinner-grow-sm"
                                role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div v-else>
                                <RouterLink
                                    :to="'/purchase-transaction/' + purchaseTransaction.id"
                                    :title="purchaseTransaction.canPost">
                                    <i v-if="!purchaseTransaction.posted"
                                        class="bi bi-pencil me-1"></i>
                                    <i v-else class="bi bi-eye me-1"></i>
                                </RouterLink>
                                <i v-if="purchaseTransaction.imported" class="bi bi-check-square text-success"
                                    title="Imported"></i>
                                <i v-else-if="purchaseTransaction.posted" class="bi bi-check-square text-warning"
                                    title="Posted"
                                    @click="unpostTransaction(purchaseTransaction)"></i>
                                <i v-else-if="purchaseTransaction.canPost === true" class="bi bi-send"
                                    @click="postTransaction(purchaseTransaction)" title="Ready to Post"></i>
                            </div>
                        </td>

                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>