<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useVenueStore } from '@/stores/venueStore';
import { useProfileStore } from '@/stores/profileStore';
// import { useUserStore } from '@/stores/userStore';
const venueStore = useVenueStore();
const profileStore = useProfileStore();
// const userStore = useUserStore();
const formFilter = ref({});
const searchtext = ref(null);
const fetchingData = ref(false);
const venueData = ref([]);
// const coordinatorID = ref();

const nextYear = computed(() => {
    let thisDate = new Date();
    return parseInt(thisDate.getFullYear()) + 1;
})

const filteredVenues = computed(() => {
    let returnVals = [];
    if (profileStore.profile.isManager || profileStore.profile.isAuthor) {
        returnVals = venueData.value;
    } else {
        for (let v in venueData.value) {
            if (venueData.value[v].coordinatorID == profileStore.profile.id) returnVals.push(venueData.value[v]);
        }
    }
    if (searchtext.value) {
        returnVals = returnVals.filter((venue) => {
            return (venue.venueName.toLowerCase().includes(searchtext.value.toLowerCase()) || venue.coordinator.name.toLowerCase().includes(searchtext.value.toLowerCase()));
        })
    }
    return returnVals;
});

const totals = computed(() => {
    let values = {
        bookings: 0,
        transactions: 0
    }
    // console.log("Filtered", filteredVenues.value);
    filteredVenues.value.forEach((filteredVenue) => {
        if (!isNaN(values.bookings)) values.bookings += parseFloat(filteredVenue.bookings)
        if (!isNaN(values.transactions)) values.transactions += parseFloat(filteredVenue.transactions)
    })
    return values;
})

const fetchVenues = async () => {
    fetchingData.value = true;
    let date = new Date();
    if (!formFilter.value.month) {
        let chosenMonth = date.getMonth();
        if (chosenMonth == 0) chosenMonth = 12;
        formFilter.value.month = chosenMonth;
    }
    if (!formFilter.value.year) formFilter.value.year = formFilter.value.year = parseInt(date.getFullYear());
    venueData.value = await venueStore.venuesReport(formFilter.value.year, formFilter.value.month);
    fetchingData.value = false;
}

onBeforeMount(async () => {
    await fetchVenues();
    // if (profileStore.profile.isManager || profileStore.profile.isAuthor) {
    //     await userStore.fetchCoordinators();
    // } else coordinatorID.value = profileStore.profile.id;
})

</script>
<template>
    <div class="venuesTransactionReport container-fluid">
        <h3>Venue Invoice Report | <RouterLink to="/purchase-transaction" class="btn btn-sm btn-warning">&plus;
            </RouterLink>
        </h3>
        <form class="d-flex align-items-center" @submit.prevent="fetchVenues">
            <label for="formFilter.month">Month</label>
            <input type="number" min="1" max="12" v-model="formFilter.month" id="formFilter.month" class="form-control">
            <label for="formFilter.year">Year</label>
            <input type="number" min="2016" :max="nextYear" v-model="formFilter.year" id="formFilter.year"
                class="form-control">
            <button class="btn btn-sm btn-info mt-2">Go</button>
        </form>
        <form @submit.prevent="">
            <label for="searchtext">Search</label>
            <input type="search" id="searchtext" v-model="searchtext" class="form-control">
        </form>
        <!-- <form class="d-flex align-items-center" v-if="profileStore.profile.isManager || profileStore.profile.isAuthor"
            @submit.prevent="coordinatorFilter">
            <label for="coordinatorID">Coordinator</label>
            <select v-model="coordinatorID" id="coordinatorID" class="form-control">
                <option value="">All</option>
                <option v-for="coordinator in userStore.coordinators" :value="coordinator.id">{{ coordinator.name }}
                </option>
            </select>
        </form> -->
        <div v-if="fetchingData" class="spinner-border text-success" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div v-else class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Venue</th>
                        <th>Coordinator</th>
                        <th class="text-end">Bookings</th>
                        <th class="text-end">Invoices</th>
                        <th class="text-end">Balance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="venue in filteredVenues">
                        <td>
                            <RouterLink :to="'/venue/' + venue.venueID">{{
                                    venue.venueName
                            }}</RouterLink>
                        </td>
                        <td><span v-if="venue.coordinator">{{ venue.coordinator.name }}</span></td>
                        <td class="text-end">
                            <span v-if="venue.bookings && !isNaN(venue.bookings)">
                                {{ venue.bookings.toFixed(2) }}
                            </span>
                        </td>
                        <td class="text-end">
                            <span v-if="venue.transactions && !isNaN(venue.transactions)">
                                {{ venue.transactions.toFixed(2) }}
                            </span>
                        </td>
                        <td class="text-end" :class="{ 'text-danger': venue.balance < 0 }">{{ venue.balance.toFixed(2)
                        }}</td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="2">Totals</th>
                        <th class="text-end">{{ totals.bookings.toFixed(2) }}</th>
                        <th class="text-end">{{ totals.transactions.toFixed(2) }}</th>
                        <th class="text-end">{{ (totals.bookings - totals.transactions).toFixed(2) }}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</template>
