<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useFinanceStore } from '@/stores/financeStore';

const thisDate = new Date();

const loading = ref(false);
const formFilter = ref(
    {
        month: parseInt(thisDate.getMonth()) + 1,
        year: parseInt(thisDate.getFullYear()),
    }
);

const financeStore = useFinanceStore();

const nextYear = computed(() => {
    return parseInt(thisDate.getFullYear()) + 1;
})

const filteredPrepays = computed(() => {
    if (!formFilter.value.month || !formFilter.value.year || formFilter.value.month < 1 || formFilter.value.month > 12 || formFilter.value.year < 2016 || formFilter.value.year > nextYear.value) return financeStore.prepays;
    let returnVal = [];
    for (let p in financeStore.prepays) {
        if (financeStore.prepays[p].month == parseInt(formFilter.value.month) && financeStore.prepays[p].year == parseInt(formFilter.value.year)) returnVal.push(financeStore.prepays[p]);
    }
    return returnVal;
})

const totalPrePay = computed(() => {
    let total = 0;
    filteredPrepays.value.forEach((prepay) => total += parseFloat(prepay.total))
    return total;
})

const formatDate = (date) => {
    let thisDate = new Date(date);
    let returnDate = '';
    returnDate += (thisDate.getDate() <= 9) ? "0" + thisDate.getDate() : thisDate.getDate();
    returnDate += "/";
    returnDate += (thisDate.getMonth() < 9) ? "0" + (thisDate.getMonth() + 1) : thisDate.getMonth() + 1;
    returnDate += "/" + thisDate.getFullYear();
    return returnDate;
}

onBeforeMount(async () => {
    loading.value = true;
    await financeStore.fetchPrePays();
    loading.value = false;
})
</script>
<template>
    <div class="purchaseTransaction container-fluid">
        <h3>Card Payment Transactions</h3>
        <div v-if="loading" class="spinner-grow" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div v-else class="prepay-transactions-list">
            <form class="d-flex align-items-center">
                <label for="formFilter.month">Month</label>
                <input type="number" min="1" max="12" v-model="formFilter.month" id="formFilter.month"
                    class="form-control">
                <label for="formFilter.year">Year</label>
                <input type="number" min="2016" :max="nextYear" v-model="formFilter.year" id="formFilter.year"
                    class="form-control">
            </form>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Venue</th>
                            <th>Sage Code</th>
                            <th>Inv. No.</th>
                            <th>Period</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="prepay in filteredPrepays" :key="prepay.id">
                            <td>{{ formatDate(prepay.taxDate) }}</td>
                            <td>
                                <RouterLink :to="'/venue/' + prepay.venueID">
                                    {{ prepay.venueName }}
                                </RouterLink>
                            </td>
                            <td>{{ prepay.sageAccount }}</td>
                            <td>{{ prepay.reference }}</td>
                            <td>{{ prepay.month }}/{{ prepay.year }}</td>
                            <td class="text-end">{{ parseFloat(prepay.total).toFixed(2) }}</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <th colspan="5">Total</th>
                        <th class="text-end">
                            {{ totalPrePay }}
                        </th>
                    </tfoot>
                </table>
            </div>
        </div>

    </div>
</template>
<style scoped>
.transactionDocUploadBtn {
    width: 70px;
}

.fileDownloadLink {
    cursor: pointer;
}
</style>