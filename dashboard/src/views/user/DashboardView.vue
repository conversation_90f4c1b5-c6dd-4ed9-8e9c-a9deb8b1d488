<script setup>
import { onBeforeMount } from 'vue';
import { useProfileStore } from '@/stores/profileStore';
import { useLeagueStore } from '@/stores/leagueStore';
import { useVenueStore } from '@/stores/venueStore';
const profileStore = useProfileStore();
const leagueStore = useLeagueStore();
const venueStore = useVenueStore();
onBeforeMount(async () => {
    await leagueStore.getLeagues();
    await venueStore.getVenues();
    await profileStore.fetchProfile();
})
</script>
<template>
    <div class="dashboardView">
    </div>
</template>