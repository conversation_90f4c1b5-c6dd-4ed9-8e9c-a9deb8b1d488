<template>
  <div class="login container">
    <h1 class="mt-4">Login</h1>

    <!-- LOGIN FORM -->
    <form v-if="!requires2FA" @submit.prevent="authenticate" class="form-signin">
      <label for="email" class="sr-only">Email address</label>
      <input
        type="email"
        id="email"
        v-model="email"
        class="form-control"
        placeholder="Email address"
        required
        autofocus
      />
      <label for="password" class="sr-only">Password</label>
      <input
        type="password"
        id="password"
        v-model="password"
        class="form-control"
        placeholder="Password"
        required
      />
      <button
        id="loginBtn"
        class="btn btn-sm btn-primary btn-block mt-2"
        type="submit"
        :disabled="loading"
      >
        <div v-if="loading" class="spinner-border spinner-border-sm text-light"></div>
        <span v-else>Sign in</span>
      </button>
    </form>

    <!-- 2FA VERIFICATION FORM -->
    <form v-else @submit.prevent="verify2FA" class="form-signin">
      <label for="code" class="sr-only">Enter 2FA Code</label>
      <input
        type="text"
        id="code"
        v-model="code"
        class="form-control"
        placeholder="Enter the verification code"
        required
        autofocus
      />
      <button
        id="verifyBtn"
        class="btn btn-sm btn-success btn-block mt-2"
        type="submit"
        :disabled="loading"
      >
        <div v-if="loading" class="spinner-border spinner-border-sm text-light"></div>
        <span v-else>Verify</span>
      </button>
    </form>

    <p class="mt-4">
      Forgotten password? Click <router-link to="/remind">here</router-link> to get a reminder.
    </p>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { useProfileStore } from '@/stores/profileStore'
  import { useAlertStore } from '@/stores/alertStore'

  const email = ref('')
  const password = ref('')
  const code = ref('')
  const loading = ref(false)
  const requires2FA = ref(false) // Toggle between login and 2FA input
  const router = useRouter()
  const profile = useProfileStore()
  const alerts = useAlertStore()

  // Login Function
  const authenticate = async () => {
    loading.value = true
    const response = await profile.login(email.value, password.value)

    if (response === true) {
      alerts.add('success', 'Success', 'You are logged in')
      router.push('/dashboard')
    } else if (response === '2FA_REQUIRED') {
      requires2FA.value = true // Show 2FA input
    } else {
      alerts.add('danger', 'Oops', 'Login failed.')
    }

    loading.value = false
  }

  // 2FA Verification Function
  const verify2FA = async () => {
    loading.value = true
    const success = await profile.verify2FA(code.value, email.value)

    if (success) {
      alerts.add('success', 'Success', 'You are fully authenticated!')
      router.push('/dashboard')
    } else {
      alerts.add('danger', 'Oops', 'Invalid 2FA code.')
    }

    loading.value = false
  }
</script>
