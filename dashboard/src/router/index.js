import { createRouter, createWebHistory } from 'vue-router'
import { useProfileStore } from '@/stores/profileStore'
import { useAlertStore } from '@/stores/alertStore'

import HomeView from '../views/guest/HomeView.vue'
import LoginView from '@/views/Guest/LoginView.vue'
import RemindView from '@/views/Guest/RemindView.vue'
import ActivateView from '@/views/Guest/ActivateView.vue'
// import RegisterView from '@/views/Guest/RegisterView.vue'

// import PaymentView from '@/views/Guest/PaymentView.vue'
// import PaidView from '@/views/Guest/PaidView.vue'

import DashboardView from '@/views/user/DashboardView.vue'
// import ProfileView from '@/views/User/ProfileView.vue'
import PasswordView from '@/views/User/PasswordView.vue'

import VenuesView from '@/views/Coordinator/VenuesView.vue'
import VenueView from '@/views/Coordinator/VenueView.vue'
import TeamsView from '@/views/Coordinator/TeamsView.vue'
import TeamView from '@/views/Coordinator/TeamView.vue'
import TeamManagementView from '@/views/Coordinator/TeamManagementView.vue'

import PurchaseTransactionView from '@/views/Coordinator/PurchaseTransaction.vue'
import PurchaseTransactionsView from '@/views/Coordinator/PurchaseTransactions.vue'
import VenuesTransactionReportView from '@/views/Coordinator/VenuesTransactionReport.vue'
import VenuePrePayTransactionsView from '@/views/Coordinator/VenuePrePayTransactions.vue'
import PurchaseTransactionFilesView from '@/views/Coordinator/PurchaseTransactionFiles.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
  },
  {
    //   path: '/register',
    //   name: 'register',
    //   component: RegisterView
    // },
    // {
    path: '/remind',
    name: 'remind',
    component: RemindView,
  },
  {
    path: '/activate/:email?/:code?',
    name: 'activate',
    component: ActivateView,
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: DashboardView,
  },
  // {
  //   path: '/profile',
  //   name: 'profile',
  //   component: ProfileView
  // },
  {
    path: '/password',
    name: 'password',
    component: PasswordView,
  },
  {
    path: '/venues',
    name: 'VenuesView',
    component: VenuesView,
  },
  {
    path: '/venue/:id?',
    name: 'VenueView',
    component: VenueView,
  },
  {
    path: '/teams',
    name: 'TeamsView',
    component: TeamsView,
  },
  {
    path: '/team/:id?',
    name: 'TeamView',
    component: TeamView,
  },
  {
    path: '/team-management/',
    name: 'TeamManagementView',
    component: TeamManagementView,
  },
  {
    path: '/purchase-transaction/:id?',
    name: 'PurchaseTransactionView',
    component: PurchaseTransactionView,
  },
  {
    path: '/purchase-transactions',
    name: 'PurchaseTransactionsView',
    component: PurchaseTransactionsView,
  },
  {
    path: '/venues-transaction-report',
    name: 'VenuesTransactionReportView',
    component: VenuesTransactionReportView,
  },
  {
    path: '/venues-prepay-transactions',
    name: 'VenuePrePayTransactionsView',
    component: VenuePrePayTransactionsView,
  },
  {
    path: '/purchase-transaction-files',
    name: 'PurchaseTransactionFiles',
    component: PurchaseTransactionFilesView,
  },
  // {
  //   path: '/paid',
  //   name: 'paid',
  //   component: PaidView
  // },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

router.beforeEach(to => {
  const navbarToggler = document.querySelector('.navbar-toggler')
  const navbarCollapse = document.querySelector('.navbar-collapse')
  if (navbarToggler) navbarToggler.classList.add('collapsed')
  if (navbarCollapse) {
    navbarToggler.setAttribute('aria-expanded', false)
    navbarCollapse.classList.remove('show')
  }
  const profile = useProfileStore()
  const guestPaths = ['/', '/activate', '/login', '/remind']
  if (profile.jwt && guestPaths.includes(to.path)) return { path: '/dashboard' }
  if (guestPaths.includes(to.path)) return true
  if (!profile.jwt) {
    const alertStore = useAlertStore()
    alertStore.add('warning', 'Security', 'You must be logged in')
    return { path: '/login' }
  }
  return true
})

export default router
