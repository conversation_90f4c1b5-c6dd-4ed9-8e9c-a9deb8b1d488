import { defineStore } from 'pinia'
import { useProfileStore } from '@/stores/profileStore'

export const useUserStore = defineStore('userStore', {
  state: () => ({
    users: [],
    user: {},
    coordinators: [],
    fetchCriteria: {
      start: 1,
      limit: 20,
    },
  }),
  actions: {
    async getUsers() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/users'
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.users = data
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    async fetchUser(userID) {
      return userID
    },
    getUser(userID) {
      for (let u in this.users) {
        if (this.users[u].id == userID) {
          this.user = this.users[u]
          return this.users[u]
        }
      }
    },
    async saveUser() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/user'
      let method = this.user.id ? 'PATCH' : 'POST'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let body = JSON.stringify(this.user)
      try {
        let response = await fetch(url, {
          method,
          headers,
          body,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.user = data
          return true
        } else return data
      } catch (err) {
        return err
      }
    },
    async searchUsers(searchText) {
      if (searchText) this.searchText = searchText
      this.user = {}
      if (!this.searchText || this.searchText.length == 0) {
        await this.getUsers()
        return true
      }
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/user-search/' +
        this.fetchCriteria.limit +
        '/' +
        (this.fetchCriteria.start - 1)
      let method = 'POST'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let body = JSON.stringify({
        searchText: this.searchText,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
          body,
        })
        let data = await response.json()
        console.log(data)
        if (response.status == 200) {
          this.users = data.data
          this.total = data.total
          this.count = data.count
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    async fetchCoordinators() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'admin.v3.api.' +
        profileStore.rootDomain +
        '/coordinators'
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.coordinators = data
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
  },
  persist: true,
})
