import { defineStore } from 'pinia'

export const useProfileStore = defineStore('profileStore', {
  state: () => ({
    endPoints: {
      reminder: '/reminder',
      login: '/authenticate',
      activate: '/activation',
      logout: '/logout',
      password: '/password',
      profile: '/profile',
    },
    production: true,
    system: {
      name: 'L4Y Dash',
    },
    jwt: null,
    auth: {},
    remind: {},
    registration: {},
    activation: {},
    changePassword: {},
    profile: {},
    cookie: {
      path: '/',
    },
    reference: {},
    logging: [],
    captureLogging: false,
  }),
  getters: {
    rootDomain() {
      if (
        this.production == false ||
        location.host.substring(0, 9) == '127.0.0.1' ||
        location.host.substring(0, 9) == 'localhost' ||
        location.host.substring(0, 27) == 'dashboard.leagues4you.local'
      ) {
        this.production = false
        return 'leagues4you.co.uk'
      }
      return 'leagues4you.co.uk'
    },
    rootProtocol() {
      return window.location.href.indexOf('staging') > -1
        ? 'https://staging.'
        : window.location.href.indexOf('local') > -1
        ? 'http://local.'
        : 'https://'
    },
    apiEndpoint() {
      return this.rootProtocol + '//q.' + this.rootDomain
    },
  },
  actions: {
    loggingAdd(text) {
      if (this.captureLogging === true) this.logging.push(text)
    },
    async login(email, password) {
      const url = this.rootProtocol + 'public.v2.api.' + this.rootDomain + '/authenticate'

      try {
        let response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, password }), // Güncellenmiş email ve password doğrudan gönderiliyor
        })

        let data = await response.json()

        if (data?.success === true) {
          return '2FA_REQUIRED'
        } else {
          console.log('Login Failed', url, { email, password })
          return data
        }
      } catch (err) {
        console.log('Login Failed', url, { email, password })
        return err
      }
    },
    logout() {
      const url = this.rootProtocol + 'user.v3.api.' + this.rootDomain + this.endPoints.logout
      this.jwt = null
      this.profile = {}
      this.storeJWT()
      return true
    },
    async register() {
      const url = this.rootApi.public + 'register'
      let status
      try {
        let response = await fetch(url, {
          method: 'POST',
          body: JSON.stringify(this.registration),
        })
        status = response.status
        if (status != 200) {
          return await response.json()
        }
        this.activation.email = this.registration.email
        this.registration = {}
        return true
      } catch (err) {
        console.log(err)
      }
    },
    async activate() {
      const url = this.rootProtocol + 'public.v3.api.' + this.rootDomain + this.endPoints.activate
      let method = 'POST'
      let body = JSON.stringify({
        email: this.activation.email,
        code: this.activation.code,
      })
      let status
      try {
        let response = await fetch(url, {
          method,
          body,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.jwt = data.token
          this.storeJWT(data)
        }
      } catch (err) {
        console.log(err)
      }
    },

    // VERIFY 2FA CODE
    async verify2FA(code, email) {
      const url = this.rootProtocol + 'public.v2.api.' + this.rootDomain + this.endPoints.activate
      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email, code }),
        })
        const data = await response.json()

        if (data?.success?.token && data.success.token.length > 0) {
          const token = data.success.token
          this.jwt = token
          this.storeJWT(token)
          return true
        } else {
          console.log('2FA Verification failed:', data)
          return false
        }
      } catch (err) {
        console.error('2FA Verification error:', err)
        return false
      }
    },
    async reminder() {
      const url = this.rootProtocol + 'public.v3.api.' + this.rootDomain + this.endPoints.reminder
      let status
      try {
        let response = await fetch(url, {
          method: 'POST',
          body: JSON.stringify(this.remind),
        })
        status = response.status
        if (status != 200) {
          return await response.json()
        } else this.activation.email = this.remind.email
        return true
      } catch (err) {
        console.log(err)
      }
    },
    async savePassword() {
      const url = this.rootProtocol + 'user.v3.api.' + this.rootDomain + this.endPoints.password
      try {
        let response = await fetch(url, {
          headers: new Headers({
            Authorization: 'Bearer ' + this.jwt,
          }),
          method: 'PATCH',
          body: JSON.stringify(this.changePassword),
        })
        let status = response.status
        let data = await response.json()
        if (status == 200) {
          this.changePassword = {}
          return true
        } else return data
      } catch (err) {
        return err
      }
    },
    async fetchProfile() {
      this.profile = {}
      if (!this.jwt) return
      const url = this.rootProtocol + 'admin.v3.api.' + this.rootDomain + this.endPoints.profile
      let headers = new Headers({
        Authorization: 'Bearer ' + this.jwt,
      })
      let response
      try {
        response = await fetch(url, {
          method: 'GET',
          headers: headers,
        })
        if (!response.ok || response.status != 200) return 'Connection Failed'
        let data = await response.json()
        this.profile = data
        return true
      } catch (err) {
        this.jwt = null
        this.storeJWT()
        return err
      }
    },
    saveProfile() {
      const url = this.rootApi.user + 'profile'
      const method = 'PATCH'
      let status
      fetch(url, {
        method: method,
        headers: new Headers({
          Authorization: 'Bearer ' + this.jwt,
        }),
        body: JSON.stringify(this.profile),
      })
        .then(response => {
          status = response.status
        })
        .catch(err => console.error('Status', status, 'Error', err))
    },
    fetchJwt() {
      if (this.jwt) return this.jwt
      let cname = 'jwt'
      let name = cname + '='
      let decodedCookie = decodeURIComponent(document.cookie)
      let ca = decodedCookie.split(';')
      for (let i = 0; i < ca.length; i++) {
        let c = ca[i]
        while (c.charAt(0) == ' ') {
          c = c.substring(1)
        }
        if (c.indexOf(name) == 0) {
          var rlt = c.substring(name.length, c.length)
          this.jwt = rlt
          return rlt
        }
      }
    },
    fetchReferenceData() {
      let url = this.rootApi.user + 'reference'
      fetch(url, {
        method: 'GET',
        headers: new Headers({
          Authorization: 'Bearer ' + this.jwt,
        }),
      })
        .then(response => {
          return response.json()
        })
        .then(data => (this.reference = data))
        .catch(err => console.error(err))
    },
    storeJWT(jwt) {
      if (jwt && jwt.token) {
        let cookieText =
          'jwt=' + jwt.token + '; path=/; domain=.' + this.rootDomain + '; expires=' + jwt.expires
        document.cookie = cookieText
      } else {
        document.cookie =
          'jwt=;path=/; domain=.' +
          this.rootDomain +
          ';expires=Thu, 01 Jan 1970 00:00:01 GMT; Max-Age=-99999999;'
      }
    },
  },
  persist: true,
})
