import { defineStore } from 'pinia'
import { useProfileStore } from '@/stores/profileStore'

export const useVenueStore = defineStore('venueStore', {
  state: () => ({
    venues: [],
    venue: {},
    purchaseTerms: [
      // { id: 1, name: 'Recurring', description: 'Billing is automated eg via stored card or direct debit' },
      {
        id: 2,
        name: 'Card Payment',
        description: 'Payment is taken from card on account',
      },
      {
        id: 3,
        name: 'Pay on Invoice',
        description: 'Payment required for full invoice value',
      },
      {
        id: 4,
        name: 'Pre-pay',
        description: 'Pay prior to booking commencement',
      },
    ],
  }),
  actions: {
    async getVenues() {
      const profileStore = useProfileStore()
      const url = profileStore.rootProtocol + 'user.v3.api.' + profileStore.rootDomain + '/venues'
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.venues = data
          return true
        }
        console.log(data)
        return data
      } catch (err) {
        console.log(err)
        return err
      }
    },
    getVenue(venueID) {
      for (let v in this.venues) {
        if (this.venues[v].id == venueID) return (this.venue = this.venues[v])
      }
      return venueID
    },
    async saveVenue() {
      const profileStore = useProfileStore()
      const url = profileStore.rootProtocol + 'user.v3.api.' + profileStore.rootDomain + '/venue'
      let method = this.venue.id ? 'PATCH' : 'POST'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let body = JSON.stringify(this.venue)
      try {
        let response = await fetch(url, {
          method,
          headers,
          body,
        })
        let data = await response.json()

        console.log('Status', response.status)
        if (response.status == 200) {
          this.venue = data
          return true
        } else return data
      } catch (err) {
        return err
      }
    },
    async fetchAccrual(venueID, month, year) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'admin.v3.api.' +
        profileStore.rootDomain +
        '/venue-monthly-accrual/' +
        venueID +
        '/' +
        year +
        '/' +
        month
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let response = await fetch(url, {
        headers,
        method,
      })
      let data = await response.json()
      if (response.status == 200) {
        return parseFloat(data.subTotal)
      } else return data
    },
    async venuesReport(year, month) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'admin.v3.api.' +
        profileStore.rootDomain +
        '/venues-monthly-accrual/' +
        year +
        '/' +
        month
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let response = await fetch(url, {
        headers,
        method,
      })
      let data = await response.json()
      console.log(data)
      if (response.status == 200) return data
    },
    coordinatorVenues(coordinatorID) {
      return this.venues.filter(v => v.coordinatorID == coordinatorID)
    },
    isCoordinatorVenue(venueID, coordinatorID) {
      if (!venueID || !coordinatorID) return false
      console.log('Does Venue', venueID, 'belong to', coordinatorID)
      let venuesToCheck = this.coordinatorVenues(coordinatorID)
      console.log('Checking', venuesToCheck)
      for (let v in venuesToCheck) {
        if (venuesToCheck[v].id == venueID) return true
      }
      return false
    },
  },
  // persist: true,
})
