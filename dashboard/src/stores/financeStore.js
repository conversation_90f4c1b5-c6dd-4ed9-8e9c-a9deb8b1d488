import { defineStore } from 'pinia'
import { useProfileStore } from '@/stores/profileStore'

export const useFinanceStore = defineStore('financeStore', {
  state: () => ({
    purchaseTransactions: [],
    purchaseTransaction: {},
    transactionTypes: [
      { id: 1, ledger: 'Sales', name: 'Invoice' },
      { id: 2, ledger: 'Sales', name: 'Credit' },
      { id: 3, ledger: 'Purchase', name: 'Invoice' },
      { id: 4, ledger: 'Purchase', name: 'Credit' },
    ],
    vatCodes: [
      { id: 1, code: 'T0', name: 'T0 (0% VAT)' },
      { id: 2, code: 'T1', name: 'T1 (20% VAT)' },
      { id: 3, code: 'T2', name: 'T2 (VAT Exempt)' },
    ],
    purchaseTransactionImportFiles: [],
    prepays: [],
  }),
  getters: {
    purchaseTransactionTypes() {
      return this.transactionTypes.filter(transactionType => transactionType.ledger == 'Purchase')
    },
    salesTransactionTypes() {
      return this.transactionTypes.filter(transactionType => transactionType.ledger == 'Sales')
    },
    purchaseItemsTotal() {
      let total = 0
      for (let i in this.purchaseTransaction.items) {
        total += this.purchaseTransaction.items[i].total
      }
      return total
    },
  },
  actions: {
    async fetchPurchaseTransactions() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transactions'
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.purchaseTransactions = data
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    async savePurchaseTransaction() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction'
      const body = new FormData()
      body.append('purchaseInvoice', JSON.stringify(this.purchaseTransaction))
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'POST'
      let response = await fetch(url, {
        headers,
        method,
        body,
      })
      let data = await response.json()
      if (response.status == 200) {
        this.purchaseTransaction = data
        return true
      }
      return data
    },
    async savePurchaseDocument(filename) {
      if (filename.size > 20971520) return 'File is too large to upload'
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction-document/' +
        this.purchaseTransaction.id
      const body = new FormData()
      body.append('purchaseTransaction', filename)
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'POST'
      let response = await fetch(url, {
        headers,
        method,
        body,
      })
      let data = await response.json()
      if (response.status == 200) {
        this.purchaseTransaction = data
        return true
      }
      return data
    },
    async getPurchaseTransaction(transactionID) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction/' +
        transactionID
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          for (let p in this.purchaseTransactions) {
            if (this.purchaseTransactions[p].id == data.id) this.purchaseTransactions[p] = data
          }
          this.purchaseTransaction = data
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    async postPurchaseTransaction(transactionID) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction-post/' +
        transactionID
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          for (let p in this.purchaseTransactions) {
            if (this.purchaseTransactions[p].id == data.id) this.purchaseTransactions[p] = data
          }
          return true
        }
      } catch (err) {
        return err
      }
    },
    async unpostPurchaseTransaction(transactionID) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction-unpost/' +
        transactionID
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          for (let p in this.purchaseTransactions) {
            if (this.purchaseTransactions[p].id == data.id) this.purchaseTransactions[p] = data
          }
          return true
        }
      } catch (err) {
        return err
      }
    },
    async addNewPurchaseItem(item) {
      if (!item.month) return 'No month specified'
      if (!item.year) return 'No year specified'
      if (!item.total) return 'No total specified'
      if (!item.mainID) return 'No Purchase Transaction ID specified'
      if (!item.vatCode) return 'No VAT Code specified'
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction-item'
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'POST'
      const body = JSON.stringify(item)
      let response = await fetch(url, {
        headers,
        method,
        body,
      })
      let data = await response.json()
      if (response.status == 200) {
        for (let p in this.purchaseTransactions) {
          if (this.purchaseTransactions[p].id == data.id) this.purchaseTransactions[p] = data
        }
        this.purchaseTransaction = data
        return true
      }
      return data
    },
    async deletePurchaseItem(itemID) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/purchase-transaction-item/' +
        itemID
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'DELETE'
      let response = await fetch(url, {
        headers,
        method,
      })
      let data = await response.json()
      if (response.status == 200) {
        for (let p in this.purchaseTransactions) {
          if (this.purchaseTransactions[p].id == data.id) this.purchaseTransactions[p] = data
        }
        this.purchaseTransaction = data
        return true
      }
      return data
    },
    async fetchPrePays() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'admin.v3.api.' +
        profileStore.rootDomain +
        '/venue-prepay-transactions'
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'GET'
      let response = await fetch(url, {
        headers,
        method,
      })
      let data = await response.json()
      if (response.status == 200) {
        this.prepays = data
        return true
      }
      return data
    },
    async fetchPurchaseTransactionFiles() {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'admin.v3.api.' +
        profileStore.rootDomain +
        '/transaction-import-files'
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'GET'
      let response = await fetch(url, {
        headers,
        method,
      })
      let data = await response.json()
      if (response.status == 200) {
        this.purchaseTransactionImportFiles = data
        return true
      }
      return data
    },
    async resendPurhaseTransactionImportFile(filename) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'admin.v3.api.' +
        profileStore.rootDomain +
        '/resend-transaction-import-file/' +
        filename
      const headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      const method = 'GET'
      let response = await fetch(url, {
        headers,
        method,
      })
      return response.status == 200 ? true : await response.json()
    },
    getTransactionType(transactionTypeID) {
      for (let t in this.purchaseTransactionTypes) {
        if (this.purchaseTransactionTypes[t].id == transactionTypeID)
          return this.purchaseTransactionTypes[t]
      }
    },
  },
  // persist: true,
})
