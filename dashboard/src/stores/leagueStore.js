import { defineStore } from 'pinia'
import { useProfileStore } from '@/stores/profileStore'

export const useLeagueStore = defineStore('leagueStore', {
  state: () => ({
    leagues: [],
    league: {},
  }),
  actions: {
    async getLeagues() {
      const profileStore = useProfileStore()
      const url = profileStore.rootProtocol + 'user.v3.api.' + profileStore.rootDomain + '/leagues'
      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.leagues = data
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    getLeague(leagueID) {
      for (let l in this.leagues) {
        if (this.leagues[l].id == leagueID) {
          this.league = this.leagues[l]
          return this.leagues[l]
        }
      }
    },
    getLeagueName(leagueID) {
      let league = this.getLeague(leagueID)
      return league && league.name ? league.name : null
    },
    async saveLeague() {
      const profileStore = useProfileStore()
      const url = profileStore.rootProtocol + 'user.v3.api.' + profileStore.rootDomain + '/league'
      let method = this.venue.id ? 'PATCH' : 'POST'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let body = JSON.stringify(this.league)
      try {
        let response = await fetch(url, {
          method,
          headers,
          body,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.league = data
          return true
        } else return data
      } catch (err) {
        return err
      }
    },
  },
  persist: true,
})
