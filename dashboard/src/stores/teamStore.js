import { defineStore } from 'pinia'
import { useProfileStore } from '@/stores/profileStore'
import { useLeagueStore } from '@/stores/leagueStore'

export const useTeamStore = defineStore('teamStore', {
  state: () => ({
    teams: [],
    team: {},
    total: 0,
    count: 0,
    fetchCriteria: {
      start: 1,
      limit: 20,
    },
    searchText: null,
    searchPage: 1,
  }),
  actions: {
    async getTeams() {
      this.team = {}
      const leagueStore = useLeagueStore()
      if (leagueStore.leagues.length == 0) await leagueStore.getLeagues()
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/teams/' +
        this.fetchCriteria.limit +
        '/' +
        (this.fetchCriteria.start - 1)

      let method = 'GET'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.teams = data.data
          this.total = data.total
          this.count = data.count
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    async searchTeams(searchText) {
      if (searchText) this.searchText = searchText
      this.team = {}
      if (!this.searchText || this.searchText.length == 0) {
        await this.getTeams()
        return true
      }
      // this.fetchCriteria.start = 1;
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol +
        'user.v3.api.' +
        profileStore.rootDomain +
        '/team-search/' +
        this.fetchCriteria.limit +
        '/' +
        (this.fetchCriteria.start - 1)
      let method = 'POST'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let body = JSON.stringify({
        searchText: this.searchText,
      })
      try {
        let response = await fetch(url, {
          method,
          headers,
          body,
        })
        let data = await response.json()

        if (response.status == 200) {
          this.teams = data.data
          this.total = data.total
          this.count = data.count
          return true
        }
        return data
      } catch (err) {
        return err
      }
    },
    getTeam(teamID) {
      for (let t in this.teams) {
        if (this.teams[t].id == teamID) this.team = this.teams[t]
      }
    },
    async saveTeam() {
      const profileStore = useProfileStore()
      const url = profileStore.rootProtocol + 'user.v3.api.' + profileStore.rootDomain + '/team'
      let method = this.team.id ? 'PATCH' : 'POST'
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let body = JSON.stringify(this.team)
      try {
        let response = await fetch(url, {
          method,
          headers,
          body,
        })
        let data = await response.json()
        if (response.status == 200) {
          this.team = {}
          return true
        } else return data
      } catch (err) {
        return err
      }
    },
    async teamManagement(management) {
      const profileStore = useProfileStore()
      const url =
        profileStore.rootProtocol + 'user.v3.api.' + profileStore.rootDomain + '/team-management'
      const method = 'POST'
      const body = JSON.stringify({
        management,
      })
      let headers = new Headers({
        Authorization: 'Bearer ' + profileStore.jwt,
      })
      let response = await fetch(url, {
        headers,
        method,
        body,
      })
      let data = await response.json()
      if (response.status !== 200) return data
      return true
    },
  },
  persist: true,
})
