<script setup>
import { useProfileStore } from "@/stores/profileStore";
import { useRouter } from "vue-router";
const profileStore = useProfileStore();
const router = useRouter();
const logout = async () => {
    let result = await profileStore.logout();
    if (result === true) router.replace("/login");
};
</script>
<template>
    <nav class="navbar navbar-expand-sm bg-light">
        <div class="container-fluid">
            <RouterLink to="/" class="navbar-brand">Dashboard</RouterLink>
            <button
                class="navbar-toggler"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent"
                aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <RouterLink to="/" class="nav-link active" aria-current="page">Home</RouterLink>
                    </li>
                    <li class="nav-item">
                        <RouterLink
                            to="/venues"
                            class="nav-link">Venues</RouterLink>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Teams
                        </a>
                        <ul class="dropdown-menu">
                            <li class="nav-item">
                                <RouterLink
                                    to="/teams"
                                    class="nav-link">Listing</RouterLink>
                            </li>
                            <li class="nav-item">
                                <RouterLink
                                    to="/team-management"
                                    class="nav-link">Management</RouterLink>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Finance
                        </a>
                        <ul class="dropdown-menu">
                            <li class="nav-item">
                                <RouterLink
                                    to="/venues-transaction-report"
                                    class="nav-link">Venue Report</RouterLink>
                                <RouterLink
                                    to="/purchase-transactions"
                                    class="nav-link">Venue Invoices</RouterLink>
                                <RouterLink
                                    to="/venues-prepay-transactions"
                                    class="nav-link">Venue Cards</RouterLink>
                                <RouterLink
                                    to="/purchase-transaction-files"
                                    class="nav-link">Import Files</RouterLink>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
                            aria-expanded="false">
                            Me
                        </a>
                        <ul class="dropdown-menu">
                            <li class="">
                                <RouterLink
                                    to="/password"
                                    class="dropdown-item">Password</RouterLink>
                            </li>
                            <li class="">
                                <a class="dropdown-item" href="#" @click.prevent="logout">Logout</a>
                            </li>
                            <!-- <li><a class="dropdown-item" href="#">Another action</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#">Something else here</a></li> -->
                        </ul>
                    </li>
                    <li class="align-self-center">
                        <a :href="profileStore.rootProtocol + '//hub.' + profileStore.rootDomain"
                            class="btn btn-sm btn-warning">Hub</a>
                    </li>
                    <!-- <li class="nav-item">
            <a class="nav-link" href="#" @click.prevent="logout">Logout</a>
          </li> -->
                </ul>
            </div>
        </div>
    </nav>
</template>
