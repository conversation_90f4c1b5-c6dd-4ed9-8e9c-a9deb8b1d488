<script setup>
import { onBeforeMount } from 'vue';
import { RouterView, useRouter } from 'vue-router'
import GuestNav from '@/navs/Guest.Nav.vue';
import UserNav from '@/navs/User.Nav.vue';
import { useProfileStore } from '@/stores/profileStore';
import { useUserStore } from '@/stores/userStore';
import Alerts from './components/Alerts.vue';

const profileStore = useProfileStore();
const userStore = useUserStore();
const router = useRouter();
onBeforeMount(async () => {
    let result1 = await profileStore.fetchJwt();
    if (!result1) router.replace("/login");
    let result2 = await profileStore.fetchProfile();
    if (result2 !== true) {
        router.replace("/login");
    }
    userStore.fetchCoordinators();
})
</script>

<template>
    <header>
        <UserNav v-if="profileStore.jwt" />
        <GuestNav v-else />
    </header>
    <RouterView />
    <Alerts />
    <footer>

    </footer>
</template>
<style lang="scss">
@import "./node_modules/bootstrap/scss/bootstrap";
@import "@/styles/main.scss";
</style>  