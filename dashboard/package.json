{"name": "dashboard", "version": "0.0.0", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"bootstrap": "^5.2.1", "bootstrap-icons": "^1.9.1", "pinia": "^2.0.21", "pinia-plugin-persistedstate": "^2.3.0", "sass": "^1.54.9", "sass-loader": "^13.0.2", "vue": "^3.2.38", "vue-router": "^4.1.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^3.0.3", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "prettier": "^2.7.1", "vite": "^3.0.9"}}