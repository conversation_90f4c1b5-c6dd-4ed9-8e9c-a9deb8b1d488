<?php

define ("ROOT_FOLDER",strrev(strstr(strrev(dirname(__FILE__)),DIRECTORY_SEPARATOR)));
// define ("APP_FOLDER", ROOT_FOLDER . "core");
define ("CORE_FOLDER", ROOT_FOLDER . "core");

header("Access-Control-Allow-Origin: *");

$init_file = CORE_FOLDER . DIRECTORY_SEPARATOR . "init.php";
include($init_file);

if (!$_GET || !$_GET['url']) exit("Done 1");

$data = explode("/",$_GET['url']);

if (!isset($data[0]) || !$data[0]) exit("Done 2");

// $docType = DocLibraryType::FromSlug(urldecode($data[0]));

// if (!$docType) exit("Done 3");

// $docVersion = DocLibraryVersion::Fetch($docType);

// if (!$docVersion) exit("Done 4");

$folder = __DIR__ . DIRECTORY_SEPARATOR . "resources";

$files = [
    'netball-terms-and-conditions' => 'terms-2025.pdf',
    'policies-and-guidelines' => 'policies-and-guidelines-2025.pdf',
    'safeguarding' => 'safeguarding-2025.pdf',
    'netball-squad-guide' => 'netball-squad-guide.pdf'
];

$target = $folder . DIRECTORY_SEPARATOR . $files[$data[0]];

if (!file_exists($target)) exit("No file");

$content_type = mime_content_type($target);

// header("Content-type: $docVersion->contentType");
header("Content-type: $content_type");
// print $docVersion->filedata;
readfile($target);
exit(0);