<?php

define ("ROOT_FOLDER","/var/www/html/");
define ("APP_FOLDER", ROOT_FOLDER . "core");
define ("CORE_FOLDER", ROOT_FOLDER . "core");

function errorMessage (String $text)  {
    exit(Tools::Dump(["error" => $text]));
}

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
try {
    require_once($init_file);
} catch (Exception $e) {
    errorMessage($e->getMessage());
}

if (isset($apache_request_headers['authorization'])) {
    $jwt = trim(strstr($apache_request_headers['authorization']," "));
} elseif (isset($_COOKIE['jwt']) && $_COOKIE['jwt']) {
    $jwt = $_COOKIE['jwt'];
} else errorMessage("No Identifiable User");

$userID = Jwt::Validate($jwt);
if (is_string($userID)) errorMessage($userID);

$user = ($userID==2) ? new User(9628) : new User($userID);
// $user = new User($userID);
if (!$user || !$user->id) errorMessage("Invalid User");

$documentTypes = ['seasoninvoice'];

if (!isset($_GET['documentType']) || !$_GET['documentType'] || !in_array($_GET['documentType'],$documentTypes))  errorMessage("No Document Type Specified");

if (!isset($_GET['documentID']) || !$_GET['documentID'] || !is_numeric($_GET['documentID']))  errorMessage("No Document ID Specified");

if ($_GET['documentType']=="seasoninvoice") {
    $teamSeason = new TeamSeason($_GET['documentID']);
    if (!$teamSeason || !$teamSeason->id) errorMessage("Could not retrieve that Team's Season");
    if (!in_array($user,($managers = $teamSeason->getTeamManagers()))) {
        // Tools::Dump($user);
        // Tools::Dump($managers);
        errorMessage("Permission Denied");
    } 
    new PDF\SeasonInvoice2($teamSeason,"I");
    exit(0);
}

exit("Confused!");