<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- development version, includes helpful console warnings -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
        integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <title>Document</title>
    <style>
        .accept-button.disabled {
            cursor: no-drop;
        }

        .form-inline {
            display: flex;
            gap: .5em;
            margin: .75em 0;
            align-items: center;
        }

        .form-inline .search-postcode {
            width: 200px;
        }

        .form-inline .search-radius {
            width: 100px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <h1>{{ title }}</h1>
            <div v-if="(!processStage)" id="welcome">
                Sign-up procedure stages are:-
                <ul>
                    <li>League Selection</li>
                    <li>Captain</li>
                    <li>Treasurer (if different)</li>
                    <li>Terms & Conditions</li>
                    <li>Payment Info</li>
                </ul>
                <button class="btn btn-success" v-on:click="(processStage++)">Start</button>
            </div>
            <div v-if="(processStage ==1)" id="leagueSelection">
                <h4>Choose a League</h4>
                <form class="form-inline">
                    Search within
                    <input type="number" v-model="search.radius" class="search-radius form-control" id="search.radius">
                    miles of
                    <input type="text" v-model="search.postcode" class="search-postcode form-control"
                        id="search.postcode" placeholder="Postcode">
                    <button type="button" class="btn btn-info">Search</button>
                </form>
                <div class="leagueList">
                    <div v-for="league in leagues" class="form-check">
                        <input class="form-check-input" name="leagueID" v-model="leagueID" type="radio"
                            :id="'league_'+league.id" :value="league.id">
                        <label class="form-check-label" :for="'league_'+league.id">
                            {{ league.name }}
                        </label>
                    </div>
                </div>
                <button class="btn btn-warning" v-on:click="(processStage = null)">&lt; Start Again</button>
                <button class="btn btn-success" v-on:click="(processStage++)">Captain &gt;</button>
            </div>
            <div v-if="(processStage == 2)" id="captainData">
                <h4>Captain</h4>
                <label for="captain.firstname">Firstname</label>
                <input type="text" id="captain.firstname" v-model="captain.firstname" class="form-control"
                    placeholder="First Name" required>
                <label for="captain.lastname">Lastname</label>
                <input type="text" id="captain.lastname" v-model="captain.lastname" class="form-control"
                    placeholder="Last Name" required>
                <label for="captain.email">Email</label>
                <input type="email" id="captain.email" v-model="captain.email" class="form-control" placeholder="email"
                    required>
                <label for="captain.mobile">Mobile</label>
                <input type="tel" id="captain.mobile" v-model="captain.mobile" class="form-control"
                    placeholder="Mobile">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="captain.isTreasurer"
                        v-model="captain.isTreasurer" value="1">
                    <label class="form-check-label" for="captain.isTreasurer">Act as Treasurer?</label>
                </div>
                <button class="btn btn-danger" v-on:click="(processStage = null)">Start Again</button>
                <button class="btn btn-warning" v-on:click="(processStage--)">&lt; Choose League</button>
                <button v-if="captain.isTreasurer" class="btn btn-success" v-on:click="(processStage+=2)">Terms
                    &gt;</button>
                <button v-else class="btn btn-success" v-on:click="(processStage++)">Treasurer &gt;</button>
            </div>
            <div v-if="(processStage == 3)" id="treasurerData">
                <h4>Treasurer</h4>
                <label for="treasurer.firstname">Firstname</label>
                <input type="text" id="treasurer.firstname" v-model="treasurer.firstname" class="form-control"
                    placeholder="First Name" required>
                <label for="treasurer.lastname">Lastname</label>
                <input type="text" id="treasurer.lastname" v-model="treasurer.lastname" class="form-control"
                    placeholder="Last Name" required>
                <label for="treasurer.email">Email</label>
                <input type="email" id="treasurer.email" v-model="treasurer.email" class="form-control"
                    placeholder="email" required>
                <label for="treasurer.mobile">Mobile</label>
                <input type="tel" id="treasurer.mobile" v-model="treasurer.mobile" class="form-control"
                    placeholder="Mobile">
                <button class="btn btn-danger" v-on:click="(processStage = null)">Start Again</button>
                <button class="btn btn-warning" v-on:click="(processStage--)">Back</button>
                <button class="btn btn-success" v-on:click="(processStage++)">Terms</button>
            </div>
            <div v-if="(processStage == 4)" id="termsAndConditions">
                <h4>Terms and Conditions v{{ terms.version }}</h4>
                <textarea name="" id="" cols="30" rows="10" class="form-control" disabled>{{ terms.text }}
                </textarea>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="termsAccepted" v-model="termsAccepted"
                        value="1">
                    <label class="form-check-label" for="captain.isTreasurer">Accept Terms</label>
                </div>
                <div class="accept-button" :class="{disabled:!termsAccepted}">
                    <button class="btn btn-success" v-on:click="(processStage++)" :disabled="(!termsAccepted)">Accept
                        &gt;</button>
                </div>
                <div class="altButtons mt-4">
                    <button class="btn btn-danger" v-on:click="(processStage = null)">Start Again</button>
                    <button class="btn btn-warning" v-on:click="(processStage--)">&lt; Treasurer</button>
                </div>
            </div>
            <div v-if="(processStage == 5)" id="paymentDetails">
                <h4>Payment Details</h4>
                <button class="btn btn-success" v-on:click="processStage++">Next</button>
                <button class="btn btn-danger" v-on:click="(processStage = null)">Clear</button>
            </div>
            <div v-if="(processStage == 6)" id="review">
                <h4>Review</h4>
                <button class="btn btn-success" v-on:click="submit">Next</button>
                <button class="btn btn-danger" v-on:click="(processStage = null)">Clear</button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>
    <script>
        var app = new Vue({
            el: '#app',
            data: {
                title: 'League Signup',
                processStage: null,
                leagueID: null,
                termsAccepted: null,
                terms: {
                    version: "1.0",
                    text: 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Repellendus placeat fuga porro aliquid odit, libero vitae velit, optio quidem, nobis tempore dicta incidunt distinctio vero vel temporibus quaerat deserunt iusto! \
                        Recusandae neque perspiciatis ea nemo necessitatibus rem tempora sed voluptatem quidem optio cupiditate voluptate fugit adipisci, sapiente culpa non delectus eligendi magni voluptatum ipsum iure hic amet harum quibusdam.Laboriosam! \
                        Modi excepturi dolorum architecto inventore expedita, magnam dolor maxime odit corrupti at sit sunt magni aliquid ad, corporis a iste doloribus distinctio ipsam omnis, sint sapiente animi blanditiis eum.Quo ? \
                        Hic voluptatum similique, veritatis explicabo sequi reprehenderit corrupti iure error voluptatem nesciunt harum quia et omnis cum illum tempora, dolore esse molestias sapiente, nulla asperiores culpa unde.Aliquam, adipisci reiciendis? \
                        Sunt voluptatem quod exercitationem eum quo, maxime optio odit provident eligendi libero neque obcaecati pariatur est ipsa eos tenetur vero, quaerat expedita itaque tempora facilis magnam! Provident, quia soluta.Aliquid.\
                        Culpa, cumque placeat voluptas sapiente consectetur illum non nemo amet asperiores vel voluptatum enim recusandae laudantium explicabo harum.Ipsum nisi natus exercitationem cum fuga distinctio ipsam minus minima quidem praesentium? \
                Laudantium in porro, necessitatibus eveniet illo omnis explicabo quas voluptate quaerat impedit facere cumque natus consequuntur dignissimos odit enim cum neque, maiores iste at repellat harum ipsa sed quasi.Nulla! \
                Aspernatur labore beatae consequatur quia, dignissimos corrupti a pariatur quis dolor animi est eius dolorem totam reprehenderit recusandae quas nostrum perspiciatis soluta laudantium? Officiis laborum, deleniti adipisci blanditiis cum quae. \
                Est commodi nemo suscipit inventore eius, voluptatem voluptate corporis exercitationem! Soluta unde nihil debitis sapiente laborum.Inventore praesentium incidunt, voluptates, numquam labore vitae unde id ipsa quam sint, asperiores repellat! \
                Accusamus suscipit repudiandae nulla aperiam accusantium amet facere repellendus veniam velit similique quod est voluptatum asperiores tempore architecto, corrupti id necessitatibus soluta fuga expedita ab.Sequi recusandae possimus omnis qui. \
                Eveniet facere animi nulla cum pariatur dolores obcaecati consectetur dolor exercitationem, tempora consequatur minus odio suscipit explicabo, cupiditate impedit accusamus laboriosam! Non voluptates iure facilis, consequatur ullam exercitationem aut quasi. \
                Doloremque praesentium, modi maxime facilis hic voluptates ipsa iusto earum fugiat aliquid nobis non voluptatum nesciunt dolore labore cum tenetur consequatur tempora corrupti quidem amet animi! Autem ad repudiandae corrupti. \
                Accusantium neque incidunt adipisci accusamus, eos doloribus eligendi corporis nam at nihil hic error, quae suscipit, praesentium doloremque.Alias praesentium sequi consequuntur vero id voluptates illum aliquid natus delectus obcaecati. \
                Accusamus quis, placeat officia sint enim distinctio aspernatur aperiam repellendus maxime accusantium ad sunt beatae nihil? Quam, officia, nulla similique suscipit voluptas facilis eum repellat quos, veniam distinctio iure nisi. \
                Id cumque nisi, voluptate nam, iusto quisquam perferendis repellendus beatae veritatis fuga reiciendis blanditiis facilis, eius nesciunt ducimus culpa saepe.Officia officiis natus recusandae.Quasi qui quia enim omnis eaque. \
                Dolorem itaque natus obcaecati ipsa error ut sed quas reprehenderit quo? Vero laborum eaque amet suscipit totam possimus labore consequatur, officiis dolor pariatur! Deleniti illum voluptate quas provident, mollitia itaque! \
                Aliquam alias delectus tempore aspernatur excepturi ratione fuga laboriosam quas quaerat quidem amet, ipsa maiores dolor in consequuntur, blanditiis dolorem veritatis debitis iure, dicta nemo tempora sint atque! Commodi, recusandae. \
                Atque veritatis officiis quos modi? Eligendi in amet, esse ipsum perferendis asperiores, nostrum labore harum, aut officia atque.Temporibus dolorum nam corporis, voluptatum quisquam porro numquam dolorem adipisci sint atque! \
                Ea nisi fuga placeat voluptas laboriosam fugiat voluptate officiis natus ad, in at sint officia ducimus quaerat incidunt ipsum adipisci.Incidunt assumenda eos soluta eveniet.Obcaecati nisi voluptatem nihil itaque! \
                Delectus reiciendis odio ex quo harum quae iusto labore ut beatae cum.Dicta ipsum quaerat vel cum vitae minima blanditiis esse nemo quidem laborum incidunt, ipsa, maiores, necessitatibus earum inventore. \
                        Tempore, nesciunt? Veritatis odit repudiandae quas porro tenetur culpa, voluptates, quaerat atque delectus, facere commodi voluptatem facilis? Optio tempora inventore delectus.Vitae, in.Culpa iure totam ab, tenetur quasi consequatur. \
                Quo incidunt tenetur explicabo doloribus laborum dolor architecto eius voluptatem blanditiis repellendus, alias exercitationem, consequuntur ipsam temporibus enim, eligendi numquam.Aut laborum temporibus voluptates aliquid asperiores.Earum in unde error! \
                Voluptatem autem voluptas id dolorem cupiditate doloremque consequatur error minus, odio laudantium, harum maxime cum commodi ut, iure eaque porro optio cumque corrupti nisi quas nobis quis atque mollitia? Quasi! \
                Accusantium expedita atque eligendi itaque sed neque nemo ullam iste repellat, alias quod, iure optio illo tenetur eius debitis sit deleniti impedit quia fugit consectetur, suscipit nihil sequi quae! Molestias. \
                Impedit inventore ad non! Magnam aliquid id quos dolor quo nihil explicabo quas assumenda eaque magni exercitationem sequi mollitia minima vero illum corrupti pariatur consequuntur, quae eligendi ullam cupiditate harum! \
                Placeat velit, tempora ea nulla harum enim similique dignissimos dolorum est, dolorem quos at exercitationem deleniti blanditiis ipsa inventore qui unde modi provident nihil iure! Mollitia, harum! Totam, optio necessitatibus. \
                Et alias eum illo eveniet! Vel perspiciatis necessitatibus, exercitationem accusantium fuga quod, illum consectetur temporibus, rem tempora id repellendus voluptate modi dicta enim! In deserunt, praesentium laudantium magnam sunt explicabo? \
                Quia sequi nemo voluptas commodi? Repudiandae a reprehenderit est distinctio eius ipsum architecto provident incidunt voluptatum.In voluptatum nihil quisquam exercitationem molestiae soluta itaque quaerat.Illo asperiores in ea est. \
                Ipsa obcaecati laboriosam dignissimos veritatis nam facere a nisi placeat repellendus aliquid pariatur temporibus ea saepe ipsam, eveniet possimus dolores dolor quam.Eos, debitis.Optio quibusdam consectetur officiis eum id. \
                Obcaecati iure dicta itaque alias mollitia dolorum molestias numquam natus minus necessitatibus, magni hic? Doloremque atque, quaerat consequuntur ex eum sequi architecto sapiente temporibus dolorum illo, laboriosam veritatis consequatur sed? \
                Quos blanditiis quibusdam quasi voluptate officia quidem qui eos dignissimos, nemo est mollitia, iusto assumenda sequi, nobis nostrum.Debitis ipsum enim beatae animi neque minus cupiditate facere aliquid assumenda rem. \
                Eaque enim illum dolor totam? Blanditiis natus recusandae laborum libero reiciendis eaque placeat cum! Impedit unde culpa eveniet, ut quod tenetur rerum iusto ab nostrum facere labore voluptas tempora optio? \
                Ut vel et ipsa quae aliquid fugiat nulla doloremque unde dolorum ullam, quia necessitatibus architecto saepe est deserunt harum, totam expedita dolores.Labore rem vitae tempore placeat! Iusto, quia fugit.\
                        Quaerat, architecto atque natus fugiat assumenda nam ratione odit corrupti blanditiis maiores, soluta eligendi, dolor nulla dicta iure ullam optio quos consequatur.Provident eligendi, sapiente dolore blanditiis odio iusto omnis! \
                Natus non velit perspiciatis et sint quidem, obcaecati quisquam expedita autem quae? Accusantium qui ea tenetur cum, excepturi, odio cupiditate itaque labore fugiat aperiam, alias aspernatur porro placeat quos earum! \
                Ipsum, maiores quisquam! Eum sit soluta iure deserunt, sed sunt eius accusantium vitae aut similique commodi ab quaerat architecto quis fugit? Sunt velit et aliquid porro pariatur vero labore quo.\
                Ratione sapiente, iste architecto soluta doloremque possimus excepturi laudantium cum consectetur nobis illum sit error assumenda aspernatur voluptatibus? Voluptatem consectetur veritatis deleniti delectus perferendis veniam minus mollitia at quia.Laborum.\
                At quaerat provident aut velit, dolorum ducimus earum similique molestias quas labore rem commodi nesciunt, cumque harum necessitatibus nulla a? Rem nesciunt, cumque vitae tempore ad nemo dicta omnis quidem!\
                Recusandae, aut.Voluptatem quas recusandae dolores, reiciendis inventore at aperiam dicta quaerat ea facilis beatae commodi sit sapiente quos quo harum in? Doloribus nihil id nisi dicta cupiditate molestiae obcaecati.\
                Impedit repudiandae eaque est, facilis, repellat ea, officia voluptas aut corrupti maxime optio rem.Quibusdam beatae fugit nemo aspernatur voluptatibus, dolorum deleniti error rem repellendus perferendis soluta minima magnam praesentium!\
                Dolore, veniam doloribus autem necessitatibus quo nemo totam, eaque voluptatibus, distinctio perferendis aperiam magni.Ipsum natus a dolorem ab modi quia.Dolores perspiciatis earum nisi, alias nulla quis veritatis rerum!\
                Tempore, nihil.Voluptatem ex est esse tempora, sapiente iste ducimus, delectus impedit sed aliquam maiores quos numquam laborum accusantium culpa atque eveniet obcaecati perferendis optio architecto.Earum, quis! Quas, officiis.\
                Quasi saepe recusandae porro nostrum? Voluptates corporis rem soluta commodi voluptatem repellat eligendi quos provident officia id dignissimos mollitia at aspernatur, quod ex? Iusto omnis mollitia magni deleniti? In, eum.\
                Quia ea nihil rerum facilis asperiores quo corrupti modi id fuga temporibus illum necessitatibus molestiae ducimus doloremque adipisci numquam hic voluptatibus enim excepturi, dicta nulla quisquam maiores, nesciunt est.Commodi!\
                Mollitia adipisci tenetur suscipit numquam quasi corporis necessitatibus hic maxime expedita quis est, aliquam nostrum eveniet minima pariatur, quo doloremque! Animi sunt eos deserunt numquam cupiditate quo, quas placeat consectetur?\
                Nam eveniet amet cupiditate quaerat officia error itaque id, tempore voluptates ratione qui possimus nemo sit perferendis excepturi! Ipsam mollitia velit dolorum molestiae fuga ab ullam ad et iusto pariatur.\
                Laboriosam molestiae possimus inventore vero? Voluptate delectus repellat ratione.Veritatis officia possimus enim repudiandae eius, dolore in cupiditate expedita, voluptatem quae nisi autem excepturi, aspernatur illum magni quas est consequatur.\
                Sed magnam quae consequatur sequi minima, porro minus, provident sunt obcaecati recusandae, optio eum placeat iste corrupti ea dolorum quisquam assumenda.Molestiae quae consequatur, soluta omnis hic fugit debitis doloribus.\
                        Temporibus, maxime dolorum, impedit atque fugit voluptatem molestias unde eaque commodi explicabo, illo recusandae libero quia perferendis dolorem harum minus omnis non laudantium? Ipsa accusantium quae fugiat id quo pariatur?\
                Pariatur, dolorum! Voluptas ab deserunt cumque, odio consequuntur tempora optio, odit excepturi doloremque sunt natus corporis voluptatem recusandae eligendi impedit soluta necessitatibus! Alias nisi voluptatibus iste velit deserunt provident deleniti.'
                },
                search: {
                    radius: 25,
                    postcode: null,
                },
                leagues: [
                    { id: 1, name: "League 1", },
                    { id: 2, name: "League 2", },
                    { id: 3, name: "League 3", },
                    { id: 4, name: "League 4", },
                    { id: 5, name: "League 5", },
                    { id: 6, name: "League 6", },
                    { id: 7, name: "League 7", },
                    { id: 8, name: "League 8", },
                    { id: 9, name: "League 9", },
                    { id: 10, name: "League 10", },
                ],
                captain: {
                    firstname: null,
                    lastname: null,
                    email: null,
                    mobile: null,
                    isTreasurer: null,
                },
                treasurer: {
                    firstname: null,
                    lastname: null,
                    email: null,
                    mobile: null
                },
            },
            computed: {
            },
            created: function () {
            },
            watch: {
                filterText: function (val) {
                },
            },
            methods: {
                readCookie(cname) {
                    let name = cname + "=";
                    let decodedCookie = decodeURIComponent(document.cookie);
                    let ca = decodedCookie.split(';');
                    for (let i = 0; i < ca.length; i++) {
                        let c = ca[i];
                        while (c.charAt(0) == ' ') {
                            c = c.substring(1);
                        }
                        if (c.indexOf(name) == 0) {
                            var rlt = c.substring(name.length, c.length);
                            return rlt;
                        }
                    }
                },
                fetchApplication() {
                },
            }
        })
    </script>
</body>

</html>