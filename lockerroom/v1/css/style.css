body, html {
    height: 100%;
}
body {
    min-height: 100%;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    /* color: #fff; */
}
label {
    margin:0;
}
.navbar {
    background-color: #171e37; 
    /* margin-bottom: 1rem; */
}
body.home {
    background-image: url("../img/laughing-women-1.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #cccccc;
    
}
body.user {
    background-image: url("../img/lockerroom2.png");
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #cccccc;
}
.dashnav a {
    color: #fff;
}
.dashnav span {
    font-size: 1rem;
}
.dashnav img {
    width: 100px; height: 100px;
}
.btn-purple {
    background-color: purple;
    color: #fff;
    text-transform: uppercase;
}
/* main {
    padding-top: 50px;
    min-height: 100vh;
} */
main.home, main.user {
    /* padding-top: 2rem; */
    background-color: rgba(100,100,100,.65);
    height: 100%;
    color: #fff;
}
.home-container {
    font-size: 1.2rem;
}
form .formSubmit {
    height: 2rem;
}
.documentTable {
    min-width: 400px;
    max-width: 650px;
    width: 100%;
}
.documentTable td {
    padding: .5rem;
}
#payments-tab a {
    color: #fff;
}
#payments-tab a.active {
    background-color: purple;
}
.bg-gray-75 {
    background-color: rgba(200,200,200,.75);
}
.bg-dark-30 {
    background-color: rgba(0,0,0,.3);
}
.max-width-500 {
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}
/* Stripe */
/**
 * The CSS shown here will not be introduced in the Quickstart guide, but shows
 * how you can use CSS to style your Element's container.
 */
 .StripeElement {
    background-color: white;
    height: 40px;
    padding: 10px 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    box-shadow: 0 1px 3px 0 #e6ebf1;
    -webkit-transition: box-shadow 150ms ease;
    transition: box-shadow 150ms ease;
    display: flex;
    flex-direction: column;
  }
  
  .StripeElement--focus {
    box-shadow: 0 1px 3px 0 #cfd7df;
  }
  
  .StripeElement--invalid {
    border-color: #fa755a;
  }
  
  .StripeElement--webkit-autofill {
    background-color: #fefde5 !important;
  }
/* main.user {
    background-color: rgba(255,255,255,.75);
    color: #333;
} */
/* form > button {
    margin-top: 1rem;
} */
.font-weight-bolder {
    font-weight: 600!important;
}
.btn-xl {
    padding: 1rem 3rem;
    font-size: 1.85rem;
    border-radius: .4rem;
}
.registrationSteps > *, .registrationSteps > *:link {
    padding-bottom: .5rem;
    border-bottom: 2px solid transparent;
    font-size: 1.5rem;
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
    cursor: pointer;
    color: white;
}
.registrationSteps > *:hover {
    color: thistle;
    border-bottom: 2px solid thistle;
    text-decoration: none;
}
.registrationSteps > *.active {
    color: #EE82EE;
    border-bottom: 2px solid #EE82EE;
}
.btn-pink {
    background-color: #EE82EE;
}
.max-300-600 {
    max-width: 300px;
}
@media (min-width:400px) {
    .max-300-600 {
        max-width: 600px;
    }
}
@media (max-width: 768px) {
    .display-1 {
        font-size: 4rem;
    }
}
/* Icon Grid */
.iconGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
}
.iconGrid img {
    width: 20vw;
}
/* dashboardGrid */
.dashboardGrid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
}
.dashboardGrid .dashItem {
    width: 20vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff;
}
.dashboardGrid .dashItem img {
    width: 100%;
    max-width: 150px;
}
@media (max-width: 480px) {
    .dashTxt {
        font-size: .9rem;
    }
}
/* Profile Form */
.profileForm {
    max-width: 500px;
    margin: 1rem auto 0 auto;
}
.formSection {
    display: flex;
    justify-content: space-between;
}
@media (max-width: 600px) {
    .profileForm {
        max-width: none;
    }
    .formSection {
        flex-direction: column;
    }
}