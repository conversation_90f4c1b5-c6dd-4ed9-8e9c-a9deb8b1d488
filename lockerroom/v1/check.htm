
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Locker Room | 0.8.0</title>
    <base href="https://lockerroom.leagues4you.co.uk/">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <!-- <link href="https://fonts.googleapis.com/css?family=Roboto:400,700,900&display=swap" rel="stylesheet"> -->
    <link href="https://fonts.googleapis.com/css2?family=Mukta&family=Satisfy&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="./css/style.css">
    <script src="https://js.stripe.com/v3/"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
</head><body class="user"><main class="user"><nav class="navbar navbar-dark">
  <a class="navbar-brand" href="./User" title="Locker Room">
    <img src="./img/logo.png" alt="" style="max-height: 50px;">
  </a>
  <div class="ml-auto" style="font-size: 1.5rem;">
    LOCKER ROOM
  </div>
  <a href="./User/Logout" class="btn btn-sm btn-purple ml-4">Logout</a>
</nav><!-- Modal -->
<!-- Dashboard Nav -->
<div class="dashboardGrid">
    <a class="dashItem" href="./User/Profile">
        <img class="dashImg" src="./img/icon-profile.png" alt="">
        <span class="dashTxt">My Profile</span>
    </a>
    <a class="dashItem" href="./User/Teams">
        <img class="dashImg" src="./img/icon-teams.png" alt="">
        <span class="dashTxt">My Teams</span>
    </a>
    <a class="dashItem" href="./User/Payments">
        <img class="dashImg" src="./img/icon-payments.png" alt="">
        <span class="dashText">My Payments</span>
    </a>
    <a class="dashItem" href="./User/Documents">
        <img class="dashImg" src="./img/icon-documents.png" alt="">
        <span class="dashTxt">Documents</span>
    </a>
</div><div class="container mt-2">
    <h1>Team | <a href="./User/Payments" class="btn btn-sm btn-purple">Payments</a></h1>
</div>
<style>
  :disabled {
    cursor: no-drop;
  }
  .greyBackground {
    background-color: rgba(150,150,150,.65); 
    padding: 2rem 2rem;
    border-radius: 1rem;
  }
  .formGroup {
    display: grid; 
    grid-template-columns: 100px 1fr 100px;
    margin: .25rem 0;
    grid-row-gap: .75rem;
    grid-column-gap: .5rem;
  }
  .formGroup > * {
    display: flex;
    align-items: center;
  }
  .formGroup > button {
    justify-content: center;
  }
  .teamHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  @media (max-width: 780px) {
    .formGroup {
      grid-template-columns: 1fr;
    }
    .teamHeader {
      flex-direction: column;
    }
  }
  .teamMessage {
    border-radius: .5rem;
    padding: .5rem;
    margin-bottom: .5rem;
  }
</style>
<div class="container greyBackground">
  <div class="teamHeader">
    <h3>Team Management</h3>
    <span class="bg-warning text-dark d-none teamMessage" id="teamMessage"></span>
  </div>
  
  <form action="/User/Teams" method="post">
    <div class="formGroup">
      <input type="hidden" name="team[id]" id="team.id" value="300">
      <label for="team.name">Team Name</label>
      <input data-leagueid="89" class="form-control" type="text" name="team[name]" id="team.name" value="Team C" readonly>
      <button type="button" id="btnNameChange" class="btn btn-info" title="Edit"><i class="far fa-edit"></i></button>
      <button type="submit" id="btnNameSave" class="d-none btn btn-success" title="Save" disabled><i class="fas fa-check-circle"></i></button>

      <label for="team.treasurer">Treasurer</label>
      <input class="form-control" style="display: inline-block;width: auto; flex:1;" type="email" name="team[treasurerEmail]" id="team.treasurer" value="<EMAIL>" readonly>
      <button type="button" id="btnTreasurerChange"  class="btn btn-info" title="Edit"><i class="far fa-edit"></i></button>
      <button type="submit" id="btnTreasurerSave" class="d-none btn btn-success" title="Save" disabled><i class="fas fa-check-circle"></i></button>
    </div>
  </form>

  <div class="teamHeader">
    <h3 class="my-4">Team Division</h3>
  </div>
  <style>
  #divisions .nav-tabs .nav-link, #fixtures .nav-tabs .nav-link {
      background-color: #ccc;
      color: #444;
      /* border: purple; */
    }
    #divisions .nav-tabs .nav-link.active, #fixtures .nav-tabs .nav-link.active {
      background-color: purple;
      color: #fff;
      border: purple;
    }
    .bg-turquoise {
      background-color: turquoise;
    }
  </style>
  <!-- Division Tabs -->
  <!-- <div id="divisions" class="col-md-12 col-sm-12 my-3">
      <ul class="nav nav-tabs" id="myTab" role="tablist">
          <li class="nav-item">
            <a class="nav-link active" id="current-table-control" data-toggle="tab" href="#current-table-division-1" role="tab" aria-controls="home" aria-selected="true">TABLE - DIVISION 1 - CURRENT SEASON</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="previous-table-control" data-toggle="tab" href="#previous-table-division-1" role="tab" aria-controls="profile" aria-selected="false">PREVIOUS TABLE - DIVISION 1</a>
          </li>
        </ul>
  </div> -->
  <!-- Division Tables -->
  <div class="col-md-12 col-sm-12">
  <div class="tabs tab-content">
    <div class="tab-pane fade show active" id="current-table-division-1" role="tabpanel" aria-labelledby="rounders-tab">
      <!-- display the content here-->
      <div class="table-responsive-md">                          
        <table class="table table-striped bg-light">
          <thead class="bg-turquoise text-light">
            <tr>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="League standing">#</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Team">Team</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Played">P</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Won">W</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Drawn">D</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Lost">L</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="For">F</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Against">A</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Goal DIfference">GD</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Win points">Pts</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Bonus points">BP</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Total points">Tot</th>
            </tr>
          </thead>
            <tbody>              <tr>
                <th scope="row">1</th>
                <td>Team A</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
              </tr>              <tr>
                <th scope="row">1</th>
                <td>Team B</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
              </tr>              <tr>
                <th scope="row">1</th>
                <td>Team C</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
              </tr>              <tr>
                <th scope="row">1</th>
                <td>Team D</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
              </tr>              <tr>
                <th scope="row">1</th>
                <td>Team E</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
              </tr>              <tr>
                <th scope="row">1</th>
                <td>Team F</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
              </tr>              <!-- <tr>
                <th scope="row">2</th>
                <td>Cheverons</td>
                <td>3</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>16</td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
              </tr>
              <tr>
                <th scope="row">3</th>
                <td>Secon Stars</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>10</td>
                <td>9</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">4</th>
                <td>Hillside</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>4</td>
                <td>6</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">5</th>
                <td>Shining Stars</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>4</td>
                <td>9</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">6</th>
                <td>Black Panthers</td>
                <td>3</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>1</td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>5</td>
              </tr>
              <tr>
                <th scope="row">7</th>
                <td>Glitterballs</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
              </tr>
              <tr>
                <th scope="row">8</th>
                <td>The Martley Crew</td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>5</td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
              </tr>
              <tr>
                <th scope="row">9</th>
                <td>Oddballs</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td>16</td>
                <td>0</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
              </tr>             -->
            </tbody>
          </table>
        </div>
        <!-- end content-->
      </div>
      <!-- Previous Division -->    
      <div class="tab-pane fade" id="previous-table-division-1" role="tabpanel" aria-labelledby="netball-tab">
        <!-- display the content here-->
        <div class="table-responsive-md">
          <table class="table table-striped bg-light">
            <thead class="bg-turquoise text-light">
              <tr>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="League standing">#</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Team">Team</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Played">P</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Won">W</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Drawn">D</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Lost">L</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="For">F</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Against">A</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Goal DIfference">GD</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Win points">Pts</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Bonus points">BP</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Total points">Tot</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th scope="row">1</th>
                <td>Martley Mavericks</td>
                <td>3</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
              </tr>
              <tr>
                <th scope="row">2</th>
                <td>Cheverons</td>
                <td>3</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
              </tr>
              <tr>
                <th scope="row">3</th>
                <td>Secon Stars</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">4</th>
                <td>Hillside</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">5</th>
                <td>Shining Stars</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">6</th>
                <td>Black Panthers</td>
                <td>3</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>5</td>
              </tr>
              <tr>
                <th scope="row">7</th>
                <td>Glitterballs</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
              </tr>
              <tr>
                <th scope="row">8</th>
                <td>The Martley Crew</td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
              </tr>
              <tr>
                <th scope="row">9</th>
                <td>Oddballs</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
              </tr>
            </tbody>
          </table>
          </div>
      <!-- end content-->
      </div>
  </div>
  </div>
  </div>
<script>
  let btnNameChange = document.getElementById("btnNameChange");
  let btnTreasurerChange = document.getElementById("btnTreasurerChange");
  let btnNameSave = document.getElementById("btnNameSave");
  let btnTreasurerSave = document.getElementById("btnTreasurerSave");
  let teamName = document.getElementById("team.name");
  let teamTreasurer = document.getElementById("team.treasurer");
  let teamMessage = document.getElementById("teamMessage");
  function enableNameEdit() {
    btnNameSave.classList.remove("d-none");
    teamName.readOnly = false;
    teamName.focus();
    btnNameChange.classList.add("d-none");
    teamMessage.innerHTML = "Enter a new team name and hit Save";
    teamMessage.classList.remove("d-none");
  }
  function enableTreasurerEdit() {
    btnTreasurerSave.classList.remove("d-none");
    teamTreasurer.readOnly = false;
    teamTreasurer.focus();
    btnTreasurerChange.classList.add("d-none");
    teamMessage.innerHTML = "Enter the Treasurer's email address and hit Save";
    teamMessage.classList.remove("d-none");
  }
  function saveTeamName() {
    console.log("Save Team ",teamName.value);
  }
  function checkTeamName(e) {
    var url = "https://api.leagues4you.co.uk/teamValidate/";
    url += e.target.dataset.leagueid + "/";
    url += e.target.value;
    fetch(encodeURI(url))
    .then(response => {
      if (response.status != 200) return response.json()
    })
    .then(data => {
      if (data) {
        teamMessage.innerHTML = "Cannot use that name " + data;
        btnNameSave.disabled = true;
      } else {
        btnNameSave.disabled = false;
        teamMessage.innerHTML = "That name looks good. Click Save when ready";
      } 
    })
  }
  function checkTreasurerEmail(e) {
    var re = /\S+@\S+\.\S+/;
    var result = re.test(e.target.value);
    if (result === true) {
      btnTreasurerSave.disabled = false;
      teamMessage.innerHTML = "That email looks good. Click Save when ready";
    } else {
        teamMessage.innerHTML = "Invalid email address";
        btnTreasurerSave.disabled = true;
    }
  }
  function saveTeam(e) {
    var url = "https://api.leagues4you.co.uk/teamSave/" + e.target.dataset.teamid;
    let postData = {
      "name": team.name.value
    };
    fetch(url, {
        method: "POST",
        // mode: "cors",
        // credentials: "include",
        body: JSON.stringify(postData)
    })
  }
  btnNameChange.addEventListener("click",enableNameEdit);
  btnTreasurerChange.addEventListener("click",enableTreasurerEdit);
  teamName.addEventListener("keyup",checkTeamName);
  teamTreasurer.addEventListener("keyup",checkTreasurerEmail);
  btnNameSave.addEventListener("click",saveTeam);
</script></main><script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js" integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous"></script>
</body>
</html>