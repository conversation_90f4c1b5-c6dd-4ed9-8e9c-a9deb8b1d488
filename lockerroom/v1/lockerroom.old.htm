<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1">
    <!-- development version, includes helpful console warnings -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
        integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="apple-touch-icon" sizes="57x57"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/favicon-16x16.png">
    <title>Document</title>
    <style>
        /* @import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,500;0,700;1,400&display=swap'); */
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,500;0,700;1,400&display=swap');

        :root {
            --dark-blue: #171e37;
            --dark-purple: #760890;
            --dark-grey: #444;
            --dark-pink: #FF1493;
        }

        .btn-purple {
            background-color: var(--dark-purple);
            color: white;
        }

        ::-webkit-scrollbar {
            width: 10px;
        }

        /* Track */
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        /* Handle */
        ::-webkit-scrollbar-thumb {
            background: #888;
        }

        /* Handle on hover */
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        body {
            /* font-family: 'Open Sans', sans-serif; */
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;

            /* background-attachment: fixed; */
            background-size: top 30px;
            background-position: right;
        }

        /* #app {} */
        .backsplash {
            position: relative;
            height: 33vh;
            /* outline: thin solid pink; */
            z-index: -1;
        }

        .backsplash-bg {
            height: 100%;
            width: 100%;
            min-width: 500px;
        }

        .backsplash-logo {
            position: absolute;
            top: 20px;
            left: 0;
            height: 100px;
            margin-left: 1rem;
        }

        .bottom-nav {
            background-color: var(--dark-blue);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 75px;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .bottom-nav a {
            width: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .bottom-nav .icon-bg {
            /* position: relative; */
            background-color: white;
            border-radius: 100%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-bg {
            /* display: none; */
            color: black;
        }

        .bottom-nav a.active .icon-bg {
            color: var(--dark-pink);
        }

        .legend {
            font-size: .6em;
            color: white;
            margin-top: .25em;
        }

        /* .bottom-nav i::after {
            content: '';
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-color: white;
        } */
        .checkmark__circle {
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            stroke-width: 2;
            stroke-miterlimit: 10;
            stroke: #7ac142;
            fill: none;
            animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .checkmark {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: block;
            stroke-width: 2;
            stroke: #fff;
            stroke-miterlimit: 10;
            margin: 10% auto;
            box-shadow: inset 0px 0px 0px #7ac142;
            animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
        }

        .checkmark__check {
            transform-origin: 50% 50%;
            stroke-dasharray: 48;
            stroke-dashoffset: 48;
            animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
        }

        .newRegistrationBox {
            justify-self: center;
            margin: 1rem auto;
            padding: 1em 2em;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: var(--dark-blue);
            color: white;
            max-width: 300px;
            min-height: 100px;
            border-radius: .5em;
            position: relative;
        }

        .newRegistrationBox::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: -1.5em;
            height: 0;
            width: 0;
            border-top: .75em solid transparent;
            border-bottom: .75em solid var(--dark-blue);
            border-left: .75em solid transparent;
            border-right: .75em solid transparent;
        }

        .page0 {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            display: flex;
            flex-direction: column;
        }

        .heroText,
        .registrationContainer {
            max-width: 960px;
            margin: 1rem auto;
        }

        .heroText {
            height: 50vh;
            position: relative;
        }

        .heroText h1 {
            position: absolute;
            top: 50%;
        }

        .registrationContainer {
            height: 50vh;
            display: flex;
            align-items: center;
            transform: translateY(-25%);
        }

        .newRegistrationBox-button button {
            background-color: var(--dark-pink);
            color: white;
            border: 0;
            border-radius: .5em;
            padding: 0.25em 2em;
            margin-top: 0.5em;
        }

        @keyframes stroke {
            100% {
                stroke-dashoffset: 0;
            }
        }

        @keyframes scale {

            0%,
            100% {
                transform: none;
            }

            50% {
                transform: scale3d(1.1, 1.1, 1);
            }
        }

        @keyframes fill {
            100% {
                box-shadow: inset 0px 0px 0px 30px #7ac142;
            }
        }
    </style>
</head>

<body>
    <div class="backsplash">
        <img class="backsplash-bg" src="color-splash-2.png" alt="">
        <img class="backsplash-logo" src="image001_1.png" alt="">
    </div>
    <div id="app" class="app">
        <div class="" id="pages" style="margin-top: -2rem;">
            <div v-if="page == 1" id="page1" class="page page1">
                <h3>Profile</h3>
                <form>
                    <input type="text" class="form-control" v-model="profile.firstname" placeholder="First Name">
                    <input type="text" class="form-control" v-model="profile.lastname" placeholder="Last Name">
                    <input type="email" class="form-control" v-model="profile.email" placeholder="Email">
                    <input type="tel" class="form-control" v-model="profile.mobile" placeholder="Mobile">
                    <button type="button" class="my-2 btn btn-sm btn-dark" v-on:click="profileSave()">Save</button>
                    <svg id="saveProfileConfirmed" class="checkmark d-none" xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </form>
            </div>
            <div v-else-if="page == 2" id="page2" class="page page2">
                <h3>Document Downloads</h3>
                <div class="document-group" style="display: flex; flex-direction: column;">
                    <a class="btn btn-purple text-start my-1"
                        href="https://leagues4you.co.uk/wp-content/uploads/2021/10/Netball-Captain-Pack-Aug-2021.pdf">Netball</a>
                    <a class="btn btn-purple text-start my-1"
                        href="https://leagues4you.co.uk/wp-content/uploads/2021/10/CP-Mixed-Netball-Oct-2021.pdf">Mixed
                        Netball</a>
                </div>

            </div>
            <div v-else-if="page == 3" id="page3" class="page page3">
                <h3>Teams</h3>
            </div>
            <div v-else-if="page == 4" id="page4" class="page page4">
                <h3>Payments</h3>
            </div>
            <!-- Home -->
            <div v-else id="page0" class="page page0">
                <div class="container">
                    <div class="heroText">
                        <h1 style="font-weight: 900; font-size: 3.5em; margin-left: 1rem;">hey<br> charlotte.</h1>
                    </div>
                    <div class="registrationContainer">
                        <div class="newRegistrationBox">
                            <div class="newRegistrationBox-text">
                                enter a new league?
                            </div>
                            <div class="newRegistrationBox-button">
                                <button>click here</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <nav class="bottom-nav">
            <a href="#" :class="{ active: page==1}" v-on:click="pageSelect(1)">
                <div class="icon-bg">
                    <i class="far fa-user"></i>
                </div>
                <span class="legend">Profile</span>
            </a>
            <a href="#" :class="{ active: page==2}" v-on:click="pageSelect(2)">
                <div class="icon-bg">
                    <i class="far fa-file-alt"></i>
                </div>
                <span class="legend">Docs</span>
            </a>
            <a href="#" :class="{ active: !page}" v-on:click="pageSelect()">
                <div class="icon-bg">
                    <i class="fas fa-home"></i>
                </div>
                <span class="legend">Home</span>
            </a>
            <a href="#" :class="{ active: page==3}" v-on:click="pageSelect(3)">
                <div class="icon-bg">
                    <i class="fas fa-users-cog"></i>
                </div>
                <span class="legend">Teams</span>
            </a>
            <a href="#" :class="{ active: page==4}" v-on:click="pageSelect(4)">
                <div class="icon-bg">
                    <i class="far fa-credit-card"></i>
                </div>
                <span class="legend">Payments</span>
            </a>
        </nav>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>
    <script>
        var app = new Vue({
            el: '#app',
            data: {
                title: 'Locker Room',
                welcome: 'hey charlotte',
                page: 0,
                profile: {}
            },
            computed: {
            },
            created: function () {
            },
            watch: {
            },
            methods: {
                pageSelect: function (e) {
                    this.page = e;
                },
                profileSaveReveal: function () {
                    document.querySelector("#saveProfileConfirmed").classList.remove("d-none");
                },
                profileSaveHide: function () {
                    document.querySelector("#saveProfileConfirmed").classList.add("d-none");
                },
                profileSave: function (e) {
                    let url = "https://api.leagues4you.co.uk";
                    fetch(url)
                        .then(() => this.profileSaveReveal())
                        .then(() => setTimeout(this.profileSaveHide, 2500));
                }
            }
        })
    </script>
</body>

</html>
</body>

</html>