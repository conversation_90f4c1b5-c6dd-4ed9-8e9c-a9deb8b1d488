<?php

namespace Home;

function Index (Array $data = []) {
    global $post, $config;
    if (isset($post['auth'])) {        
        $sql = "INSERT INTO `logins` SET `username` = :username, `ip` = :ip, `agent` = :agent, `result` = :result"; 
        $sqlData['username'] = $post['auth']['email'];
        $sqlData['ip'] = $_SERVER['REMOTE_ADDR'];
        $sqlData['agent'] = $_SERVER['HTTP_USER_AGENT'];
        $sqlData['result'] = 1;     
        \User::Authenticate($post['auth']);
        if (!\User::isLoggedIn()) {
            $sqlData['result'] = 0;new \Database($sql, $sqlData);
            Narrate("Login Failed: {$post['auth']['email']} {$post['auth']['password']}");
            Message("You can try again, request a password reminder or contact Support");
            Message("Login failed.","warning");
        } else {
            new \Database($sql, $sqlData);
            Narrate("Login Success: {$post['auth']['email']}");
            $url = $config['system']['url']."/User";
            Message("Login Successful","success");
            header("Location: ".$url); exit(0);
        }
        
    }
    if (isset($post['reminder']['email'])) {
        $user = \User::EmailLookup($post['reminder']['email']);
        if ($user) {
            $rlt = $user->PasswordReset();
        } else Message("We could not issue a password reminder. Is the email address spelt correctly? You can try again or contact Support for further help","warning");
    }
    if (isset($post['register'])) {
        $regRlt = \User::Registration($post['register']);
        (is_string($regRlt)) ? Message($regRlt,"danger") : Message("We have created your account. Please check your email for an activation link. Please check your junk folder too.","success");
    } 
    return $data;
}

function Register(Array $data = []) {
    global $post;
    if (isset($post['register'])) {
        $regRlt = \User::Registration($post['register']);
        (is_string($regRlt)) ? Message($regRlt,"danger") : Message("We have created your account. Please check your email for an activation link. Please check your junk folder too.","success");
    } 
    return $data;
}

function Activate (Array $data = []) {
    $activationResult = (isset($data[0]) && isset($data[1])) ?  \User::Activate($data[0], $data[1]) : null;
    
    if ($activationResult) {
        Narrate("Activation Success: {$data[0]}");
        $userID = \User::byEmail($data[0]);
        \User::Login($userID);
        if (\User::isLoggedIn()) {
            global $Config;
            Message("Please set a new password. To make a password secure, try joining 2 or 3 memorable words together");
            Message("Your account has been activated and you have been logged in","success");
            header("Location: {$Config['Url']}/User/PasswordChange"); exit(0);
        }
        Message("Activation failed","danger");
    } else Narrate("Activation Failure: " . print_r($data,true));
    return $data;
}

function Password (Array $data = []) {
    global $post;
    if (isset($post['reminder']['email'])) {
        Narrate("Password Reset Requested: {$post['reminder']['email']}");
        $user = \User::EmailLookup($post['reminder']['email']);
        if ($user) {
            $rlt = $user->PasswordReset();
        } else Message ("We could not issue a password reminder. Is the email address spelt correctly? You can try again or contact Support for further help","warning");
    }
    return $data;
}

function PasswordReset(Array $data = []) {
    if (isset($data[0]) && $data[0] && isset($data[1]) && $data[1]) {
        Narrate("Password Reset Completed: {$data[0]}");
        $user = \User::EmailLookup($data[0]);
        // echo "<pre>";
        // echo print_r($user,true);
        // exit("</pre>");
        if (!$user || !$user->id) {
            Message("Apologies - we could not process the password reset. You can try again or contact support","warning");
            header("Location: {$Config['Url']}/Home"); exit(0);
        } 
        $rlt = $user->ResetPassword($data[1]);
        if ($rlt !== true) {
            Message("Apologies - we could not process the password reset. You can try again or contact support","warning");
            header("Location: {$Config['Url']}/Home"); exit(0);
        } else {
            if (\User::LoggedIn()) {
                Message("We have automatically logged you in.","success");
                Message(" Please change your password. For security, try 3 memorable words joined together");
                header("Location: {$Config['Url']}/User/PasswordChange"); exit(0);
            }
        }
    } else Narrate("Password Reset Failed: {$data[0]}");
    Message("Apologies - we could not process the password reset. You can try again or contact support","warning");
    header("Location: {$Config['Url']}/Home"); exit(0);
}

function TeamRegistration (Array $data = []) {
    global $post;
    if (isset($post['register']) && $post['register']) {
        $regRlt = \User::Registration($post['register']);
    } 
    $data['sports'] = \Sport::Listing();
    $data['regions'] = \Region::Listing();
    $data['leagues'] = \League::Listing();
    return $data;
}

function CompleteRegistration (Array $data = []) {
    unset($_SESSION['registrationUserID']);
}