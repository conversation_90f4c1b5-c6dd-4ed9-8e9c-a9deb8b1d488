<?php

namespace User;

function Index (Array $data = []) {
    $data['user'] = \User::AuthUser();
    return $data;
}
function TeamName (Array $data = []) {
    global $post;
    if (isset($post['newTeam'])) {
        if (!isset($_SESSION['newTeam'])) {
            $_SESSION['newTeam'] = [
                "managerID" => \User::AuthUserID(),
                "captainID" => \User::AuthUserID(),
                "treasurerEmail" => \User::AuthUserEmail()
            ];
        } 
        $_SESSION['newTeam']['leagueID'] = $post['newTeam']['leagueID'];
        $_SESSION['newTeam']['seasonID'] = \Season::DefaultLeague($post['newTeam']['leagueID']);
        $_SESSION['newTeam']['divisionID'] = \Division::DefaultSeason($_SESSION['newTeam']['seasonID']);
    }
    return $data;
}
function TeamTreasurer (Array $data = []) {
    global $post;
    if (isset($post['newTeam'])) {
        $_SESSION['newTeam']['name'] = $post['newTeam']['name'];
    }
    return $data;
}
function TeamTerms (Array $data = []) {
    global $post;
    if (isset($post['newTeam'])) {
        $_SESSION['newTeam']['treasurerEmail'] = $post['newTeam']['treasurerEmail'];
    }
    if (isset($_SESSION['newTeam'])) {
        if (isset($_SESSION['newTeam']['leagueID'])) {
            $data['league'] = new \League($_SESSION['newTeam']['leagueID']);
            $data['captainsPack'] = $data['league']->getCaptainsPack();
        }
    }
    // echo "<!--\n";
    // print_r($_SESSION['newTeam']);
    // echo "-->\n";
    return $data;
}
function Payments(Array $data=[]) {
    global $post, $get;
    if (isset($post['treasuryTeams'])) {
        foreach ($post['treasuryTeams'] as $key => $val) {
            $team = new \Team($key);
            $team->setPaymentMethod($val);
            $team->Save();
        }
        $data['tab'] = "teams";
        // exit(Dump($post['teamCards']));
    } 
    $data['user'] = \User::AuthUser();
    $data['stripe'] = new \Stripe();
    if (isset($get['si'])) {
        /* Returning after a Setup Intent */
        $si = $data['stripe']->getSetupIntent($get['si']);
        $data['tab'] = "cards";
        if ($si->status=="succeeded") {
            Message("Your card has been successfully added","success");
            $subject = "New Payment Card Added";
            $message[] = "Hi";
            $message[] = $data['user']->__toString(). " has successfully added a payment card";
            if ($si->payment_method) $message[] = "Payment Method " . $si->payment_method;
            $message[] = "Thanks and best wishes";
            $message[] = "leagues4you";
            \Email::Issue($subject,$message,["<EMAIL>" => "Marc Conlon"]);
        } 
    }
    if (isset($get['pi'])) {
        /* Returning after a Setup Intent */
        $si = $data['stripe']->getPaymentIntent($get['pi']);
        $data['tab'] = "payments";
        if ($si->status=="succeeded") Message("Your payment has been successfully processed","success");
    }
    if (isset($post['removeCard'])) {
        $data['stripe']->RemoveCard($post['removeCard']);
        $data['tab'] = "cards";
        Message("The payment card has been removed","success");
    }
    $data['payments'] = \FinanceReceipt::User($data['user']);
    $data['cards'] = \Stripe::PaymentCards($data['user']->id);
    $data['treasuryTeams'] = \Team::TreasuryTeams($data['user']);
    if (!isset($data['tab']) || !$data['tab']) $data['tab'] = "main";
    return $data;
}
function Card (Array $data = []) {
    global $get;
    $data['user'] = \User::AuthUser();
    // if (!$data['user']->getStripeCustomerID()) $data['user']->setStripeCustomerID();
    $data['stripeCustomer'] = \Stripe::GetCustomer($data['user']->id);
    if (!$data['stripeCustomer']) $data['stripeCustomer'] = \Stripe::CreateCustomer($data['user']->id,$data['user']->email);
    $data['stripe'] = new \Stripe();
    $data['setupIntent'] = \Stripe::CreateSetupIntent($data['stripeCustomer']->id);
    return $data;
}
function CardPay (Array $data = []) {
    global $get;
    $data['user'] = \User::AuthUser();
    $data['stripeCustomer'] = \Stripe::GetCustomer($data['user']->id);
    $data['paymentIntent'] = (isset($get['pi']) && $get['pi']) ? \Stripe::getPaymentIntent($get['pi']) : \Stripe::CreatePaymentIntent($data['stripeCustomer']->id);
    if ($data['paymentIntent']->status == "succeeded" || $data['paymentIntent']->status == "requires_capture") {
        header("Location: ./Payments?pi={$data['paymentIntent']->id}");
        Message("That payment has already been processed");
        exit(0);
    }
    $data['stripe'] = new \Stripe();
    return $data;
}
function Profile(Array $data = []) {
    global $post;
    if (isset($post['profile'])) {
        $user = new \User($post['profile']['id']);
        $user->Load($post['profile']);
        $user->Save();
        Message("Profile information updated","success");
        Narrate("Profile: " . implode(", ",$post['profile']));
    }
    $data['user'] = \User::LoggedIn();
    return $data;
}
function PasswordChange (Array $data = []) {
    global $post;
    if (isset($post['password'])) {
        $user = \User::LoggedIn();
        $rlt = $user->ChangePassword($post['password']);
        if ($rlt ===true) {
            global $Config;
            Message("Password updated","success");
            Narrate("Password updated for " . $user->getEmail());
            if (($leagueID=$user->getRegistrationLeagueID())) {
                Narrate("Redirecting to Team Setup for League ID $leagueID");
                header("Location: {$Config['Url']}/User/Teams");
            } else header("Location: {$Config['Url']}/User");
            exit(0);
        } else Message($rlt,"warning");
    }
    return $data;
}
function Club (Array $data = []) {
    global $post;
    if (isset($post['club'])) {
        $post['club']['userID'] = \User::AuthUserID();
        $club = new \Club();
        $club->Load($post['club']);
        $club->Save();
    }
    $data['club'] = \Club::User(\User::AuthUserID());
    return $data;
}
function Team (Array $data = []) {
    // global $post;
    // if (isset($post['invitationEmail'])) {
    //     /* Returns  */
    //     $user = \User::EmailLookup($post['invitationEmail']);
    //     if ($user) {
    //         /* Need to Create the User *
    //     }
    //     echo "Invite {$post['invitationEmail']}<br>";
    //     \TeamFollower::Invitation();
    // }
    
    if (isset($data[0]) && $data[0]) {
        $data['team'] = new \Team($data[0]);
        $data['divisionTeams'] = \Team::byDivision($data['team']->getDivisionID());
        $data['division'] = new \Division($data['team']->getDivisionID());
        $data['divisionTable'] = \Standing::Division($data['team']->getDivisionID());
    } 
    return $data;
}
function Teams (Array $data = []) {
    global $post;
    $data['user'] = \User::AuthUser();
    if (isset($post['team'])) {
        $team = new \Team($post['team']['id']);
        if (($oldName=$team->getName()) != $post['team']['name']) {
            $team->setName($post['team']['name']);
            Message("$oldName has been renamed to {$post['team']['name']}");
        }
        if (!$team->validateTreasurerEmail($post['team']['treasurerEmail'])) {
            $team->setNewTreasurer($post['team']['treasurerEmail']);
        }
        $team->Save();
    }
    if (isset($_SESSION['newTeam']) && isset($post['teamStage']) && $post['teamStage']==4) {
        /* If different Treasurer Email ... */
        $treasurerInvite = null;
        if ($_SESSION['newTeam']['treasurerEmail'] != \User::AuthUserEmail()) {
            /* Does Email User Exist? */
            $_SESSION['newTeam']['treasurerID'] = \User::byEmail($_SESSION['newTeam']['treasurerEmail']);
            if (!$_SESSION['newTeam']['treasurerID']) {
                $_SESSION['newTeam']['treasurerID'] = \User::Invitation($_SESSION['newTeam']['treasurerEmail'],\User::AuthUser());
            } else {
                $treasurerInvite = true;
            }
        } else $_SESSION['newTeam']['treasurerID'] = \User::AuthUserID();
        $team = new \Team();
        $team->Load($_SESSION['newTeam']);
        $team->Save();
        if ($treasurerInvite === true) $team->treasurerInvitation();
        $data['user'] = \User::AuthUser();
        $data['user']->setRegistrationLeagueID();
        $data['user']->Save();
        $league = new \League($_SESSION['newTeam']['leagueID']);
        Message("Thanks. You have entered <b>{$_SESSION['newTeam']['name']}</b> into <b>" . $league->getName()."</b>","success");
        Message("Confirmation of your entry has been sent to your email account along with a copy of your Captain Pack. Good Luck for the season ahead");
        Message("Please keep an eye on spam/junk email folders in case our email finds it way there","warning");
        $subject = "Congratulations!";
        $message[] = "Hi";
        $message[] = "Thanks for joining " . $league->getName();
        $message[] = "We're pleased to confirm that <b>{$_SESSION['newTeam']['name']}</b> is registered into the league";
        $message[] = "Your league co-ordinator will be in touch soon";
        $message[] = "The captains pack for your League is available <a href=\"".$league->getCaptainsPack()."\">here</a>";
        $message[] = "Thanks and best wishes";
        $message[] = "leagues4you";
        \Email::Issue($subject,$message,[\User::AuthEmail() => \User::AuthEmail()]);
    }
    unset($_SESSION['newTeam']);
    $data['leagues'] = \League::Open();
    $data['teams'] = \Team::Managed(\User::AuthUserID());
    return $data;
}
function Members (Array $data = []) {

    return $data;
}
function Logout() {
    if (isset($_SESSION['newTeam'])) unset($_SESSION['newTeam']);
    \User::Logout();
    Message("You have been logged out","success");
    header("Location: /");
    exit(0);
}