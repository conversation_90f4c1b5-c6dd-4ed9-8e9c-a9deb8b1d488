<?php

class MailChimp {

    // protected $datacentre = "us19";
    // protected $url = "api.mailchimp.com/3.0";
    // protected $api = "*************************************";
    // protected $defaultList = "2d7cd73d48";
    protected $datacentre, $url, $api, $defaultList;
    protected $auth;
    protected $listID;
    protected $method = "GET";
    protected $data = [];
    protected $endPoint;
    protected $exec, $info;

    function __construct() {
        global $MailChimpSettings;
        $this->url = "https://{$MailChimpSettings->datacentre}.{$MailChimpSettings->url}";
        $this->auth = base64_encode( 'user:'.$MailChimpSettings->api);
        $this->listID = $MailChimpSettings->defaultList;
    }
    function Query() {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "{$this->url}/{$this->endPoint}");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json',
            'Authorization: Basic '.$this->auth));
        curl_setopt($ch, CURLOPT_USERAGENT, 'PHP-MCAPI/2.0');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        if ($this->method == "POST" || $this->method == "PUT") {
            if ($this->method == "PUT") {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
                curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($this->data));
            } else {
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($this->data));
            } 
        }
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $this->exec = json_decode(curl_exec($ch));
        $this->info = curl_getinfo($ch);
        curl_close($ch);
    }
    static function Exists(String $email) {
        $mailchimp = new static();
        $mailchimp->endPoint = "lists/{$mailchimp->listID}/members/".md5(strtolower($email));
        $mailchimp->Query();
        return $mailchimp;
    }

    static function Create (String $email) {
        $mailchimp = new static(); 
        $mailchimp->data = [
            'email_address' => $email,
            'status' => "unsubscribed",
            "email_type" => "html"
        ];
        $mailchimp->endPoint = "lists/{$mailchimp->listID}/members/";
        $mailchimp->method="POST";
        $mailchimp->Query();
        return $mailchimp;
    }
    static function Update (String $email, $status = "subscribed", String $firstname = null, String $lastname = null) {
        /**
         * Use $status = "subscribed" to instantly subscribe a user
         * Use $status = "pending" to issue a subscription email,
         * Use $status = "unsubscribed"
         */
        $mailchimp = new static(); 
        $mailchimp->data = [
            'email_address' => $email,
            'status' => $status,
            "email_type" => "html"
        ];
        if ($firstname && $lastname) $mailchimp->data['merge_fields']  = ['FNAME' => $firstname,'LNAME' => $lastname];
        $mailchimp->endPoint = "lists/{$mailchimp->listID}/members/".md5(strtolower($email))."?skip_merge_validation=true";
        $mailchimp->method="PUT";
        $mailchimp->Query();
        return $mailchimp;
    }
}