<?php

class Sport extends Base {

    protected $dbTable = "sports";
    protected $name;
    protected $captainsPack;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    /* Getters */
    function getCaptainsPack() { return $this->captainsPack;}

    /* Statics */
    static function Listing() {
        $sql = "SELECT * FROM `sports` ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }
    static function Packs() {
        $sql = "SELECT * FROM `sports` WHERE `captainsPack` IS NOT NULL ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }
}