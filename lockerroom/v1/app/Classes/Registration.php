<?php

class Registration {

    static function Process (Array $data) {

        global $Config;
        $user = new User();
        // $user->setFirstname($data['firstname']);
        // $user->setLastname($data['lastname']);
        $user->setEmail($data['email']);
        // $user->setMobile($data['mobile']);
        $user->setPassword();
        $user->setActivationCode();
        $user->Save();

        $subject = "Registration at " . $Config['Name'];
        $message[] = "Hello";
        $message[] = "Thanks for registering with us at " . $Config['Name'];
        $message[] = "To activate your account with us, please click <a href=\"" . $Config['Url'] . "/Home/Activate/" . $user->getEmail() . "/" . $user->getActivationCode() . "\">here</a>";
        $message[] = "Many thanks";
        $message[] = $Config['Name'];
        $to = [$user->getEmail() => $user->getName()];
        $cc = [];
        $bcc = [$Config['Email'] => $Config['Name']];
        $attachments = [];
        // Email::Issue ($subject, $message, $to, $cc, $bcc, $attachments);
        Dump($message);

        Message("We have successfully registered your account. Please check your email for an activation link","success");
        // return $sqlData;

    }

}