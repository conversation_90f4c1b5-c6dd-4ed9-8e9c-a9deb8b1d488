<?php

class Route {

    protected $controller = "Home";
    protected $action = "Index";
    protected $variables = [];
    protected $url = [];
    protected $view;

    function __construct(String $url = null) {
        //  exit("URL: $url"); 
        if ($url) {
            $this->url = $url;
            $elements = explode("/",$this->url);
            if (is_array($elements) && count($elements)>0) {
                // exit(Dump($elements));
                $this->controller = array_shift($elements);
                if ($elements) $this->action = array_shift($elements);
                if ($elements) $this->variables = $elements;
            }
        }
        Narrate("URL: {$this->controller}/{$this->action}");
        // exit("C: {$this->controller}. M: {$this->action}");        
    }

    function Display () {
        global $config;
        if ($this->controller !="Home" && !\User::isLoggedIn() ) {
            header("Location: " . $config['system']['url']);
            exit(0);    
        }
        if (function_exists($this->controller."\\".$this->action)) $data = call_user_func($this->controller."\\".$this->action,$this->variables);
        ?><body class="<?php echo strtolower($this->controller);?>"><main class="<?php echo strtolower($this->controller);?>"><?php
        $this->view = __DIR__."./../Views/{$this->controller}/{$this->action}.{$this->controller}.php";
        \FinanceReceipt::ConfirmAll();
        \FinanceReceipt::CaptureAll();
        if (\User::isLoggedIn()) {
            include("./app/Views/menus/user.menu.php");
        } else include("./app/Views/menus/guest.menu.php");
        Messages();
        include($this->view);
        ?></main><?php
    }
}