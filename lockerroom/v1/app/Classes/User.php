<?php
class User extends Base {
    protected $dbTable = "users";
    protected $dbFields = ["introducerID","firstname", "lastname", "email","mobile","clubname", "password","activationCode","activationIP", "activationStamp","registrationLeagueID"];
    protected $minData = [
        /* Listing of AND / OR min requirements to save */
    ];
    protected $introducerID;
    public $firstname, $lastname;
    public $email, $password;
    public $mobile;
    protected $registrationLeagueID;
    // public $landline,$extension;
    protected $clubname;
    public $activationCode, $activationIP, $activationStamp;
    function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() { return "{$this->email}";}
    function isValid() {
        return ($this->activationStamp) ? true : false;
    }
    function Save() {
        /* Returns INT id on success, STRING on failure */
        /* Validation */
        if (!$this->email || !filter_var($this->email, FILTER_VALIDATE_EMAIL)) return "Must have a valid email address";
        /* Default Values */
        if (!$this->password) $this->password = $this->setPassword();
        if (!$this->registrationLeagueID) $this->registrationLeagueID = null;
        parent::Save();
        return (int)$this->id; /* Successfully saved */
    }
    /* Getters */
    function getFirstname() {return $this->firstname;}
    function getLastname() {return $this->lastname;}
    function getEmail() {return $this->email;}
    function getMobile() {return $this->mobile;}
    // function getLandline() {return $this->landline;}
    // function getExtension() {return $this->extension;}
    /* Getter Combinations */
    // function getTelephone() {
    //     return ($this->extension) ? $this->landline . " x{$this->extension}" : $this->landline;
    // }
    function getName() {return "$this->firstname $this->lastname";}
    function getClubname() { return $this->clubname;}
    function getActivationCode() { return $this->activationCode;}
    function getRegistrationLeagueID() { return $this->registrationLeagueID;}
    
    /* Setters */
    function setFirstname(String $firstname) { $this->firstname = $firstname;}
    function setLastname(String $lastname) { $this->lastname = $lastname;}
    function setEmail(String $email) { 
        if (!filter_var($email,FILTER_VALIDATE_EMAIL)) return "$email is not a vlaid email address";
        $this->email = $email;
    }
    function setMobile(String $mobile) { $this->mobile = $mobile;}
    function setLandline(String $landline) { $this->landline = $landline;}
    function setExtension(String $extension) { $this->extension = $extension;}
    function ChangePassword(Array $data) {
        /* Expects $data[0] and $data[1] as password and confirmed. */
        /* Return BOOLEAN true or STRING $error */
        if (!isset($data[0]) || !isset($data[1])) {
            $msg = "Cannot update password. Missing password and/or confirmed password";
            Logging::Add("{$this} $msg");
            return $msg;
        } 
        if (!$data[0]) {
            $msg = "Supplied Password is empty";
            Logging::Add("{$this} cannot update password. $msg");
            return $msg;
        } 
        if (!$data[1]) {
            $msg = "Confirmation Password empty";
            Logging::Add("{$this} cannot update password. $msg");
            return $msg;
        } 
        if ($data[0] != $data[1]) {
            $msg = "Passwords do not match";
            Logging::Add("{$this} cannot update password. $msg");
            return $msg;
        } 
        $this->setPassword($data[0]);
        $this->Save();
        Logging::Add("{$this} updated password.");
        return true;        
    }
    function setPassword(String $password=null) { 
        if (!$password) $password = md5(time());
        $this->password = password_hash($password, PASSWORD_DEFAULT);
    }
    function setActivationCode() { $this->activationCode = substr(md5(time()),0,10);}
    function setStripeCustomerID() {
        if ($this->stripeCustomerID || !$this->email) return;
        $stripe = new Stripe();
        $stripeCustomer = $stripe->CreateCustomer($this->email);
        $this->stripeCustomerID = $stripeCustomer->id;
    }
    function setRegistrationLeagueID() {
        $this->registrationLeagueID = null;
    }
    function PasswordReset($minutes = 15) {
        global $config;
        $resetCode = substr(md5(time()),0,20);
        $sql = "UPDATE `users` SET `resetIP` = :resetIP, `resetSession` = :resetSession, `resetCode` = :resetCode, `resetExpiry` = :resetExpiry WHERE `id` = :id";
        $sqlData =  [
            "resetIP" => $_SERVER['REMOTE_ADDR'],
            "resetSession" => session_id(),
            "resetCode" => $resetCode,
            "resetExpiry" => date('Y-m-d H:i:s',strtotime("+$minutes minutes")),
            "id" => $this->id
        ];
        $db = new Database($sql,$sqlData);
        if ($db->errors) return implode(". ",$db->errors);
        if (!$db->affectedRows) return (Message("We could not issue you with an activation code. You can try again or contact Support for further assistance. NB - If you have already requested a password reminder - you must wait 15 minutes before requesting another.","warning")); 
        $subject = "Password Reset Request from " . $config['system']['name'];
        $message[] = "Hi {$this->firstname}";
        $message[] = "We have received a password reset request for your account at " . $config['system']['name'];
        $message[] = "Please click <span  class=\"display: inline-block; background-color: blue; color: white; padding: 10px;\"><a href=\"{$config['system']['url']}/Home/PasswordReset/{$this->email}/$resetCode\">here</a></span> to set your new password";
        $message[] = "This link will autmatically expire in $minutes minutes";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to[$this->email] = $this->__toString();
        // $cc = $bcc = [];
        // $attachments = [];
        $emailRlt = \Email::Issue ($subject, $message, $to);
        if ($emailRlt) {
            Logging::Add("Lockeroom {$this} requests password reset");
            Message("We have sent you a Password Reminder email with an activation code","success");
            return true;
        } 
        Message($emailRlt,"warning");
        Logging::Add("Lockeroom {$this} fails password reset. $emailRlt");
    }
    function ResetPassword(String $resetCode) {
        if ($this->resetCode && $this->resetCode == $resetCode) {
            $sql = "UPDATE `users` SET `resetIP` = NULL, `resetSession` = NULL, `resetCode` = NULL, `resetExpiry` = NULL";
            $sqlData["id"] = $this->id;
            if (!$this->activationStamp) {
                $sql .= ", `activationIP` = :activationIP, `activationStamp` = NOW()";
                $sqlData['activationIP'] = $_SERVER['REMOTE_ADDR'];
            }
            $sql .= "WHERE `id` = :id";
            $db = new Database($sql,$sqlData);
            $this->resetIP = null;
            $this->resetSession = null;
            $this->resetCode = null;
            $this->resetExpiry = null;
            // $this->Save();
            static::Login($this->id);
            return true;
        } else {
            static:: Logout();
            return false;
        }
    }
    function IssueActivation(String $invitationEmail = null) {
        global $config;
        $this->setPassword();
        $this->setActivationCode();
        $this->Save();
        $subject = "Registration at " . $config['system']['name'];
        $message[] = "Hello";
        $message[] = ($invitationEmail) ? "You have been invited to join us at {$config['system']['name']} by $invitationEmail" : "Thanks for registering with us at " . $config['system']['name'];
        $message[] = "To activate your account with us, please click <a href=\"" . $config['system']['url'] . "/Home/Activate/" . $this->getEmail() . "/" . $this->getActivationCode() . "\">here</a>";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to = [$this->getEmail() => $this->getName()];
        // $cc = $bcc = [];
        // $bcc = [$Config['internalEmail'] => "Codeframe"];
        // $attachments = [];
        Logging::Add("Lockeroom Activation to $invitationEmail with code " . $this->getActivationCode());
        Email::Issue ($subject, $message, $to);
    }
    /* Statics */
    static function Authenticate (Array $credentials) {
        $sql = "SELECT `id`,`password` FROM `users` WHERE `email` = :email AND `password` IS NOT NULL AND `activationStamp` IS NOT NULL";
        $db = new Database($sql,["email" => $credentials['email']]);
        if ($db->errors) return implode(". ",$db->errors);
        if (!$db->rows) {
            Logging::Add("Lockeroom Login Failed (Bad Username): for {$credentials['email']}");
            return "Login Failed (Error " . __LINE__.")";
        } 
        if (password_verify ($credentials['password'] ,$db->rows[0]['password']) === true) {
            Logging::Add("Lockeroom Login Success: for {$credentials['email']}");
            static::Login($db->rows[0]['id']);
        } else Logging::Add("Lockeroom Login Failed (Bad Password): for {$credentials['email']}");
    }
    static function Activate(String $email, String $code) {
        $sqlData = [
            "email" => $email,
            "activationCode" => $code,
            "activationIP" => $_SERVER['REMOTE_ADDR'],
        ];
        $sql = "UPDATE `users` SET `activationCode` = NULL, `activationIP` = :activationIP, `activationStamp` = NOW() WHERE `email` = :email AND `activationCode` = :activationCode";
        $db = new Database($sql,$sqlData);
        if ($db->affectedRows == 1) {
            Logging::Add("Lockerroom Activation Success: for $email");
            return true;
        } else Logging::Add("Lockerroom Activation Failure: for $email using $code from {$_SERVER['REMOTE_ADDR']}");
    }
    static function byEmail(String $email) {
        $sql = "SELECT `id` FROM `users` WHERE `email` = :email";
        $db = new Database($sql,["email" => $email]);
        // $db->Query();
        if ($db->errors) return implode(". ",$db->errors);
        return (isset($db->rows[0]['id'])) ? $db->rows[0]['id'] : false;
    }
    static function Login(Int $userID) {
        $_SESSION['userID'] = $userID;
        session_regenerate_id();
    }
    static function Logout() {
        Logging::Add("Logout: ".User::AuthUser());
        session_regenerate_id();
        unset($_SESSION['userID']);
    }
    static function isLoggedIn() {
        if (isset($_SESSION['userID']) && $_SESSION['userID']) return true;
    }
    static function LoggedIn() {
        if (isset($_SESSION['userID']) && $_SESSION['userID']) return new static($_SESSION['userID']);
    }
    static function AuthUserID() {
        return (isset($_SESSION['userID']) && $_SESSION['userID']) ? $_SESSION['userID'] : null;
    }
    static function AuthUser() {
        return (isset($_SESSION['userID']) && $_SESSION['userID']) ? new static($_SESSION['userID']) : null;
    }
    static function AuthEmail() {
        if (isset($_SESSION['userID']) && $_SESSION['userID']) {
            $user = new static($_SESSION['userID']);
            return $user->email;
        } 
    }
    static function AuthUserEmail() {
        return static::AuthEmail();
    }
    static function EmailLookup(String $email) {
        /*
            Find a User by Email
            Returns USER on Success, BOOLEAN false on failure.
        */
        $sql = "SELECT * FROM `users` WHERE `email` = :email";
        $db = new Database($sql,["email" => $email]);
        if ($db->errors) return implode(". ",$db->errors);
        if (isset($db->rows[0])) {
            $user = new static();
            $user->Load($db->rows[0]);
            return $user;
        }
    }
    static function MobileLookup(String $mobile) {
        /*
            Find a User by Mobile
            Returns USER on Success, BOOLEAN false on failure.
        */
        $sql = "SELECT * FROM `users` WHERE `mobile` = :mobile";
        $db = new Database($sql,["mobile" => $mobile]);
        if ($db->errors) return implode(". ",$db->errors);
        if (isset($db->rows[0])) {
            $user = new static();
            $user->Load($db->rows[0]);
            return $user;
        }
    }
    static function Registration(Array $data = []) {
        if (!isset($data['email']) || !$data['email'] || !filter_var($data['email'],FILTER_VALIDATE_EMAIL)) return "Missing or Invalid Email Address";
        $exists = static::byEmail($data['email']);
        if ($exists) {
            $msg = "We could not create your account. The email address is registered already";
            Logging::Add("Registration: {$data['email']} : $msg");
            return $msg;
        } 
        $exists = static::MobileLookup($data['mobile']);
        if ($exists) {
            $msg = "We could not create your account. The mobile number is already registered.";
            Logging::Add("Registration: {$data['mobile']} : $msg");
            return $msg;
        } 
        $user = new static();
        $user->Load($data);
        $user->Save();
        if ($user->id) {
            Logging::Add("Registration: Success. {$user}");
            $user->IssueActivation();
            return (int)$user->id;
        } 
        Logging::Add("Registration: Failed. ".print_r($data,true));
        return "We could not setup your account - please try again or contact support for more help";
    }
    static function Invitation (String $invitee, User $inviter) {
        global $config;
        $newUser = new static();
        $newUser->Load(["email" => $invitee, "introducerID" => $inviter->id]);
        $newUser->setPassword();
        $newUser->setActivationCode();
        $newUser->Save();
        $subject = "Inivitation to join at " . $config['system']['organisation'];
        $message[] = "Hi there";
        $message[] = $inviter->__toString()." has invited you to get involved at " . $config['system']['organisation'];
        $message[] = "It will be great to have you onboard.";
        $message[] = "To activate your account with us, simply click <a href=\"" . $config['system']['url'] . "/Home/Activate/" . $newUser->getEmail() . "/" . $newUser->getActivationCode() . "\">here</a>";
        $message[] = "Once you're activated, head to the <b>My Payments</b> section within your locker room! It's great to have you on board!";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to = [$newUser->getEmail() => $newUser->getName()];
        // $cc = [];
        // $bcc = [$config['system']['internalEmail'] => "Codeframe"];
        // $attachments = [];
        $msg = "LockerRoom Invitation to " . $newUser->getEmail() . " by " . $inviter->__toString();
        Logging::Add($msg);
        Email::Issue ($subject, $message, $to);
        return $newUser->id;
    }
    static function isAuthor() {
        $authUser = static::AuthUser();
        if (!$authUser) return;
        if ($authUser->getEmail() == "<EMAIL>") return true;
    }
}