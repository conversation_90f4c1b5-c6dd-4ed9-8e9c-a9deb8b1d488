<?php

class Notification extends Base {

    protected $dbTable = "notifications";
    protected $dbFields = ["fromID","toID","message","status"];
    protected $created, $fromID, $toID, $message, $status;

    static function toUser (Int $userID) {
        $sql = "SELECT * FROM `notifications` WHERE `toID` = :userID ORDER BY `created` ASC";
        $db = new Database($sql,["userID" => $userID]);
        return $db->rows;
    }

    static function fromUser (Int $userID) {
        $sql = "SELECT * FROM `notifications` WHERE `fromID` = :userID ORDER BY `created` ASC";
        $db = new Database($sql,["userID" => $userID]);
        return $db->rows;
    }

    static function Listing() {
        $sql = "SELECT * FROM `notifications` ORDER BY `created` ASC";
        $db = new Database($sql);
        return $db->rows;
    }
}