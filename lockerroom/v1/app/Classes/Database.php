<?php
class Database {

    private $pdo;
    private $statement;
    private $sql, $data;
    public $errors = [];
    public $result;
    public $rows = [];
    public $lastInsertID;
    public $affectedRows;
    public $params;

    function __construct(String $sql, Array $data = []) {
        global $config;
        $this->sql = $sql; $this->data = $data;
        try {
            $pdoOptions = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_EMULATE_PREPARES => true
            ];
            $this->pdo = new \PDO("mysql:dbname={$config['db']['name']};host={$config['db']['host']}", $config['db']['username'], $config['db']['password'],$pdoOptions);
        } catch (\PDOException $e) {
            $this->errors[] = $e->getMessage();
        }
        try {
            $this->statement = $this->pdo->prepare($this->sql);
            $this->statement->execute($this->data);
            switch (strstr($this->sql," ",true)) {
                case "INSERT":
                    $this->lastInsertID = $this->pdo->lastInsertId(); break;
                case "UPDATE":
                case "DELETE":
                    $this->affectedRows = $this->statement->rowCount(); break;
                case "SELECT":
                    while ($row = $this->statement->fetch(PDO::FETCH_ASSOC)) $this->rows[] = $row;                    
            }
        } catch (\PDOException $e) {
            $this->errors[] = $e->getMessage();
            // $this->params = print_r($this->statement->debugDumpParams(),true);
        } catch (Exception $e) {
            $this->errors[] = $e->getMessage();
            // $this->params = print_r($this->statement->debugDumpParams(),true);
        }
    }
    static function Setup () {
        $sqlTableData = __DIR__."/../Core/sqlTables.php";
        if (!file_exists($sqlTableData)) return "$sqlTableData does not exist";
        include($sqlTableData);
        if (!isset($sqlTables) || !is_array($sqlTables) || !$sqlTables) return "SQL Data missing or not an array";
        foreach ($sqlTables as $table => $fields) {
            $sql = "CREATE TABLE IF NOT EXISTS `$table` ("; $conn = null;
            foreach ($fields as $fieldName => $field) {
                $sql .= $conn . "`$fieldName` {$field['type']}"; 
                if (isset($field['attributes'])) $sql .= " {$field['attributes']}";
                if (isset($field['notNull'])) $sql .= " NOT NULL";
                if (isset($field['default'])) $sql .= " default {$field['default']}";
                if (isset($field['key'])) $sql .= " {$field['key']}";
                if (isset($field['extra'])) $sql .= " {$field['extra']}";
                if (isset($field['default'])) $sql .= " NOT NULL";
                $conn = ", ";
            }
            $sql .=")";
            $db = new static($sql);
        }
        if ($db->errors) {
            echo "$sql<br>";
            echo implode("<br>",$db->errors);
        }
    }

}
