<?php

class FinanceReceipt extends Base {

    protected $dbTable = "financeReceipts";
    protected $dbFields = ["teamID","userID","total","stripePaymentIntentID","stripePaymentMethodID","status","deleted"];
    protected $userID, $teamID, $total;
    protected $stripePaymentIntentID;
    protected $stripePaymentMethodID;
    protected $status;
    protected $statusID; # 1 = Failed, 2 = Pre-Auth, 3 = Complete
    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        if (!$this->userID) return "No User specified";
        if (!$this->total) return "No total specified";
        parent::Save();
        if (!$this->stripePaymentIntentID || $this->stripePaymentMethodID) {
            if (!$this->stripePaymentIntentID) $this->setStripePaymentIntentID();
            if (!$this->stripePaymentMethodID) $this->setStripePaymentMethodID();
            parent::Save();
        }
    }
    function Cancel() {
        $this->deleted = date('Y-m-d H:i:s');
        if ($this->stripePaymentIntentID) \Stripe::CancelPaymentIntent($this->stripePaymentIntentID);
    }
    /* Setters */
    function setUserID(Int $userID) { $this->userID = $userID;}
    function setTeamID(Int $teamID) { 
        $this->teamID = $teamID;
        $this->stripePaymentMethodID = Team::getTeamPaymentMethod($teamID);
    }
    function setTotal(Float $total) { $this->total = $total;}
    function Check() {
        $status = null;
        if (!$this->teamID) {
            $status = "Missing a Team";$this->Save();
        }  elseif (!$this->userID) {
            $status = "Missing a Treasurer";
        } elseif (!$this->total) {
            $status = "Has no total to bill";
        } elseif (!$this->stripePaymentIntentID) {
            $status = "Missing billing data";
        } elseif (!$this->stripePaymentMethodID) {
            $status = "Missing billing instructions";
        }
        if ($status) {
            $this->status = $status; $this->Save(); return;
        }
        if ($this->teamID && $this->userID && $this->total && $this->stripePaymentIntentID && $this->stripePaymentMethodID) {
            return true;
        }
    }
    function Confirm() {
        $this->status = \Stripe::ConfirmIntent($this->stripePaymentIntentID);
        $this->Save();
    }
    function Capture() {
        $intent = \Stripe::CaptureIntent($this->stripePaymentIntentID);
        $this->status = $intent->status;
        $this->Save();
        if ($this->status == "requires_capture") {
            Message("Requires Capture detected");
            $treasurer = Team::Treasurer($this->teamID);
            if (!$treasurer['stripePaymentMethodID']) {
                Team::setTeamPaymentMethod($this->teamID,$intent->payment_method);
                Message("For convenience, we have assigned this card to pay for " . new Team($this->teamID)." automatically in future. You can change this in the <b>My Payments</b> section");
            } 
        }        
    }
    function setStripePaymentIntentID() {
        $user = new User($this->userID);
        $stripeCustomer = Stripe::GetCustomer($user->id);
        if (!$stripeCustomer) $stripeCustomer = Stripe::CreateCustomer($user->id, $user->getEmail());
        $paymentIntent = Stripe::CreatePaymentIntent($stripeCustomer->id, $this->total, "Receipt ID {$this->id}");
        $this->stripePaymentIntentID = $paymentIntent->id;
    }
    function setStripePaymentMethodID() { 
        $this->stripePaymentMethodID = Team::getTeamPaymentMethod($this->teamID);
        if (!$this->stripePaymentMethodID) return;
        Stripe::PaymentIntentPaymentMethod($this->stripePaymentIntentID,$this->stripePaymentMethodID);
        $this->Save();
    } 
    function setStatus(String $status) { $this->status = $status;}
    /* Getters */
    function getUserID() { return $this->userID;}
    function getTeamID() { return $this->teamID;}
    function getTotal() { return $this->total;}
    function getStripePaymentIntentID() { return $this->stripePaymentIntentID;}
    function getStripePaymentMethodID() { return $this->stripePaymentMethodID;}
    function getStatus() { return $this->status;}
    /* Statics */
    static function User(User $user) {
        $sql = "SELECT * FROM `financeReceipts` WHERE `userID` = :userID AND `deleted` IS NULL";
        $db = new Database($sql, ["userID" => $user->id]);
        if ($db->rows) return $db->rows;
    } 
    static function byStripePaymentIntentID (String $stripePaymentIntentID) {
        $sql = "SELECT * FROM `financeReceipts` WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
        $db = new Database($sql,["stripePaymentIntentID" => $stripePaymentIntentID]);
        if ($db->rows) return $db->rows[0];
    }
    static function UpdateTeamPaymentMethod(Int $teamID, String $stripePaymentMethodID = null) {
        // Message(__METHOD__);
        $openStatuses = Stripe::PaymentIntentStatuses(["open" => true]);
        $sql = "SELECT `id`, `stripePaymentIntentID` FROM `financeReceipts` WHERE `teamID` = :teamID AND (`status` IS NULL OR `status` = '' OR `status` = '" . implode("' OR `status` = '",array_keys($openStatuses))."')";
        // Message($sql);
        $db = new Database($sql,["teamID" => $teamID]);
        if ($db->rows) {
            // Message("Updating Team $teamID = $stripePaymentMethodID");
            $sql = "UPDATE `financeReceipts` SET `stripePaymentMethodID` = :stripePaymentMethodID, `status` = NULL";
            $conn = " WHERE ";
            foreach($db->rows as $r) {
                $sql .= $conn . "`id` = {$r['id']}"; $conn = "AND ";
                Stripe::PaymentIntentPaymentMethod([$r['stripePaymentIntentID']], $stripePaymentMethodID);
            }
            $db = new Database($sql,["stripePaymentMethodID" => $stripePaymentMethodID]);
        }
        // Dump($db->rows);
        //  
    }
    static function ConfirmAll() {
        $sql = "SELECT `id`, `stripePaymentIntentID`,`stripePaymentMethodID` FROM `financeReceipts` WHERE `status` IS NULL";
        $db = new Database($sql);
        if (!$db->rows) return;
        foreach ($db->rows as $r) {
            try {
                $status = Stripe::ConfirmIntent($r['stripePaymentIntentID']);
                $sql = "UPDATE `financeReceipts` SET `status` = :status WHERE `id` = :id";
                // Message("Status $status for {$r['stripePaymentIntentID']}");
            } catch (Exception $e) {
                $status = $e->getMessage();
            }
            new Database($sql, ["status" => $status,"id" => $r['id']]);
            // echo "Processed Stripe Intent {$r['stripePaymentIntentID']} now $status<br>";
        } 
    }
    static function CaptureAll () {
        $sql = "SELECT `id`, `stripePaymentIntentID` FROM `financeReceipts` WHERE `status` = 'requires_capture'";
        $db = new Database($sql);
        if (!$db->rows) return;
        $sql = "UPDATE `financeReceipts` SET `status` = :status WHERE `id` = :id";
        foreach ($db->rows as $r) {
            try {
                $intent = Stripe::CaptureIntent($r['stripePaymentIntentID']);
                $status = $intent->status;
                $sql = "UPDATE `financeReceipts` SET `status` = :status WHERE `id` = :id";
            } catch (Exception $e) {
                $status = $e->getMessage();
            }
            new Database($sql, ["status" => $status,"id" => $r['id']]);
        }
    }
} 