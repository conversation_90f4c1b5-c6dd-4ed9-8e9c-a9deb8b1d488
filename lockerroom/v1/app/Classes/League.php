<?php

class League extends Base {

    protected $dbTable = "leagues";
    public $name, $regionID, $sportID;
    protected $coordinator;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    /* Getters */
    function getSportID() { return $this->sportID;}
    function __toString() { return "{$this->name}";}

    function getCaptainsPack() {
        $sport = new Sport($this->sportID);
        return $sport->getCaptainsPack();
    }
    
    function getCoordinator() {
        return new User($this->coordinator);
    }
    function getCoordinatorID() { return $this->coordinator;}
    
    /* Statics */
    static function Listing() {
        $sql = "SELECT * FROM `leagues` ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }

    static function Live() {
        // $sql = "SELECT * FROM `leagues` WHERE `visible` = 1 ORDER BY `name` ASC";
        $sql = "SELECT `leagues`.* FROM `leagues` WHERE `id` IN (SELECT `seasons`.`leagueID` FROM `seasons` LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id WHERE seasonStatus.live = 1) ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }

    static function Open() {
        // $sql = "SELECT * FROM `leagues` WHERE `visible` = 1 AND (`status` IS NULL OR `status` = 0) ORDER BY `name` ASC";
        $sql = "SELECT `leagues`.* FROM `leagues` WHERE `id` IN (SELECT `seasons`.`leagueID` FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL) ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;        
    }

    static function Statuses() {
        return [
            0 => "Open for Registration",
            1 => "Join Waiting List",
            2 => "Closed for Registration"
        ];
    }
}