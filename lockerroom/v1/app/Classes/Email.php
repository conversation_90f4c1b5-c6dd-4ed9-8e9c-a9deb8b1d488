<?php

// use \phpmailer\phpmailer\phpmailer;

// include (__DIR__."/../vendor/phpmailer/phpmailer/src/PHPMailer.php");

class Email extends \PHPMailer\PHPMailer\PHPMailer {
    public $Host = 'localhost';
    public $Port = 25;

    function __construct ($defaults = true) {
        parent::__construct();
        $this->SMTPSecure = 'tls';
        // global $config;
        // $emailSettings = $config['email'];
        // $this->exceptions = true;
        // $this->isSMTP();
        // $this->SMTPAuth = true;
        // $this->Host = $emailSettings['server'];
        // $this->Username = $emailSettings['username'];
        // $this->Password = $emailSettings['password'];
        // $this->Port = $emailSettings['ports']['outbound'];
        $emailSettings = [
            "host" => "smtp.gmail.com",
            "username" => "<EMAIL>",
            "password" => "(S1mpl1f1c4t10n-Pr0gr4m!)",
            "from" => [
                "email" => "<EMAIL>",
                "name" => "bloomnetball",
            ]
        ];
        $this->exceptions = true;
        $this->isSMTP();
        $this->SMTPSecure = 'tls';
        $this->SMTPAuth = true;
        $this->Host = $emailSettings["host"];
        $this->Username = $emailSettings["username"];
        $this->Password = $emailSettings['password'];
        $this->Port = 587;
        if ($defaults === true) {
            $this->setFrom($emailSettings['from']['email'], $emailSettings['from']['name']);
            $this->addReplyTo($emailSettings['from']['email'], $emailSettings['from']['name']);
            $this->Sender = $this->Username;
            if (isset($emailSettings['bcc'])) {
                foreach ($emailSettings['bcc'] as $bccAddress) {
                    $this->addBCC($bccAddress, $bccAddress);
                }
            }
        }
        $this->isHTML(true);
    }
    function send() {
        $this->Body = "<font face=\"sans-serif\" color=\"#000000\">$this->Body</font>";
        try {
            return parent::send();
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    static function Issue (String $subject, Array $message, Array $to, Array $cc = [], Array $bcc = [], Array $attachments = []) {
        if (!$subject) return "Missing Subject";
        if (!$message) return "Missing Message";
        if (!$to) return "Missing Recipients";
        $e = new self(true);
        foreach ($to as $toEmail => $toName) {
            if (!filter_var($toEmail, FILTER_VALIDATE_EMAIL)) {
                $errorMsg = "Invalid TO email address $toEmail ($toName)";
                return $errorMsg;
            }
            $e->addAddress($toEmail, ($toName) ? $toName : $toEmail);
        }
        $e->Subject = $subject;
        $template = file_get_contents(__DIR__."/../Templates/Email/email-1.htm");
        $template = str_replace(["[SUBJECT]","[MESSAGE]"],[$subject,"<p>".implode("</p><p>",$message)."</p>"],$template);
        $e->Body = $template;
        $e->AltBody = "\n".implode("\n",$message);
        if ($cc) {
            foreach ($cc as $ccEmail => $ccName) {
                if (array_key_exists($ccEmail,$to)) continue;
                if (!filter_var($ccEmail, FILTER_VALIDATE_EMAIL)) {
                    $errorMsg = "Invalid CC email address $ccEmail ($ccName)";
                    return $errorMsg;
                }
                $e->addCC($ccEmail, ($ccName) ? $ccName : $ccEmail);
            } 
        }
        if ($bcc) {
            foreach ($bcc as $bccEmail => $bccName) {
                if (array_key_exists($bccEmail,$to) || array_key_exists($bccEmail,$cc)) continue;
                if (!filter_var($bccEmail, FILTER_VALIDATE_EMAIL)) {
                    $errorMsg = "Invalid BCC email address $bccEmail ($bccName)";
                    return $errorMsg;
                }
                $e->addBCC($bccEmail, ($bccName) ? $bccName : $bccEmail);
            } 
        }
        if ($attachments) {
            foreach ($attachments as $attachment) {
                if (!file_exists($attachment)) return "The accompanying attachment - $attachment could not be located";
                $e->addAttachment($attachment);
            } 
        }
        return $e->send();
    }
    static function Test ($to = ["<EMAIL>" => "A2Z Tech"]) {
        $subject = "Test Message";
        $message = ["Test message from {$_SERVER['HTTP_HOST']}","Sent " .date('Y-m-d H:i:s')];
        $cc = [];
        $bcc = [];
        $attachments = [];
        \Tools\Dump(static::Issue($subject,$message,$to,$cc,$bcc,$attachments));
    }
}
