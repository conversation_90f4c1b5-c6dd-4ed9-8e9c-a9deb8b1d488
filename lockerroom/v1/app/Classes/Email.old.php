<?php

// error_reporting(0);

use \PHPMailer\PHPMailer\PHPMailer;

// include (__DIR__."/../vendor/phpmailer/phpmailer/src/PHPMailer.php");

class Email extends PHPMailer {

    function __construct ($defaults = true) {
        parent::__construct();
        $this->exceptions = true;
        #$this->Mailer = "sendmail";
        if ($defaults === true) {
            global $config;
            $this->setFrom($config['system']['email'], $config['system']['name']);
            $this->addReplyTo($config['system']['email'], $config['system']['name']);
            $this->Sender = $config['system']['email'];
        }
    }

    function send() {
        $this->Body = "<font face=\"sans-serif\" color=\"#000000\">$this->Body</font>";
        try {
            return parent::send();
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    static function Issue (String $subject, Array $message, Array $to, Array $cc = [], Array $bcc = [], Array $attachments = []) {
        if (!$subject) return "Missing Subject";
        if (!$message) return "Missing Message";
        if (!$to) return "Missing Recipients";
        global $config;
        $e = new self(true);
        foreach ($to as $toEmail => $toName) {
            if (!filter_var($toEmail, FILTER_VALIDATE_EMAIL)) {
                $errorMsg = "Invalid TO email address $toEmail ($toName)";
                return $errorMsg;
            }
            $e->addAddress($toEmail, ($toName) ? $toName : $toEmail);
        }
        $e->Subject = $subject;
        $template = file_get_contents(__DIR__."/../Templates/Email/email-1.htm");
        $template = str_replace(["[SUBJECT]","[MESSAGE]"],[$subject,"<p>".implode("</p><p>",$message)."</p>"],$template);
        // $e->Body = "<p>".implode("</p><p>",$message)."</p>";
        $e->Body = $template;
        $e->AltBody = "\n".implode("\n",$message);
        $e->setFrom($config['system']['email'], $config['system']['name']);
        $e->addReplyTo($config['system']['email'], $config['system']['name']);
        $e->Sender = $config['system']['email'];
        if ($cc) {
            foreach ($cc as $ccEmail => $ccName) {
                if (array_key_exists($ccEmail,$to)) continue;
                if (!filter_var($ccEmail, FILTER_VALIDATE_EMAIL)) {
                    $errorMsg = "Invalid CC email address $ccEmail ($ccName)";
                    return $errorMsg;
                }
                if (!$ccName) $ccName = $ccEmail;
                $e->addCC($ccEmail, $ccName);
            } 
        }
        if ($bcc) {
            foreach ($bcc as $bccEmail => $bccName) {
                if (array_key_exists($bccEmail,$to) || array_key_exists($bccEmail,$cc)) continue;
                if (!filter_var($bccEmail, FILTER_VALIDATE_EMAIL)) {
                    $errorMsg = "Invalid BCC email address $bccEmail ($bccName)";
                    return $errorMsg;
                }
                if (!$bccName) $bccName = $bccEmail;
                $e->addBCC($bccEmail, $bccName);
            } 
        }
        $e->addBCC($config['system']['internalEmail'], $config['system']['internalEmail']);
        $e->addBCC("<EMAIL>", "Charlotte Waugh");
        if ($attachments) {
            foreach ($attachments as $attachment) {
                if (!file_exists($attachment)) return "The accompanying attachment - $attachment could not be located";
                $e->addAttachment($attachment);
            } 
        }
        return $e->send();
    }
    static function Test ($to = ["<EMAIL>" => "A2Z Tech"]) {
        $subject = "Test Message";
        // echo $subject."<br>";
        $message = ["Test message from {$_SERVER['HTTP_HOST']}","Sent " .date('Y-m-d H:i:s')];
        $cc = [];
        $bcc = [];
        $attachments = [];
        return static::Issue($subject,$message,$to,$cc,$bcc,$attachments);
    }
}
