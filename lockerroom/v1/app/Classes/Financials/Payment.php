<?php
namespace Models\Financials;

use Core\Base as Base;
use Core\Datastore as Datastore; 
use Core\Message as Message;
use Models\User as User;
use Fpdf\Fpdf;

class Payment extends Base {
    
    protected $dbTable = "paymentMain", $dbKey = "id";
    protected $dbFields = ["type","userID"];

    protected $id, $type, $userID;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function __toString() {
        return new PaymentType($this->type) . "-{$this->id}";
    }
    function Save() {
        parent::Save();
    }
    function Receipt () {
        // $transactionType = new TransactionType($this->type);
        // $transactionItems = TransactionItem::byTransaction($this->id);
        // $folder = "/var/www/html/app/Downloads/Transactions/";
        // $file = "{$transactionType}-{$this->id}.pdf";
        // if ($this->issue) return ["folder" => $folder, "file" => $file];
        // $pdf = new FPDF('P','mm','A4');
        // $pdf->AddPage();
        // $pdf->Image("/var/www/html/img/leagues4you-logo-only_260x60.png",131,12);
        // $pdf->setXY(10,14);
        // $pdf->SetFont('Arial','B',32);
        // $pdf->Cell(0,15,$transactionType->name,"B");
        // $pdf->SetFont('Arial','',12);
        // $pdf->setXY(150,35);
        // $from = ["Leagues 4 You", "The Malvern Spa Hotel","Grovewood Rd","Malvern","WR14 1GD"];
        // $pdf->MultiCell(0,5,implode("\n",$from),null,"L");
        // $pdf->setXY(10,$pdf->GetY()+5);
        // $contact = new User($this->user);
        // $to[] = $contact->name();
        // if ($contact->email) $to[] = $contact->email;
        // $pdf->SetFont('Arial','B',12);
        // $pdf->Cell(0,5,"To:",null,1);
        // $pdf->SetFont('Arial','',12);
        // $pdf->MultiCell(0,5,implode("\n",$to),null,"L");
        // $pdf->Ln(5);
        // $pdf->SetFont('Arial','B',20);
        // $pdf->Cell(0,15,"Items","B",1);
        // $pdf->Ln(5);
        // $pdf->SetFont('Arial','B',12);
        // $pdf->Cell(20,7,"Qty");
        // $pdf->Cell(100,7,"Description");
        // $pdf->Cell(35,7,"Unit",null,null,"R");
        // $pdf->Cell(35,7,"SubTotal",null,1,"R");
        // $pdf->SetFont('Arial','',12);
        // foreach ($transactionItems as $k => $v) {
        //     $pdf->Cell(20,5,$v->qty);
        //     $pdf->Cell(100,5,$v->description);
        //     $pdf->Cell(35,5,$v->unit,null,null,"R");
        //     $pdf->Cell(35,5,$v->total,null,1,"R");    
        // }
        // $pdf->Line(10,$pdf->GetY()+15,200,$pdf->GetY()+15);
        // // $pdf->SetXY(150,250);
        // $pdf->Ln(5);
        // $pdf->SetFont('Arial','B',12);
        // $pdf->Cell(20,5,"Total");
        // $pdf->SetFont('Arial','B',12);
        // $pdf->Cell(170,5,$this->total,null,null,"R");
        // $pdf->Output('F',$folder.$file);
        // return ["folder" => $folder,"file" => $file];
    } 
    static function PerTransaction (Int $transactionID) {
        return PaymentItem::PerTransaction($transactionID);
    }
    
}