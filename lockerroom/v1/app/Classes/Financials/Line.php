<?php
namespace Models\Financials;
use Core;

class Line extends Core\Base {
    
    protected $id, $transaction;
    protected $qty, $title, $description;
    protected $unit;
    protected $net = null;
    protected $vatRate = 0, $vat = null;
    protected $total;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        parent::Save();
    }
}