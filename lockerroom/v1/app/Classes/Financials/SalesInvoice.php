<?php
namespace Models\Financials;
use Core\Datastore;
use Models\Team;
use Models\Fixture;
use Models\User;
use Models\Division;

class SalesInvoice extends Transaction {

    protected $type = 1;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        parent::Save();
    }
    static function Listing (Int $transactionType = null) {
        return parent::Listing(1);
    }
    static function TeamDivision (Int $divisionID, Int $teamID) {
        $sql = "SELECT `id` FROM `transactionMain` WHERE `divisionID` = $divisionID AND `teamID` = $teamID";
        $rlt = Datastore::Query($sql);
        if (!$rlt || !is_array($rlt) || count($rlt)==0) {
            $t = new static();
            $team = new Team($teamID);
            $t->date = date('Y-m-d');
            $t->divisionID = $divisionID;
            $t->teamID = $teamID;
            $t->contactID = $team->captain;
            $t->Save();
            return $t->id;
        } else return $rlt[0]['id'];
    }
    static function Fixture (Fixture $fixture) {
        $division = new Division($fixture->divisionID);
        $home = new Team($fixture->home);
        $away = new Team($fixture->away);
        if (!$home->captain || !$away->captain) {
            $msg = null;
            if (!$home->captain) $msg .= "Missing Home captain.";
            if (!$away->captain) $msg .= " Missing Away captain";
            $fixture->setBillingInfo(trim($msg));
            return;
        }
        $captain = new User($home->captain);
        $homeTransaction = static::Raise($captain->id, $division->seasonID);
        $fixture->homeTrans = $homeTransaction->id;
        TransactionItem::Raise ($homeTransaction->id, $home->id, $fixture->id, $fixture->feeMatch);
        $captain = new User($away->captain);
        $awayTransaction = static::Raise($captain->id, $division->seasonID);
        $fixture->awayTrans = $awayTransaction->id;
        TransactionItem::Raise ($awayTransaction->id, $away->id, $fixture->id, $fixture->feeMatch);
        $fixture->Save();
        $homeTransaction->Save();
        $awayTransaction->Save();
    }
}
