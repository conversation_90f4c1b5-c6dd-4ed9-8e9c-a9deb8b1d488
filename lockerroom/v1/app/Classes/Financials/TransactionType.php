<?php
namespace Models\Financials;
use Core;

class TransactionType extends Core\Base {
    protected $dbTable = "transactionTypes", $dbKey = "id";
    protected $id, $name, $shortCode, $ledger, $action;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function __toString() {
        return "{$this->shortCode}";
    }
    function Save() {
        parent::Save();
    }
    static function Listing () {
        $return[1] = new static();
        $return[1]->name = "Sales Invoice";
        $return[1]->shortCode = "SI";
        $return[1]->ledger = 1; # Sales

        $return[5] = new static();
        $return[5]->name = "Sales Credit";
        $return[5]->shortCode = "SC";
        $return[5]->ledger = 1; # Sales

        $return[10] = new static();
        $return[10]->name = "Purchase Invoice";
        $return[10]->shortCode = "PI";
        $return[10]->ledger = 2; # Purchase

        $return[15] = new static();
        $return[15]->name = "Purchase Credit";
        $return[15]->shortCode = "PC";
        $return[15]->ledger = 2; # Purchase

        return $return;
    }
}
