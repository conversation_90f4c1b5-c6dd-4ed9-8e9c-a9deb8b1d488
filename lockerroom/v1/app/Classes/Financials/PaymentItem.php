<?php
namespace Models\Financials;
use Core;
use Core\Datastore as Datastore; 
use Models\Fixture as Fixture;
use Models\Booking as Booking;
use Models\Team as Team;
use Core\Message as Message;

class PaymentItem extends Core\Base {
    
    protected $dbTable = "paymentSub", $dbKey = "id";
    protected $dbFields = ["paymentID","transactionID","amount"];

    protected $id;
    protected $main, $transactionID, $amount;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function __toString() {
        return "{$this->amount}";
    }
    function Save() {
        $payment = new Payment($this->paymentID);
        $paymentType - new PaymentType($payment->type);
        if ($paymentType->action == "-") $this->amount = -abs($this->amount);
        parent::Save();
    }
    static function PerTransaction (Int $transactionID) {
        $sql = "SELECT SUM(`amount`) AS `total` FROM `paymentSub` WHERE `transactionID` = $transactionID";
        $rlt = Datastore::Query($sql);
        return ( (isset($rlt[0]['total'])) && $rlt[0]['total']) ? $rlt[0]['total'] : 0;
    }
}