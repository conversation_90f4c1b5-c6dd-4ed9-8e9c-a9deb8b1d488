<?php
namespace Models\Financials;
use Core;

class Card extends Core\Base {
    
    public $paymentResult = false;
    private $testSecretKey = "sk_test_nPP6uGTKsyOJf2R7aLh1NXRQ00ejuiVoR6";
    // private $liveSecretKey = "********************************";
    private $testPublishableKey = "pk_test_FmEPJMe1qk5A7mWzpqRS7mNP00xLYunUMn";
    // private $livePublishableKey = "pk_live_pSGTGMBWD9zKGlGv4cjC0QUK";
    protected $secretKey, $publishKey; # Set these to Live or Test as appropriate

    public function __construct() {
        // $this->secretKey = $this->liveSecretKey;
        // $this->publishKey = $this->livePublishableKey;
        $this->secretKey = $this->testSecretKey;
        $this->publishKey = $this->testPublishableKey;

        \Stripe\Stripe::setApiKey($this->secretKey);
    }

    function Save () {
        parent::Save();
    }
    static function TestCards () {
        return "4000 0025 0000 3155"; # Triggers SCA Auth on Save or 1st Payment
        // return "****************"; # Triggers SCA Auth always
        // return "****************"; # Returns "Insufficient Funds" after successful SCA Auth
    }
    static function Store (String $token) {
        $stripe = new static();
        $setupIntent = \Stripe\SetupIntent::create([
            "usage" => "off_session"
        ]);
        $paymentMethod = \Stripe\PaymentMethod::create([
            'type' => 'card',
            'card' => [
              'token' =>  $token
            ]
          ]);
          $setupIntent->confirm([
            'payment_method' => $paymentMethod->id
          ]);
    }
    static function getIntent (String $intentID) {
        $sql = "SELECT * FROM `stripe_intents` WHERE `intentID` = :intentID";
        $rlt = Core\Datastore::Query($sql, ["intentID" => $intentID]);
        if (!$rlt || !is_array($rlt) || count($rlt) ==0) return;
        return $rlt[0];
    }
    static function getPaymentMethod (String $paymentMethodID) {
        $sql = "SELECT * FROM `stripe_paymentMethods` WHERE `stripeID` = :paymentMethodID";
        $rlt = Core\Datastore::Query($sql, ["paymentMethodID" => $paymentMethodID]);
        if (!$rlt || !is_array($rlt) || count($rlt) ==0) return;
        return $rlt[0];
    }    
    static function IntentStatus (String $intentID) {
        $stripe = new static();
        $intent = \Stripe\SetupIntent::retrieve($intentID);
        // dump($intent); return;
        $sql = "UPDATE `stripe_intents` SET `pm` = :pm, `status` = :status WHERE `intentID` = :intentID";
        $data = [
            "pm" => $intent->payment_method, 
            "status" => $intent->status, 
            "intentID" => $intentID];
        Core\Datastore::Update($sql,$data);
        return $intent;
    }
    static function UserPaymentLinks () {
        # Look-up any stripe_intents not linked.
        $sql = "SELECT ``";
    //     $sql = "SELECT `pm`, `"
    //     $payment_method = \Stripe\PaymentMethod::retrieve('pm_1F9y7wGDf9qiHCIbaZy25rxL');
    //     $payment_method->attach(['customer' => 'cus_FfLUIQqt50Ku14']);        
    }
    static function PaymentMethodStatus (String $paymentMethodID) {
        $stripe = new static();
        $paymentMethod = \Stripe\PaymentMethod::retrieve($paymentMethodID);
        $sql = "INSERT INTO `stripe_paymentMethods` SET `stripeID` = :stripeID,  `brand` = :brand, `exp_month` = :exp_month, `exp_year` = :exp_year, `last4` = :last4 ON DUPLICATE KEY UPDATE `brand` = :brand, `exp_month` = :exp_month, `exp_year` = :exp_year, `last4` = :last4";
        $data = [
            "stripeID" => $paymentMethodID,
            "brand" => $paymentMethod->card->brand,
            "exp_month" => $paymentMethod->card->exp_month,
            "exp_year" => $paymentMethod->card->exp_year,
            "last4" => $paymentMethod->card->last4
        ];
        Core\Datastore::Insert($sql, $data);
        return $paymentMethod;
    }
    static function getCustomer (Int $userID) {
        if (!$userID)return;
        $sql = "SELECT `stripeCustomerID` FROM `users` WHERE `id` = :id";
        $rlt = Core\Datastore::Query($sql, ["id" => $userID]);
        if (isset($rlt[0]['stripeCustomerID'])) return $rlt[0]['stripeCustomerID'];
        return static::setCustomer($userID);
    }
    static function setCustomer (Int $userID) {
        $stripe = new static();
        $user = new \Models\User($userID); if (!$user || !$user->id) return;
        $customer = \Stripe\Customer::create(["email" => $user->email]);
        $sql = "UPDATE`users` SET `stripeCustomerID` = :stripeCustomerID WHERE `id` = :id";
        Core\Datastore::Insert($sql,["stripeCustomerID" => $customer->id, "id" => $userID]);
        return $customer->id;
    }
    static function Add (Int $userID) {
        /**
         * Assumes <script src="https://js.stripe.com/v3/"></script> already included on page
         */
        $stripe = new static();
        $setupIntent = \Stripe\SetupIntent::create([]);
        $sql = "INSERT INTO `stripe_intents` SET `user` = :userID, `intentID` = :intentID";
        Core\Datastore::Insert($sql, ["userID" => $userID, "intentID" => $setupIntent->id]);
        echo '
        <style>
            .card-element {
                max-width: 500px;
                display: flex;
                flex-direction: column;
            }
            .response {
                color: white;
                padding: 1rem;
            }
            .responseFailed {
                background-color: red;
            }
            .responseSuccess {
                background-color: green;
            }
        </style>
        <div class="card-element" id="card-element"></div>
        <div id="card-errors" role="alert"></div>
        <button class="btn" style="margin-top: 1rem;" id="card-button" data-secret="'.$setupIntent->client_secret.'">Save Card</button>
        <p class="response" id="response"></p>
        <script>
            var stripe = Stripe("'.$stripe->publishKey.'");
            var elements = stripe.elements();
            var cardElement = elements.create("card");
            cardElement.mount("#card-element");

            var cardButton = document.getElementById("card-button");
            var clientSecret = cardButton.dataset.secret;
            
            cardButton.addEventListener("click", function(ev) {
              stripe.handleCardSetup(
                clientSecret, cardElement, {}
              ).then(function(result) {
                  var responseElement = document.getElementById("response");
                if (result.error) {
                    location.href = "Billing/Cards?result=failure";
                    // responseElement.classList.add("responseFailed");
                    // responseElement.innerHTML = "Apologies - we could not add that card. Authentication failed";
                } else {
                    location.href = "Billing/Cards?result=success";
                    // responseElement.classList.add("responseSuccess");
                    // responseElement.innerHTML = "Thank you. You have successfully registered your payment card with us";
                }
              });
            });            
        </script>
        ';
    }
    static function Available (Int $userID) {
        $sql = "SELECT * FROM `stripe_paymentMethods` WHERE `deleted` IS NULL AND `stripeID` IN (SELECT `pm` FROM `stripe_intents` WHERE `user` = :userID)";
        return Core\Datastore::Query($sql, ["userID" => $userID]);
    }
    static function Process () {
        $sql ="SELECT `intentID`FROM `stripe_intents` WHERE `status` IS NULL";
        $rlt = Core\Datastore::Query($sql);
        if (!$rlt || !is_array($rlt) || count($rlt)==0) return;
        foreach ($rlt as $r) {
            $intent = static::IntentStatus($r['intentID']);
            if (!$intent->payment_method) continue;
            $paymentMethod = static::PaymentMethodStatus($intent->payment_method);
        }
    }
    static function Linking () {
        $stripe = new static();
        $sql ="SELECT `id`,`intentID`,`user` FROM `stripe_intents` WHERE `linked` IS NULL";
        $rlt = Core\Datastore::Query($sql);
        if (!$rlt) return;
        foreach ($rlt as $r) {
            $intent = \Stripe\SetupIntent::retrieve($r['intentID']);
            if (!$intent->payment_method) continue;
            $paymentMethod = \Stripe\PaymentMethod::retrieve($intent->payment_method);
            $user = new \Models\User($r['user']);
            $paymentMethod->attach(['customer' => $user->stripeCustomerID]);
            Core\Datastore::Update("UPDATE `stripe_intents` SET `linked` = 1 WHERE `id` = " . $r['id']);
        }
    }    
    static function paymentMethodByID (Int $id) {
        $sql = "SELECT * FROM `stripe_paymentMethods` WHERE `id` = :id";
        $rlt = Core\Datastore::Query($sql, ["id" => $id]);
        if (!$rlt || !is_array($rlt) || count($rlt) != 1) return;
        return $rlt[0];
    }
    static function Remove (Int $id) {
        $stripe = new static();
        $sql = "SELECT `stripeID` FROM `stripe_paymentMethods` WHERE `id` = :id";
        $rlt = Core\Datastore::Query($sql, ["id" => $id]);
        if (!$rlt || !is_array($rlt) || count($rlt) ==0) return;
        $payment_method = \Stripe\PaymentMethod::retrieve($rlt[0]['stripeID']);
        $payment_method->detach();
        Core\Datastore::Update("UPDATE `stripe_paymentMethods` SET `deleted` = :deleted WHERE `id` = :id",["deleted" => date('Y-m-d H:i:s'),"id" => $id]);
        Core\Message::Set("We have removed that card from our system","success");
    }
    static function Charge (Int $userID = null, Array $lineItems = []) {
        $user = new \Models\User($userID);
        $cards = static::Available($userID);
        $stripe = new static();
        return \Stripe\PaymentIntent::create([
            'amount' => (array_sum($lineItems)*100),
            'currency' => 'gbp',
            'payment_method_types' => ['card'],
            'customer' => $user->stripeCustomerID,
            'payment_method' => $cards[0]['stripeID'],
            'off_session' => true,
            'confirm' => true,
        ]);
    }
}