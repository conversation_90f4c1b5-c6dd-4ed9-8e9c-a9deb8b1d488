<?php

/*
See: https://stripe.com/docs/payments/integration-builder

Test Card (No Auth) 4242 4242 4242 4242
Test Card (Always Req. Auth) 4000 0027 6000 3184
Test Card (Auth initially then Insufficient Funds) 4000 0082 6000 3178
*/

# Hold UserID => StripeCustomerID mappings in stripeCustomers table
# Create Finance Payments and Refunds table which have Stripe, GoCardless and PayPal as options.

class Stripe {

    protected $secretKey;
    public $publicKey;
    protected $live = false;
    protected $client;

    function __construct($live = true) {
        global $config;
        $this->live = $live;
        if ($this->live === true) {
            $this->secretKey = $config['stripe']['liveSecretKey'];
            $this->publicKey = $config['stripe']['livePublicKey'];
        } else {
            $this->secretKey = $config['stripe']['testSecretKey'];
            $this->publicKey = $config['stripe']['livePublicKey'];
        }
        \Stripe\Stripe::setApiKey($this->secretKey);
        $this->client = new \Stripe\StripeClient($this->secretKey);
    }
    function RemoveCard(String $paymentMethodID) {
        $this->client->paymentMethods->detach($paymentMethodID);
    }
    function ProgressPaymentIntent (String $paymentIntentID, String $paymentMethodID) {
        /**
         * Allowable PaymentIntent Cancel Statuses are
         * requires_payment_method, requires_capture, requires_confirmation, or requires_action
         * Possible PaymentIntent Cancel Reasons are
         * duplicate, fraudulent, requested_by_customer, or abandoned
         */
        $data = [
            "payment_method" => $paymentMethodID,
            // 'confirm' => true,
            'off_session' => true            
        ];
        try {
            $pi = $this->client->paymentIntents->confirm($paymentIntentID,$data);
            $sql = "UPDATE `stripePayments` SET `payment_method` = :payment_method WHERE `intentID` = :intentID";
            $db = new Database($sql, ["payment_method" => $paymentMethodID, "intentID" => $paymentIntentID]);
            return $pi;
        } catch (Exception $e) {
            return (String) $e->getMessage();
        }
    }
    function CapturePaymentIntent(String $paymentIntentID, Float $amount_to_capture = null) {
        /**
         * PaymentIntent status must be requires_capture
         * If amount_to_capture specified, must be same or less than pre-auth'd amount
         */
        # Is PaymentIntent valid?
        $paymentIntent = $this->getPaymentIntent($paymentIntentID);
        if (!$paymentIntent || $paymentIntent->status != "requires_capture") return (string)"Invalid or Uncapturable";
        $amount_to_capture = (!$amount_to_capture) ? $paymentIntent->amount : $amount_to_capture *= 100;
        if ($amount_to_capture > $paymentIntent->amount) return (String)"Cannot charge " . number_format($amount_to_capture/100,2) ." which exceeds the maximum of the pre-authorised amount of " . number_format($paymentIntent->amount/100,2) .". To bill more will require a new charge.";
        $pi = $this->client->paymentIntents->capture($paymentIntentID,["amount_to_capture" => $amount_to_capture]);
        $sql = "UPDATE `stripePayments` SET `status` = :status";
        $db = new Database($sql,["status" => $pi->status,"amount" => $amount_to_capture]);
        return $pi;
    }
    function Refund (String $paymentIntentID, Float $amount = null) {
        /* Returns STRING error message or Stripe Refund Object */
        $paymentIntent = $this->getPaymentIntent($paymentIntentID);
        # Verifying how much is refundable
        $refundable = 0;
        foreach ($paymentIntent->charges->data as $item) {
            $refundable += ($item->amount - $item->amount_refunded);
        }
        if (!$refundable) return (String)"Nothing available to refund from this charge";
        # Amount will be represented in pence.
        if (!$amount) {
            $amount = $refundable;
        } else $amount *= 100;
        if ($amount > $refundable) return (String)"Maximum refund against this charge is " . ($refundable / 100);
        return $this->client->refunds->create([
            'payment_intent' => $paymentIntentID,
            'amount' => $amount
        ]);
    }
    function getSetupIntent(String $setupIntent) {
        /* Returns full setup Intent object */
        return \Stripe\SetupIntent::retrieve($setupIntent);
    }
    function setupForm($setupIntent) {
        $formAction = "./User/Payments";
        $successRedirect = "./User/Payments";
        ?>
        <form action="<?php echo $formAction;?>" id="payment-form" class="mx-2">
            <h3>Card Details</h3>
            <p>Please enter the details for your card below.</p>
            <div id="failureResult" class="bg-danger text-light d-none p-3 my-2"></div>
            <div id="card-element"></div>
            <button data-secret="<?php echo $setupIntent->client_secret; ?>" type="button" id="card-button" class="btn btn-purple my-4">
                Save Card
            </button>
            <p title="<?php echo $setupIntent->id; ?>">Ref: <?php echo $setupIntent->id; ?></p>
        </form>
        <script>
            var stripe = Stripe('<?php echo $this->publicKey;?>');
            var elements = stripe.elements();
            var cardElement = elements.create('card');
            cardElement.mount('#card-element');

            var cardholderName = document.getElementById('cardholder-name');
            var cardButton = document.getElementById('card-button');
            var clientSecret = cardButton.dataset.secret;

            cardButton.addEventListener('click', function(ev) {

                stripe.confirmCardSetup(
                    clientSecret,
                    {
                        payment_method: {card: cardElement},
                    }
                ).then(function(result) {
                    if (result.error) {
                        document.getElementById("failureResult").innerHTML = "<b>Sorry.</b> We could not authenticate those card details. You can try again, try another card or contact your Co-ordinator for more help.<br>The message we received back from the card provider reads " + result.error.message;
                        document.getElementById("failureResult").classList.remove("d-none");
                    } else {
                        window.location.replace("<?php echo $successRedirect;?>?si=<?php echo $setupIntent->id; ?>");
                    }
                });
            });
        </script><?php
    }
    function paymentForm($paymentIntent) {
        $formAction = "./User/Payments";
        $successRedirect = "./User/Payments";
         ?>
        <form action="<?php echo $formAction;?>" method="post" id="payment-form">
            <h4>Pay &pound;<?php echo number_format($paymentIntent->amount/100,2);?></h4>
            <p>Please enter your card details below</p>
            <div id="failureResult" class="bg-danger text-light d-none p-3 my-2"></div>
            <div id="card-element"></div>
            <div id="card-errors" role="alert"></div>
            <!-- <label for="saveCard">
                Save Card?
                <input type="checkbox" name="saveCard" id="saveCard" value="1" checked>
            </label><br> -->
            <button class="btn btn-sm btn-purple my-4" id="submit">Pay</button>
            <p title="<?php echo $paymentIntent->id; ?>">Ref: <?php echo $paymentIntent->id; ?></p>
        </form>
        <style>
            .StripeElement {
                background-color: white;
                height: 40px;
                padding: 10px 12px;
                border-radius: 4px;
                border: 1px solid transparent;
                box-shadow: 0 1px 3px 0 #e6ebf1;
                -webkit-transition: box-shadow 150ms ease;
                transition: box-shadow 150ms ease;
                display: flex;
                flex-direction: column;
            }
  
            .StripeElement--focus {
                box-shadow: 0 1px 3px 0 #cfd7df;
            }
  
            .StripeElement--invalid {
                border-color: #fa755a;
            }
  
            .StripeElement--webkit-autofill {
                background-color: #fefde5 !important;
            }
        </style>
        <script>
            var stripe = Stripe('<?php echo $this->publicKey;?>');
            var elements = stripe.elements();
            var style = {
                base: {
                    color: "#32325d",
                }
            };
            var card = elements.create("card", { style: style });
            card.mount("#card-element");
            var clientSecret = "<?php echo $paymentIntent->client_secret;?>";
            card.on('change', ({error}) => {
                const displayError = document.getElementById('card-errors');
                if (error) {
                    displayError.textContent = error.message;
                } else {
                    displayError.textContent = '';
                }
            });
            var form = document.getElementById('payment-form');
            form.addEventListener('submit', function(ev) {
                ev.preventDefault();
                stripe.confirmCardPayment(clientSecret, {
                    payment_method: {card: card},
                    payment_method: intent.last_payment_error.payment_method.id,
                    setup_future_usage: 'off_session'
                }).then(function(result) {
                    if (result.error) {
                        document.getElementById("failureResult").innerHTML = "<b>Sorry.</b> We could not authenticate those card details. You can try again, try another card or contact your Co-ordinator for more help.<br>The message we received back from the card provider reads " + result.error.message;
                        document.getElementById("failureResult").classList.remove("d-none");
                    } else {
                        window.location.replace("<?php echo $successRedirect;?>?pi=<?php echo $paymentIntent->id; ?>");
                    }
                });
            })
            </script><?php
    }
    function Charge(Float $amount, String $paymentMethodID, String $description = null) {
        if (!$description) {
            global $Config;
            $description = "Payment to {$Config['Organisation']}";
        } 
        return $this->client->charges->create([
            'amount' => ($amount * 100),
            'currency' => 'gbp',
            'source' => $paymentMethodID,
            'description' => $description,
          ]);
    }
    static function GetCustomer(Int $userID) {
        $stripe = new static();
        $sql = "SELECT `stripeCustomerID` FROM `stripeCustomers` WHERE `userID` = :userID";
        $db = new Database($sql, ["userID" => $userID]);
        if ($db->rows) {
            $customer = $stripe->client->customers->retrieve($db->rows[0]['stripeCustomerID']);
            if ($customer) return $customer;
        } 
    }
    static function CreateCustomer (Int $userID, String $email) {
        /* Returns full Stripe Customer object */
        $customer = \Stripe\Customer::create(["email" => $email]);
        $sql = "INSERT INTO `stripeCustomers` SET `userID` = :userID, `stripeCustomerID` = :stripeCustomerID";
        $db = new Database($sql, ["userID" => $userID, "stripeCustomerID" => $customer->id]);
        return $customer;
    }
    static function CreateSetupIntent (String $stripeCustomerID) {
        /* Returns full setup Intent object */
        try {
            $si = \Stripe\SetupIntent::create([
                "customer" => $stripeCustomerID,
                "payment_method_types" => ['card'],
                "usage" => "off_session"
            ]);
            return $si;
        } catch (Exception $e) {
            return (String)$e->getMessage();
        }
    }
    static function CreatePaymentIntent(String $stripeCustomerID, Float $amount, String $description = null) {
        /**
         * Returns Stripe Payment Intent on SUCCESS, String [Error Message] on FAILURE
         * Payment Intent Statuses
         * requires_payment_method, requires_capture, requires_confirmation, requires_action
         */
        if (!$description) {
            global $Config;
            $description = "Payment to {$Config['Name']}";
        } 
        $stripe = new static();
        try {
            $intent = $stripe->client->paymentIntents->create([
                'amount' => ($amount*100),
                'currency' => 'gbp',
                'customer' => $stripeCustomerID,
                'description' => $description,
                'capture_method' => 'manual'
            ]);
            // $sql = "INSERT INTO `stripePayments` SET `stripeCustomerID` = :stripeCustomerID, `intentID` = :intentID, `amount` = :amount, `status` = :status";
            // $data = [
            // "stripeCustomerID" => $stripeCustomerID,
            // "intentID" => $intent->id,
            // "amount" => $amount,
            // "status" => $intent->status
            // ];
            // $db = new Database($sql, $data);
            // Dump($db);
            return $intent;
        } catch (Exception $e) {
            return (String) $e->getMessage();
        }
    }
    static function PaymentIntentPaymentMethod(Array $paymentIntentIDs = [], String $paymentMethodID) {
        if (!$paymentIntentIDs)
        
        $return = [];
        foreach ($paymentIntentIDs as $paymentIntentID) {
            $stripe = new static();
            $return[] = $stripe->client->paymentIntents->update(
                $paymentIntentID,
                ['payment_method' => $paymentMethodID]
              );
        }
        return $return;
    }
    static function CancelPaymentIntent(String $paymentIntentID, String $reason = "duplicate") {
        $stripe = new static();
        $data = ($reason) ? ["cancellation_reason" => $reason] : [];
        try {
            $pi = $stripe->client->paymentIntents->cancel(
                $paymentIntentID,
                $data
            );
            $sql = "UPDATE `stripePayments` SET `status` = :status";
            $db = new Database($sql,["status" => $pi->status]);
            return $pi;
        } catch (Exception $e) {
            return (String)$e->getMessage();
        } 
    }
    static function PaymentAuth(String $stripePaymentIntentID) {
        $stripe = new static();
        $paymentIntent = static::getPaymentIntent($stripePaymentIntentID);
        $successRedirect = "./User/Payments";?>
        <button class="btn btn-sm btn-purple" id="authenticate">Confirm <?php echo $paymentIntent->amount;?></button>
        <script>
            function CardAuth() {
                var stripe = Stripe('<?php echo $stripe->publicKey;?>');
                stripe
                .confirmCardPayment('<?php echo $paymentIntent->client_secret;?>')
                .then(function(result) {
                    window.location.replace("<?php echo $successRedirect;?>?pi=<?php echo $stripePaymentIntentID; ?>");
                });
            }
            document.getElementById("authenticate").addEventListener("click",CardAuth);
        </script><?php
    }
    static function PaymentCards(Int $userID) {
        $stripeCustomer = static::GetCustomer($userID);
        if (!$stripeCustomer) return;
        $stripe = new static();
        return \Stripe\PaymentMethod::all([
            'customer' => $stripeCustomer->id,
            'type' => 'card',
          ]);
    }
    static function ConfirmIntent(String $paymentIntentID) {
        $stripe = new static();
        $intent = $stripe->client->paymentIntents->confirm($paymentIntentID);
        // Message("Payment ID $paymentIntentID = {$intent->status}");
        return $intent->status;
    }
    static function CaptureIntent(String $paymentIntentID) {
        $stripe = new static();
        return $stripe->client->paymentIntents->capture($paymentIntentID);
    }
    static function getPaymentIntent(String $paymentIntentID) {
        $stripe = new static();
        $pi = $stripe->client->paymentIntents->retrieve($paymentIntentID);
        /*
            if the payment is succeeded and the teamID does not have a Payment Method
            in the treasurers table - allocate this Payment Method to the Team
        */
        $sql = "UPDATE `financeReceipts` SET `status` = :status WHERE `stripePaymentIntentID` = :intentID";
        $db = new Database($sql, ["status" => $pi->status, "intentID" => $paymentIntentID]);
        // exit(Dump($db));
        return $pi;
    }
    static function PaymentIntentStatuses(Array $filter = []) {
        /*
            See: https://stripe.com/docs/payments/intents#intent-statuses
            This function returns ALL Status ENUMS (if no filter submitted)
            Otherwise, statuses are looped and only returned if they ==== match
        */
        $statuses = [
            "requires_payment_method" => ["open" => true],
            "requires_confirmation" => ["open" => true],
            "requires_action" => ["open" => true],
            "processing" => ["open" => false],
            "requires_capture" => ["open" => true],
            "canceled" => ["open" => false],
            "succeeded" => ["open" => false]
        ];
        if (!$filter) return $statuses;
        $return = [];
        foreach ($filter as $filterName => $filterValue) {
            foreach ($statuses as $statusName => $status) {
                if (!isset($status[$filterName]) || $status[$filterName] !== $filterValue) continue;
                $return[$statusName] = $status;
            }
        }
        return $return;
    }
    static function Test_PaymentIntentStatuses() {
        $allStatuses = static::PaymentIntentStatuses();
        echo "<h4>All Stripe Payment Intent Statuses</h4>";
        Dump($allStatuses);
        $openStatuses = static::PaymentIntentStatuses(["open" => true]);
        echo "<h4>Only Open Stripe Payment Intent Statuses</h4>";
        Dump($openStatuses);
        $closedStatuses = static::PaymentIntentStatuses(["open" => false]);
        echo "<h4>Only Closed Stripe Payment Intent Statuses</h4>";
        Dump($closedStatuses);
    }
    static function getPaymentMethod(String $paymentMethodID) {
        $stripe = new static();
        return $stripe->client->paymentMethods->retrieve($paymentMethodID);
    }
}