<?php

class UserContact extends Base {

    protected $dbTable = "userContacts";
    protected $dbFields = ["userID","contactID","deleted"];
    protected $dbOnDuplicate = ["userID","contactID","deleted"];
    protected $userID, $contactID;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        /* Validation */
        if (!$this->userID || !$this->contactID) return "A User ID and Contact ID are both required";
        $sql = "INSERT INTO {$this->dbTable} SET `userID` = :userID, `contactID` = :contactID ON DUPLICATE KEY UPDATE `deleted` = NULL"; 
        $sqlData = ["userID" => $this->userID, "contactID" => $this->contactID];
        $db = new Database($sql, $sqlData);
        if ($db->errors) return implode(". ",$db->errors);
        if (!$this->id) $this->id = $db->lastInsertID;
        return (int)$this->id;
    }
    /* Getters */
    function getUserID() { return $this->userID;}
    function getContactID() { return $this->contactID;}
    /* Setters */
    function setUserID(Int $userID) { $this->userID = $userID;}
    function setContactID(Int $contactID) { $this->contactID = $contactID;}
    /* Static */
    static function Contacts(Int $userID) {
        $sql = "SELECT `contactID` FROM `userContacts` WHERE `userID` = :userID";
        $db = new Database($sql, ["userID" => $userID]);
        if ($db->errors) return implode(". ",$db->errors);
        $return = [];
        foreach ($db->rows as $r) {
            $return[] = new User($r['contactID']);
        }
        return $return;
    }
}