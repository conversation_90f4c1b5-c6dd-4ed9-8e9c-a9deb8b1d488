<?php

class Division extends Base {

    protected $dbTable = "divisions";

    protected $name;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function __toString() {
        return "{$this->name}";
    }
    static function DefaultSeason (Int $seasonID) {
        $sql = "SELECT `id` FROM `divisions` WHERE `deleted` IS NULL AND `seasonID` = :seasonID ORDER BY `created` DESC LIMIT 1";
        $db = new Database($sql, ["seasonID" => $seasonID]);
        if ($db->rows) return $db->rows[0]['id']; 
    }
    static function Default (Season $season) {
        $divisionID = static::DefaultSeason($season->id);
        /* If not Division - create one */
        return $divisionID;
    }
}