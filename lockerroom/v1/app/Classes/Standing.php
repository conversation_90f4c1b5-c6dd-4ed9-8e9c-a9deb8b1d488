<?php

class Standing extends Base {

    protected $dbTable = "standings";
    protected $fixtureID;
    protected $teamID;
    protected $points, $bp;
    protected $win, $draw;
    protected $plus, $minus;
    protected $text;
    protected $dbFields = ["fixtureID", "teamID", "points","bp", "win", "draw","plus","minus","text"];

    function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() { return "{$this->name}";}
    function getFixtureID() { return $this->fixtureID;}
    function getTeamID() { return $this->teamID;}
    function getPoints () { return $this->points;}
    function getBp () { return $this->bp;}
    function getTotal () { return ($this->points + $this->bp);}
    function getWon () { return $this->won;}
    function getDrawn () { return $this->drawn;}
    function getPlus () { return $this->plus;}
    function getMinus () { return $this->minus;}
    function getText () { return $this->text;}

    static function Division (Int $divisionID) {
        $sql = "SELECT `teamID`, COUNT(`teamID`) AS `played`, COUNT(`win`) AS `won`, COUNT(`draw`) AS `drawn`, (COUNT(`teamID`) - COUNT(`win`) - COUNT(`draw`)) AS `lost`, COALESCE(SUM(`points`),0) AS `points`, COALESCE(SUM(`bp`),0) AS `bp`, (COALESCE(SUM(`points`),0) + COALESCE(SUM(`bp`),0)) AS `total`, SUM(`plus`) AS `for`, SUM(`minus`) AS `against`, (SUM(`plus`) - SUM(`minus`)) AS `gd` FROM `standings` WHERE `deleted` IS NULL AND teamID IN (SELECT `id` FROM `teams` WHERE `divisionID` = $divisionID) GROUP BY `teamID` ORDER BY `total` DESC, `gd` DESC";
        $rlt = new Database($sql);
        if (!$rlt->rows) {
            $teams = Team::byDivision($divisionID);
            if ($teams) {
                foreach ($teams as $team) {
                    $return[] = [
                        "teamID" => $team->id,
                        "played" => 0,
                        "won" => 0,
                        "drawn" => 0,
                        "lost" => 0,
                        "for" => 0,
                        "against" => 0,
                        "gd" => 0,
                        "points" => 0,
                        "bp" => 0,
                        "total" => 0
                    ];
                }
                return $return;
            }
        } else return $rlt->rows;
    }
}