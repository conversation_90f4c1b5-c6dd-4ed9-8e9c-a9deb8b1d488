<?php

class Base {

    protected $dbTable, $dbKey = "id", $dbOrder = ["name" => "ASC"];
    protected $created, $updated, $deleted;
    public $id;
    protected $name;
    protected $dbFields = [];
    protected $dbOrderBy = [];
    protected $minData = [];

    function __construct($id = null) {
        if ($id && $this->dbTable && $this->dbKey) {
            $sql = "SELECT * FROM `$this->dbTable` WHERE `$this->dbKey` = $id";
            $db = new Database($sql);
            // $db->Query();
            if ($db->errors) return implode(". ", $db->errors);
            if (!$db->rows) return "No record found";
            $this->Load($db->rows[0]);
        } 
    }
    function Load(Array $data) {
        foreach ($data as $k => $v) $this->{$k} = $v;
    }
    function __get($property) {
        if (isset($this->$property)) return $this->$property;
    }
    function __set($property, $value) {
        $this->$property = $value;
    }
    function Save() {
        // echo __METHOD__."<br>";
        
        if (!$this->dbFields) return (Message("No database fields setup for "  . get_called_class()));
        $sql = null; $sqlData = [];
        foreach ($this->dbFields as $field) {
            if ($sql) $sql .=",";
            $sql .= "`$field` = :$field";
            $sqlData[$field] = $this->$field;
        }
        if (!$this->{$this->dbKey}) {
            $sql = "INSERT INTO `$this->dbTable` SET $sql";
            $db = new Database($sql, $sqlData);
            // $db->Insert();
            // if ($db->errors) return (Message(implode(". ",$db->errors)));
            if (!$db->lastInsertID) {
                $errorData[] = ($ref =uniqid());
                $errorData = array_merge($errorData,$db->errors);
                $errorData[] = $sql;
                $errorData = array_merge($errorData,$sqlData);
                $this->LogError($errorData);
                return (Message("Sorry, we had problems saving to the database. If you need to contact Support, please quote <b>$ref</b>","warning"));
            }
            $this->{$this->dbKey} = $db->lastInsertID;
        } else {
            $sql = "UPDATE `$this->dbTable` SET $sql WHERE `$this->dbKey` = :$this->dbKey";
            $sqlData[$this->dbKey] = $this->{$this->dbKey};
            $db = new Database($sql, $sqlData);
            if (!is_numeric($db->affectedRows)) {
                $errorData[] = ($ref =uniqid());
                $errorData = array_merge($errorData,$db->errors);
                $errorData[] = $sql;
                $errorData = array_merge($errorData,$sqlData);
                $this->LogError($errorData);
                return (Message("Sorry, we could not save some information. If you need to contact Support, please quote <b>$ref</b>","warning"));
            } else return $db->affectedRows;
        }
    }
    function LogError(Array $data) {
        global $config;
        $errorFolder = CheckFolder($config['system']['path']."app/dberrors/");
        $errorFile = date('Y-m-d').".csv";
        $stream = fopen($errorFolder.$errorFile,"a");
        fputcsv($stream,$data);
    }
    function __clone() { $this->id = null;}
    function getID() { return $this->id;}
    function getName() { return $this->name;}
    static function Fetch() {
        $o = new static();
        $orderBy = ($o->dbOrder) ? "ORDER BY " . key($o->dbOrder) ." " . current($o->dbOrder) : null;
        $db = new Database(($sql="SELECT * FROM `$o->dbTable` $orderBy"));
        if (!$db->errors) return implode(". ",$db->errors);
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s;
        }
        return $return;
    }
    static function Archive (Int $id) {
        // Messaging\Add("Deleting $id");
        $obj = new static($id);
        $obj->Load(["deleted" => date('Y-m-d H:i:s')]);
        $obj->Save();
    }
    static function Duplicate(Int $id) {
        $orig = new static($id);
        $new = clone($orig);
        $new->Save();
        return $new;
    }
    static function Listing() {
        $obj = new static();
        $sql = "SELECT * FROM {$obj->dbTable}";
        if ($obj->dbOrderBy) {
            $sql .=  " ORDER BY";
            foreach ($obj->dbOrderBy as $field => $sortOrder) {
                $sql .=  "$field $sortOrder";
            }
        }
    }
    static function Query (String $sql = null, Array $data = []) {
        $obj = new static();
        if (!$sql) $sql = "SELECT * FROM `{$obj->dbTable}`";
        $db = new Db($sql, $data);
        // echo $sql;
        if (!$db->rows) return [];
        // Tools::Dump($db);
        $return = [];
        foreach ($db->rows as $r) {
            $o = new static();
            $o->Load($r);
            $return[] = $o;
        }
        return $return;
    }
}