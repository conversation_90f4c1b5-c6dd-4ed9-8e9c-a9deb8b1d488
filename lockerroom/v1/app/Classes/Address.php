<?php

class Address extends Base {
    protected $dbTable = "addresses";
    protected $dbFields = ["line1", "line2", "town","county","postcode","country"];
    protected $line1, $line2, $town, $county, $postcode, $country;

    function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() { return "{$this->line1}, {$this->town}, {$this->postcode}";}
    /* Getters */
    function getLine1() {return $this->line1;}
    function getLine2() {return $this->line2;}
    function getTown() {return $this->town;}
    function getCounty() {return $this->county;}
    function getPostcode() {return $this->postcode;}
    function getCountry() {return $this->country;}

    /* Setters */
    function setLine1(String $line1) { $this->line1 = $line1;}
    function setLine2(String $line2) { $this->line2 = $line2;}
    function setTown(String $town) { $this->town = $town;}
    function setCounty(String $county) { $this->county = $county;}
    function setPostcode(String $postcode) { $this->postcode = $postcode;}
    function setCountry(String $country) { $this->country = $country;}
    /* Statics */

}