<?php

class TeamFollower extends Base {

    protected $dbTable = "teamFollowers";
    protected $dbFields = ["adminID","userID","teamID","roles","type","requested","accepted","deleted"];
    protected $adminID, $userID, $teamID;
    // protected $captain, $treasurer, $player, $supporter;
    protected $roles;
    protected $type; # I : Invitation or R : Request 
    protected $requested, $accepted, $deleted; # Datetimes

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    /* Checkers */
    function hasRole(String $role) {
        $roles = $this->getRoles();
        return (in_array($role,$roles)) ? true : false;
    }
    /* Getters */
    function getRoles() { return unserialize($this->roles);}
    function getTeamID() { return $this->teamID;}
    function getUserID() { return $this->userID;}
    /* Setters */
    function setAdminID(Int $adminID) { $this->adminID = $adminID;}
    function setUserID(Int $userID) { $this->userID = $userID;}
    function setTeamID(Int $teamID) { $this->teamID = $teamID;}
    // function setCaptain() { $this->captain = 1;}
    // function setTreasurer() { $this->treasurer = 1;}
    // function setPlayer() { $this->player = 1;}
    // function setSupporter() { $this->supporter = 1;}
    function setRoles(Array $roles) { $this->roles = serialize($roles);}
    function setType(String $type) { $this->type = $type;}
    /* Methods */
    function countRole (String $role) {
        $sql = "SELECT COUNT(`id`) AS `total` FROM `teamFollowers` WHERE `roles` LIKE '%$role%' AND `requested` IS NOT NULL AND `accepted` IS NOT NULL AND `deleted` IS NULL";
        $db = new Database($sql);
        return (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
    }
    function Issue() {
        $this->requested = date('Y-m-d H:i:s');
        $this->accepted = null;
        $this->Save();
    }
    static function Invitation (Int $userID, Int $teamID, Array $roles) {
        /* Check if each Role is a valid option */
        $invite = (($existingID = static::Exists($userID, $teamID))) ? new static($existingID) : new static();
        $invite->setAdminID(User::AuthUserID());
        $invite->setUserID($userID);
        $invite->setTeamID($teamID);
        $invite->setRoles($roles);
        $invite->setType("I");
        $invite->Issue();
        return $invite;
    }

    static function Exists(Int $userID, Int $teamID) {
        $sql = "SELECT `id` FROM `teamFollowers` WHERE `userID` = :userID AND `teamID` = :teamID";
        $db = new Database($sql, ["userID" => $userID, "teamID" => $teamID]);
        if ($db->rows) return $db->rows[0]['id'];
    }
    static function Roles() {
        return [
            "captain",
            "treasurer",
            "player",
            "supporter"
        ];
    }
    static function Listing() {
        $sql = "SELECT * FROM `leagues` ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }
    static function Followers(Int $teamID) {
        $sql = "SELECT * FROM `teamFollowers` WHERE `teamID` = :teamID";
        echo "$userID : $sql";
        $db = new Database($sql, ["teamID" => $teamID]);
        if ($db->rows) {
            $return = [];
            foreach ($db->rows as $r) {
                $tf = new static();
                $tf->Load($r);
                $return[] = $tf;
            }
        } else $return = null;
        return $return;
    }
    static function Admin (Int $adminID) {
        $sql = "SELECT * FROM `teamFollowers` WHERE `adminID` = :adminID";
        $db = new Database($sql, ["adminID" => $adminID]);
        if ($db->rows) {
            $return = [];
            foreach ($db->rows as $r) {
                $tf = new static();
                $tf->Load($r);
                $return[] = $tf;
            }
        } else $return = null;
        return $return;
    }
}