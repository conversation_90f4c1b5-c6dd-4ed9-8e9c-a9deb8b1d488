<?php

class Organisation extends Base {
    protected $dbTable = "organisations";
    protected $dbFields = ["name", "companyReg", "vatReg"];
    protected $name, $companyReg, $vatReg;

    function __construct(Int $id = null) { parent::__construct($id);}
    function Save() {
        /* Validation */
        if (!$this->name) return "Organisation must have a name";
        parent::Save();
        return (int)$this->id; /* Successfully saved */
    }
    function __toString() { return "{$this->name}";}
    /* Getters */
    function getName() {return $this->name;}
    function getCompanyReg() {return $this->companyReg;}
    function getVatReg() {return $this->vatReg;}

    /* Setters */
    function setName(String $name) { $this->name = $name;}
    function setCompanyReg(String $companyReg) { $this->companyReg = $companyReg;}
    function setVatReg(String $vatReg) { $this->vatReg = $vatReg;}
    /* Statics */

}