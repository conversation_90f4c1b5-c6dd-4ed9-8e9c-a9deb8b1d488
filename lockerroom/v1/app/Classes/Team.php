<?php

class Team extends Base {

    protected $dbTable = "teams";
    protected $dbFields = ["managerID","captainID","name","leagueID","seasonID","divisionID","treasurerID","treasurerEmail","treasurerAccepted","treasurerStripePaymentMethodID"];
    protected $name, $clubID;
    protected $leagueID, $seasonID, $divisionID;
    protected $managerID, $captainID;
    protected $treasurerEmail, $treasurerAccepted, $treasurerID, $treasurerStripePaymentMethodID;

    protected $league, $captain;

    function __constuct(Int $id = null) {
        parent::__constuct($id);
    }
    function __toString() { return $this->name;}
    function fullName() {
        return "{$this->name} " . $this->getLeagueName();
    }
    function getLeagueName() {
        if (!$this->league) $this->league = new League($this->leagueID);
        return $this->league->name;
    }
    function getLeagueID() {
        return $this->leagueID;
    }
    function getLeague() {
        if (!$this->league) $this->league = new League($this->leagueID);
        return $this->league;
    }
    function inviteCaptain(String $email) {
        /* Test for Valid Email */
        if (!filter_var($email,FILTER_VALIDATE_EMAIL)) return "Not a valid email address";
        /* Does User exist? If Not, Create User and Send General Invite */
        $user = \User::EmailLookup($email);
        if (!$user) {
            $user = new \User();
            $user->Load(["email" => $email]);
            $user->IssueActivation(\User::AuthEmail());
        }
        /* Create Captain Invitation with Notification */
    }
    function setPaymentMethod(String $treasurerStripePaymentMethodID) {
        $this->treasurerStripePaymentMethodID = $treasurerStripePaymentMethodID;
    }
    function getStripePaymentMethodID() {
        return $this->treasurerStripePaymentMethodID;
    }
    function getDivisionID() { 
        $teamSeasons = TeamSeason::Lookup($this->id);
        if (!$teamSeasons) return [];
        
        return $this->divisionID;
    }
    function getCaptainID() {
        return $this->captainID; 
    }
    function getCaptain() {
        if (!$this->captain) $this->captain = new User($this->captainID);
        return ($this->captain) ? $this->captain : $this->captainInvite;
        // if ($this->captainID) {
        //     $user = new User($this->captainID); return $user->__toString();
        // } else return $this->captainInvite;
    }
    function getCaptainsPack() {
        $league = $this->getLeague();
        return $league->getCaptainsPack();
    }
    function getCoordinator() {
        $league = $this->getLeague();
        return $league->getCoordinator();
    }
    function getTreasurer() {
        if ($this->treasurerID) {
            $user = new User($this->treasurerID); return $user->__toString();
        } else return $this->treasurerInvite;
    }
    function getTreasurerEmail() {
        if ($this->treasurerID) {
            $user = new User($this->treasurerID); return $user->getEmail();
        } else return $this->treasurerInvite;
    }
    function treasurerInvitation() {
        if (!$this->treasurerID) return;
        $treasurer = new \User($this->treasurerID); if (!$treasurer) return;
        
    }
    function validateTreasurerEmail(String $email) {
        return ($this->getTreasurerEmail() == $email) ? true : false;
    }
    function setNewTreasurer(String $email) {
        global $config;
        $user = User::EmailLookup($email);
        if ($user) {
            $this->treasurerID = $user->id;
            $this->treasurerAccepted = null;
            $subject = "Inivitation to become a Treasurer at " . $config['system']['name'];
            $message[] = "You have a request to be a Treasurer for " . $this->name;
            $message[] = "To confirm this, please log on to the Locker Room to confirm";
            $message[] = "Many thanks";
            $message[] = $config['system']['name'];
            $to = [$user->getEmail() => $user->getName()];
            $cc = $bcc = $attachments = [];
            Email::Issue ($subject, $message, $to, $cc, $bcc, $attachments);
        } else {
            $this->treasurerID = User::Invitation($email,User::AuthUser());
            $this->treasurerAccepted = null;
        }
        $this->Save();
    }
    function paymentStatus() {
        if (!$this->treasurerStripePaymentMethodID) {
            return ["danger","Requires Payment Details"];
        } else {
            $pm = Stripe::getPaymentMethod($this->treasurerStripePaymentMethodID);
            $expiryDate = strtotime("{$pm->card->exp_year}-{$pm->card->exp_month}-01");
            if ($expiryDate < time()) {
                # Card Expired
                return ["danger","Has an expired Payment card"];
            } elseif ($expiryDate < strtotime("+30 days")) {
                # Expires within a month
                return ["warning","Has a payment card which expires soon"];
            }
        }
        return ["success","Has Valid Payment Details"];
    }
    function IssueNewRegistrationConfirmation() {
        $subject = "Congratulations on joining ".$this->getLeague()."!";
        $message[] = "Hi";
        $message[] = "We're pleased to confirm that <b>{$this->name}</b> is registered into " . $this->getLeagueName();
        $message[] = "Your league co-ordinator ".$this->getCoordinator() . " will be in touch soon";
        $message[] = "The captains pack for your League is available <a href=\"".$this->getCaptainsPack()."\">here</a>";
        $message[] = "Thanks and best wishes";
        $message[] = "leagues4you";
        $to = [$this->getCaptain()->email => $this->getCaptain()];
        $cc = [$this->getCoordinator()->email => $this->getCoordinator()];
        $bcc = [];
        Email::Issue($subject,$message,$to,$cc,$bcc);
    }
    static function Club (Int $clubID) {
        /* Returns Array of Team objects on success, NULL on failure */
        $sql = "SELECT * FROM `teams` WHERE  `deleted` IS NULL AND `clubID` = :clubID";
        $db = new Database($sql, ["clubID" => $clubID]);
        if ($db->rows) {
            $return = [];
            foreach ($db->rows as $r) {
                $team = new static();
                $team->Load($r);
                $return[] = $team;    
            }
        } else $return = null;
        return $return;
    }
    static function Listing() {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN teamSeasons ON `teams`.`id` = `teamSeasons`.`teamID` WHERE `teams`.`deleted` IS NULL AND `teamSeasons`.`deleted` IS NULL ORDER BY `teams`.`name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }
    /* Setters */
    function setName(String $name) {
        $this->name = $name;
    }
    /* Treasury Functions */
    static function InviteTreasurer(Int $teamID, Int $userID) {
        $sql = "SELECT * FROM `treasurers` WHERE `teamID` = :teamID AND `userID` = :userID AND `deleted` IS NULL";
        $db = new Database($sql,["teamID" => $teamID, "userID" => $userID]);
        if ($db->rows) return;
        $sql = "INSERT INTO `treasurers` SET `teamID` = :teamID, `userID` = :userID, `invited` = :invited";
        $db = new Database($sql, ["teamID" => $teamID, "userID" => $userID, "invited" => date("Y-m-d H:i:s")]);
    }
    static function AcceptTreasurer(Int $teamID, Int $userID) {
        $sql = "UPDATE `treasurers` SET `accepted` = NOW() WHERE `teamID` = :teamID AND `userID` = :userID AND `invited` IS NOT NULL AND `deleted` IS NULL AND `accepted` IS NULL";
        $db = new Database($sql, ["teamID" => $teamID, "userID" => $userID]);
    }
    static function DeclineTreasuere(Int $teamID, Int $userID) {
        return static::ResignTreasurer($teamID,$userID);
    }
    static function SetTreasurer(Int $teamID, Int $userID) {
        $sql = "INSERT INTO `treasurers` SET `teamID` = :teamID, `userID` = :userID, `invited` = :invited, `accepted` = :invited";
        $db = new Database($sql, ["teamID" => $teamID, "userID" => $userID, "invited" => date("Y-m-d H:i:s")]);
        // Resign any other Treasurers
        if ($db->lastInsertID) {
            $sql = "UPDATE `treasurers` SET `deleted` = NOW() WHERE `teamID` = :teamID and `id` <> :lastID";
            $db = new Database($sql,["teamID" => $teamID,"lastID" => $db->lastInsertID]);
        }
    }
    static function ResignTreasurer(Int $teamID, Int $userID) {
        $sql = "UPDATE `treasurers` SET `deleted` = NOW() WHERE `teamID` = :teamID AND `userID` = :userID AND `deleted` IS NULL";
        $db = new Database($sql, ["teamID" => $teamID, "userID" => $userID]);
    }
    static function Treasurer (Int $teamID) {
        $sql = "SELECT `userID`, `stripePaymentMethodID`,`accepted` FROM `treasurers` WHERE `teamID` = :teamID AND `deleted` IS NULL ORDER BY `accepted`";
        $db = new Database($sql, ["teamID" => $teamID]);
        if ($db->rows) return $db->rows[0];
    }
    static function TreasuryTeams (User $user) {
        $sql = "SELECT * FROM `teams` WHERE `treasurerID` = :userID";
        $db = new Database($sql, ["userID" => $user->id]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $t = new static();
            $t->Load($r);
            (!$return) ? $return[] = $t : array_push($return,$t);
        }
        return $return;
    }
    static function setTeamPaymentMethod(Int $id, String $stripePaymentMethodID = null) {
        if (!$stripePaymentMethodID) $stripePaymentMethodID = null;
        $sql = "UPDATE `treasurers` SET `stripePaymentMethodID` = :stripePaymentMethodID WHERE `id` = :id";
        $db = new Database($sql,["stripePaymentMethodID" => $stripePaymentMethodID, "id" => $id]);
        if ($db->affectedRows) {
            $sql = "SELECT * FROM `treasurers` WHERE `id` = :id";
            $db = new Database($sql,["id" => $id]);
            FinanceReceipt::UpdateTeamPaymentMethod($db->rows[0]['teamID'],$stripePaymentMethodID);
            Message("Payment Method amended successfully for " . new Team($db->rows[0]['teamID']),"success");
        }
    }
    static function getTeamPaymentMethod(Int $teamID) {
        $sql = "SELECT `stripePaymentMethodID` FROM `treasurers` WHERE `teamID` = :teamID AND `deleted` IS NULL";
        $db = new Database($sql,["teamID" => $teamID]);
        if ($db->rows) return $db->rows[0]['stripePaymentMethodID'];
    }
    static function Managed (Int $userID) {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` WHERE `teams`.`deleted` IS NULL AND `teamSeasons`.`deleted` IS NULL AND (`teams`.`captainID` = $userID OR `teams`.`treasurerID` = $userID) GROUP BY `teams`.`id`";
        $db = new \Database($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $team = new static();
            $team->Load($r);
            (!$return) ? $return[] = $team : array_push($return,$team);
        }
        return $return;
    }
    static function byDivision(Int $divisionID) {
        $sql = "SELECT * FROM `teams` WHERE `divisionID` = :divisionID";
        $db = new Database($sql,["divisionID" => $divisionID]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $t = new static();
            $t->Load($r);
            (!$return) ? $return[] = $t : array_push($return,$t);
        }
        return $return;
    }
    static function Resubscribe (Int $teamID) {
        $sql = "UPDATE `teams` SET `nextConfirmed` = NOW(), `nextConfirmedBy` = " . User::AuthUserID() . " WHERE `id` = $teamID";
        $rlt = new Db($sql);
    }

}