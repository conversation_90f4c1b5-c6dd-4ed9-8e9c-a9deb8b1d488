<?php

class Venue extends Base {

    protected $dbTable = "venues";
    protected $dbFields = ["name"];
    protected $name;
    protected $line1, $line2;
    protected $town, $county;
    protected $postcode;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function setName(String $name) { $this->name = $name;}

    function __toString() {
        return "{$this->name}";
    }
}