<?php

// use \phpmailer\phpmailer\phpmailer;

// include (__DIR__."/../vendor/phpmailer/phpmailer/src/PHPMailer.php");

class Email2 extends \PHPMailer\PHPMailer\PHPMailer {
    public $Host = 'localhost';
    public $Port = 25;

    function __construct ($defaults = true) {
        parent::__construct();
        global $config;
        $emailSettings = $config['email'];
        $this->exceptions = true;
        $this->isSMTP();
        $this->SMTPSecure = 'tls';
        $this->SMTPAuth = true;
        $this->Host = $emailSettings['server'];
        $this->Username = $emailSettings['username'];
        $this->Password = $emailSettings['password'];
        $this->Port = $emailSettings['ports']['outbound'];
        if ($defaults === true) {
            $this->setFrom($emailSettings['mailbox'], $emailSettings['fromName']);
            $this->addReplyTo($emailSettings['mailbox'], $emailSettings['fromName']);
            $this->Sender = $emailSettings['mailbox'];
        }
        // $this->isMail();
        $this->isHTML(true);
    }
    function send() {
        $this->Body = "<font face=\"sans-serif\" color=\"#000000\">$this->Body</font>";
        try {
            return parent::send();
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    static function Issue (String $subject, Array $message, Array $to, Array $cc = [], Array $bcc = [], Array $attachments = []) {
        if (!$subject) return "Missing Subject";
        if (!$message) return "Missing Message";
        if (!$to) return "Missing Recipients";
        $e = new self(true);
        foreach ($to as $toEmail => $toName) {
            if (!filter_var($toEmail, FILTER_VALIDATE_EMAIL)) {
                $errorMsg = "Invalid TO email address $toEmail ($toName)";
                return $errorMsg;
            }
            $e->addAddress($toEmail, ($toName) ? $toName : $toEmail);
        }
        $e->Subject = $subject;
        $template = file_get_contents(__DIR__."/../Templates/Email/email-1.htm");
        $template = str_replace(["[SUBJECT]","[MESSAGE]"],[$subject,"<p>".implode("</p><p>",$message)."</p>"],$template);
        $e->Body = $template;
        $e->AltBody = "\n".implode("\n",$message);
        if ($cc) {
            foreach ($cc as $ccEmail => $ccName) {
                if (array_key_exists($ccEmail,$to)) continue;
                if (!filter_var($ccEmail, FILTER_VALIDATE_EMAIL)) {
                    $errorMsg = "Invalid CC email address $ccEmail ($ccName)";
                    return $errorMsg;
                }
                $e->addCC($ccEmail, ($ccName) ? $ccName : $ccEmail);
            } 
        }
        if ($bcc) {
            foreach ($bcc as $bccEmail => $bccName) {
                if (array_key_exists($bccEmail,$to) || array_key_exists($bccEmail,$cc)) continue;
                if (!filter_var($bccEmail, FILTER_VALIDATE_EMAIL)) {
                    $errorMsg = "Invalid BCC email address $bccEmail ($bccName)";
                    return $errorMsg;
                }
                $e->addBCC($bccEmail, ($bccName) ? $bccName : $bccEmail);
            } 
        }
        if ($attachments) {
            foreach ($attachments as $attachment) {
                if (!file_exists($attachment)) return "The accompanying atatchment - $attachment could not be located";
                $e->addAttachment($attachment);
            } 
        }
        return $e->send();
    }
    static function Test ($to = ["<EMAIL>" => "Marc Conlon"]) {
        $subject = "Test Message";
        $message = ["Test message from {$_SERVER['HTTP_HOST']}","Sent " .date('Y-m-d H:i:s')];
        $cc = [];
        $bcc = [];
        $attachments = [];
        \Tools\Dump(static::Issue($subject,$message,$to,$cc,$bcc,$attachments));
    }
}
