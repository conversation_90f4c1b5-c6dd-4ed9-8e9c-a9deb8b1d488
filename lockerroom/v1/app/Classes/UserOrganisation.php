<?php

class UserOrganisation extends Base {

    protected $dbTable = "userOrganisations";
    protected $dbFields = ["userID","organisationID","deleted"];
    protected $dbOnDuplicate = ["userID","organisationID","deleted"];
    protected $userID, $organisationID;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        /* Validation */
        if (!$this->userID || !$this->organisationID) return "A User ID and Organisation ID are both required";
        $sql = "INSERT INTO {$this->dbTable} SET `userID` = :userID, `organisationID` = :organisationID ON DUPLICATE KEY UPDATE `deleted` = NULL"; 
        $sqlData = ["userID" => $this->userID, "organisationID" => $this->organisationID];
        $db = new Database($sql, $sqlData);
        if ($db->errors) return implode(". ",$db->errors);
        if (!$this->id) $this->id = $db->lastInsertID;
        return (int)$this->id;
    }
    /* Getters */
    function getUserID() { return $this->userID;}
    function getOrganisationID() { return $this->organisationID;}
    /* Setters */
    function setUserID(Int $userID) { $this->userID = $userID;}
    function setOrganisationID(Int $organisationID) { $this->organisationID = $organisationID;}
    /* Static */
    static function Organisations(Int $userID) {
        $sql = "SELECT `organisationID` FROM `userOrganisations` WHERE `userID` = :userID";
        $db = new Database($sql, ["userID" => $userID]);
        if ($db->errors) return implode(". ",$db->errors);
        $return = [];
        foreach ($db->rows as $r) {
            $return[] = new Organisation($r['organisationID']);
        }
        return $return;
    }
}