<?php

class Club extends Base {

    protected $dbTable = "clubs";
    protected $dbFields = ["name","userID"];
    public $name, $userID;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function getName() {
        return $this->name;
    }
    static function User(Int $userID) {
        /* Returns Club object on success, NULL on failure */
        $sql = "SELECT * FROM `clubs` WHERE `userID` = :userID";
        $db = new Database($sql, ["userID" => $userID]);
        if ($db->rows) {
            $club = new static();
            $club->Load($db->rows[0]);
            return $club;
        }
    }
    static function Listing() {
        $sql = "SELECT * FROM `clubs` ORDER BY `name` ASC";
        $db = new Database($sql);
        return $db->rows;
    }
}