<?php

class Season extends Base {

    protected $dbTable = "seasons";

    public $name;
    protected $nextID;

    function __toString() {
        return $this->name;
    }
    
    static function DefaultLeague(Int $leagueID) {
        $sql = "SELECT `nextID` FROM `seasons` WHERE `status` = 1 AND `leagueID` = :leagueID";
        $db = new Database($sql, ["leagueID" => $leagueID]);
        if ($db->rows) return $db->rows[0]['nextID']; 
    }
    static function Default (League $league) {
        if (!$league->id) return;
        $sql = "SELECT * FROM `seasons` WHERE `leagueID` = :leagueID ORDER BY `status` DESC";
        $rlt = new Db($sql,["leagueID" => $league->id]);
        if (!$rlt->rows) return;
        foreach ($rlt->rows as $r) {
            if ($r['status'] == 1 && $r['nextID']) return $r['nextID'];
        }
        return array_pop($rlt->rows)['id'];
    }
    
    static function Registration (League $league) {
        $sql = "SELECT * FROM `seasons` WHERE `openForRegistration` = 1 AND `leagueID` = {$league->id}";
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }

    static function Next (League $league) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL AND `seasons`.`leagueID` = {$league->id}";
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }

}