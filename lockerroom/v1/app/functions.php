<?php

function Dump($data) {
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

function Narrate(String $message) {
    return;
    global $config;
    $folder = CheckFolder($config['system']['path']."app/logs/");
    $file = session_id().".csv";
    $fp = fopen($folder.$file,"a");
    fputcsv($fp,[date('Y-m-d H:i:s'),$message]);
    fclose($fp);
}

function Message($text, $type = "info") {
    $_SESSION['messages'][$type][] = $text;
}

function Messages() {
    if (isset($_SESSION['messages'])) {
        foreach ($_SESSION['messages'] as $type => $messages) {?>
            <div class="alert alert-<?php echo strtolower($type);?> alert-dismissible fade show" role="alert"><?php
            $prepend = null;
            foreach ($messages as $message) {
                echo $prepend . $message;
                $prepend = "<br>";
            }?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div><?php
        }
        unset($_SESSION['messages']);
    }
}

function CheckFolder ($folder) {
    /* Add trailing slash if not present */
    if (substr($folder,0,1)=="/") $folder = substr($folder,1);
    if (substr($folder,-1)=="/") $folder = substr($folder,0,strlen($folder)-1);
    $elements = explode("/",$folder);
    $folderToCheck = null;
    foreach($elements as $element) {
        $folderToCheck .= "/".$element;
        if ($element == "..") continue;
        if (!file_exists($folderToCheck)) {
            $old = umask(0);
            mkdir($folderToCheck,0775);
            umask($old);
        } 
    }
    return "$folderToCheck/";
}