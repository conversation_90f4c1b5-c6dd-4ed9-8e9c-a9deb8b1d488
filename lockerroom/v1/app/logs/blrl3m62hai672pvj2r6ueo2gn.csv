"2020-09-12 09:49:08","Login Success: <EMAIL>"
"2020-09-12 09:49:08","IP: **********. Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36"
"2020-09-12 09:49:08","URL: User/Index"
"2020-09-12 09:49:08","IP: **********. Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36"
"2020-09-12 09:49:08","URL: favicon.ico/Index"
"2020-09-12 09:50:47","IP: **********. Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36"
"2020-09-12 09:50:47","URL: User/Profile"
"2020-09-12 09:50:48","IP: **********. Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36"
"2020-09-12 09:50:48","URL: favicon.ico/Index"
"2020-09-12 09:53:55","IP: **********. Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36"
"2020-09-12 09:53:55","URL: User/Profile"
"2020-09-12 09:53:55","Profile: Array
(
    [id] => 2
    [firstname] => Mark
    [lastname] => Conlon
    [email] => <EMAIL>
    [mobile] => 
)
"
"2020-09-12 09:55:36","IP: **********. Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36"
"2020-09-12 09:55:36","URL: User/Profile"
"2020-09-12 09:55:36","Profile: 2, Mark, Conlon, <EMAIL>, "
