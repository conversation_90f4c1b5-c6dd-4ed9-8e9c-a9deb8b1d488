<?php
# Development Error Setting
error_reporting(E_ALL); 
ini_set('display_errors', 1); 
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1); 
# Production Error Setting
// error_reporting(E_ALL);
// ini_set('display_errors', 0);
// ini_set('log_errors', 1);
// ini_set('display_startup_errors', 0); 
include("config.php");
global $config;
if (file_exists(__DIR__."/functions.php")) include(__DIR__."/functions.php");
#( int $lifetime [, string $path [, string $domain [, bool $secure = FALSE [, bool $httponly = FALSE ]]]] ) 
$cookieLifetime = 4; # 4 hours;
$pathOnDomain = "/"; # All paths
$cookieDomain = '.'.$config['system']['parentDomain'];
$cookieSecure = true; # https only?
$httpOnly = false; # Only send via HTTP (eg not JS) 
session_set_cookie_params(60*60*($cookieLifetime), $pathOnDomain, $cookieDomain,$cookieSecure,$httpOnly);
if(!isset($_SESSION)) {
    Narrate("New Session");
    session_start();
} 
// echo "<!-- " . session_id() . " -->";
if (file_exists(__DIR__."/vendor/autoload.php")) include(__DIR__."/vendor/autoload.php");
Narrate("IP: {$_SERVER['REMOTE_ADDR']}. Agent: {$_SERVER['HTTP_USER_AGENT']}");
$includeFolder = __DIR__ ."/Controllers/";
$include_files = scandir($includeFolder);
foreach ($include_files as $include_file) {
    if ($include_file != "." && $include_file != "..") require_once($includeFolder.$include_file);
}

// Class Autoloading;
function ClassAutoloader($class) {
    // if ($class=="TeamSeason") echo "$class<br>";
    // echo "Loading $class<br>";
    $file = __DIR__."/Classes/$class.php";
    if (file_exists($file))return (include_once ($file));
    // exit("Not $file<br>");
    $file = __DIR__.DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."core".DIRECTORY_SEPARATOR."classes".DIRECTORY_SEPARATOR."$class.php";
    if (file_exists($file))return (include_once ($file));
    // exit("No $file");
}
spl_autoload_register("ClassAutoloader");
// Autoloading from Core;
// function CoreAutoloader($class) {
//     // echo "Loading $class<br>";
//     $file = __DIR__."../../core/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
// }
// spl_autoload_register("CoreAutoloader");
$GLOBALS['post'] = $_POST;
$GLOBALS['get'] = $_GET;
$GLOBALS['url'] = (isset($_GET['url'])) ? $_GET['url'] : null;