<?php include (__DIR__."/../menus/dashboard.menu.php");?>
<!--
<?php print_r($_SESSION['newTeam']);?>
-->
<style>
    #teamReg4Submit:disabled {
        cursor: no-drop;
    }
</style>
<div class="container">
    <div class="p-4 my-4"  style="background-color: rgba(150,150,150,.65);">
        <div class="d-flex justify-content-center" style="color: rgb(0,0,80);">
            <span>Team Name</span>
            <span class="arrow mx-2"> &gt;</span>
            <!-- <span>Captain</span>
            <span class="arrow mx-2"> &gt;</span> -->
            <span>Treasurer</span>
            <span class="arrow mx-2"> &gt;</span>
            <span class="active" style="border-bottom: medium solid rgb(0,0,80);">Ts &amp; Cs</span>
        </div>
        <form action="/User/Teams" method="post" class="d-flex justify-content-center mt-4">
            <div class="d-flex flex-column align-items-center" style="color: rgb(0,0,80);"x>
                <h5>Confirm Terms and Conditions</h5>
                <div class="d-flex align-items-center mb-2">
                    <a target="_blank" href="<?php echo $data['captainsPack'];?>">
                        <img src="/img/pdf-icon.png" alt="" style="height: 60px;">
                    </a>
                    <span class="mx-4">Captain's Pack Netball</span>
                    <div class="d-flex flex-column align-items-center mr-4">
                        <span>Accept</span>
                        <input type="checkbox" name="newTeam[termsAccepted]" id="teamReg4Agree" value="1">
                    </div>
                    <button type="submit" class="btn btn-purple ml-4" id="teamReg4Submit"  name="teamStage" value="4" disabled>Next</button>
                </div>
                <p id="termsPrompt" class="bg-warning p-2">Please take a moment to read thoroughly through our captain pack, by ticking that you have read the pack you are agreeing to our Ts&Cs.</p>
                <small>A copy of this (and any other) relevant documents will be emailed to you and are downloadable from the Documents section of the Locker Room.</small>
            </div>
        </form>
    </div>
</div>
<script>
    var checkbox = document.getElementById("teamReg4Agree");
    var termsPrompt = document.getElementById("termsPrompt");
    var teamReg4Submit = document.getElementById("teamReg4Submit");

    function checkboxVal() {
        if (checkbox.checked === true) {
            termsPrompt.classList.add("d-none");
            teamReg4Submit.disabled = false;
        } else {
            termsPrompt.classList.remove("d-none");
            teamReg4Submit.disabled = true;
        }
    }
    checkbox.addEventListener("click",checkboxVal);
</script>