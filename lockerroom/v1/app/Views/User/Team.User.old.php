<!-- Modal -->
<div class="modal fade" id="memberModal" tabindex="-1" role="dialog" aria-labelledby="memberModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="memberModalLabel">Add a New Member</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="invitation[teamID]" id="invitation.teamID" value="<?php echo $data[0];?>">
            <label for="invitation.email">Email Address</label>
            <input type="email" name="invitation[email]" id="invitation.email" class="form-control" placeholder="User's email address" required>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="1" id="roles.captain" name="roles[captain]">
              <label class="form-check-label" for="roles.captain">Captain</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="1" id="roles.treasurer" name="roles[treasurer]">
              <label class="form-check-label" for="roles.treasurer">Treasurer</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="1" id="roles.player" name="roles[player]">
              <label class="form-check-label" for="roles.player">Player</label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" value="1" id="roles.supporter" name="roles[supporter]">
              <label class="form-check-label" for="roles.supporter">Supporter</label>
            </div>
            <button type="submit" class="btn btn-primary">Invite</button>
        </form>
      </div>
    </div>
  </div>
</div>
<div class="container-fluid">
    <h1>Team | <a href="./User/Teams" class="btn btn-sm btn-warning">Back</a></h1>
    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="pills-main-tab" data-toggle="pill" href="#pills-main" role="tab" aria-controls="pills-main" aria-selected="true">Main</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="pills-members-tab" data-toggle="pill" href="#pills-members" role="tab" aria-controls="pills-members" aria-selected="false">Members</a>
        </li>
    </ul>
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade show active" id="pills-main" role="tabpanel" aria-labelledby="pills-main-tab">
            <h4>Main</h4>
            <form action="" method="post">
                <label for="team.name">Name</label>
                <input type="text" name="team[name]" id="team.name" class="form-control name" placeholder="Your team name" required value="<?php if (isset($data['team'])) echo $data['team']->getName();?>">
                <label for="team.regionID">Region</label>
                <select class="form-control" name="team[regionID]" id="team.regionID">
                    <option value="">...</option>
                </select>
                <label for="team.sportID">Sport</label>
                <select class="form-control" name="team[sportID]" id="team.sportID">
                    <option value="">...</option>
                </select>
                <button type="submit" class="btn btn-primary">Save</button>
            </form>
        </div>
        <div class="tab-pane fade" id="pills-members" role="tabpanel" aria-labelledby="pills-members-tab">
            <h4>Members | <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#memberModal">New</button></h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th class="text-center">Captain</th>
                        <th class="text-center">Treasurer</th>
                        <th class="text-center">Player</th>
                        <th class="text-center">Supporter</th>
                    </tr>
                </thead>
                <tbody><?php
                if (isset($data['followers'])) {
                    foreach ($data['followers'] as $f) {
                        // $roles = $f->getRoles();?>
                    <tr>
                        <td><?php echo new \User($f->getUserID());?></td>
                        <td class="text-center"><?php if ($f->hasRole("captain")) echo "&check;";?></td>
                        <td class="text-center"><?php if ($f->hasRole("treasurer")) echo "&check;";?></td>
                        <td class="text-center"><?php if ($f->hasRole("player")) echo "&check;";?></td>
                        <td class="text-center"><?php if ($f->hasRole("supporter")) echo "&check;";?></td>
                    </tr><?php
                    }
                } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>