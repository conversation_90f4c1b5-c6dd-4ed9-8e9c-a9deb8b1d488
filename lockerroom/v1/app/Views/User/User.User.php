<?php $postname = "user";?>
<div class="container-fluid">
    <h1>User | <a href="./User/Users" class="btn btn-sm btn-info">Back</a></h1>
    <ul class="nav nav-tabs" id="myTab" role="tablist" style="margin-bottom: 1rem;">
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab']=="main") echo ' active';?>" id="main-tab" data-toggle="tab" href="#main" role="tab" aria-controls="main" aria-selected="<?php echo ($data['tab']=="main") ? 'true' : 'false';?>">Main</a>
        </li>
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab']=="organisations") echo ' active';?>"" id="organisations-tab" data-toggle="tab" href="#organisations" role="tab" aria-controls="organisations" aria-selected="<?php echo ($data['tab']=="organisations") ? 'true' : 'false';?>">Organisations</a>
        </li>
    </ul>
    <div class="tab-content" id="myTabContent">
        <div class="tab-pane fade<?php if ($data['tab']=="main") echo '  show active';?>" id="main" role="tabpanel" aria-labelledby="main-tab">
            <h5>Main</h5>
            <form action="./User/Users" method="post">
                <input type="hidden" name="<?php echo $postname;?>[id]" value="<?php echo $data[$postname]->id;?>">
                <label for="<?php echo $postname;?>[firstname]">First Name</label>
                <input type="text" name="<?php echo $postname;?>[firstname]" id="<?php echo $postname;?>.firstname" class="form-control" value="<?php echo $data[$postname]->getFirstname();?>">
                <label for="<?php echo $postname;?>[lastname]">Lastname</label>
                <input type="text" name="<?php echo $postname;?>[lastname]" id="<?php echo $postname;?>.lastname" class="form-control" value="<?php echo $data[$postname]->getLastname();?>">
                <label for="<?php echo $postname;?>[email]">Email</label>
                <input type="email" name="<?php echo $postname;?>[email]" id="<?php echo $postname;?>.email" class="form-control" value="<?php echo $data[$postname]->getEmail();?>" required>
                <label for="<?php echo $postname;?>[mobile]">Mobile</label>
                <input type="tel" name="<?php echo $postname;?>[mobile]" id="<?php echo $postname;?>.mobile" class="form-control" value="<?php echo $data[$postname]->getMobile();?>">
                <label for="<?php echo $postname;?>[landline]">Landline</label>
                <input type="tel" name="<?php echo $postname;?>[landline]" id="<?php echo $postname;?>.landline" class="form-control" value="<?php echo $data[$postname]->getLandline();?>">
                <label for="<?php echo $postname;?>[extension]">Extension</label>
                <input type="number" name="<?php echo $postname;?>[extension]" id="<?php echo $postname;?>.extension" class="form-control" value="<?php echo $data[$postname]->getExtension();?>">
                <button type="submit" class="btn btn-primary">Save</button>
            </form>
        </div>
        <div class="tab-pane fade<?php if ($data['tab']=="organisations") echo '  show active';?>" id="organisations" role="tabpanel" aria-labelledby="organisations-tab">
            <h5>Organisations</h5>
        </div>
    </div>
</div>