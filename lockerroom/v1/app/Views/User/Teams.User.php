<div class="container">
    <?php include (__DIR__."/../menus/dashboard.menu.php");?>
    <h2 class="text-center my-4">Create a New Team</h2>
    <form action="/User/TeamName" method="post" class="form-inline justify-content-center">
        <span class="mr-2">Choose your League</span> 
        <select name="newTeam[leagueID]" id="newTeam.leagueID" class="form-control" required>
            <option value="">...</option><?php
            foreach ($data['leagues'] as $league) { ?>
                <option value="<?php echo $league['id'];?>"<?php if (isset($data['user']) && $data['user']->getRegistrationLeagueID()==$league['id']) echo ' selected';?>><?php echo $league['name'];?></option><?php
            }?>
        </select>
        <button type="submit" class="btn btn-purple ml-4" name="teamStage" value="1">Next</button>
    </form><?php
    if (isset($data['teams']) && $data['teams']) {?>
    <div class="d-flex align-items-center justify-content-center flex-wrap greyBg mt-4"><?php
    foreach ($data['teams'] as $team) { 
        if ($team->getCaptainID() != \User::AuthUserID()) continue;?>
        <a href="/User/Team/<?php echo $team->id;?>" class="d-flex flex-column align-items-center text-light px-2" style="border: thin solid #ccc;
    border-radius: 1rem;">
            <img src="/img/team-icon.png" alt="" style="height: 100px;">
            <p class="d-flex flex-column align-items-center">
                <span><?php echo $team->__toString();?></span>
                <span><?php echo $team->getLeagueName();?></span>
            </p>
        </a><?php
    } ?>
    </div><?php
    } ?>
</div>