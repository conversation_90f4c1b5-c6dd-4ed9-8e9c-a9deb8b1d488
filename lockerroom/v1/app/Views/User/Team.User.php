<!-- Modal -->
<?php include (__DIR__."/../menus/dashboard.menu.php");?>
<div class="container mt-2">
    <h1><?= $data['team']->getName() ?> | <?= $data['team']->getLeagueName() ?></h1>
</div>
<style>
  :disabled {
    cursor: no-drop;
  }
  .greyBackground {
    background-color: rgba(150,150,150,.65); 
    padding: 2rem 2rem;
    border-radius: 1rem;
  }
  .formGroup {
    display: grid; 
    grid-template-columns: 100px 1fr 100px;
    margin: .25rem 0;
    grid-row-gap: .75rem;
    grid-column-gap: .5rem;
  }
  .formGroup > * {
    display: flex;
    align-items: center;
  }
  .formGroup > button {
    justify-content: center;
  }
  .teamHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  @media (max-width: 780px) {
    .formGroup {
      grid-template-columns: 1fr;
    }
    .teamHeader {
      flex-direction: column;
    }
  }
  .teamMessage {
    border-radius: .5rem;
    padding: .5rem;
    margin-bottom: .5rem;
  }
</style>
<div class="container greyBackground">
  <div class="teamHeader">
    <h3>Team Management</h3>
    <span class="bg-warning text-dark d-none teamMessage" id="teamMessage"></span>
  </div>
  
  <form action="/User/Teams" method="post">
    <div class="formGroup">
      <input type="hidden" name="team[id]" id="team.id" value="<?php echo $data['team']->id;?>">
      <label for="team.name">Team Name</label>
      <input data-leagueid="<?= $data['team']->getLeagueID() ?>" class="form-control" type="text" name="team[name]" id="team.name" value="<?= $data['team']->getName() ?>" readonly>
      <button type="button" id="btnNameChange" class="btn btn-info" title="Edit"><i class="far fa-edit"></i></button>
      <button type="submit" id="btnNameSave" class="d-none btn btn-success" title="Save" disabled><i class="fas fa-check-circle"></i></button>

      <label for="team.treasurer">Treasurer</label>
      <input class="form-control" style="display: inline-block;width: auto; flex:1;" type="email" name="team[treasurerEmail]" id="team.treasurer" value="<?php echo $data['team']->getTreasurerEmail();?>" readonly>
      <button type="button" id="btnTreasurerChange"  class="btn btn-info" title="Edit"><i class="far fa-edit"></i></button>
      <button type="submit" id="btnTreasurerSave" class="d-none btn btn-success" title="Save" disabled><i class="fas fa-check-circle"></i></button>
    </div>
  </form>

  <div class="paymentStatus">
    <h3 class="my-4">Status</h3>
    <div class=""><?php
    $paymentStatus = $data['team']->paymentStatus(); ?>
      <p class="paymentStatusText bg-<?php echo $paymentStatus[0];?> p-2">Your Team <?php echo $paymentStatus[1];?></p>
      <?php
        $resubscribed = \TeamSeason::Resubscribed($data['team'], new \League($data['team']->getLeagueID()));
        if ($resubscribed === true) { ?>
        <p class="bg-success text-light p-2">
            &check; <b>Great</b>. You are confirmed for next Season
        </p><?php
        } elseif ($resubscribed === false) { ?>
          <a href="/User/Team/<?= $data['team']->id ?>?resubscribe=true" class="btn btn-warning">Click Here to Join Next Season</a><?php
        } ?>
      </p>
    </div>
  </div>

  <div class="teamHeader">
    <h3 class="my-4">Team Division</h3>
  </div>

  <style>
  #divisions .nav-tabs .nav-link, #fixtures .nav-tabs .nav-link {
      background-color: #ccc;
      color: #444;
      /* border: purple; */
    }
    #divisions .nav-tabs .nav-link.active, #fixtures .nav-tabs .nav-link.active {
      background-color: purple;
      color: #fff;
      border: purple;
    }
    .bg-turquoise {
      background-color: turquoise;
    }
  </style>
  
  <!-- Division Tabs -->
  <!-- <div id="divisions" class="col-md-12 col-sm-12 my-3">
      <ul class="nav nav-tabs" id="myTab" role="tablist">
          <li class="nav-item">
            <a class="nav-link active" id="current-table-control" data-toggle="tab" href="#current-table-division-1" role="tab" aria-controls="home" aria-selected="true">TABLE - DIVISION 1 - CURRENT SEASON</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="previous-table-control" data-toggle="tab" href="#previous-table-division-1" role="tab" aria-controls="profile" aria-selected="false">PREVIOUS TABLE - DIVISION 1</a>
          </li>
        </ul>
  </div> -->
  <!-- Division Tables -->
  <?php
  // $data['divisionTeams'] = [
  //   ["id" => 1,"name" => "Team A"],
  //   ["id" => 2,"name" => "Team B"],
  //   ["id" => 3,"name" => "Team C"],
  //   ["id" => 4,"name" => "Team D"],
  //   ["id" => 5,"name" => "Team E"],
  //   ["id" => 6,"name" => "Team F"]
  // ];
  // echo "<pre>"; print_r($data['divisionTable']); echo "</pre>";

  // $data['divisionTable'] = [];
  if (isset($data['divisionTable']) && $data['divisionTable']) { ?>
<div class="col-md-12 col-sm-12">
  <div class="tabs tab-content">
    <div class="tab-pane fade show active" id="current-table-division-1" role="tabpanel" aria-labelledby="rounders-tab">
      <!-- display the content here-->
      <div class="table-responsive">                          
        <table class="table table-striped bg-light">
          <thead class="bg-turquoise text-light">
            <tr>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Team">Team</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Played">P</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Won">W</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Drawn">D</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Lost">L</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="For">F</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Against">A</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Goal DIfference">GD</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Win points">Pts</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Bonus points">BP</th>
              <th scope="col" data-toggle="tooltip" data-placement="top" title="Total points">Tot</th>
            </tr>
          </thead>
            <tbody><?php
              $counter = 1;
              foreach ($data['divisionTable'] as $team) {?>
                <tr>
                  <td><?php echo new \Team($team['teamID']);?></td>
                  <td><?php echo $team['played'];?></td>
                  <td><?php echo $team['won'];?></td>
                  <td><?php echo $team['drawn'];?></td>
                  <td><?php echo $team['lost'];?></td>
                  <td><?php echo $team['for'];?></td>
                  <td><?php echo $team['against'];?></td>
                  <td><?php echo $team['gd'];?></td>
                  <td><?php echo $team['points'];?></td>
                  <td><?php echo $team['bp'];?></td>
                  <td><?php echo $team['total'];?></td>
                </tr><?php
                $counter++;
              } ?>
            </tbody>
          </table>
        </div>
        <!-- end content-->
      </div>
      <!-- Previous Division -->    
      <div class="tab-pane fade" id="previous-table-division-1" role="tabpanel" aria-labelledby="netball-tab">
        <!-- display the content here-->
        <div class="table-responsive-md">
          <table class="table table-striped bg-light">
            <thead class="bg-turquoise text-light">
              <tr>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="League standing">#</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Team">Team</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Played">P</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Won">W</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Drawn">D</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Lost">L</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="For">F</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Against">A</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Goal DIfference">GD</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Win points">Pts</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Bonus points">BP</th>
                <th scope="col" data-toggle="tooltip" data-placement="top" title="Total points">Tot</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th scope="row">1</th>
                <td>Martley Mavericks</td>
                <td>3</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>5</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
              </tr>
              <tr>
                <th scope="row">2</th>
                <td>Cheverons</td>
                <td>3</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>10</td>
              </tr>
              <tr>
                <th scope="row">3</th>
                <td>Secon Stars</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>4</td>
                <td>2</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">4</th>
                <td>Hillside</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">5</th>
                <td>Shining Stars</td>
                <td>3</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>2</td>
                <td>1</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>6</td>
              </tr>
              <tr>
                <th scope="row">6</th>
                <td>Black Panthers</td>
                <td>3</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>5</td>
              </tr>
              <tr>
                <th scope="row">7</th>
                <td>Glitterballs</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
              </tr>
              <tr>
                <th scope="row">8</th>
                <td>The Martley Crew</td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td>0</td>
              </tr>
              <tr>
                <th scope="row">9</th>
                <td>Oddballs</td>
                <td>3</td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td>4</td>
                <td>0</td>
                <td>1</td>
                <td>2</td>
                <td>0</td>
              </tr>
            </tbody>
          </table>
          </div>
      <!-- end content-->
      </div><?php
      } else echo "Nothing to show";?>
  </div>
  </div>
  <?php
          /*
              <!-- <div class="teamHeader">
                <h3 class="mt-2 mb-4">Team Fixtures &amp; Results</h3>
              </div>
               -->
            <!-- <div id="fixtures" class="col-md-12 col-sm-12">
              <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item">
                  <a class="nav-link active" id="upcoming-fixtures" data-toggle="tab" href="#upcoming-fixtures" role="tab" aria-controls="home" aria-selected="true">UPCOMING FIXTURES</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="latest-results-control" data-toggle="tab" href="#latest-results" role="tab" aria-controls="profile" aria-selected="false">LATEST RESULTS</a>
                </li>
              </ul>
          </div> -->
          
          <div class="col-md-12 col-sm-12 pt-4">
                <div class="tabs tab-content">
                    <div class="tab-pane fade show active" id="upcoming-fixtures" role="tabpanel" aria-labelledby="rounders-tab">
                        <!-- display the content here-->
                        <div class="table-responsive-md">
                        <table class="table table-striped bg-light upcoming-fixtures">
                            <thead>
                              <tr>
                                <th colspan="6" class="bg-turquoise text-center text-light">WEDNESDAY 4<sup>TH</sup> MARCH</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr>
                                <td>MARTLEY MAVERICKS</td>
                                <td class="text-turquoise"> V </td>
                                <td>THE MARTLEY CREW</td>
                                <td>7 : 00 pm</td>
                                <td>Rossmore Leisure Centre</td>
                                <td>Herbert Avenue</td>
                              </tr>
                              <tr>
                                <td>ODDBALLS</td>
                                <td class="text-turquoise"> V </td>
                                <td>HILLSIDE</td>
                                <td>7 : 40 pm</td>
                                <td>Sobell Leisure Centre</td>
                                <td>Hornsey Road</td>
                              </tr>
                              <tr>
                                <td>CHEVERONS</td>
                                <td class="text-turquoise"> V </td>
                                <td>SHINING STARS</td>
                                <td>8 : 20 pm</td>
                                <td>Broadstone Middle School</td>
                                <td>Dunyeats Road</td>
                              </tr>
                              <tr>
                                <td>BLACK PANTHERS</td>
                                <td class="text-turquoise"> V </td>
                                <td>SECON STARS</td>
                                <td>9 : 00 pm</td>
                                <td>Lytchett Minster School</td>
                                <td>Post Green Road</td>
                              </tr>
                              <thead>
                                <tr>
                                  <th colspan="6" class="bg-turquoise text-center text-light">WEDNESDAY 11<sup>TH</sup> MARCH</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr>
                                  <td>MARTLEY MAVERICKS</td>
                                  <td class="text-turquoise"> V </td>
                                  <td>THE MARTLEY CREW</td>
                                  <td>7 : 00 pm</td>
                                  <td>Carter Community School</td>
                                  <td>Blandford Close</td>
                                </tr>
                                <tr>
                                  <td>ODDBALLS</td>
                                  <td class="text-turquoise"> V </td>
                                  <td>HILLSIDE</td>
                                  <td>7 : 40 pm</td>
                                  <td>The Junction Sports</td>
                                  <td>Station Approach</td>
                                </tr>
                                <tr>
                                  <td>CHEVERONS</td>
                                  <td class="text-turquoise"> V </td>
                                  <td>SHINING STARS</td>
                                  <td>8 : 20 pm</td>
                                  <td>Abergavenny Leisure Centre</td>
                                  <td>Old Hereford Road</td>
                                </tr>
                                <tr>
                                  <td>BLACK PANTHERS</td>
                                  <td class="text-turquoise"> V </td>
                                  <td>SECON STARS</th>
                                  <td>9 : 00 pm</td>
                                  <td>Riverside Sports & Leisure Club</td>
                                  <td>St Oswalds Road</td>
                                </tr>
                              
                            </tbody>
                          </table>
                          </div>
                        <!-- end content-->
                    </div>
                    <div class="tab-pane fade" id="latest-results" role="tabpanel" aria-labelledby="netball-tab">
                         <!-- display the content here-->
                         <div class="table-responsive-md">
                            <table class="table table-striped bg-light latest-results">
                                <thead>
                                  <tr>
                                    <th colspan="7" class="bg-turquoise text-center text-light">WEDNESDAY 4<sup>TH</sup> MARCH</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <td>MARTLEY MAVERICKS</td>
                                    <td>2</td>
                                    <td class="text-turquoise"> V </td>
                                    <td>3</td>
                                    <td>HILLSIDE</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>
                        
                                  </tr>
                                  <tr>
                                    <td>ODDBALLS</td>
                                    <td>0</td>
                                    <td class="text-turquoise"> V </td>
                                    <td>1</td>
                                    <td>THE MARTLEY CREW</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>
                                  </tr>
                                  <tr>
                                    <td>BLACK PANTHERS</td>
                                    <td>6</td>
                                    <td class="text-turquoise"> V </td>
                                    <td>1</td>
                                    <td>SHINING STARS</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>

                                  </tr>
                                  <tr>
                                    <td>CHEVERONS</td>
                                    <td>3</td>
                                    <td class="text-turquoise"> V </td>
                                    <td>3</td>
                                    <td>SECON STARS</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                    <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>

                                  </tr>
                                  <thead>
                                    <tr>
                                      <th colspan="7" class="bg-turquoise text-center text-light">WEDNESDAY 11<sup>TH</sup> MARCH</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td>ODDBALLS</td>
                                      <td>2</td>
                                      <td class="text-turquoise"> V </td>
                                      <td>1</td>
                                      <td>THE MARTLEY CREW</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>
                                    </tr>
                                    <tr>
                                      <td>MARTLEY MAVERICKS</td>
                                      <td>1</td>
                                      <td class="text-turquoise"> V </td>
                                      <td>4</td>
                                      <td>HILLSIDE</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>
                                    </tr>
                                    <tr>
                                      <td>SECON STARS</td>
                                      <td>0</td>
                                      <td class="text-turquoise"> V </td>
                                      <td>0</td>
                                      <td>SHINING STARS</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">Jane Doe</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>
                                    </tr>
                                    <tr>
                                      <td>BLACK PANTHERS</td>
                                      <td>4</td>
                                      <td class="text-turquoise"> V </td>
                                      <td>0</td>
                                      <td>CHEVERONS</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">John Doe</td>
                                      <td data-toggle="tooltip" data-placement="top" title="Player of the month">Steve Smith</td>
                                    </tr>
                                  
                                </tbody>
                              </table>
                              </div>
                        <!-- end content-->
                    </div><?php
                    */
                    ?>
</div>
<script>
  let btnNameChange = document.getElementById("btnNameChange");
  let btnTreasurerChange = document.getElementById("btnTreasurerChange");
  let btnNameSave = document.getElementById("btnNameSave");
  let btnTreasurerSave = document.getElementById("btnTreasurerSave");
  let teamName = document.getElementById("team.name");
  let teamTreasurer = document.getElementById("team.treasurer");
  let teamMessage = document.getElementById("teamMessage");
  function enableNameEdit() {
    btnNameSave.classList.remove("d-none");
    teamName.readOnly = false;
    teamName.focus();
    btnNameChange.classList.add("d-none");
    teamMessage.innerHTML = "Enter a new team name and hit Save";
    teamMessage.classList.remove("d-none");
  }
  function enableTreasurerEdit() {
    btnTreasurerSave.classList.remove("d-none");
    teamTreasurer.readOnly = false;
    teamTreasurer.focus();
    btnTreasurerChange.classList.add("d-none");
    teamMessage.innerHTML = "Enter the Treasurer's email address and hit Save";
    teamMessage.classList.remove("d-none");
  }
  function saveTeamName() {
    console.log("Save Team ",teamName.value);
  }
  function checkTeamName(e) {
    var url = "https://api.leagues4you.co.uk/teamValidate/";
    url += e.target.dataset.leagueid + "/";
    url += e.target.value;
    fetch(encodeURI(url))
    .then(response => {
      if (response.status != 200) return response.json()
    })
    .then(data => {
      if (data) {
        teamMessage.innerHTML = "Cannot use that name " + data;
        btnNameSave.disabled = true;
      } else {
        btnNameSave.disabled = false;
        teamMessage.innerHTML = "That name looks good. Click Save when ready";
      } 
    })
  }
  function checkTreasurerEmail(e) {
    var re = /\S+@\S+\.\S+/;
    var result = re.test(e.target.value);
    if (result === true) {
      btnTreasurerSave.disabled = false;
      teamMessage.innerHTML = "That email looks good. Click Save when ready";
    } else {
        teamMessage.innerHTML = "Invalid email address";
        btnTreasurerSave.disabled = true;
    }
  }
  function saveTeam(e) {
    var url = "https://api.leagues4you.co.uk/teamSave/" + e.target.dataset.teamid;
    let postData = {
      "name": team.name.value
    };
    fetch(url, {
        method: "POST",
        // mode: "cors",
        // credentials: "include",
        body: JSON.stringify(postData)
    })
  }
  btnNameChange.addEventListener("click",enableNameEdit);
  btnTreasurerChange.addEventListener("click",enableTreasurerEdit);
  teamName.addEventListener("keyup",checkTeamName);
  teamTreasurer.addEventListener("keyup",checkTreasurerEmail);
  btnNameSave.addEventListener("click",saveTeam);
</script>