<?php
    $data['transactionTypes'] = [
        1 => ["id" => 1, "name" => "Invoice","type" => "T","action" => "+"],
        2 => ["id" => 2, "name" => "Credit","type" => "T","action" => "-"],
        3 => ["id" => 3, "name" => "Payment","type" => "P","action" => "-"],
        4 => ["id" => 4, "name" => "Refund","type" => "P","action" => "+"]
    ];
    $data['transactions'] = [
        [
            "id" => 1, 
            "type" => 1, 
            "date" => "2020-07-01", 
            "description" => "Team A Winter Season Fixtures",
            "items" => [
                ["id" => 1, "date" => "2020-07-08", "description" => "Team A v Team B","total" => 27],
                ["id" => 2, "date" => "2020-07-15", "description" => "Team A v Team C","total" => 27],
                ["id" => 3, "date" => "2020-07-22", "description" => "Team A v Team D","total" => 27],
                ["id" => 4, "date" => "2020-07-29", "description" => "Team A v Team E","total" => 27]
            ],
            "total" => 108
        ],
        [
            "id" => 2, 
            "type" => 2, 
            "date" => "2020-07-15", 
            "description" => "Credit Fixture Team A v Team C",
            "items" => [
                ["id" => 5, "date" =>  "2020-07-15", "description" => "Credit Fixture Team A v Team C", "total" => -27]
            ] ,
            "total" => -27
        ],
        [
            "id" => 3, 
            "type" => 3, 
            "date" => "2020-07-15",
            "items" => [
                ["id" => 5, "description" => "Credit Fixture Team A v Team C", "total" => -27]
            ] 
        ],
        [
            "id" => 4, 
            "type" => 4, 
            "date" => "2020-07-15",
            "items" => [
                ["id" => 5, "description" => "Credit Fixture Team A v Team C", "total" => -27]
            ] 
        ]
    ];
    $transactions = [];
    foreach ($data['transactions'] as $t) {
        $headerDate = $t['date'];
        if (!$t['items']) continue;
        foreach ($t['items'] as $item) {
            if (isset($item['date']) && $item['date'] > date('Y-m-d')) continue;
            $thisDate = (isset($item['date'])) ? $item['date'] : $headerDate;
            $transactions[] = [
                "date" => $thisDate,
                "type" => $data['transactionTypes'][$t['type']]['name'],
                "description" => $item['description'],
                "amount" => $item['total']
            ];
        }
    }
    function sortByDate($e1, $e2) {
        if ($e1['date'] < $e2['date']) return -1;
        if ($e1['date'] > $e2['date']) return 1;
        return 0;
    }
    usort($transactions, 'sortByDate'); 
    $paymentBalance = $transactionBalance = 0;

    $data['tab']="teams";
?>
<div class="container">
    <?php include (__DIR__."/../menus/dashboard.menu.php");?>
    <div class="max-width-500">
        <ul class="nav nav-pills my-3 justify-content-center" id="payments-tab" role="tablist">
        <?php
        /*
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab']=="main") echo ' active';?>" id="pills-main-tab" data-toggle="pill" href="#pills-main" role="tab" aria-controls="pills-main" aria-selected="true">Statement of Account</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab']=="invoices") echo ' active';?>" id="pills-invoices-tab" data-toggle="pill" href="#pills-invoices" role="tab" aria-controls="pills-invoices" aria-selected="false">Invoices</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab']=="payments") echo ' active';?>" id="pills-payments-tab" data-toggle="pill" href="#pills-payments" role="tab" aria-controls="pills-payments" aria-selected="false">Payments</a>
            </li>
        */ ?>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab']=="teams") echo ' active';?>" id="pills-teams-tab" data-toggle="pill" href="#pills-teams" role="tab" aria-controls="pills-teams" aria-selected="false">Team Payments</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab']=="cards") echo ' active';?>" id="pills-cards-tab" data-toggle="pill" href="#pills-cards" role="tab" aria-controls="pills-cards" aria-selected="false">Payment Cards</a>
            </li>
        </ul>
        <div class="d-flex tab-content justify-content-center p-3 bg-gray-75" id="pills-tabContent">
            <div class="tab-pane fade<?php if ($data['tab']=="main") echo ' show active';?>" id="pills-main" role="tabpanel" aria-labelledby="pills-main-tab">
                <table class="table" style="color: #171e37;">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Reference</th>
                            <th class="text-right">Amount</th>
                            <th class="text-right">Balance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>20/07/2020</td>
                            <td>Invoice</td>
                            <td>Malvern Springs v Team B</td>
                            <td class="text-right">27.00</td>
                            <td class="text-right">27.00</td>
                        </tr>
                        <tr>
                            <td>20/07/2020</td>
                            <td>Payment</td>
                            <td>Malvern Springs v Team B</td>
                            <td class="text-right">27.00</td>
                            <td class="text-right">0.00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="tab-pane fade<?php if ($data['tab']=="invoices") echo ' show active';?>" id="pills-invoices" role="tabpanel" aria-labelledby="pills-invoices-tab">
                <h5>Invoices</h5>
            </div>
            <div class="tab-pane fade<?php if ($data['tab']=="payments") echo ' show active';?>" id="pills-payments" role="tabpanel" aria-labelledby="pills-payments-tab">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Team</th>
                            <th>Status</th>
                            <th class="text-right">Amount</th>
                        </tr>
                    </thead>
                    <tbody><?php
                    if (isset($data['payments'])) {
                        foreach ($data['payments'] as $payment) { ?>
                        <tr>
                            <td title="Ref <?php echo $payment['id'];?>"><?php echo date("d/m/Y",strtotime($payment['updated']));?></td>
                            <td><?php echo new \Team($payment['teamID']);?></td>
                            <td title="Status <?php echo $payment['status'];?>"><?php 
                            if ($payment['status']=="succeeded") {
                                echo "Paid";
                            } elseif ($payment['status']=="requires_capture") {
                                echo "Authorised";
                            } else {
                                if ($payment['status'] == "requires_action") {
                                    \Stripe::PaymentAuth($payment['stripePaymentIntentID']);
                                }?>
                                <a href="./User/CardPay?pi=<?php echo $payment['stripePaymentIntentID'];?>" class="btn btn-sm btn-purple">Pay &pound;<?php echo $payment['total'];?></a><?php
                            }?>
                            </td>
                            <td class="text-right"><?php 
                                if ($payment['status']=="succeeded" || $payment['status']=="requires_capture") echo $payment['total'];?>
                            </td>
                        </tr><?php
                        }
                    } ?>
                    </tbody>
                </table>
            </div>
            <div class="tab-pane fade<?php if ($data['tab']=="cards") echo ' show active';?>" id="pills-cards" role="tabpanel" aria-labelledby="pills-cards-tab">
                <table class="table" style="color: #171e37;">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Last 4</th>
                            <th>Expiry</th>
                            <th class="text-right"><a href="./User/Card" class="btn btn-sm btn-success">Add</a></th>
                        </tr>
                    </thead>
                    <tbody><?php
                    if (isset($data['cards']->data)) {
                        foreach ($data['cards']->data as $val) { ?>
                        <tr>
                            <td><?php echo ucwords($val->card->brand) . " " . ucwords($val->card->funding);?></td>
                            <td><?php echo $val->card->last4;?></td>
                            <td><?php 
                                echo ($val->card->exp_month<10) ? "0" . $val->card->exp_month : $val->card->exp_month;
                                echo "/".$val->card->exp_year;?></td>
                            <td>
                                <form action="./User/Payments" method="post">
                                    <button type="submit" name="removeCard" value="<?php echo $val->id;?>" class="btn btn-sm btn-danger">Remove</button>
                                </form>
                            </td>
                        </tr><?php
                        }
                    } ?>
                    </tbody>
                </table>
            </div>
            <div class="tab-pane fade<?php if ($data['tab']=="teams") echo ' show active';?>" id="pills-teams" role="tabpanel" aria-labelledby="pills-teams-tab">
                <form action="" method="post">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Team</th>
                                <th>Card</th>
                            </tr>
                        </thead>
                        <tbody><?php
                    if (isset($data['treasuryTeams'])) {
                        foreach($data['treasuryTeams'] as $team) {?>
                            <tr>
                                <td><?php echo $team;?></td>
                                <td><?php
                            if (!isset($data['cards']->data) || !$data['cards']->data) {
                                echo 'No Payment Cards <a href="./User/Card" class="btn btn-sm btn-success">Create One</a>';
                            } else {?>
                                    <select name="treasuryTeams[<?php echo $team->id;?>]"  class="form-control form-control-sm">
                                        <option value="">...</option><?php
                                            foreach ($data['cards']->data as $val) { ?>
                                        <option value="<?php echo $val->id;?>"<?php if ($val->id==$team->getStripePaymentMethodID()) echo ' selected';?>><?php echo ucwords($val->card->brand). " **** " . $val->card->last4 . " ex. {$val->card->exp_month}/{$val->card->exp_year}";?></option><?php
                                        } ?>
                                    </select><?php
                            } ?>
                                </td>
                            </tr><?php
                        }
                    } ?>
                        </tbody>
                    </table>
                    <button type="submit" class="btn btn-purple" name="tab" value="teams">Update</button>
                </form>
                <?php #Dump($data['cards']->data);?>
            </div>
        </div>
    </div>
</div>