<?php $postname = "address";?>
<div class="container-fluid">
    <h1>Address</h1>
    <ul class="nav nav-tabs" id="myTab" role="tablist" style="margin-bottom: 1rem;">
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab']=="main") echo ' active';?>" id="main-tab" data-toggle="tab" href="#main" role="tab" aria-controls="main" aria-selected="<?php echo ($data['tab']=="main") ? 'true' : 'false';?>">Main</a>
        </li>
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab']=="contacts") echo ' active';?>"" id="contacts-tab" data-toggle="tab" href="#contacts" role="tab" aria-controls="contacts" aria-selected="<?php echo ($data['tab']=="contacts") ? 'true' : 'false';?>">Contacts</a>
        </li>
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab']=="organisations") echo ' active';?>"" id="organisations-tab" data-toggle="tab" href="#organisations" role="tab" aria-controls="organisations" aria-selected="<?php echo ($data['tab']=="organisations") ? 'true' : 'false';?>">Organisations</a>
        </li>
    </ul>
    <div class="tab-content" id="myTabContent">
        <div class="tab-pane fade<?php if ($data['tab']=="main") echo '  show active';?>" id="main" role="tabpanel" aria-labelledby="main-tab">
            <form action="./User/Addresses" method="post">
                <h5>Main</h5>
                <input type="hidden" name="<?php echo $postname;?>[id]" value="<?php echo $data[$postname]->id;?>">
                <label for="<?php echo $postname;?>[line1]">Line 1</label>
                <input type="text" name="<?php echo $postname;?>[line1]" id="<?php echo $postname;?>.line1" class="form-control" value="<?php echo $data[$postname]->getLine1();?>">
                <label for="<?php echo $postname;?>[line2]">Line 2</label>
                <input type="text" name="<?php echo $postname;?>[line2]" id="<?php echo $postname;?>.line2" class="form-control" value="<?php echo $data[$postname]->getLine2();?>">
                <label for="<?php echo $postname;?>[town]">Town</label>
                <input type="text" name="<?php echo $postname;?>[town]" id="<?php echo $postname;?>.town" class="form-control" value="<?php echo $data[$postname]->getTown();?>">
                <label for="<?php echo $postname;?>[county]">County</label>
                <input type="text" name="<?php echo $postname;?>[county]" id="<?php echo $postname;?>.county" class="form-control" value="<?php echo $data[$postname]->getCounty();?>">
                <label for="<?php echo $postname;?>[postcode]">Post Code</label>
                <input type="text" name="<?php echo $postname;?>[postcode]" id="<?php echo $postname;?>.postcode" class="form-control" value="<?php echo $data[$postname]->getPostcode();?>">
                <label for="<?php echo $postname;?>[country]">Country</label>
                <input type="text" name="<?php echo $postname;?>[country]" id="<?php echo $postname;?>.country" class="form-control" value="<?php echo $data[$postname]->getCountry();?>">
                <button type="submit" class="btn btn-primary">Save</button>
            </form>
        </div>
        <div class="tab-pane fade<?php if ($data['tab']=="organisations") echo '  show active';?>" id="organisations" role="tabpanel" aria-labelledby="organisations-tab">
            <h5>Organisations</h5>
        </div>
        <div class="tab-pane fade<?php if ($data['tab']=="contacts") echo '  show active';?>" id="contacts" role="tabpanel" aria-labelledby="contacts-tab">
            <h5>Contacts</h5>
        </div>
    </div>
</div>