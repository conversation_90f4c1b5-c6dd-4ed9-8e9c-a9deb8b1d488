<?php include (__DIR__."/../menus/dashboard.menu.php");?>
<!--
<?php print_r($_SESSION['newTeam']);?>
-->
<style>
:disabled {
    cursor: no-drop;
}
</style>
<div class="container">
    <div class="p-4 my-4"  style="background-color: rgba(150,150,150,.65);">
        <div class="d-flex justify-content-center" style="color: rgb(0,0,80);">
            <span class="active" style="border-bottom: medium solid rgb(0,0,80);">Team Name</span>
            <!-- <span class="arrow mx-2"> &gt;</span>
            <span>Captain</span> -->
            <span class="arrow mx-2"> &gt;</span>
            <span>Treasurer</span>
            <span class="arrow mx-2"> &gt;</span>
            <span>Ts &amp; Cs</span>
        </div>
        <form action="/User/TeamTreasurer" method="post" class="d-flex justify-content-center mt-4">
            <div class="d-flex flex-column align-items-center" style="color: rgb(0,0,80);"x>
                <div class="d-flex flex-column align-items-center">
                    <h5>Enter your team name</h5>
                    <input data-leagueid="<?php echo $_SESSION['newTeam']['leagueID'];?>" type="text" name="newTeam[name]" id="newTeam.name" class="form-control mb-3 mt-2" required>
                    <button id="btnNameSave" type="submit" class="btn btn-purple" name="teamStage" value="2" disabled>Next</button>
                    <!-- <input class="formSubmit ml-2" type="image" id="loginSubmit" alt="Login"
            src="./img/submitButton.png"> -->
                </div>
                <p id="teamMessage" class="mt-4 p-2 text-light"></p>
            </div>
        </form>
    </div>
</div>
<script>
    var teamMessage = document.getElementById("teamMessage");
    var btnNameSave = document.getElementById("btnNameSave");
    var teamName = document.getElementById("newTeam.name");
    function checkTeamName(e) {
        var url = "https://api.leagues4you.co.uk/teamValidate/";
        url += e.target.dataset.leagueid + "/";
        url += e.target.value;
        fetch(encodeURI(url))
        .then(response => {
            if (response.status != 200) return response.json()
        })
        .then(data => {
            if (data) {
                teamMessage.innerHTML = "Cannot use that name " + data;
                btnNameSave.disabled = true;
                teamMessage.classList.add("bg-warning");
                teamMessage.classList.remove("bg-success");
            } else {
                btnNameSave.disabled = false;
                teamMessage.innerHTML = "That name looks good. Click Next when ready";
                teamMessage.classList.remove("bg-warning");
                teamMessage.classList.add("bg-success");
            } 
        })
    }
    teamName.addEventListener("keyup",checkTeamName);
</script>