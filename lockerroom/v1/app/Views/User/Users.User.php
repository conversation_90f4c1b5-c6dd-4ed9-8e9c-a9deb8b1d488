<div class="container-fluid">
    <h1>Users | <a href="./User/User" class="btn btn-sm btn-success">New</a></h1>
    <table class="table">
        <thead>
            <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
                <th>Mobile</th>
                <th>Landline</th>
                <th>...</th>
            </tr>
        </thead>
        <tbody><?php
        if ($data['users']) {
            foreach ($data['users'] as $user) { ?>
            <tr>
                <td><?php echo $user->getFirstname();?></td>
                <td><?php echo $user->getLastname();?></td>
                <td><?php echo $user->getEmail();?></td>
                <td><?php echo $user->getMobile();?></td>
                <td><?php echo $user->getTelephone();?></td>
                <td><a href="./User/User/<?php echo $user->id;?>" class="btn btn-sm btn-success">&rarr;</a></td>
            </tr><?php
            }
        } ?>
        </tbody>
    </table>
</div>