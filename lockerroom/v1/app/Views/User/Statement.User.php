<?php
    $data['transactions'] = [
        ["id" => 1, "type" => "Invoice", "date" => "2020-07-12", "description" => "Team A v Team B","total" => 27],
        ["id" => 2, "type" => "Payment", "date" => "2020-07-12", "description" => "Payment","total" => -27],
        ["id" => 3, "type" => "Invoice", "date" => "2020-07-19", "description" => "Team A v Team B","total" => 27],
        ["id" => 4, "type" => "Refund", "date" => "2020-07-19", "description" => "Refund","total" => -10],
        ["id" => 5, "type" => "Payment", "date" => "2020-07-19", "description" => "Payment","total" => 12]
    ];
    $balance = 0;
?>
<div class="container">
    <h1>Statement of Account</h1>
    <div class="max-width-500">
        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" id="pills-main-tab" data-toggle="pill" href="#pills-main" role="tab" aria-controls="pills-main" aria-selected="true">Payments</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="pills-statement-tab" data-toggle="pill" href="#pills-statement" role="tab" aria-controls="pills-statement" aria-selected="false">Statement</a>
            </li>
        </ul>
        <div class="tab-content" id="pills-tabContent">
            <div class="tab-pane fade show active" id="pills-main" role="tabpanel" aria-labelledby="pills-main-tab">
                <h4>Payment Schedule</h4>        
            </div>
            <div class="tab-pane fade" id="pills-statement" role="tabpanel" aria-labelledby="pills-statement-tab">
                <h4>Statement of Account</h4>        
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Reference</th>
                            <th class="text-right">&pound; Amount</th>
                            <th class="text-right">&pound; Balance</th>
                        </tr>
                    </thead>
                    <tbody><?php
                    foreach ($data['transactions'] as $t)  {
                        $balance += $t['total'];?>
                        <tr>
                            <td><?php echo date('d/m/Y',strtotime($t['date']));?></td>
                            <td><?php echo $t['type'];?></td>
                            <td><?php echo $t['description'];?></td>
                            <td class="text-right <?php if ($t['total']<0) echo " text-danger";?>"><?php echo number_format($t['total'],2);?></td>
                            <td class="text-right"><?php echo number_format($balance,2);?></td>
                        </tr><?php
                    } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>