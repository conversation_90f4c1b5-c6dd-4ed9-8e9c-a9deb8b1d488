<?php include (__DIR__."/../menus/dashboard.menu.php");?>
<!--
<?php print_r($_SESSION['newTeam']);?>
-->
<div class="container">
    <div class="p-4 my-4"  style="background-color: rgba(150,150,150,.65);">
        <div class="d-flex justify-content-center" style="color: rgb(0,0,80);">
            <span>Team Name</span>
            <span class="arrow mx-2"> &gt;</span>
            <!-- <span>Captain</span>
            <span class="arrow mx-2"> &gt;</span> -->
            <span class="active" style="border-bottom: medium solid rgb(0,0,80);">Treasurer</span>
            <span class="arrow mx-2"> &gt;</span>
            <span>Ts &amp; Cs</span>
        </div>
        <form action="/User/TeamTerms" method="post" class="d-flex justify-content-center mt-4">
            <div class="d-flex flex-column align-items-center" style="color: rgb(0,0,80);">
                <div class="d-flex flex-column mb-2 align-items-center">
                    <h5>Confirm Treasurer Email Address</h5>
                    <input type="email" name="newTeam[treasurerEmail]" id="newTeam.treasurerEmail" class="form-control my-3" value="<?php echo $_SESSION['newTeam']["treasurerEmail"];?>" required>
                    <button type="submit" class="btn btn-purple ml-4" name="teamStage" value="3">Next</button>
                    <!-- <input class="formSubmit ml-2" type="image" id="loginSubmit" alt="Login" src="./img/submitButton.png"> -->
                </div>
                <p style="font-size: .9rem;">You can change this to another user. The user will be sent a request to confirm. NB: Please note your team and any subsequent league entries will not be confirmed until a treasurer is appointed.</p>
            </div>
        </form>
    </div>
</div>