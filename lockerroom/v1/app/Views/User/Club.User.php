<div class="container-fluid">
    <h1>My Club</h1>
    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="pills-main-tab" data-toggle="pill" href="#pills-main" role="tab" aria-controls="pills-main" aria-selected="true">Main</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="pills-teams-tab" data-toggle="pill" href="#pills-teams" role="tab" aria-controls="pills-teams" aria-selected="false">Teams</a>
        </li>
    </ul>
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade show active" id="pills-main" role="tabpanel" aria-labelledby="pills-main-tab">
            <h4>Club Info.</h4>
            <form action="" method="post">
                <input type="hidden" name="club[id]" value="<?php if (isset($data['club'])) echo $data['club']->id;?>">
                <label for="club.name">Name</label>
                <input type="text" name="club[name]" id="club.name" class="form-control" placeholder="Club Name" value="<?php if (isset($data['club'])) echo $data['club']->getName();?>">
                <button type="submit" class="btn btn-primary">Save</button>
            </form>
        </div>
        <div class="tab-pane fade" id="pills-teams" role="tabpanel" aria-labelledby="pills-teams-tab">
            <h4>Teams | <a href="./User/TeamReg1" class="btn btn-sm btn-info">New</a></h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th class="text-center">Player</th>
                        <th class="text-center">Treasurer</th>
                        <th class="text-center">Captain</th>
                        <th>...</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Team A</td>
                        <td class="text-center">&check;</td>
                        <td class="text-center">&check;</td>
                        <td class="text-center">&nbsp;</td>
                        <td><button class="btn btn-sm btn-danger">Edit</button></td>
                        <td><button class="btn btn-sm btn-success">Invite</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>