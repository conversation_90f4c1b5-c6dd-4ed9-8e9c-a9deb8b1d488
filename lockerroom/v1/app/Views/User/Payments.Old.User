<?php
    $data['transactionTypes'] = [
        1 => ["id" => 1, "name" => "Invoice","type" => "T","action" => "+"],
        2 => ["id" => 2, "name" => "Credit","type" => "T","action" => "-"],
        3 => ["id" => 3, "name" => "Payment","type" => "P","action" => "-"],
        4 => ["id" => 4, "name" => "Refund","type" => "P","action" => "+"]
    ];
    $data['transactions'] = [
        [
            "id" => 1, 
            "type" => 1, 
            "date" => "2020-07-01", 
            "description" => "Team A Winter Season Fixtures",
            "items" => [
                ["id" => 1, "date" => "2020-07-08", "description" => "Team A v Team B","total" => 27],
                ["id" => 2, "date" => "2020-07-15", "description" => "Team A v Team C","total" => 27],
                ["id" => 3, "date" => "2020-07-22", "description" => "Team A v Team D","total" => 27],
                ["id" => 4, "date" => "2020-07-29", "description" => "Team A v Team E","total" => 27]
            ],
            "total" => 108
        ],
        [
            "id" => 2, 
            "type" => 2, 
            "date" => "2020-07-15", 
            "description" => "Credit Fixture Team A v Team C",
            "items" => [
                ["id" => 5, "date" =>  "2020-07-15", "description" => "Credit Fixture Team A v Team C", "total" => -27]
            ] ,
            "total" => -27
        ],
        [
            "id" => 3, 
            "type" => 3, 
            "date" => "2020-07-15",
            "items" => [
                ["id" => 5, "description" => "Credit Fixture Team A v Team C", "total" => -27]
            ] 
        ],
        [
            "id" => 4, 
            "type" => 4, 
            "date" => "2020-07-15",
            "items" => [
                ["id" => 5, "description" => "Credit Fixture Team A v Team C", "total" => -27]
            ] 
        ]
    ];
    $transactions = [];
    foreach ($data['transactions'] as $t) {
        $headerDate = $t['date'];
        if (!$t['items']) continue;
        foreach ($t['items'] as $item) {
            if (isset($item['date']) && $item['date'] > date('Y-m-d')) continue;
            $thisDate = (isset($item['date'])) ? $item['date'] : $headerDate;
            $transactions[] = [
                "date" => $thisDate,
                "type" => $data['transactionTypes'][$t['type']]['name'],
                "description" => $item['description'],
                "amount" => $item['total']
            ];
        }
    }
    function sortByDate($e1, $e2) {
        if ($e1['date'] < $e2['date']) return -1;
        if ($e1['date'] > $e2['date']) return 1;
        return 0;
    }
    usort($transactions, 'sortByDate'); 
    $paymentBalance = $transactionBalance = 0;
?>
<div class="modal fade" id="paymentCardModal" tabindex="-1" role="dialog" aria-labelledby="paymentCardModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="paymentCardModalLabel">Payment Card</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <div class="d-flex">
                <label for="">Card Number</label>
                <input type="text" name="card[number]" id="card.number" class="form-control" placeholder="4242 4242 4242 4242" required>
            </div>
            <div class="d-flex">
                <label for="">Exp Month</label>
                <select name="card[expiryMonth]" id="card.expiryMonth" class="form-control" required>
                    <option value="">...</option>
                    <option value="">Jan</option>
                    <option value="">Feb</option>
                </select>
            </div>
            <div class="d-flex">
                <label for="">Exp Year</label>
                <select name="card[expiryYear]" id="card.expiryMonth" class="form-control" required>
                    <option value="">...</option>
                    <option value="">2020</option>
                    <option value="">2021</option>
                    <option value="">2022</option>
                    <option value="">2023</option>
                </select>
            </div>
            <div class="d-flex">
                <label for="">Security Code</label>
                <input type="number" name="card[cvv]" id="card.cvv" class="form-control" placeholder="123" required>
            </div>
            <button type="submit" class="btn btn-success">Add</button>
        </form>
      </div>
    </div>
  </div>
</div>
<div class="container-fluid">
    <h1>Statement of Account</h1>
    <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="pills-main-tab" data-toggle="pill" href="#pills-main" role="tab" aria-controls="pills-main" aria-selected="true">Statement of Account</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="pills-statement-tab" data-toggle="pill" href="#pills-statement" role="tab" aria-controls="pills-statement" aria-selected="false">Invoices/Credits</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="pills-cards-tab" data-toggle="pill" href="#pills-cards" role="tab" aria-controls="pills-cards" aria-selected="false">Payment Cards</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="pills-teams-tab" data-toggle="pill" href="#pills-teams" role="tab" aria-controls="pills-teams" aria-selected="false">Team Payments</a>
        </li>
    </ul>
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade show active" id="pills-main" role="tabpanel" aria-labelledby="pills-main-tab">
            <h4>Payments</h4>        
            <table class="table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Reference</th>
                        <th class="text-right">&pound; Amount</th>
                        <th class="text-right">&pound; Balance</th>
                    </tr>
                </thead>
                <tbody><?php
                foreach ($transactions as $t)  {
                    $paymentBalance += $t['amount'];?>
                    <tr>
                        <td><?php echo date('d/m/Y',strtotime($t['date']));?></td>
                        <td><?php echo $t['type'];?></td>
                        <td><?php echo $t['description'];?></td>
                        <td class="text-right <?php if ($t['total']<0) echo " text-danger";?>"><?php echo number_format($t['amount'],2);?></td>
                        <td class="text-right"><?php echo number_format($paymentBalance,2);?></td>
                    </tr><?php
                } ?>
                </tbody>
            </table>
        </div>
        <div class="tab-pane fade" id="pills-statement" role="tabpanel" aria-labelledby="pills-statement-tab">
            <h4>Transactions</h4>        
            <table class="table">
                <thead>
                    <tr>
                        <th>Due Date</th>
                        <th>Reference</th>
                        <th class="text-right">&pound; Amount</th>
                        <th class="text-right">&pound; Balance</th>
                    </tr>
                </thead>
                <tbody><?php
                foreach ($data['transactions'] as $t)  {
                    if ($t['type']>2) continue;
                    $transactionBalance += $t['total'];?>
                    <tr class="bg-info">
                        <th colspan="5"><?php echo $data['transactionTypes'][$t['type']]['name']. " " . $t['id'];?></th>
                    </tr><?php
                    foreach ($t['items'] as $i) {
                    ?>
                    <tr>
                        <td><?php echo date('d/m/Y',strtotime($i['date']));?></td>
                        <td><?php echo $i['description'];?></td>
                        <td class="text-right <?php if ($i['total']<0) echo " text-danger";?>"><?php echo number_format($i['total'],2);?></td>
                        <td class="text-right"><?php echo number_format($transactionBalance,2);?></td>
                    </tr><?php
                    }
                } ?>
                </tbody>
            </table>
        </div>
        <div class="tab-pane fade" id="pills-cards" role="tabpanel" aria-labelledby="pills-cards-tab">
            <h3>Payment Cards | <button type="button" class="btn btn-sm btn-warning" data-toggle="modal" data-target="#paymentCardModal">Add a Card</button></h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Last 4</th>
                        <th>Expiry</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>VISA</td>
                        <td>4529</td>
                        <th>02/22</th>
                        <td><button class="btn btn-sm btn-danger">Remove</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="tab-pane fade" id="pills-teams" role="tabpanel" aria-labelledby="pills-teams-tab">
            <h3>Team Payments</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Team A</td>
                        <td><button class="btn btn-sm btn-success">Accept</button></td>
                    </tr>
                    <tr>
                        <td>Team B</td>
                        <td>
                            <select name="" id="" class="form-control">
                                <option value="">...</option>
                                <option value="1" selected>VISA **** 4269 Ex 02/22</option>
                            </select>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>