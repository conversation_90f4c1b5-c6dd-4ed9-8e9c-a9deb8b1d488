<div class="registrationSteps d-flex justify-content-center pt-3">
    <a href="./Home/Register">About Me</a>
    <a class="active mx-4">My Team</a>
</div>
<div class="container-fluid pt-5">
    <h1 class="text-center display-3">My Team</h1>
    <form action="./Home/CompleteRegistration" method="post" class="d-flex flex-column align-items-center">
        
        <select name="register[sportID]" id="registration.sportID" class="form-control max-300-600 my-1">
            <option value="">Select Your Sport</option><?php
            if (isset($data['sports'])) {
                foreach ($data['sports'] as $sport) {?>
            <option value="<?php echo $sport['id'];?>"><?php echo $sport['name'];?></option><?php
                }
            }?>
        </select>

        <select name="register[regionID]" id="registration.regionID" class="form-control max-300-600 my-1 d-none">
            <option value="">Select Your Region</option><?php
            if (isset($data['regions'])) {
                foreach ($data['regions'] as $region) {?>
            <option value="<?php echo $region['id'];?>"><?php echo $region['name'];?></option><?php
                }
            }?>
        </select>

        <select name="register[leagueID]" id="registration.leagueID" class="form-control max-300-600 my-1 d-none">
            <option value="">Select Your League</option><?php
            if (isset($data['leagues'])) {
                foreach ($data['leagues'] as $league) {?>
            <option data-regionid="<?php echo $league['regionID'];?>" value="<?php echo $league['id'];?>"><?php echo $league['name'];?></option><?php
                }
            }?>
        </select>

        <select name="register[action]" id="registration.action" class="form-control max-300-600 my-1 d-none">
            <option value="">I want to ...</option>
            <option value="1">Join a Team</option>
            <option value="2">Create a Team</option>
        </select>

        <select name="register[teamID]" id="registration.teamID" class="form-control max-300-600 my-1 d-none">
            <option value="">Select Team</option><?php
            if (isset($data['teams'])) {
                foreach ($data['teams'] as $team) {?>
            <option value="<?php echo $team['id'];?>"><?php echo $team['name'];?></option><?php
                }
            }?>
        </select>
        
        <input type="text" name="register[teamName]" id="registration.teamName" class="form-control max-300-600 my-1 d-none" placeholder="Enter Your Team Name">
        <button id="registrationSubmit"  type="submit" class="btn btn-lg btn-pink d-none">Next</button>
    </form>
</div>
<script>
    var sportDropdown = document.getElementById("registration.sportID");
    var regionDropdown = document.getElementById("registration.regionID");
    var leagueDropdown = document.getElementById("registration.leagueID");
    var actionDropdown = document.getElementById("registration.action");
    var teamDropdown = document.getElementById("registration.teamID");
    var teamInput = document.getElementById("registration.teamName");
    function dataCheck() {
        if (sportDropdown.value) {
            console.log("Sport",sportDropdown.value);
            regionDropdown.classList.remove("d-none");
        } else regionDropdown.classList.add("d-none");

        if (regionDropdown.value) {
            console.log("Region",regionDropdown.value);
            leagueDropdown.classList.remove("d-none");
        } else leagueDropdown.classList.add("d-none");

        if (leagueDropdown.value) {
            console.log("League",leagueDropdown.value);
            actionDropdown.classList.remove("d-none");
        } else actionDropdown.classList.add("d-none");

        if (actionDropdown.value) {
            console.log("Action ",actionDropdown.value);
            if (actionDropdown.value == 1) {
                teamDropdown.classList.remove("d-none");
                teamInput.classList.add("d-none");
            } else if (actionDropdown.value == 2) {
                teamInput.classList.remove("d-none");
                teamDropdown.classList.add("d-none");
            }
        } else {
            teamDropdown.classList.add("d-none");
            teamInput.classList.add("d-none");
        } 
        if (sportDropdown.value && regionDropdown.value && leagueDropdown.value && actionDropdown.value && (teamDropdown.value || teamInput.value)) {
            registrationSubmit.classList.remove("d-none");
        } else registrationSubmit.classList.add("d-none");
    }
    sportDropdown.addEventListener("change",dataCheck);
    regionDropdown.addEventListener("change",dataCheck);
    leagueDropdown.addEventListener("change",dataCheck);
    actionDropdown.addEventListener("change",dataCheck);
    teamDropdown.addEventListener("change",dataCheck);
    teamInput.addEventListener("keyup",dataCheck);
</script>