<div class="registrationSteps d-flex justify-content-center pt-3">
    <a class="active">About Me</a>
    <span class="mx-4">My Team</span>
</div>
<div class="container-fluid pt-5">
    <h1 class="text-center display-3">My Info</h1>
    <form action="./Home/TeamRegistration" method="post" class="d-flex flex-column align-items-center">
        <input type="text" name="register[firstname]" id="register.firstname" class="form-control max-300-600 mt-5 mb-2" placeholder="Your Firstname" required value="<?php if (isset($data['userRegistration'])) echo $data['userRegistration']->firstname;?>">
        <input type="text" name="register[lastname]" id="register.lastname" class="form-control my-2 max-300-600" placeholder="Your Lastname" required value="<?php if (isset($data['userRegistration'])) echo $data['userRegistration']->lastname;?>">
        <input type="email" name="register[email]" id="register.email" class="form-control my-2 max-300-600" placeholder="Your Email Address" required value="<?php if (isset($data['userRegistration'])) echo $data['userRegistration']->email;?>">
        <input type="tel" name="register[mobile]" id="register.mobile" class="form-control my-2 max-300-600" placeholder="Your Mobile Number" required value="<?php if (isset($data['userRegistration'])) echo $data['userRegistration']->mobile;?>">
        <button type="submit" class="btn btn-lg mt-5 btn-pink">Next</button>
    </form>
</div>
<script>
    var mobileInput = document.getElementById("register.mobile");
    function numbersOnly() {
        if (!mobileInput.value) return;
        var permittedChars = [1,2,3,4,5,6,7,8,9,0];
        var chars = mobileInput.value.split("");
        var returnVal = "";
        for (c in chars) {
            for (var i = 0; i < permittedChars.length; i++) {
                if (permittedChars[i] == chars[c]) {
                    returnVal += chars[c];
                    break;
                }
            }
        }
        mobileInput.value = returnVal;
    }
    mobileInput.addEventListener("keyup",numbersOnly);
</script>