<?php global $post; ?>
<style>
    .loginScreen {
        display: flex;
        padding: 1rem;
        margin-top: 20vh;
    }
    .loginScreen section {
        flex:1;
        margin: 1rem;
        padding: 1rem;
    }
    .loginScreen input {
            margin-bottom: 1rem;
        }
    .greyBg {
        background-color: rgba(0,0,0,.3);
        padding: 0 1rem;
    }
    .buttonGroup {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: .5rem;
    }
    .formNote {
        font-size: .9rem;
        color: #ccc;
    }
    @media (max-width: 700px) {
        .loginScreen {
            flex-direction: column;
            margin-top: 0;
            background-color: rgba(100,100,100,.65);
        }
        .greyBg {
            padding: 0;
        }
        .formNote {
            margin-bottom: .25rem;
        }
        h4 {
            font-size:1.25rem;
        }
        input {
            height: calc(1.25em + .5rem + 2px);
        }
        .loginScreen section {
            margin: .5rem;
        }
        body.home {
            background-image: none;
            background-color: rgba(100,100,100,.95);
        }
    }
</style>
<div class="loginScreen">
    <section class=" greyBg">
        <h4>Login</h4>
        <!-- <button id="fb_login_btn" class="btn btn-primary" onclick="">Login with FB</button> -->
        <form action="/Home" method="post">    
            <input type="email" name="auth[email]" id="auth.email" class="form-control" placeholder="Email Address" required>
            <input type="password" name="auth[password]" id="auth.password" class="form-control" placeholder="Password" required>
            <div class="buttonGroup loginButtons">
                <button type="submit" class="btn btn-sm btn-purple">Login</button>
                <small><a style="color: #ccc;" href="./Home/Password">Forgotten password?</a></small>
            </div>
        </form>
        <p class="formNote">Log into your Locker Room to find all things relating to you and your club. View fixtures, results, manage your team, speak to your coordinator. Whatever it is, you'll find it here.</p>
    </section>
    <section class="greyBg">
        <h4>Register</h4>
        <form action="/Home" method="post">
            <input type="hidden" name="register[registrationLeagueID]" value="<?php if (isset($post['leagueID'])) echo $post['leagueID'];?>">
            <input type="email" name="register[email]" id="register.email" class="form-control" placeholder="Email Address" required>
            <input type="tel" name="register[mobile]" id="register.mobile" class="form-control" placeholder="Mobile (or Landline) Number" required>
            <button type="submit" class="btn btn-sm btn-purple mb-2">Register</button>
        </form>
        <p class="formNote">It's super-quick to register.<br>Pop your email address above and we'll send you an activation link.</p>
    </section>
</div>
<script>
    function statusChangeCallback (response) {
        if (response.status == "connected") {
            FB.api('/me', function(response) {
                console.log('Me',response);
            });
            // window.location.replace("https://lockerroom.leagues4you.co.uk/User");
        } 
        console.log("Response",response);
    }
    function loginWithFacebook() {
        FB.login(function(response) {
            // statusChangeCallback (response)

        }, {scope: 'public_profile,email'});
    }
    window.onload = function () {
        FB.getLoginStatus(function(response) {
            statusChangeCallback(response);
        });
    }
    // document.getElementById("fb_login_btn").addEventListener("click",loginWithFacebook);
</script>