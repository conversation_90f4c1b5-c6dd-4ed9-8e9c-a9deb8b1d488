<?php
namespace Messaging;

function Available() {
    if (isset($_SESSION['messaging']) && is_array($_SESSION['messaging']) && $_SESSION['messaging']) return true;
}
function Add ($message, $level="info") {
    $_SESSION['messaging'][$level][] = $message;
    // echo "Added $message<br>";
}
function Show() {
    if (isset($_SESSION['messaging']) && $_SESSION['messaging']) {
        foreach ($_SESSION['messaging'] as $level => $messages) {?>
         <div class="alert alert-<?php echo $level;?> alert-dismissible fade show" role="alert"><?php 
            foreach ($messages as $message) {
                echo "$message<br>";
            }?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div><?php
        }
        Clear();
    }
}
function Clear() {
    unset($_SESSION['messaging']);
}