<?php

namespace Tools;

function Dump ($data) {
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

function CheckFolder ($folder) {
    /* Add trailing slash if not present */
    if (substr($folder,0,1)=="/") $folder = substr($folder,1);
    if (substr($folder,-1)=="/") $folder = substr($folder,0,strlen($folder)-1);
    $elements = explode("/",$folder);
    $folderToCheck = null;
    foreach($elements as $element) {
        $folderToCheck .= "/".$element;
        if ($element == "..") continue;
        if (!file_exists($folderToCheck)) {
            $old = umask(0);
            mkdir($folderToCheck,0775);
            umask($old);
        } 
    }
    return "$folderToCheck/";
}

function FolderFiles($folder) {
    $files = scandir($folder);
    if (!$files || !is_array($files)) return;
    $return = [];
    foreach ($files as $file) {
        if ($file == "." || $file == "..") continue;
        $return[] = $file;
    }
    return $return;
}
 