<?php

$sqlTables = [
    "users" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "firstname" => ["type" => "varchar(50)"],
        "lastname" => ["type" => "varchar(100)"],
        "email" => ["type" => "varchar(150)", "key" => "unique"],
        "mobile" => ["type" => "varchar(20)"],
        "landline" => ["type" => "varchar(20)"],
        "extension" => ["type" => "varchar(10)"],
        "password" => ["type" => "varchar(60)"],
        "activationCode" => ["type" => "varchar(10)"],
        "activationExpiry" => ["type" => "datetime"],
        "activationIP" => ["type" => "varchar(20)"],
        "activationStamp" => ["type" => "datetime"],
        "resetCode" => ["type" => "varchar(10)"],
        "resetExpiry" => ["type" => "datetime"],
        "resetIP" => ["type" => "varchar(20)"],
        "resetSession" => ["type" => "varchar(50)"],
    ],
    "addresses" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "line1" => ["type" => "varchar(100)"],
        "line2" => ["type" => "varchar(100)"],
        "town" => ["type" => "varchar(100)"],
        "county" => ["type" => "varchar(100)"],
        "postcode" => ["type" => "varchar(10)"],
        "country" => ["type" => "varchar(100)","default" => "'United Kingdom'"],
    ],
    "organisations" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "name" => ["type" => "varchar(100)"],
        "companyReg" => ["type" => "varchar(20)"],
        "vatReg" => ["type" => "varchar(20)"],
    ],
    "userAddresses" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "userID" => ["type" => "int(11)","attributes" => "unsigned"],
        "addressID" => ["type" => "int(11)","attributes" => "unsigned"]
    ],
    "organisationAddresses" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "organisationID" => ["type" => "int(11)","attributes" => "unsigned"],
        "addressID" => ["type" => "int(11)","attributes" => "unsigned"]
    ],
    "userContacts" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "userID" => ["type" => "int(11)","attributes" => "unsigned"],
        "contactID" => ["type" => "int(11)","attributes" => "unsigned"]
    ],
    "userOrganisations" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "userID" => ["type" => "int(11)","attributes" => "unsigned"],
        "organisationID" => ["type" => "int(11)","attributes" => "unsigned"]
    ],
    "userAddresses" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "userID" => ["type" => "int(11)","attributes" => "unsigned"],
        "addressID" => ["type" => "int(11)","attributes" => "unsigned"]
    ],
    "stripeCustomers" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "userID" => ["type" => "int(10) unsigned"],
        "stripeCustomerID" => ["type" => "varchar(30)"]
    ],
    "financeReceipts" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "deleted" => ["type" => "datetime"],
        "teamID" => ["type" => "int(10) unsigned"],
        "userID" => ["type" => "int(10) unsigned"],
        "total" => ["type" => "float(8,2)"],
        "stripePaymentIntentID" => ["type" => "varchar(30)"],
        "stripePaymentMethodID" => ["type" => "varchar(30)"],
        "status" => ["type" => "varchar(100)"],
        "statusID" => ["type" => "tinyint(4) unsigned"]
    ],
    "treasurers" => [
        "id" => ["type" => "int(11)","attributes" => "unsigned","key" => "primary key","extra" => "auto_increment"],
        "created" => ["type" => "datetime", "default" => "current_timestamp()",],
        "updated" => ["type" => "datetime","default" => "current_timestamp()","extra" => "on update current_timestamp()"],
        "invited" => ["type" => "datetime"],
        "accepted" => ["type" => "datetime"],
        "deleted" => ["type" => "datetime"],
        "userID" => ["type" => "int(10) unsigned"],
        "teamID" => ["type" => "int(10) unsigned"],
        "stripePaymentMethodID" => ["type" => "varchar(30)"],
    ]
];

$sqlUniques = [
    "organisationAddresses" => [
        "organisationAddress" => ["organisationID", "addressID"]
    ],
    "userAddresses" => [
        "userAddress" => ["userID", "addressID"]
    ],
    "userContacts" => [
        "userContact" => ["userID", "contactID"]
    ]
];