<?php
include (__DIR__."/app/Init.php");
// $rlt = \Database::Setup();#Dump($rlt);

// $userID = 69; # <EMAIL>
// $teamID = 201; # Chelsea
// \Team::SetTreasurer($teamID, $userID);

// $userID = 68; # <EMAIL>
// $teamID = 198; # Liverpool
// \Team::SetTreasurer($teamID, $userID);

// $userID = 66; # charlottewa<PERSON>@live.co.uk
// $teamID = 203; # Leicester
// \Team::SetTreasurer($teamID, $userID);

// exit(0);

/**
 * Steps for Payment
 * 1a. Treasurer should be defined. (Set or Invite/Accept)
 * 1b. Treasurer should allocate a valid card against the Team (via Locker Room payments)
 * 
 * 2a. Raise a FinanceReceipt
 * 2b. FinanceReceipt gets an accompanying Stripe Payment Intent.
 * 2c. FinanceReceipt gets an accompanying Stripe Payment Method
 * 3a. Payment Intent Confirmed.
 * 3b. Payment Intent Captured.
*/
// $teams = [198,199,200];
// $userID = 69;
// # Make User a Treasurer of these Teams
// foreach ($teams as $teamID) \Team::SetTreasurer($teamID, $userID);
// # Raise Bill for each Team
// $amount = 27; 
// foreach ($teams as $teamID) {
//     $financeReceipt = new \FinanceReceipt();
//     $financeReceipt->Save();
//     $financeReceipt->setTeamID($teamID);
//     $financeReceipt->setUserID($userID);
//     $financeReceipt->setTotal($amount);
//     $financeReceipt->Save();
//     $receiptIDs[] = $financeReceipt->id;
// }
// exit(Dump($financeReceipt));
// exit(0);
// // $receiptIDs = [28,29,30];
// foreach ($receiptIDs as $receiptID) {
//     // $financeReceiptID = 27;
//     $financeReceipt = new \FinanceReceipt($receiptID);
//     if ($financeReceipt->Check() === true) $financeReceipt->Confirm();
//     echo "Finance Receipt $receiptID = " . $financeReceipt->getStatus() . "<br>";
//     // if ($financeReceipt->getStatus() === "requires_capture") $financeReceipt->Capture();
//     // exit(Dump($financeReceipt));
// }
// exit(0);
// // $financeReceipt->Capture();


// \Team::InviteTreasurer($teamID, $userID);
// \Team::AcceptTreasurer($teamID, $userID);
// $user = new \User($userID);
// $cardCustomer = \Stripe::GetCustomer($financeReceipt->getUserID());
// if (!$cardCustomer) {
//     $user = new \User($financeReceipt->getUserID());
//     $cardCustomer = \Stripe::CreateCustomer($user->id, $user->getEmail());
// }
// $treasurer = \Team::Treasurer($financeReceipt->getTeamID()); #Dump($treasurer);
// if (!$treasurer || !isset($treasurer['userID']) || !$treasurer['userID']) {
//     $financeReceipt->setStatus("Team has no treasurer");
//     $financeReceipt->Save();
// } elseif (!$treasurer['accepted']) {
//     $financeReceipt->setStatus("Team treasurer has not accepted role");
//     $financeReceipt->Save();
// } elseif (!$treasurer['stripePaymentMethodID']) {
//     $treasurerUser = new \User($treasurer['userID']);
//     $financeReceipt->setStatus($treasurerUser->__toString()." has no payment card for this Team");
//     $financeReceipt->Save();
// } elseif ($treasurer['stripePaymentMethodID']) {
//     $paymentIntent = \Stripe::getPaymentIntent($financeReceipt->getStripePaymentIntentID(),$treasurer['stripePaymentMethodID']);
// }
// echo $financeReceipt->getStatus();


// $rlt = \Database::Setup();Dump($rlt);
?>
<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body> -->
<?php


// $stripe = new \Stripe();
// $paymentCards = $stripe->PaymentCards($user->getStripeCustomerID());
// $paymentCard = $paymentCards->data[0];
// $paymentCardID = $paymentCard->id;
// try {
//     $paymentIntent = $stripe->CreatePaymentIntent($user->getStripeCustomerID(),$paymentCard->id,$amount);
//     echo "Payment {$paymentIntent->id} {$paymentIntent->status}";
//     // Dump($paymentIntent);
// } catch (Exception $e) {
//     echo $e->getMessage();
// }

// $stripe = new \Stripe();
// $paymentIntentID = "pi_1HDrWQFdHxSefRNnbGFBbLHo";
// $refund = $stripe->Refund($paymentIntentID,.95);
// if (is_string($refund)) exit($refund);
// echo "{$paymentIntent->id} is now {$paymentIntent->status}";

// $stripe = new \Stripe();
// $paymentIntentID = "pi_1HDrTWFdHxSefRNnIPAbSr42";
// $paymentIntent = $stripe->CancelPaymentIntent($paymentIntentID,"abandoned");
// if (is_string($paymentIntent)) exit($paymentIntent);
// echo "{$paymentIntent->id} is now {$paymentIntent->status}";

// $paymentIntentID = "pi_1HDrYNFdHxSefRNnhJSi00tr";
// $stripe = new \Stripe();
// $paymentIntent = $stripe->CapturePaymentIntent($paymentIntentID,20);
// if (is_string($paymentIntent)) exit($paymentIntent);
// echo "{$paymentIntent->id} is now {$paymentIntent->status}";

/**
 * Testing
 * 1. Create Stripe Customer ID
 * 2. Capture Card Details for off_session
 * 3. Raise Pre-Auth
 * 4. Capture
 * 5. Refund
 */
// $stripe = new \Stripe();
// $amount = 30.55;

// $userID = 69; # <EMAIL> 4242 4242 4242 4242
// $user = new \User($userID);
// $paymentIntent = $stripe->CreatePaymentIntent($user->getStripeCustomerID(),$amount);
// echo (is_string($paymentIntent)) ? $paymentIntent : "Payment {$paymentIntent->id} {$paymentIntent->status}"; echo "<br>";
// $paymentCards = $stripe->PaymentCards($user->getStripeCustomerID());
// $paymentCard = $paymentCards->data[0];
// $paymentIntent = $stripe->ProgressPaymentIntent($paymentIntent->id,$paymentCard->id);
// echo (is_string($paymentIntent)) ? $paymentIntent : "Payment {$paymentIntent->id} {$paymentIntent->status}"; echo "<br>";


// $userID = 70; # <EMAIL> 4000 0027 6000 3184
// $user = new \User($userID);
// $paymentIntent = $stripe->CreatePaymentIntent($user->getStripeCustomerID(),$amount);
// $paymentIntentID = "pi_1HE12rFdHxSefRNnwbtqtvcR";
// $paymentIntent = $stripe->getPaymentIntent($paymentIntentID);
// if ($paymentIntent->status == "succeeded" || $paymentIntent->status == "requires_capture") exit("Payment Intent $paymentIntentID is {$paymentIntent->status}");
// exit(Dump($paymentIntent));
// echo (is_string($paymentIntent)) ? $paymentIntent : "Payment {$paymentIntent->id} {$paymentIntent->status}"; echo "<br>";
// $paymentCards = $stripe->PaymentCards($user->getStripeCustomerID());
// $paymentCard = $paymentCards->data[0];
// $paymentIntent = $stripe->ProgressPaymentIntent($paymentIntent->id,$paymentCard->id);
// echo (is_string($paymentIntent)) ? $paymentIntent : "Payment {$paymentIntent->id} {$paymentIntent->status}"; echo "<br>";
// echo $paymentIntent->status."<br>";
?>    
    <!-- </body>
</html> -->

<!-- <script src="https://js.stripe.com/v3/"></script>
<?php #$stripe->paymentForm($paymentIntent);?> -->

<?php
/**
 * Allow Customer to store CC details
 * Allow Customer to Pay with 
 * - Existing Card
 * - New Card (with Save option)
 * Allow Customer to View and Remove stored cards
 */