<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            height: 100vh;
            width: 100vw;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .box1 {
            background-color: pink;
            height: 60vh;
            width: 60vw;
            position: relative;
        }

        .innerBox1 {
            position: absolute;
            background-color: blue;
            height: 30vh;
            width: 30vw;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
        }

        .innerBox1::after {
            content: '';
            position: absolute;
            background-color: red;
            height: 15vh;
            width: 15vw;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
        }
    </style>
</head>

<body>
    <div class="box1">
        <div class="innerBox1"></div>
    </div>
</body>

</html>