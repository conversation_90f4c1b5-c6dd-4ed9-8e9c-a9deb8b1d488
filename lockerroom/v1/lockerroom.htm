<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"> -->
    <script src="https://js.stripe.com/v3/"></script>
    <script src="https://unpkg.com/vue@3"></script>


    <link rel="apple-touch-icon" sizes="57x57"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16"
        href="https://leagues4you.co.uk/wp-content/themes/leagues4you1/icons/favicon-16x16.png">
    <title>Locker Room</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
        integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="stylesheet" href="lockerroom.css">
</head>

<body>
    <div id="app">

        <nav class="topNav">
            <a href="https://leagues4you.co.uk">
                <img src="logo_in_blue.png" alt="leagues4you" style="height:75px; margin-left: 1em;"></a>
        </nav>

        <header>
            <img class="bg_sm" src="img/lr_bg_square.png" alt="">
            <img class="bg_lg" src="img/lr_bg_rect.png" alt="">
        </header>

        <div class="messages">
            <p v-if="messages.success" class="message bg-dark-green text-white">{{ messages.success }}</p>
            <p v-if="messages.warning" class="message bg-dark-orange text-white">{{ messages.warning }}</p>
            <p v-if="messages.danger" class="message bg-dark-red text-white">{{ messages.danger }}</p>
            <p v-if="messages.info" class="message bg-light-blue">{{ messages.info }}</p>
            <div v-if="helpInfo.title" class="helpInfo bg-dark-pink">
                <h5>{{ helpInfo.title }}</h5>
                <p class="bg-dark-pink" v-for="para in helpInfo.text">{{ para }}</p>
                <button class="button button-sm button-dark-blue" @click="helpInfo = {}">Close</button>
            </div>
        </div>

        <div v-if="!jwt && screenID != 99" class="screens guestScreens">

            <!-- Home | Welcome -->
            <main v-if="!guestScreen" class="screen screen1">
                <div class="container">
                    <h1>hello.</h1>
                    <p style="color: #19112e; font-size: .9em; margin-top: 1em;">Welcome to the leagues4you lockerroom.
                    </p>
                    <p style="color: #19112e; font-size: .9em; margin-top: 1em;">Here
                        you'll find all
                        things to do with your teams.</p>
                    <p style="color: #19112e; font-size: .9em; margin-top: 1em;">
                        Log in or register to access.
                    </p>
                    <div id="pdf"></div>
                    <button class="button button-pink mr-2" style="margin-top: 1em;"
                        @click="guestScreen = 'login'">Login</button>
                    <button class="button button-dark-blue" @click="guestScreen = 'register'">Register</button>
                </div>
            </main>

            <!-- Home | Login -->
            <main v-if="guestScreen == 'login'" class="screen screen1">
                <div class="container">
                    <h1>log in.</h1>
                    <form style="display:flex; flex-direction: column; margin-top: 1em;">
                        <input type="email" id="auth.email" placeholder="Email" v-model="auth.email">
                        <input type="password" id="auth.password" placeholder="Password" v-model="auth.password">
                        <button @click.prevent="login" class="button button-pink mt-3"
                            style="align-self: flex-start;">Login</button>
                    </form>
                    <button class="button button-dark-blue mr-2" @click="guestScreen = 'reminder'">Password
                        Reminder</button>
                    <button class="button button-dark-blue" @click="guestScreen = null">Back</button>
                </div>
            </main>

            <!-- Home | Register -->
            <main v-if="guestScreen == 'register'" class="screen screen1">
                <div class="container">
                    <h1>register.</h1>
                    <p>Enter your email, look out for an activation code.</p>
                    <form>
                        <label for="register.email">Email</label>
                        <input type="email" id="register.email" v-model="register.email" placeholder="Email Address">
                        <button class="button button-pink mt-2" @click.prevent="registration">Register</button>
                    </form>
                    <button class="button button-dark-blue" @click="guestScreen = 'reminder'">Password Reminder</button>
                </div>
            </main>

            <!-- Home | Activate -->
            <main v-if="guestScreen == 'activate'" class="screen screen1">
                <div class="container">
                    <h1>activate.</h1>
                    <p>Enter your email, look out for an activation code.</p>
                    <form>
                        <label for="register.email">Email</label>
                        <input type="email" id="register.email" v-model="register.email" placeholder="Email Address">
                        <label for="register.code">Code</label>
                        <input type="text" id="register.code" v-model="register.code" placeholder="Activation Code">
                        <button class="button button-pink mt-2" @click.prevent="activation">Activate</button>
                    </form>
                    <button class="button button-dark-blue" @click="guestScreen = null">Back</button>
                </div>
            </main>

            <!-- Home | Reminder -->
            <main v-if="guestScreen == 'reminder'" class="screen screen1">
                <div class="container">
                    <h1>reminder.</h1>
                    <p>Enter your email address, look out for an reminder code in your inbox.</p>
                    <form>
                        <label for="register.email">Email</label>
                        <input type="email" id="register.email" v-model="register.email" placeholder="Email Address">

                        <button class="button button-pink mt-2" @click.prevent="reminder">Send Reminder</button>
                    </form>
                    <button class="button button-dark-blue" @click="guestScreen = null">Back</button>
                </div>
            </main>

        </div>

        <div v-else-if="jwt && screenID != 99" class="screens userScreens">

            <!-- Profile -->
            <main v-if="screenID == 2" class="screen screen2">
                <div class="container">
                    <h1>profile.</h1>
                    <form class="profile-details">
                        <label for="profile.firstname">First Name</label>
                        <input class="profile-group-input" type="text" id="profile.firstname" placeholder="First Name"
                            v-model="profile.firstname">
                        <label for="profile.lastname">Last Name</label>
                        <input class="profile-group-input" type="text" id="profile.lastname" placeholder="Last Name"
                            v-model="profile.lastname">

                        <label for="profile.email">Email</label>
                        <input class="profile-group-input profile-group-input-email" type="email" id="profile.email"
                            placeholder="Email" v-model="profile.email">

                        <label for="profile.mobile">Mobile</label>
                        <input class="profile-group-input" type="tel" id="profile.mobile" placeholder="Mobile"
                            v-model="profile.mobile">

                        <label for="profile.line1">Address</label>
                        <input class="profile-group-input" type="text" id="profile.line1" placeholder="Address Line 1"
                            v-model="profile.line1">
                        <!-- <label for="profile.town">Town</label>
                        <input class="profile-group-input" type="text" id="profile.town" placeholder="Town"
                            v-model="profile.town"> -->
                        <label for="profile.postcode">Postcode</label>
                        <input class="profile-group-input" type="text" id="profile.postcode" placeholder="Postcode"
                            v-model="profile.postcode">
                        <label for="profile.dob">Date of Birth</label>
                        <input class="profile-group-input" type="date" id="profile.dob" placeholder="Date of Birth"
                            v-model="profile.dob">

                    </form>
                    <button class="button button-pink" @click.prevent="saveProfile">Save</button>
                    <button class="button button-dark-blue mx-1" @click="screenID = 7">Change Password</button>
                    <button class="button button-dark-blue" @click="logout">Logout</button>
                </div>
            </main>

            <!-- Password Change -->
            <main v-else-if="screenID == 7" class="screen screen2">
                <div class="container">
                    <h1>change password.</h1>
                    <label for="passChange.password1">Enter Password</label>
                    <input type="password" id="passChange.password1" v-model="passChange.password1"
                        style="border: thin solid #eee; border-radius: .25em;" class="my-2"
                        placeholder="Enter Password">
                    <label for="passChange.password2">Confirm Password</label>
                    <input type="password" id="passChange.password2" v-model="passChange.password2"
                        style="border: thin solid #eee; border-radius: .25em;" placeholder="Confirm Password">
                    <button class="button button-pink mt-2" @click.prevent="updatepassword">Update Password</button>
            </main>

            <!-- Documents -->
            <main v-else-if="screenID == 3" class="screen screen3">
                <div class="container">
                    <h1 style="margin-bottom: .5em;">leagues4you library.</h1>
                    <div class="document-group" style="display: flex; flex-direction: column;">
                        <a v-for="l in library" class="document-item" :href="l.slug" target="_blank"><i
                                class="fa-solid fa-file" style="margin-right: 1em;;"></i>{{
                            l.name }}</a>
                    </div>
                </div>
            </main>

            <!-- Teams -->
            <main v-else-if="screenID == 4" class="screen screen4">
                <div class="container">

                    <div v-if="profile.teams.length > 0" class="teams-list">
                        <h1>my teams.</h1>
                        <div class="teamSelection"
                            style="display:flex; align-items: center; justify-content: space-between;">
                            <select id="teamID" v-model="teamID" class="form-control">
                                <option v-for="t in profile.teams" :value="t.id">{{ t.name }}</option>
                            </select>
                            <div v-if="teamID && !teamManager" class="followUnfollow">
                                <button class="button button-blue-outer" @click="unfollow(teamID)">
                                    <span v-if="isFollowing && !teamManager">Unfollow</span>
                                    <span v-else-if="!isFollowing">Follow</span>
                                </button>
                            </div>
                        </div>

                        <div v-if="teamID" class="teamData mt-2">
                            <div v-if="teamLoading" class="team-loading">
                                <p>Loading...</p>
                            </div>
                            <div v-else class="teamData-header" style="display: flex; align-items: center;">
                                <button class="button teamData-view-option" @click="divisionViewOption = 'tables'"
                                    :class="{ selected : divisionViewOption == 'tables'}">Tables</button>

                                <button class="button teamData-view-option mx-1" @click="divisionViewOption = 'results'"
                                    :class="{ selected : divisionViewOption == 'results'}">Results</button>

                                <button class="button teamData-view-option mr-1"
                                    @click="divisionViewOption = 'fixtures'"
                                    :class="{ selected : divisionViewOption == 'fixtures'}">Fixtures</button>

                                <select id="seasonID" v-model="seasonID" class="form-control">
                                    <option v-for="s in selectedTeam.seasons" :value="s.id">{{ s.name }}</option>
                                </select>
                            </div>

                            <div v-if="seasonID" class="teamData-main">

                                <div class="divisionSelection">
                                    <button class="button button-sm teamData-division-btn mr-1"
                                        v-for="division in season.divisions" :key="division.id"
                                        :class="{ selected : division.id==divisionID}"
                                        @click="setDivision(division.id)">{{
                                        division.name
                                        }}</button>
                                    <label class="switch">
                                        <input type="checkbox" @click="teamViewAll = !teamViewAll">
                                        <span class="slider round"></span>
                                    </label>
                                    <span style="font-size:.5em; margin-left: .25em;">Show All</span>
                                </div>

                                <div v-if="divisionViewOption == 'results'" class="teamData-main-results">
                                    <table class="resultsTable">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th class="text-center">Home</th>
                                                <th class="text-center">v</th>
                                                <th class="text-center">Away</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="r in filteredResults(selectedDivision.results)">
                                                <td style="font-size: .75em;">{{ r.fixtureDate }}</td>
                                                <td class="text-center" style="font-size: .75em;">{{ r.homeTeam
                                                    }}<br><span v-if="r.homePom" style="font-size: .75em;">
                                                        ({{ r.homePom }})
                                                    </span><span v-else>&nbsp;</span></td>
                                                <td class="text-center">{{ parseInt(r.homeScore) }} &minus; {{
                                                    parseInt(r.awayScore) }}</td>
                                                <td class="text-center" style="font-size: .75em;">{{ r.awayTeam
                                                    }}<br><span v-if="r.awayPom" style="font-size: .75em;">({{ r.awayPom
                                                        }})</span><span v-else>&nbsp;</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div v-if="divisionViewOption == 'tables'" class="teamData-main-tables">
                                    <table class="divisionTable mt-2">
                                        <thead>
                                            <tr>
                                                <th>Team</th>
                                                <th class="text-centre">P</th>
                                                <th class="text-centre">W</th>
                                                <th class="text-centre">D</th>
                                                <th class="text-centre">L</th>
                                                <th class="text-centre">F</th>
                                                <th class="text-centre">A</th>
                                                <th class="text-centre">GD</th>
                                                <th class="text-centre">Pts</th>
                                                <th class="text-centre">BP</th>
                                                <th class="text-centre">Tot</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="t in selectedDivision.table">
                                                <td>{{ t.team }}</td>
                                                <td class="text-centre">{{ t.played }}</td>
                                                <td class="text-centre">{{ t.won }}</td>
                                                <td class="text-centre">{{ t.drawn }}</td>
                                                <td class="text-centre">{{ t.lost }}</td>
                                                <td class="text-centre">{{ t.for }}</td>
                                                <td class="text-centre">{{ t.against }}</td>
                                                <td class="text-centre">{{ t.gd }}</td>
                                                <td class="text-centre">{{ t.points }}</td>
                                                <td class="text-centre">{{ t.bp }}</td>
                                                <td class="text-centre">{{ t.total }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div v-if="divisionViewOption == 'fixtures'" class="teamData-main-fixtures"
                                    style="font-size: .8em;;">
                                    <table class="resultsTable">
                                        <tbody v-for="f in filteredFixtures(selectedDivision.schedule)">
                                            <tr>
                                                <td><b>{{ formatDate(f.startDate) }}</b></td>
                                                <td>
                                                    <b>{{ f.home }} v {{ f.away }}</b>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>{{ f.startTime.substring(0, 5) }}</td>
                                                <td>
                                                    {{ f.venue }}, {{ f.pitchCourt }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <hr class="mt-2">
                    </div>

                    <!-- Team Search -->
                    <div class="teams-search mt-2">
                        <p><span v-if="profile.teams.length < 1">You aren't following or managing any teams as yet.
                            </span></p>
                        <input type="search" id="searchTeamText" class="form-control" v-model="searchTeamText"
                            @keyup="searchTeams" placeholder="Search for a team...">
                        <div v-if="searchedTeams.length > 0" class="teams-followed" style="font-size: .8em;">
                            <div class="search-team-result" v-for="searchedTeam in searchedTeams"
                                style="display: flex; justify-content: space-between; align-items: center; margin: .5em 0;">
                                <div class="search-team-result-data">
                                    <b>
                                        {{ searchedTeam.teamName }}
                                    </b><br>
                                    {{ searchedTeam.leagueName }}
                                </div>
                                <div class="search-team-result-btn">
                                    <button class="button button-sm button-pink"
                                        @click="viewTeam(searchedTeam.teamID)"><i class="fa-solid fa-eye"></i></button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </main>

            <!-- Admin -->
            <main v-else-if="screenID == 5" class="screen screen5">
                <div class="container" style="font-size:.8em;">

                    <div v-if="profile.invites && profile.invites.length > 0" class="treasurer-invitations">
                        <h1>treasurer invitation.</h1>
                        <div v-for="invite in profile.invites" class="treasurer-invite">
                            <p class="treasurer-invite-text">
                                You have been invited by {{ invite.inviter.firstname }} {{ invite.inviter.lastname }}
                                ({{
                                invite.inviter.email }}) to be the Treasurer for {{ invite.team.name }}
                            </p>
                            <div class="treasurer-invite-buttons">
                                <button class="button button-pink treasurer-invite-accept-button"
                                    @click="acceptInvite(invite.id)">Accept</button>
                                <button class="button button-dark-grey treasurer-invite-decline-button"
                                    @click="declineInvite(invite.id)">Decline</button>
                            </div>
                        </div>
                    </div>

                    <div v-else class="team-management">
                        <h1>team management.</h1>
                        <div class="teamSelections" style="display:flex;">
                            <select id="adminTeamID" v-model="adminTeamID">
                                <option v-for="team in managedTeams" :value="team.id">{{ team.name }} | {{
                                    team.league.name
                                    }}
                                </option>
                            </select>
                        </div>

                        <div v-if="adminTeam && adminTeam.id" class="adminTeamDetail">

                            <div class="staffDetails">

                                <label for="">Captain <i class="fa-solid fa-circle-info"
                                        @click="showHelp('captain')"></i></label>
                                <div class="details details-captain">

                                    <p v-if="adminTeam.captain">
                                        {{ adminTeam.captain.name }}
                                    </p>

                                    <button v-if="financials.canReenter"
                                        class="button button-md admin-reenter-season-button button-dark-blue"
                                        @click="teamReenter">Enter Next
                                        Season</button>

                                </div>

                            </div>

                            <div class="staffDetails">
                                <label for="">Treasurer <i class="fa-solid fa-circle-info"
                                        @click="showHelp('treasurer')"></i>
                                </label>
                                <div v-if="adminTeam.treasurer" class="details details-treasurer">
                                    <p>{{ adminTeam.treasurer.name }}</p>
                                    <div v-if="adminTeam.treasurer.id != adminTeam.captain.id"
                                        class="treasurerRemoveButtons">
                                        <button v-if="adminTeam.treasurer.id == profile.id"
                                            class="button button-sm button-blue-outer"
                                            @click="removeTreasurer">Resign</button>
                                        <button v-else class="button button-sm button-blue-outer"
                                            @click="removeTreasurer">Remove</button>
                                    </div>
                                    <div v-if="adminTeam.treasurer.id == adminTeam.captain.id"
                                        class="details details-treasurer">
                                        <button class="button button-sm button-pink" @click="screenID = 10"><i
                                                class="fa-solid fa-user"></i>
                                            Invite</button>
                                    </div>
                                </div>

                            </div>

                            <div class="paymentDetails">
                                <label for="">Payment <i class="fa-solid fa-circle-info"
                                        @click="showHelp('paymentCard')" style="margin-bottom:.5em;"></i>
                                </label>
                                <div v-if="financials.card && financials.card.paymentMethodID"
                                    class="details details-payment">
                                    <p>{{ financials.card.name }}</p>
                                    <button v-if="adminTeam.treasurer.id == profile.id"
                                        class="button button-sm button-red-outer"
                                        @click="cancelCard(financials.card.paymentMethodID)">
                                        <span>Remove</span>
                                    </button>
                                </div>
                                <div v-else class="details details-payment">
                                    <p>&nbsp;</p>
                                    <button v-if="adminTeam.treasurer.id == profile.id"
                                        class="button button-sm button-pink" @click="setNewTeamPaymentCard"><i
                                            class="far fa-credit-card"></i> Add Card</button>
                                </div>
                            </div>

                            <div class="paymentDocuments">
                                <div class="paymentOptions" v-if="financials.card && financials.card.paymentMethodID">
                                    <div class="paymentOption paymentOption-weekly bg-dark-pink selected"
                                        @click="togglePayOption()">
                                        <p class="paymentOption-text">Pay Weekly</p>
                                        <p class="paymentOption-price">&pound;{{ adminTeam.season.fixtureCharge }}</p>
                                        <p v-if="!financials.paymentOption" class="paymentOption-selected"><i
                                                class="fa-solid fa-circle-check"></i></p>
                                    </div>
                                    <div class="paymentOption paymentOption-full bg-dark-blue"
                                        @click="togglePayOption(1)">
                                        <p class="">Pay in Full</p>
                                        <p class="">&pound;{{ financials.fullBalancePayment.total }}</p>
                                        <p v-if="financials.paymentOption == 1" class="paymentOption-selected"><i
                                                class="fa-solid fa-circle-check"></i></p>
                                    </div>
                                </div>
                                <div style="display: flex; flex-direction: column;">
                                    <button class="button button-sm button-pink"><i class="fa-solid fa-file-pdf"></i>
                                        Invoice</button>
                                    <button class="button button-sm button-dark-blue" @click="screenID = 11"><i
                                            class="fa-solid fa-file-pdf"></i>
                                        Statement</button>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </main>

            <!-- Team Statement -->
            <main v-else-if="screenID == 11" class="screen screen11">
                <div class="container">
                    <h1>{{ adminTeam.name.toLowerCase()}} statement.</h1>
                    <p>Current Balance: <b>{{ statement.total.toFixed(2) }}</b></p>
                    <table class="statementTable" v-if="statement.items.length > 0">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th class="text-right">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="statementItem in statement.items">
                                <td>{{ formatDate1(statementItem.date) }}</td>
                                <td>{{ statementItem.description }}</td>
                                <td class="text-right">{{ statementItem.amount }}</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="2">Total Balance</th>
                                <th class="text-right">{{ statement.total.toFixed(2) }}</th>
                            </tr>
                        </tfoot>
                    </table>
                    <p v-else>Nothing to show at the moment.</p>
                </div>
            </main>

            <!-- New Team -->
            <main v-else-if="screenID == 6" class="screen screen6">

                <!-- League -->
                <div v-if="!newTeam.stage || newTeam.stage == 1" class="add-a-team add-a-team-leagueid"
                    style="padding: 1em;">
                    <h1 style="margin-bottom: 1em;">first, let's find your league.</h1>
                    <div>
                        <div>
                            <input type="search" id="leagueSearchName" v-model="leagueSearchName" @keyup="searchLeagues"
                                placeholder="search by league name">
                        </div>
                        <p style="margin:.5em;">or</p>
                        <div>
                            <input type="text" id="search.postcode" v-model="search.postcode"
                                placeholder="your postcode">
                            <button @click="leagueSearchPostcode" class="button button-dark-blue mt-4">
                                <span>Find</span>
                            </button>
                        </div>
                    </div>

                    <div class="search-results">
                        <div class="local-venues">
                            <div v-for="venue in search.venues" :key="venue.id" class="local-venue">
                                <div v-if="venue.leagues.length > 0" class="league-search-result"
                                    style="border-bottom: thin solid #ddd; margin: .25em 0; padding-bottom: .25em;">
                                    <div class="venueLeagues">
                                        <div v-for="league in venue.leagues" class="venueLeague">
                                            <div class="veueLeague-name" style="font-size: .8em;">
                                                <b>{{ league.name }}</b>
                                            </div>
                                            <div class="veueLeague-name" style="font-size: .8em;">
                                                {{ venue.name }}
                                            </div>
                                            <div class="venue-league-select">
                                                <button class="button button-sm button-pink"
                                                    @click.prevent="newteamLeagueChoose(venue,league)">Choose</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Name -->
                <div v-if="newTeam.stage == 2" class="add-a-team add-a-team-name" style="padding: 1em;">
                    <h1>great, now decide on your team name.</h1>
                    <p style="font-size: .8em; color: #666; margin:1.5em 0;">{{ newTeam.league.name }}<br>{{
                        newTeam.venue.name }}</p>
                    <input type="text" id="newTeam.name" v-model="newTeam.name" placeholder="Enter a Team Name"
                        @keyup="teamNameCheck" style="margin:1.5em 0;">
                    <div class="buttons">
                        <button class="button button-dark-blue" @click.prevent="newTeam.stage--">Back</button>
                        <button class="button button-pink ml-1" :disabled="!newTeam.nameValid"
                            @click.prevent="newTeam.stage++">Next</button>
                    </div>
                    <p v-if="newTeam.message" class="text-red">{{ newTeam.message }}</p>
                </div>

                <!-- Terms -->
                <div v-if="newTeam.stage == 3" class="add-a-team add-a-team-terms" style="padding: 1em;">
                    <h1>nearly there, just review our t's&amp;c's.</h1>
                    <canvas id="terms-and-conditions" style="border: 1px solid black; direction: ltr;"></canvas>
                    <div class="pagination-terms">
                        <div class="prev">
                            <button class="button button-sm button-pink" v-if="pdfPageNum>1" @click="pdfPageNum--;">&lt;
                                &minus; Prev</button>
                        </div>
                        <div class="next">
                            <button class="button button-sm button-pink" v-if="pdfPageNum < pdfPages"
                                @click="pdfPageNum++;">Next &minus;&gt;</button>
                        </div>
                    </div>
                    <p style="font-size:.8em;">To add {{newTeam.name}} into {{ newTeam.league.name }} at {{
                        newTeam.venue.name }} press accept below.
                    </p>
                    <div class="buttons mt-2">
                        <button class="button button-dark-blue" @click.prevent="goto('teamname')">Back</button>
                        <button class="button button-pink ml-1" @click.prevent="goto('payment')">Accept</button>
                    </div>
                </div>

                <!-- Payment -->
                <div v-if="newTeam.stage == 4" class="add-a-team add-a-team-payment" style="padding: 1em;">
                    <h1>set up payments.</h1>
                    <p>To accept a team into a league, we require a valid form of payment.</p>
                    <p>At any time later, you can change your payment details or appoint another person to be treasurer.
                    </p>
                    <p>No payments are taken until the season commences.</p>
                    <button class="button button-dark-blue mt-4" @click.prevent="goto('terms')">Back</button>
                    <button class="button button-dark-grey mx-2" @click.prevent="skipPayment">Skip</button>
                    <form v-show="this.newTeam.addPayment === true" id="payment-form">
                        <div id="payment-element">
                            <!--Stripe.js injects the Payment Element-->
                        </div>
                        <button class="button button-pink" id="submit" @click.prevent="processPaymentCard">Save
                            Card</button>
                        <div v-if="stripeError" id="error-message" class="hidden">{{ stripeError }}</div>
                    </form>
                    <button v-show="this.newTeam.addPayment !== true" class="button button-pink"
                        @click.prevent="addPayment">Add A Card</button>
                </div>

                <!-- Review -->
                <div v-if="newTeam.stage == 5" class="add-a-team add-a-team-review" style="padding: 1em;">
                    <h1>lets, double check we've got everything right.
                    </h1>
                    <p style="font-size:.8em; margin: 1em 0;">You are calling your team {{ newTeam.name }} and you've
                        entered {{
                        newTeam.league.name }}. You'll
                        play at {{ newTeam.venue.name }} on {{ newTeam.league.day }}s and the
                        league starts on {{ formatDate(newTeam.league.launch) }}. The weekly fixture cost is &pound;{{
                        newTeam.league.fixtureCharge }}.
                        Your payment card <span v-if="newTeam.payment_method">has been added.</span>
                        <span v-else>is to be completed later.</span>
                    </p>
                    <button class="button button-dark-blue" @click.prevent="newTeam.stage--">Back</button>
                    <button class="button button-pink ml-2" @click.prevent="submitTeam">Submit</button>
                </div>

                <!-- Complete -->
                <div v-if="newTeam.stage == 6" class="add-a-team add-a-team-review" style="padding: 1em;">
                    <h1>wahoo.</h1>
                    <p>We'll be sending you an email super soon confirming all your details.</p>
                    <p>In the meantime you'll find your
                        new team in the teams section below. Head to the admin section to manage payments or to the docs
                        to
                        see some info about the leagues.
                    </p>
                    <p>We wish you all the best for the season ahead!
                    </p>
                    <button class="button button-pink mt-4" @click="teamCompleted">Finish</button>
                </div>

            </main>

            <!-- New Payment Card -->
            <main v-else-if="screenID == 8" class="screen screen8">
                <div class="container" style="font-size:.8em;">
                    <h1>new payment card.</h1>
                    <img v-show="waitNewCard" src="img/spinner.gif" alt="">
                    <form id="new-payment-card-form">
                        <div id="new-payment-card-element">
                            <!-- elements -->
                        </div>
                        <button class="button button-pink" id="submit" @click.prevent="processNewCard">Add Card</button>
                        <div v-if="stripeError" id="error-message" class="hidden">{{ stripeError }}</div>
                    </form>
                </div>
            </main>

            <!-- Re-enter a Team -->
            <main v-else-if="screenID == 9" class="screen screen9">
                <div class="container" style="font-size:.8em;">
                    <div v-if="!reenterStage" class="re-enterStage1">
                        <h1>re-enter new season.</h1>
                        <p>Please double-check this information</p>
                        <p>
                            You are entering <b>{{ financials.canReenter.team.name }}</b> into <b>{{
                                financials.canReenter.season.name }}</b> of
                            <b>{{
                                financials.canReenter.league.name }}</b> playing at <b>{{
                                financials.canReenter.venue.name
                                }}</b>
                        </p>
                        <p>You'll play on <b>{{ financials.canReenter.day }}s</b> starting on <b>{{
                                formatDate(financials.canReenter.launchDate) }}</b>. The weekly fixture cost will be
                            <b>&pound;{{
                                financials.canReenter.fixtureCharge }}.</b>
                        </p>
                        <p>
                            Your current payment card details will be carried forward although you can amend these
                            anytime
                            up to
                            the day prior to the first fixture.</span>
                        </p>
                        <p>If this all looks good, please select <b>Next</b> or alternatively, please contact the League
                            Coordinator <b>{{ financials.canReenter.coordinator.firstname }} {{
                                financials.canReenter.coordinator.lastname}}</b> via email at <b>{{
                                financials.canReenter.coordinator.email }}</b></p>
                        <div class="reenter-team-buttons mt-2">
                            <button class="button button-dark-grey mr-1" @click="screenID = 5">Cancel</button>
                            <button class="button button-pink" @click="reenterStage =1">Next</button>
                        </div>
                    </div>
                    <div v-else-if="reenterStage == 1" class="re-enterStage1">
                        <h2>terms and conditions</h2>
                        <canvas id="terms-and-conditions" style="border: 1px solid black; direction: ltr;"></canvas>
                        <div class="pagination-terms">
                            <div class="prev">
                                <button class="button button-sm button-pink" v-if="pdfPageNum>1"
                                    @click="pdfPageNum--;">&lt;
                                    &minus; Prev</button>
                            </div>
                            <div class="next">
                                <button class="button button-sm button-pink" v-if="pdfPageNum < pdfPages"
                                    @click="pdfPageNum++;">Next &minus;&gt;</button>
                            </div>
                        </div>
                        <button class="button button-pink mt-4" @click="reenterConfirmed">Accept</button>
                    </div>
                    <div v-else class="re-enterStage2">
                        <h2>wahoo.</h2>
                        <p>We'll be sending you an email super soon confirming all the details.</p>
                        <p>We wish you all the best for the season ahead!</p>
                        <button class="button button-pink mt-4" @click="screenID = 4">Finish</button>
                    </div>
            </main>

            <!-- New Treasurer -->
            <main v-else-if="screenID == 10" class="screen screen10">
                <div class="container">
                    <h1>invite a treasurer.</h1>
                    <p>To invite someone to participate as a team treasurer for <b>{{ adminTeam.name }}</b> enter
                        their
                        email address here to begin</p>
                    <input type="email" v-model="newTreasurer.email" placeholder="<EMAIL>"
                        class="treasurerEmail mt-2 max-600 w-90">
                    <button class="button button-sm button-pink" @click="sendTreasurerInvite">Send Invite</button>
                </div>
            </main>

            <!-- Home | Dashboard -->
            <main v-else-if="!screenID" class="screen screen1">
                <div class="container" style="height: 60vh;">
                    <h1>hello <span v-if="this.profile.firstname">{{ this.profile.firstname.toLowerCase()
                            }}.</span></h1>
                    <div class="enter-league-team" style="margin-top: 20vh;">
                        <h2 class="enter-league-team-text">
                            enter a new team?
                        </h2>
                        <button class="enter-league-team-button" @click="screenID = 6">click here</button>
                    </div>
                </div>
            </main>

        </div>

        <div v-else class="screens generalScreens">
            <main v-if="screenID == 99" class="screen screen2">
                <div class="container-fluid">
                    <img style="width: 100%" src="img/spinner.gif" alt="">
                </div>
            </main>
        </div>

        <!-- Footer -->
        <footer v-if="jwt">
            <nav class="bottom-nav">
                <button class="bottom-nav-link" @click="screenID = 2" :class="{ active: screenID == 2 }">
                    <i class="far fa-user"></i>
                    <span class="legend">Profile</span>
                </button>

                <button class="bottom-nav-link" @click="screenID = 3" :class="{ active: screenID == 3 }">
                    <i class="far fa-file-alt"></i>
                    <span class="legend">Docs</span>
                </button>

                <button class="bottom-nav-link" @click="screenID = null"
                    :class="{ active: (!screenID || screenID ==1) }">
                    <i class="fas fa-home"></i>
                    <span class="legend">Home</span>
                </button>

                <button class="bottom-nav-link" @click="screenID = 4" :class="{ active: screenID == 4 }">
                    <i class="fas fa-users-cog"></i>
                    <span class="legend">Teams</span>
                </button>

                <button
                    v-if="(managedTeams && managedTeams.length > 0) || (profile.invites && profile.invites.length > 0)"
                    class="admin-nav bottom-nav-link" @click="screenID = 5" :class="{ active: screenID == 5 }">
                    <div v-if="hasAlerts" class="admin-alert">!</div>
                    <i class="far fa-credit-card"></i>
                    <span class="legend">Admin</span>
                </button>
            </nav>
        </footer>

    </div>

    <script src="pdfjs/pdf.js"></script>
    <script>
        Vue.createApp({
            data() {
                return {
                    financials: {},
                    pdfjsLib: null,
                    pdfPageNum: 1,
                    pdfPages: null,
                    adminTeamID: null,
                    adminTeam: {},
                    reenterStage: null,
                    statement: [],
                    alerts: [],
                    seasonPayMode: 'Pay Per Fixture',
                    newTreasurer: {},
                    helpInfo: {},
                    teamLoading: true,
                    waitNewCard: false,
                    leagueSearchName: null,
                    library: [],
                    guestScreen: null,
                    passChange: {},
                    register: {},
                    auth: {},
                    textareaScrollheight: null,
                    screenID: null,
                    welcomeMessage: null,
                    newteamLeagueSearchText: null,
                    setupIntent: {},
                    leagueID: null,
                    teamName: null,
                    treasurerApproved: false,
                    treasurerFirstname: null,
                    treasurerLastname: null,
                    treasurerEmail: null,
                    treasurerMobile: null,
                    teamApproved: false,
                    teamTermsScrolled: true,
                    treasurer: {},
                    teams: [],
                    teamView: {
                        season: {},
                        division: {},
                    },
                    teamID: null,
                    seasonID: null,
                    season: {},
                    divisionID: null,
                    selectedDivision: {},
                    divisionViewOption: null,
                    selectedTeam: {},
                    selectedSeasons: [],
                    searchTeamText: null,
                    searchedTeams: [],
                    cards: [],
                    teamProcess: 1,
                    teamError: null,
                    name: null,
                    newTeam: {},
                    newCard: {},
                    teamView: {},
                    setupIntent: {},
                    leagues: [],
                    stripe: null,
                    jwt: null,
                    profile: {},
                    messages: {
                        success: null,
                        warning: null,
                        danger: null,
                        info: null
                    },
                    clearMessages: null,
                    search: {
                        radius: 30,
                        postcode: null,
                        postcodeLatLng: null,
                        error: null,
                        venues: []
                    },
                    stripe: {},
                    stripeError: null,
                    stripeElements: {},
                    isTeamAdmin: false,
                    teamViewAll: false
                }
            },
            methods: {
                acceptInvite(inviteID) {
                    console.log("Attempting to Accept");
                    let url = "https://user.v2.api.leagues4you.co.uk/accept-treasurer-invite/" + inviteID;
                    console.log("URL", url);
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                this.setMessage("Invitation Accepted", "success");
                                this.removeInvite(inviteID);
                                this.adminTeam = json.success;
                                if (this.profile.teams && this.profile.teams.length > 0) {
                                    for (let t in this.profile.teams) {
                                        if (this.profile.teams[t].id == json.success.id) {
                                            this.profile.teams[t] = this.adminTeam = json.success;
                                            return;
                                        }
                                    }
                                } else {
                                    this.profile.teams.push(json.success);
                                    this.adminTeamID = json.success.id;
                                }
                            } else this.setMessage(json.error, "danger");
                        })
                },
                activation() {
                    let url = "https://public.v2.api.leagues4you.co.uk/activation";
                    fetch(url, {
                        method: 'post',
                        body: JSON.stringify(this.register)
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                var cookieTxt = "jwt=" + json.success.token + "; path=" + json.success.cookie.path + "; domain=" + new URL(window.location).hostname + "; expires=" + json.success.cookie.expires;
                                document.cookie = cookieTxt;
                                this.jwt = json.success.token;
                                this.screenID = 7;
                            } else {
                                console.error(json.error);
                            }
                        })
                },
                addPayment() {
                    this.newTeam.addPayment = true;
                    this.addPaymentCard();
                },
                addPaymentCard() {
                    // console.log("Add Payment Card Called");
                    let pe = document.querySelector("#payment-element");
                    this.stripe = Stripe(this.newTeam.setupIntent.pk);
                    this.stripeElements = this.stripe.elements(
                        { clientSecret: this.newTeam.setupIntent.client_secret }
                    );
                    let paymentElement = this.stripeElements.create('payment');
                    paymentElement.mount('#payment-element');
                    // console.dir(this.newTeam);
                },
                addNewPaymentCard() {
                    let url = "https://user.v2.api.leagues4you.co.uk/setupIntent/";
                    fetch(url, {
                        method: 'get',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            // console.log("SetupIntent Result", json);
                            let ele = document.querySelector("#new-payment-card-element");
                            this.stripe = Stripe(json.success.pk);
                            this.stripeElements = this.stripe.elements(
                                { clientSecret: json.success.client_secret }
                            );
                            let paymentElement = this.stripeElements.create('payment');
                            paymentElement.mount('#new-payment-card-element');
                        })
                        .then(() => this.waitNewCard = false)
                },
                alertChecks() {
                    return;
                    this.alerts = [];
                    if ((!this.managedTeams || this.managedTeams.length == 0) && (!this.profile.invites || this.profile.invites.length == 0)) return;
                    for (let t in this.managedTeams) {
                        if (this.managedTeams[t].card.length == 0) this.alerts.push('Payment Card required');
                    }
                    if (this.profile.invites && this.profile.invites.length > 0) this.alerts.push('Invitations require a response');
                },
                cancelCard(stripePaymentMethodID) {
                    if (!stripePaymentMethodID) return;
                    let url = "https://user.v2.api.leagues4you.co.uk/cancel-card/" + stripePaymentMethodID;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        })
                    })
                        .then(response => response.json())
                        .then(json => this.fetchProfile())
                        .then(() => {
                            let teamID = this.adminTeamID;
                            this.adminTeam.card = {};
                            this.adminTeamID = null;
                            this.adminTeamID = teamID;
                        })
                },
                declineInvite(inviteID) {
                    console.log("Attempting to Decline");
                    let url = "https://user.v2.api.leagues4you.co.uk/decline-treasurer-invite/" + inviteID;
                    console.log("URL", url);
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                this.setMessage("Invitation Declned", "success");
                                this.removeInvite(inviteID);
                            } else this.setMessage(json.error, "danger");
                        })
                        .then(() => this.fetchProfile())
                },
                fetchLibrary() {
                    let url = "https://user.v2.api.leagues4you.co.uk/library";
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt
                        }),
                    })
                        .then(response => response.json())
                        .then(json => this.library = json.success)
                },
                fetchProfile() {
                    // let destinationScreen = this.screenID;
                    // this.screenID = 99;
                    this.adminTeamID = null;
                    if (!this.jwt) return;
                    let url = "https://user.v2.api.leagues4you.co.uk/profile";
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                this.profile = json.success;
                                return;
                                if (this.managedTeams) {
                                    this.isTeamAdmin = true;
                                    if (this.managedTeams.length == 1) {
                                        this.adminTeamID = this.teamID = this.managedTeams[0].id;
                                    }
                                    this.alertChecks();
                                }
                            } else console.error(json.error);
                        })
                        .then(() => {
                            if (this.screenID == 99) this.screenID = null;
                            // this.screenID = destinationScreen;
                            if (this.profileCheck() !== true) {
                                this.setMessage("To continue with the Locker Room. Please fully complete your profile", "warning");
                                // this.profileCheck().forEach((check) => {
                                //     this.setMessage(check, "warning");
                                // })
                                this.screenID = 2;
                            }
                        })
                        .catch(() => {
                            this.jwt = null;
                            this.screenID = null;
                            this.setMessage("We could not load your user information. Please try again or contact us for further assistance", "danger");
                        })
                },
                fetchSetupIntent() {
                    let url = "https://user.v2.api.leagues4you.co.uk/setupIntent/";
                    fetch(url, {
                        method: 'get',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            // console.log("New SI", json.success);
                            this.newCard.setupIntent = this.newTeam.setupIntent = json.success
                        });
                },
                fetchTeam() {
                    this.team = {}; this.divisionID = null; this.divisionViewOption = null;
                    if (!this.teamID) return;
                    let url = "https://public.v2.api.leagues4you.co.uk/team/" + this.teamID;
                    this.teamLoading = true;
                    fetch(url)
                        .then(response => response.json())
                        .then(json => {
                            this.selectedTeam = json.success;
                            if (this.selectedTeam.seasons) {
                                if (this.selectedTeam.seasons.length == 1) {
                                    this.seasonID = this.selectedTeam.seasons[0].id;
                                } else {
                                    for (let s in this.selectedTeam.seasons) {
                                        if (this.selectedTeam.seasons[s].status.isCurrent === true) this.seasonID = this.selectedTeam.seasons[s].id;
                                    }
                                }
                            }
                        })
                        .then(() => {
                            let found = false;
                            for (let t in this.profile.teams) {
                                if (this.profile.teams[t].id == this.selectedTeam.id) found = true;
                            }
                            if (found === false) {
                                console.log("Need to add to Profile teams array");
                                let teamData = {
                                    id: this.selectedTeam.id,
                                    name: this.selectedTeam.name,
                                    league: {
                                        id: null,
                                        name: "League"
                                    }
                                }
                                this.profile.teams.push(teamData);
                            }
                        })
                        .then(() => this.teamLoading = false)
                },
                fetchSeason(seasonID) {
                    let url = "https://public.v2.api.leagues4you.co.uk/season/" + seasonID;
                    this.teamLoading = true;
                    fetch(url)
                        .then(response => response.json())
                        .then(json => {
                            // console.log(json.success);
                            this.season = json.success;
                            // if (this.selectedSeason.divisions) {
                            //     for (let d in this.selectedSeason.divisions) {
                            //         if (this.selectedSeason.divisions[d].status.isCurrent === true) this.seasonID = this.selectedSeason.divisions[d].id;
                            //     }
                            // }
                        })
                        .then(() => this.teamLoading = false);
                },
                fetchLeagues() {
                    let url = "https://api.leagues4you.co.uk/leagues";
                    fetch(url)
                        .then(response => response.json())
                        .then(data => {
                            data.forEach(league => {
                                this.leagues.push(league);
                            })
                        });
                },
                followTeam(teamID) {
                    let url = "https://user.v2.api.leagues4you.co.uk/team-follow/" + teamID;
                    // console.log(url);
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => this.fetchProfile())
                        .then(() => this.searchTeamText = null)
                        .then(() => this.searchedTeams = [])
                        .then(() => this.teamID = teamID)
                },
                filteredResults(results) {
                    let returnVal = [];
                    for (let r in results) {
                        if (this.teamViewAll === true) {
                            returnVal.push(results[r]);
                        } else {
                            if (results[r].homeID == this.teamID || results[r].awayID == this.teamID) returnVal.push(results[r]);
                        }
                    }
                    return returnVal;
                },
                filteredFixtures(schedule) {
                    let returnVal = [];
                    for (let s in schedule) {
                        if (this.teamViewAll === true) {
                            returnVal.push(schedule[s]);
                        } else {
                            if (schedule[s].homeTeamID == this.teamID || schedule[s].awayTeamID == this.teamID) returnVal.push(schedule[s]);
                        }
                    }
                    return returnVal;
                },
                formatDate(date) {
                    let thisDate = new Date(date);
                    let thisDay, ordinal;
                    const month = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
                    let monthDate = parseInt(thisDate.getDate());
                    if (monthDate > 3 && monthDate < 21) {
                        ordinal = "th";
                    } else {
                        switch (monthDate % 10) {
                            case 1: ordinal = "st"; break;
                            case 2: ordinal = "nd"; break;
                            case 3: ordinal = "rd"; break;
                            default: ordinal = "th"; break;
                        }
                    }
                    return thisDate.getDate() + ordinal + " " + month[thisDate.getMonth()].substring(0, 3) + " " + thisDate.getFullYear();
                },
                formatDate1(date) {
                    let thisDate = new Date(date);
                    let thisDay = thisDate.getDate();
                    let thisMonth = thisDate.getMonth() + 1;
                    thisDay = (thisDay < 10) ? "0" + thisDay : thisDay;
                    thisMonth = (thisMonth < 10) ? "0" + thisMonth : thisMonth;
                    return thisDay + "/" + thisMonth + "/" + thisDate.getFullYear();
                },
                goto(screenName) {
                    if (screenName == "terms") {
                        this.newTeam.stage = 3;
                        this.newTeam.addPayment = false;
                    }
                    if (screenName == "teamname") {
                        this.newTeam.stage = 1;
                    }
                    if (screenName == "payment") {
                        this.newTeam.stage = 4;
                        if (this.newTeam.addPayment === true) this.addPaymentCard();
                    }
                },
                inviteTreasurer(team) {
                    this.newTreasurer.team = team;
                    this.screenID = 10;
                },
                newteamLeagueChoose(venue, league) {
                    this.newTeam.venue = venue;
                    this.newTeam.league = league;
                    this.newTeam.stage = 2;
                },
                readCookie(cname) {
                    let name = cname + "=";
                    let decodedCookie = decodeURIComponent(document.cookie);
                    let ca = decodedCookie.split(';');
                    for (let i = 0; i < ca.length; i++) {
                        let c = ca[i];
                        while (c.charAt(0) == ' ') {
                            c = c.substring(1);
                        }
                        if (c.indexOf(name) == 0) {
                            var rlt = c.substring(name.length, c.length);
                            return rlt;
                        }
                    }
                },
                login() {
                    this.screenID = 99;
                    // return;
                    var url = "https://public.v2.api.leagues4you.co.uk/authenticate/";
                    fetch(url, {
                        method: 'POST',
                        body: JSON.stringify(this.auth)
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.token) {
                                var cookieTxt = "jwt=" + json.token + "; path=" + json.cookie.path + "; domain=" + new URL(window.location).hostname + "; expires=" + json.cookie.expires;
                                document.cookie = cookieTxt;
                                this.jwt = json.token;
                                // console.log(cookieTxt);
                                // window.location = "/User";
                            } else {
                                this.setMessage(json.message, "danger")
                                console.error(json);
                                // var loginMessage = document.getElementById("loginMessage");
                                // loginMessage.innerHTMTL = json.message;
                                // loginMessage.classList.remove("d-none");
                            }
                        })
                    // .then(() => this.screenID = null)
                },
                logout() {
                    this.screenID = null;
                    this.jwt = null;
                    this.adminTeam = this.profile = {};
                    var cookieTxt = "jwt=; path=/; domain=" + new URL(window.location).hostname + "; expires=-1";
                    document.cookie = cookieTxt;
                },
                newTeamCard(teamID) {
                    this.waitNewCard = true;
                    this.screenID = 8;
                    // console.log("New Card for Team", teamID);
                    this.newCard.teamID = parseInt(teamID);
                    this.newCard.userID = parseInt(this.profile.id);
                    let url = "https://user.v2.api.leagues4you.co.uk/setupIntent/";
                    fetch(url, {
                        method: 'get',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            this.newCard.setupIntent = json.success;
                            let ele = document.querySelector("#new-payment-card-element");
                            this.stripe = Stripe(this.newCard.setupIntent.pk);
                            this.stripeElements = this.stripe.elements(
                                { clientSecret: this.newCard.setupIntent.client_secret }
                            );
                            let paymentElement = this.stripeElements.create('payment');
                            paymentElement.mount('#new-payment-card-element');
                        })
                        .then(() => this.waitNewCard = false)
                },
                leagueSearchPostcode() {
                    this.search.postcodeLatLng = {};
                    if (this.search.postcode) {
                        let url = 'https://v2.api.codeframe.co.uk/postcode/' + this.search.postcode;
                        fetch(url)
                            .then(response => response.json())
                            .then(json => {
                                if (json.success) {
                                    this.search.postcodeLatLng = {
                                        lat: json.success.data.lat,
                                        lng: json.success.data.lng,
                                    }
                                } else {
                                    this.setMessage("Postcode " + json.error, 'warning');
                                }
                            })
                            .then(() => this.localLeagues())
                    }
                },
                localLeagues() {
                    if (this.search.postcodeLatLng.lat && this.search.postcodeLatLng.lng) {
                        let url = "https://public.v2.api.leagues4you.co.uk/venues-local/" + this.search.postcodeLatLng.lat + "/" + this.search.postcodeLatLng.lng + "/" + (this.search.radius * 1.60934);
                        fetch(url)
                            .then(response => response.json())
                            .then(json => {
                                // console.log(json.success);
                                if (json.success) {
                                    this.search.venues = json.success;
                                } //else console.log(json);
                            })
                    }
                },
                teamNameCheck() {
                    if (!this.newTeam.name) return false;
                    let url = "https://public.v2.api.leagues4you.co.uk/team-name-checker/";
                    // console.log("Checking", this.newTeam.name);
                    fetch(url, {
                        method: 'post',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                        body: JSON.stringify(this.newTeam)
                    })
                        .then(response => response.json())
                        .then(json => {
                            // console.log(json);
                            if (json.error) {
                                this.newTeam.nameValid = false;
                                this.newTeam.message = json.error;
                                // this.setMessage(json.error, 'warning');
                            } else {
                                this.newTeam.message = null;
                                // this.success(json.success, 'success');
                                if (this.newTeam.name != json.success) this.newTeam.name = json.success;
                                this.newTeam.nameValid = true;
                            }
                        })
                },
                processNewCard() {
                    // console.log("New Card", this.newCard);
                    this.stripe.confirmSetup({
                        elements: this.stripeElements,
                        redirect: 'if_required',
                    })
                        .then((response) => {
                            if (response.error) {
                                console.log(response.error);
                                this.setMessage("Sorry - " + response.error.message + " Please try again or contact us for further assistance", "danger");
                            } else if (response.setupIntent) {
                                this.newCard.setupIntentID = response.setupIntent.id;
                                this.newCard.teamID = this.adminTeamID;
                                let url = "https://user.v2.api.leagues4you.co.uk/new-team-payment";
                                fetch(url, {
                                    method: 'post',
                                    headers: new Headers({
                                        'authorization': 'Bearer ' + this.jwt,
                                    }),
                                    body: JSON.stringify(this.newCard)
                                })
                                    .then(response => response.json())
                                    .then(json => this.adminTeam = json.success)
                                    .then(() => this.fetchProfile())
                                    .then(() => this.screenID = 5)
                            } else {
                                console.log("Something else", response);
                                // console.log("Result: Something else");
                            }
                        })
                },
                processPaymentCard() {
                    this.stripe.confirmSetup({
                        elements: this.stripeElements,
                        redirect: 'if_required',
                    })
                        .then(response => {
                            // console.dir(response.setupIntent);
                            // console.dir(response.setupIntent.payment_method);
                            // this.newTeam.paymentMethod = response.setupIntent;
                            if (response.error) {
                                // console.log("Result: Error");
                                this.stripeError = "Sorry, we could not add that card. Please try again.";
                            } else if (response.setupIntent) {
                                this.setupIntent.status = response.setupIntent.status;
                                this.newTeam.payment_method = response.setupIntent.payment_method;
                                // console.log("Result: Success", response.setupIntent.status);
                                this.stripeError = null;
                                this.newTeam.stage = 5;
                            } else {
                                // console.log("Result: Something else");
                            }
                        })

                    // if (error) {
                    //     const messageContainer = document.querySelector('#error-message');
                    //     messageContainer.textContent = error.message;
                    // } else {
                    //     this.newTeam.stage = 5;
                    // }
                },
                profileCheck() {
                    let profileErrors = [];
                    if (!this.profile.firstname) profileErrors.push('First Name');
                    if (!this.profile.lastname) profileErrors.push('Last Name');
                    if (!this.profile.email) profileErrors.push('Email address');
                    if (!this.profile.line1) profileErrors.push('Address line 1');
                    if (!this.profile.postcode) profileErrors.push('Postcode');
                    if (!this.profile.dob) profileErrors.push('Date of Birth');
                    return (profileErrors.length > 0) ? profileErrors : true;
                },
                reenterTeam(teamID) {
                    this.teamID = teamID;
                    this.screenID = 9;
                },
                reenterConfirmed() {
                    // if (!this.adminTeam || !this.adminTeam.id) return;
                    // if (!this.adminTeam.canReenter.id || !this.adminTeam.canReenter.id) return;
                    let url = "https://user.v2.api.leagues4you.co.uk/team-reenter/" + this.financials.canReenter.team.id;
                    url += "/" + this.financials.canReenter.season.id;
                    // console.log("URL", url); return;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            this.reenterStage = 2;
                            this.financials.canReenter = null;
                        })
                },
                registration() {
                    if (!this.register.email) return;
                    this.jwt = null;
                    let url = "https://public.v2.api.leagues4you.co.uk/registration";
                    fetch(url, {
                        method: 'post',
                        body: JSON.stringify(this.register)
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                this.guestScreen = 'activate';
                                this.setMessage("Thanks. We have registered your account. Look out for the ativation code in your inbox and enter it now", 'success');
                            } else {
                                this.guestScreen = null;
                                this.setMessage("We could not register your account. Does it exist already? Try a password reminder", 'danger');
                                this.register = {}
                            }
                        })
                },
                removeInvite(inviteID) {
                    for (let i in this.profile.invites) {
                        if (this.profile.invites[i].id == inviteID) this.profile.invites.splice(i, 1);
                    }
                },
                removeTreasurer() {
                    let url = "https://user.v2.api.leagues4you.co.uk/treasurer-remove/" + this.adminTeam.id;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            console.log(json.success);
                            for (let t in this.profile.teams) {
                                if (this.profile.teams[t].id == json.success.id) {
                                    this.profile.teams[t] = this.adminTeam = json.success;
                                    console.log("Updated");
                                }
                            }
                        })
                },
                reminder() {
                    if (!this.register.email) return;
                    let url = "https://public.v2.api.leagues4you.co.uk/reminder/" + this.register.email;
                    fetch(url)
                        .then(response => response.json())
                    // .then(json => console.log(json));
                    this.guestScreen = 'activate';
                },
                renderTerms() {
                    const url = 'https://cdn.leagues4you.co.uk/netball-terms-and-conditions';
                    console.log("PDF", url);
                    pdfjsLib.GlobalWorkerOptions.workerSrc =
                        'pdfjs/pdf.worker.js';
                    const loadingTask = pdfjsLib.getDocument(url);
                    (async () => {
                        const pdf = await loadingTask.promise;
                        this.pdfPages = pdf.numPages;

                        const page = await pdf.getPage(this.pdfPageNum);
                        const scale = .8;
                        const viewport = page.getViewport({ scale });
                        // Support HiDPI-screens.
                        const outputScale = window.devicePixelRatio || 1;

                        //
                        // Prepare canvas using PDF page dimensions
                        //
                        const canvas = document.getElementById("terms-and-conditions");

                        const context = canvas.getContext("2d");

                        canvas.width = Math.floor(viewport.width * outputScale);
                        canvas.height = Math.floor(viewport.height * outputScale);
                        // canvas.style.width = Math.floor(viewport.width) + "px";
                        canvas.style.width = "300px";
                        // canvas.style.height = Math.floor(viewport.height) + "px";
                        canvas.style.height = "425px";

                        const transform = outputScale !== 1
                            ? [outputScale, 0, 0, outputScale, 0, 0]
                            : null;

                        //
                        // Render PDF page into canvas context
                        //
                        const renderContext = {
                            canvasContext: context,
                            transform,
                            viewport,
                        };
                        page.render(renderContext);
                    })()
                },
                updatepassword() {
                    let url = "https://user.v2.api.leagues4you.co.uk/passchange/";
                    fetch(url, {
                        method: 'POST',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                        body: JSON.stringify(this.passChange)
                    })
                        .then(response => response.json())
                        .then(json => {
                            if (json.success) {
                                this.setMessage("Password Updated", "success");
                                this.screenID = 1;
                            } else {
                                this.setMessage(json.error, "danger");
                            }
                        })
                },
                setDivision(divisionID) {
                    this.divisionID = divisionID;
                },
                sendTreasurerInvite() {
                    let url = "https://user.v2.api.leagues4you.co.uk/invite-treasurer/" + this.adminTeam.id + "/" + this.newTreasurer.email;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => this.screenID = 5)
                        .then(() => this.setMessage("We'll send a Treasurer invite out to " + this.newTreasurer.email + " soon.", "success"))
                        .then(() => this.newTreasurer = {})
                },
                setNewTeamPaymentCard() {
                    this.screenID = 8;
                    this.addNewPaymentCard();
                },
                searchLeagues() {
                    if (!this.leagueSearchName || this.leagueSearchName.length < 3) return;
                    let url = "https://public.v2.api.leagues4you.co.uk/league-search/" + encodeURI(this.leagueSearchName);
                    // console.log(url);
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            // console.log(json);
                            if (json.success) {
                                this.search.venues = json.success;
                            } else console.error(json.error);
                        })
                },
                searchTeams() {
                    this.searchedTeams = [];
                    if (!this.searchTeamText || this.searchTeamText.length < 3) return;
                    let url = "https://public.v2.api.leagues4you.co.uk/team-search/" + encodeURI(this.searchTeamText);
                    // console.log(url);
                    fetch(url)
                        .then(response => response.json())
                        .then(json => this.searchedTeams = json.success)
                    // .then(() => console.log(this.searchedTeams))
                },
                saveProfile() {
                    let url = "https://user.v2.api.leagues4you.co.uk/profile";
                    let requestMethod = "PATCH";
                    fetch(url, {
                        method: 'post',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                        body: JSON.stringify(this.profile)
                    })
                        .then(response => response.json())
                        .then(json => {
                            // console.log(json);
                            if (json.success) {
                                this.profile = json.success;
                                this.setMessage("Profile Updated", 'success');
                            } else this.setMessage(json.error, 'danger');
                        })
                },
                setMessage(text, level) {
                    if (this.clearMessages) clearTimeout(this.clearMessages);
                    this.messages[level] = text;
                    this.clearMessages = setTimeout(() => this.messages = {}, 5000);
                },
                skipPayment() {
                    this.newTeam.addPayment = false;
                    this.newTeam.stage++;
                },
                showHelp(item) {
                    switch (item) {
                        case 'captain':
                            this.helpInfo.title = "What is a Captain?";
                            this.helpInfo.text = [
                                "Well, we're glad you asked!",
                                "A captain has overall responsibility for a team from organising players through to picking the team, handing out the contraband (no, not that) accepting our terms and conditions and either paying the team fixture charges or appointing a treasurer to do that for them.",
                            ];
                            break;
                        case 'treasurer':
                            this.helpInfo.title = "What is a Treasurer?";
                            this.helpInfo.text = [
                                "We're glad you asked about that too!",
                                "Whilst a captain retains overall control and responsibility for the team they can lighten the load by asking a treasurer to step in a handle the money. Caring is sharing!",
                            ];
                            break;
                        case 'paymentCard':
                            this.helpInfo.title = "Payment Card";
                            this.helpInfo.text = [
                                'testing'
                            ];
                            break;
                        case 'paymentOptions':
                            this.helpInfo.title = "Payment Options";
                            this.helpInfo.text = [
                                'You can choose to pay on the day of each fixture or you can pay for the remainder of the Season at once'
                            ];
                            break;
                    }
                },
                submitTeam() {
                    let url = "https://user.v2.api.leagues4you.co.uk/team-submit";
                    let teamData = {
                        teamName: this.newTeam.name,
                        leagueID: this.newTeam.league.id,
                        stripePaymentMethodID: this.newTeam.payment_method
                    }
                    fetch(url, {
                        method: 'post',
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                        body: JSON.stringify(teamData)
                    })
                        .then(response => response.json())
                        .then(json => {
                            console.log("New Team Response", json);
                            this.teamID = json.success.id;
                            this.fetchProfile();
                        })
                    this.newTeam.stage++;
                },
                teamCompleted() {
                    this.screenID = 4;
                    this.teamID = this.newTeam.teamID;
                    this.newTeam = {};
                },
                teamFinancials() {
                    this.financials = {};
                    if (!this.adminTeam.id) return;
                    let url = "https://user.v2.api.leagues4you.co.uk/team-financials/" + this.adminTeam.id;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            console.log(json);
                            if (json.success) this.financials = json.success;
                        })
                },
                teamReenter() {
                    if (!this.financials.canReenter) return;
                    this.screenID = 9;
                },
                togglePayOption(payOptionVal) {
                    if (this.financials.paymentOption == payOptionVal) {
                        console.log("No change"); return;
                    } else console.log("Change to ", payOptionVal);
                    let url = "https://user.v2.api.leagues4you.co.uk/set-payment-option/" + this.adminTeam.id;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => {
                            this.adminTeam = json.success;
                            if (this.adminTeam.payInFull == 1) {
                                this.setMessage("You have elected to pay for the Full Season", "success");
                            } else this.setMessage("You have chosen to pay for each fixture as they occur", "success");
                        })
                        .then(() => this.financials.paymentOption = (payOptionVal == 1) ? 1 : null)
                },
                unfollow(teamID) {
                    let url = "https://user.v2.api.leagues4you.co.uk/team-unfollow/" + teamID;
                    fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                        .then(response => response.json())
                        .then(json => this.fetchProfile())
                        .then(() => {
                            this.teamID = (this.profile.teams.length > 0) ? this.profile.teams[0].id : null;
                        })
                },
                viewTeam(teamID) {
                    if (!teamID) return;
                    this.followTeam(teamID);
                    this.searchedTeams = [];
                    this.searchTeamText = null;
                    this.teamID = teamID;
                },
            },
            computed: {
                newteamLeagueOptions() {
                    let returnableLeagues = [];
                    if (!this.newteamLeagueSearchText) return returnableLeagues;
                    for (let l in this.leagues) {
                        if (this.leagues[l].name.toLowerCase().includes(this.newteamLeagueSearchText.toLowerCase())) {
                            returnableLeagues.push(this.leagues[l]);
                            if (this.leagueID && this.leagues[l].id == this.leagueID) {
                                returnableLeagues = [this.leagues[l]];
                                break;
                            }
                        }
                    };
                    if (returnableLeagues.length == 1) {
                        this.newteamLeagueSearchText = returnableLeagues[0].name;
                        this.leagueID = returnableLeagues[0].id;
                    }
                    return returnableLeagues;
                },
                leagueTeamFound() {
                    return (this.newteamLeagueOptions.length == 1) ? true : false;
                },
                divisionResults() {
                    if (!this.season) return;
                    for (let d in this.season.divisions) {
                        if (this.season.divisions[d].id == this.divisionID) return this.season.divisions[d];
                    }
                },
                isFollowing() {
                    if (!this.teamID) return;
                    if (this.profile.teams) {
                        for (let t in this.profile.teams) {
                            if (this.profile.teams[t].id == this.teamID) return true;
                        }
                    }
                },
                managedTeams() {
                    let returnVal = [];
                    if (this.profile.teams) {
                        for (let t in this.profile.teams) {
                            if ((this.profile.teams[t].captain && this.profile.teams[t].captain.id == this.profile.id) || (this.profile.teams[t].treasurer && this.profile.teams[t].treasurer.id == this.profile.id)) {
                                returnVal.push(this.profile.teams[t]);
                            }
                        }
                    }
                    return returnVal;
                },
                teamManager() {
                    if (this.managedTeams.length > 0) {
                        for (let t in this.managedTeams) {
                            if (this.managedTeams[t].id == this.teamID) return true;
                        }
                    }
                },
                hasAlerts() {
                    if (this.managedTeams.length > 0) {
                        for (let t in this.managedTeams) {
                            if (this.managedTeams[t].alerts) return true;
                        }
                    }
                    return false;
                }
            },
            watch: {
                adminTeamID() {
                    if (!this.adminTeamID) return;
                    if (!this.profile.teams || this.profile.teams.length == 0) return;
                    for (let t in this.profile.teams) {
                        if (this.profile.teams[t].id == this.adminTeamID) {
                            this.adminTeam = this.profile.teams[t];
                            this.teamFinancials();
                            return;
                        }
                    }
                },
                reenterStage() {
                    if (this.reenterStage == 1) {
                        this.renderTerms();
                    }
                },
                teamProcess() {
                    if (this.teamProcess == 4) {
                        this.stripe = Stripe('pk_test_51HS2BSLOCeRFt5luGoTqd5ssxwJw736SARhvCyuh5reIN19qRIFg2hMEsylHIIlVVVFuFgEORgNU41LYUtA1BiIp00u78WXhM3');
                        let options = [];
                        const elements = this.stripe.elements(options);
                        // Create and mount the Payment Element
                        const paymentElement = elements.create('payment');
                        paymentElement.mount('#payment-element');
                        this.pdfjsLib = window['pdfjs-dist/build/pdf'];
                    }
                },
                jwt() {
                    console.log("JWT amended to ", this.jwt);
                    this.fetchProfile();
                },
                screenID() {
                    console.log("Screen ID", this.screenID);
                    if (this.screenID == 6) {
                        this.fetchSetupIntent();
                        if (!this.search.postcode && this.profile.postcode) this.search.postcode = this.profile.postcode;
                        if (this.newTeam.stage == 4 && this.newTeam.addPayment === true) {
                            console.log("I want to call it again");
                            this.addPaymentCard();
                        } else {
                            console.log("Not calling", "Stage", this.newTeam.stage, "AddPayment", this.newTeam.addPayment);
                        }
                    }
                    if (this.screenID == 3) this.fetchLibrary();
                    if (this.screenID == 11) {
                        let url = "https://user.v2.api.leagues4you.co.uk/team-statement/" + this.adminTeam.id;
                        fetch(url, {
                            headers: new Headers({
                                'authorization': 'Bearer ' + this.jwt,
                            }),
                        })
                            .then(response => response.json())
                            .then(json => {
                                console.log(json);
                                this.statement = json.success;
                            })
                    }
                },
                teamID() {
                    this.fetchTeam();
                },
                seasonID() {
                    this.fetchSeason(this.seasonID);
                },
                season() {
                    this.divisionID = null;
                    if (!this.season.divisions) return;
                    for (const d in this.season.divisions) {
                        if (!this.season.divisions[d].teams) continue;
                        for (const t in this.season.divisions[d].teams) {
                            if (this.season.divisions[d].teams[t].id == this.teamID) {
                                this.divisionID = this.season.divisions[d].id;
                                return;
                            }
                        }
                    }
                },
                divisionID() {
                    for (const d in this.season.divisions) {
                        if (!this.season.divisions[d].teams) continue;
                        for (const t in this.season.divisions[d].teams) {
                            if (this.season.divisions[d].id == this.divisionID) {
                                this.selectedDivision = this.season.divisions[d];
                                if (!this.divisionViewOption) this.divisionViewOption = 'tables';
                                // console.log(this.selectedDivision);
                            }
                        }
                    }
                },
                newTeam: {
                    deep: true,
                    handler() {
                        if (this.newTeam.stage && !this.newTeam.setupIntent) this.fetchSetupIntent();
                        if (this.newTeam.stage == 3) this.renderTerms();
                    }
                },
                helpInfo: {
                    deep: true,
                    handler() {
                        console.log("Help", this.helpInfo.title);
                    }
                },
                pdfPageNum() {
                    this.renderTerms();
                }
            },
            mounted() {
                this.fetchLeagues();
                // if (document.getElementById("teamTermsTextarea")) document.getElementById("teamTermsTextarea").addEventListener('scroll', this.teamTermsTextareaScrolled);
                if (!this.jwt) this.jwt = this.readCookie('jwt');
                this.pdfjsLib = pdfjsLib;
                // this.renderTerms = renderTerms;
            }
        }).mount('#app')
    </script>

</body>

</html>