<?php
session_start();
include('api-config.php');
$bookingId = $_SESSION['c2c_multi_booking_id'];

$page = @$_GET['page'] ? htmlspecialchars(trim(@$_GET['page'])) : null;


// API Request
//if (!isset($bookingId)) response(['success' => false, 'message' => 'Booking ID not set']);


if (isset($_POST['c2c_booker'])) {
    if (!isset($_POST['c2c_booker']['discountCode'])) $_POST['c2c_booker']['discountCode'] = null;
    $url = "https://{$prefixUrl}api.leagues4you.co.uk/taster-multibook?bookerTasterID={$bookingId}"
    . "&firstname=" . urlencode($_POST['c2c_booker']['firstname'])
    . "&lastname=" . urlencode($_POST['c2c_booker']['lastname'])
    . "&email=" . urlencode($_POST['c2c_booker']['email'])
    . "&mobile=" . urlencode($_POST['c2c_booker']['mobile'])
    . "&discountCode=" . urlencode($_POST['c2c_booker']['discountCode'])
    . "&teamName=" . urlencode($_POST['c2c_booker']['teamName']);
    
    $c2c_multi_booking = json_decode(file_get_contents($url), true);
    response(['success' => true, 'data' => $c2c_multi_booking]);
}


if (isset($_POST['newAttendee'])) {
    $url = "https://{$prefixUrl}api.leagues4you.co.uk/taster-multibook?newAttendeeTasterID={$bookingId}&firstname=" . urlencode($_POST['newAttendee']['firstname']) . "&lastname=" . urlencode($_POST['newAttendee']['lastname']) . "&email=" . urlencode($_POST['newAttendee']['email']) . "&mobile=" . urlencode($_POST['newAttendee']['mobile']);
    $c2c_multi_booking = json_decode(file_get_contents($url), true);
    response(['success' => true, 'data' => $c2c_multi_booking]);
}

if (isset($_POST['removeAttendee'])) {
    $url = "https://{$prefixUrl}api.leagues4you.co.uk/taster-multibook?removeAttendee={$bookingId}&attendeeID=" . urlencode($_POST['removeAttendee']);
    $c2c_multi_booking = json_decode(file_get_contents($url), true);
    response(['success' => true, 'data' => $c2c_multi_booking]);
}

if (isset($_POST['cancelBooking'])) {
    unset($c2c_multi_booking, $_SESSION['c2c_multi_booking_id']);
}

// Confirm Booking
if (isset($_GET['confirmBooking'])) {
    $url = "https://{$prefixUrl}api.leagues4you.co.uk/taster-multibook?bookingID={$bookingId}";
    $c2c_multi_booking = json_decode(file_get_contents($url), true);
    unset($_SESSION['c2c_multi_booking_id']);
    response(['success' => true, 'data' => $c2c_multi_booking]);
}

$searchText = @$_GET['search'] ? htmlspecialchars(trim($_GET['search'])) : null;

if (isset($searchText)) {
    $c2c_sessions = json_decode(file_get_contents("http://{$prefixUrl}api.leagues4you.co.uk/tasters-sport/1"), true);

    // If searchText is not "full", apply fuzzy search, else skip the search
    if ($searchText !== "full") {
        $c2c_sessions = fuzzySearchArray($c2c_sessions, $searchText);
    }

    response(['success' => true, 'sessions' => $c2c_sessions]); // Return filtered results
}

if (isset($_GET['wildcard'])) {
    $c2c_sessions = json_decode(file_get_contents("http://{$prefixUrl}api.leagues4you.co.uk/wildcard/1,5,7"), true);

    response(['success' => true, 'sessions' => $c2c_sessions]); 
}

if (isset($_GET['tournament'])) {
    $c2c_sessions = json_decode(file_get_contents("http://{$prefixUrl}api.leagues4you.co.uk/tournament/1"), true);
    
    response(['success' => true, 'sessions' => $c2c_sessions]); 
}


if($page == 'incident-report'){
    // Collect POST data in a form-encoded format
    $data = [
        'name' => $_POST['names']['first_name'],
        'dob' => convertDateToYMD($_POST['datetime']),
        'contact_number' => $_POST['numeric_field'],
        'date_of_incident' => convertDateToYMD($_POST['datetime_1']),
        'time_of_incident' => $_POST['datetime_2'],
        'venueId' => $_POST['dropdown'],
        'leagueId' => $_POST['dropdown_1'],
        'description' => $_POST['description'],
        'your_name' => $_POST['input_text'],
        'witness' => $_POST['input_radio'] == 'yes' ? 1 : 0,
        'incident_report' => 1,
        'status' => 'open',
        'injured_person_type' => $_POST['dropdown_2'],
        'taken_to_hospital' => $_POST['dropdown_3'],
        'injury_details' => $_POST['description_1'],
        'location' => $_POST['input_text_1'],
        'severity' => $_POST['dropdown_3'] == 'Yes' ? 'Amber' : 'Green',
        'no_consent' => $_POST['input_radio_2'] == 'yes' ? 1 : 0,
        'under_18'=> $_POST['input_radio_3'] == 'yes' ? 1 : 0,
        'relationship_to_player'=> $_POST['input_text_2'],
    ];

    // echo "<pre>";
    // print_r($data);exit;
    // Build the form-encoded query string for the POST request
    $postData = http_build_query($data);

    // cURL to send form data to the external API
    $ch = curl_init("https://public.v2.api.leagues4you.co.uk/report-an-incident");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/x-www-form-urlencoded'
    ));
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData); // Send as form data, not JSON

    // Execute the POST request
    $response = curl_exec($ch);
    curl_close($ch);

    // Handle response from external API
    if ($response === false) {
        echo "Error submitting to external API.";
    } else {
         echo json_encode([
            'success' => true,
            'message' => 'Incident report successfully submitted.',
            'response' => json_decode($response, true) // Decode and include the API response
        ]);
    }

    exit;
}