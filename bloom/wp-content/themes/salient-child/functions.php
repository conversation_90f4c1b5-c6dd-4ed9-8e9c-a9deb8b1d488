<?php
include 'seo.php';

function hide_admin_menu_items_for_editors() {
    if (current_user_can('editor')) {
        remove_menu_page('admin.php?page=vc-general');
        remove_menu_page('edit.php?post_type=nectar_slider');
        remove_menu_page('edit.php?post_type=salient_g_sections');
    }
}
add_action('admin_menu', 'hide_admin_menu_items_for_editors', 999);

add_action('wp_enqueue_scripts', 'salient_child_enqueue_styles', 10);

function salient_child_enqueue_styles() {

    $nectar_theme_version = nectar_get_theme_version();
    wp_enqueue_style('salient-child-style', get_stylesheet_directory_uri() . '/style.css', '', '0.0.24');
    wp_enqueue_script('salient-child-script', get_stylesheet_directory_uri() . '/js/c2c_details.js', array('jquery'), '0.0.7', true);
    wp_enqueue_style('nectar-linecon');

    if (is_rtl()) {
        wp_enqueue_style('salient-rtl',  get_template_directory_uri() . '/rtl.css', array(), '1', 'screen');
    }
}
function couch2court_rewrite_rules() {
    // Match URLs like /couch2court-details/some-slug and redirect to couch2court-details page with league_slug as a query var
    add_rewrite_rule(
        '^couch2court-details/([^/]*)/?$',  // Match the URL pattern
        'index.php?pagename=couch2court-details&couch2court_slug=$matches[1]',  // Redirect with query var
        'top'
    );
}
add_action('init', 'couch2court_rewrite_rules');

function league_rewrite_rules() {
    // Match URLs like /couch2court-details/some-slug and redirect to couch2court-details page with league_slug as a query var
    add_rewrite_rule(
        '^social-leagues/([^/]*)/?$',  // Match the URL pattern
        'index.php?pagename=social-leagues&league_slug=$matches[1]',  // Redirect with query var
        'top'
    );
}
add_action('init', 'league_rewrite_rules');

// Register 'league_slug' and 'couch2court_slug' as query variables
function custom_query_vars($vars) {
    $vars[] = 'league_slug';
    $vars[] = 'couch2court_slug';
    return $vars;
}
add_filter('query_vars', 'custom_query_vars');

// Function to render VC Composer shortcodes inside a custom shortcode
function session_info_section_content() {
    $couch2court_id = 0;
    $couch2court_slug = get_query_var('couch2court_slug');
    if ($couch2court_slug) $couch2court_id = explode('-', $couch2court_slug)[0];
    $venue_name = "Not Found";

    $venue_address = "Not Found";
    $location = "Not Found";
    $Price = "Not Found";
    $day = "Not Found";
    $time = "Not Found";
    $date = "Not Found";
    $badge = "";
    $api_url = get_url_prefix() . "api.leagues4you.co.uk/c2cSession/$couch2court_slug";

    $content = session_info_section_content_vc_content($api_url, $couch2court_id);
    return do_shortcode($content);
}

// Register the custom shortcode
add_shortcode('session_info_section', 'session_info_section_content');

function join_facebook_section_content() {
    $couch2court_slug = get_query_var('couch2court_slug');
    $facebook_group_url = "https://www.facebook.com/BLOOMNetball";
    $api_url = get_url_prefix() . "api.leagues4you.co.uk/c2cSession/$couch2court_slug";
    $page = "Couch2Court®";
    $data = fetch_api_data($api_url);
    if (isset($data) && !empty($data['league'])) {
        if (!empty($data['league']['league_fb_group'])) {
            $facebook_group_url = $data['league']['league_fb_group'];
        }
        if (empty($data['league']['league_fb_group']) && !empty($data['tasterSession']['fb_page'])) {
            $facebook_group_url = $data['tasterSession']['fb_page'];
        }
    }

    $content = common_facebook_vc_content($facebook_group_url, $page);
    return do_shortcode($content);
}

// Register the custom shortcode
add_shortcode('join_facebook_section', 'join_facebook_section_content');

function session_coordinator_shortcode_content() {
    $couch2court_slug = get_query_var('couch2court_slug');
    $coordinator_name = "Not Found";
    $coordinator_email = "#";
    $coordinator_image_link = get_stylesheet_directory_uri() . '/images/dummy_profile.png';
    $coordinator_bio = "Have any additional questions? Our coordinator is more than willing to help. 
                        Alternatively you can email <NAME_EMAIL>";
    $api_url = get_url_prefix() . "api.leagues4you.co.uk/c2cSession/$couch2court_slug";

    $data = fetch_api_data($api_url);

    if (!empty($data)) {
        $coordinator_name = esc_html($data['coordinator']['firstname']);
        $coordinator_email = 'mailto:' .  antispambot($data['coordinator']['email']);
        if (!empty($data['coordinator']['profilePictureUrl'])) {
            $coordinator_image_link = esc_url($data['coordinator']['profilePictureUrl']);
        }
        if (isset($data['coordinator']['bio'])) {
            $coordinator_bio = esc_html($data['coordinator']['bio']);
        }
    }

    $content = common_coordinator_content('Have any questions?', $coordinator_image_link, $coordinator_name, $coordinator_email, $coordinator_bio);

    return do_shortcode($content);
}

// Register the custom shortcode
add_shortcode('session_coordinator_content', 'session_coordinator_shortcode_content');


function league_info_section_content() {
    $league_slug = get_query_var('league_slug');
    $api_url = get_url_prefix() . "public.v2.api.leagues4you.co.uk/league-info/$league_slug";
    $league_name = "Not Found";
    $venue_name = "Not Found";
    $league_address = "Not Found";
    $date = "Not Found";
    $time = "Not Found";
    $location = "Not Found";
    $price = "0";
    $formattedDate = "01-01-1970";
    $bottom_text = "";
    $badge_text = "";
    $day = "Wednesday";

    $content = league_info_section_content_vc_content($api_url);

    return do_shortcode($content);
}
add_shortcode('league_details_league_info_section', 'league_info_section_content');



function league_facebook_section_content() {
    $league_slug = get_query_var('league_slug');
    $facebook_group_url = "https://www.facebook.com/BLOOMNetball";
    $api_url = get_url_prefix() . "public.v2.api.leagues4you.co.uk/league-info/$league_slug";
    $page = 'league';

    $data = fetch_api_data($api_url);
    if (isset($data) && !empty($data['facebook'])) {
        if (!empty($data['facebook']['playerRequestGroup'])) {
            $facebook_group_url = $data['facebook']['playerRequestGroup'];
        }
    }

    $content = common_facebook_vc_content($facebook_group_url, $page);
    return do_shortcode($content);
}

// Register the custom shortcode
add_shortcode('league_facebook_section', 'league_facebook_section_content');

function league_coordinator_shortcode_content() {
    $league_slug = get_query_var('league_slug');
    $coordinator_name = "Not Found";
    $coordinator_email = "#";
    $coordinator_image_link = get_stylesheet_directory_uri() . '/images/dummy_profile.png';
    $coordinator_bio = "Have any additional questions? Our coordinator is more than willing to help. 
                        Alternatively you can email <NAME_EMAIL>";
    $api_url = get_url_prefix() . "public.v2.api.leagues4you.co.uk/league-info/$league_slug";
    $data = fetch_api_data($api_url);
    if ($data) {
        $coordinator_name = esc_html($data['coordinator']['firstname']);
        $coordinator_email =  $data['coordinator']['email'];
        $coordinator_image_link = esc_url($data['coordinator']['profilePictureUrl']);
        if (isset($data['coordinator']['bio'])) {
            $coordinator_bio = esc_html($data['coordinator']['bio']);
        }
    }
    $content = common_coordinator_content('League Coordinator', $coordinator_image_link, $coordinator_name, $coordinator_email, $coordinator_bio);
    return do_shortcode($content);
}
add_shortcode('league_coordinator_content', 'league_coordinator_shortcode_content');

function league_teams_section() {
    $league_slug = get_query_var('league_slug');

    $league_slug = get_query_var('league_slug');
    $api_url = get_url_prefix() . "public.v2.api.leagues4you.co.uk/league-info/$league_slug";
    $session_name = "Not Found";

    $content = league_teams_section_vc_content($api_url);
    return do_shortcode($content);
}
add_shortcode('league_details_league_teams_section', 'league_teams_section');


function fetch_api_data($api_url) {
    // Create a unique transient key based on the API URL
    $transient_key = 'api_cache_' . md5($api_url);

    // Attempt to get cached data
    $cached_data = get_transient($transient_key);
    if ($cached_data !== false) {
        return $cached_data; // Return cached data if available
    }

    // If no cached data, perform the API request
    $response = wp_remote_get($api_url);

    // Check for errors
    if (is_wp_error($response)) {
        // Log the error or handle it as needed
        error_log('API Request Failed: ' . $response->get_error_message());
        return false;
    }

    // Get the response body
    $body = wp_remote_retrieve_body($response);

    // Decode the JSON response into an associative array
    $data = json_decode($body, true);

    // Check if the data was decoded successfully
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('JSON Decoding Failed: ' . json_last_error_msg());
        return false;
    }

    // Cache the data for 30 seconds
    set_transient($transient_key, $data, 30);

    return $data;
}


function get_url_prefix() {
    // Get the current site's host name
    $host = parse_url(home_url(), PHP_URL_HOST);

    // Check if the URL starts with "local."
    if (strpos($host, 'local.') === 0) {
        return 'http://local.';
    }
    // Check if the URL starts with "staging."
    elseif (strpos($host, 'staging.') === 0) {
        return 'https://staging.';
    }
    // Default to a secure base URL
    else {
        return 'https://';
    }
}

/* Remove the salient studio */
function nectar_generate_salient_studio_button() { 
    return false; 
  } 
 function nectar_custom_studio_templates_for_vc() { 
   return false; 
 }


require_once get_stylesheet_directory() . '/components/session_info_section.php';
require_once get_stylesheet_directory() . '/components/league_info_section.php';
require_once get_stylesheet_directory() . '/components/facebook_section.php';
require_once get_stylesheet_directory() . '/components/coordinator_info_section.php';
require_once get_stylesheet_directory() . '/components/leagues_teams_info_section.php';
