<?php
function session_info_section_content_vc_content($api_url, $couch2court_id)
{
    $data = fetch_api_data($api_url);
    if (!empty($data)) {
        $venue_name = esc_html($data['venue']['name']);
        $address_with_comma = !empty($data['venue']['address1']) 
            ? esc_html($data['venue']['address1']) . ', ' 
            : '';
        $town_with_comma = !empty($data['venue']['town']) 
            ? esc_html($data['venue']['town']) . ', ' 
            : '';
        $venue_address = $address_with_comma . $town_with_comma . esc_html($data['venue']['postcode']);
        $location = strtoupper(esc_html($data['tasterSession']['location']));
        $Price = strval($data['tasterSession']['charge']);
        $day =  strval(date('l', strtotime($data['tasterSession']['date'])));
        $time = strval(date("g:i A", strtotime($data['tasterSession']['time'])));
        $date =  date('jS F Y', (strtotime($data['tasterSession']['date'])));

        $fullyBooked = $data['tasterSession']['fullyBooked'];
        $buttonContent = $fullyBooked 
        ? '<div style="color: red; font-weight: bold; font-size: 1.2em;">Fully Booked</div>'
        : '[nectar_cta btn_style="arrow-animation" heading_tag="h5" button_color="extra-color-gradient-1" text_color="#000000" button_border_thickness="0px" link_type="regular" alignment="left" alignment_tablet="default" alignment_phone="default" constrain_group_3="yes" constrain_group_4="yes" display="block" display_tablet="inherit" display_phone="inherit" link_text="Book Now!" margin_top="35" padding_top="15" padding_bottom="15" padding_left="35" padding_right="35" border_radius="50px" url="/checkout"]';
        
        $limitedSpaces = $fullyBooked ? '' : ' [nectar_responsive_text font_size_min="14px" font_size_max="20px" font_size_desktop="1.1vw" font_line_height="1.5" text_direction="default"]
                                Limited spaces remaining
                            [/nectar_responsive_text]';
    }
    


    $content = '[vc_column 
                    top_padding_desktop="4vw" 
                    bottom_padding_desktop="1vw" 
                    left_padding_desktop="4vw" 
                    constrain_group_101="yes" 
                    right_padding_desktop="4vw" 
                    top_padding_tablet="8vw" 
                    left_padding_tablet="8vw" 
                    constrain_group_103="yes" 
                    right_padding_tablet="8vw" 
                    bottom_margin_tablet="20" 
                    column_element_direction_desktop="default" 
                    column_element_spacing="10px" 
                    desktop_text_alignment="default" 
                    tablet_text_alignment="default" 
                    phone_text_alignment="default" 
                    background_color="#ffffff" 
                    background_color_opacity="1" 
                    background_hover_color_opacity="1" 
                    column_backdrop_filter="none" 
                    column_shadow="none" 
                    column_border_radius="20px" 
                    column_link_target="_self" 
                    column_position="default" 
                    advanced_gradient_angle="0" 
                    gradient_direction="left_to_right" 
                    overlay_strength="0.3" 
                    width="1/3" 
                    tablet_width_inherit="default" 
                    animation_type="default" 
                    bg_image_animation="none" 
                    border_type="simple" 
                    column_border_width="none" 
                    column_border_style="solid" 
                    gradient_type="default" 
                    column_padding_type="advanced"
                ]
                    [nectar_badge display_tag="label" 
                        badge_style="default" 
                        bg_color_type="custom" 
                        bg_color_custom="#00df74" 
                        text_color="#ffffff" 
                        padding="small" 
                        border_radius="20px" 
                        display="block" 
                        text="Couch2Court®" 
                        margin_bottom="20"
                    ]
                    [nectar_responsive_text font_size_desktop="1.8vw" 
                        font_size_tablet="22px" 
                        font_line_height="1.3" 
                        text_direction="default"
                    ]
                        <h3>' . $venue_name . '</h3>
                    [/nectar_responsive_text]
                    [vc_row_inner column_margin="default" 
                        column_direction="default" 
                        column_direction_tablet="default" 
                        column_direction_phone="default" 
                        text_align="left" 
                        row_position="default" 
                        row_position_tablet="inherit" 
                        row_position_phone="inherit" 
                        overflow="visible" 
                        pointer_events="all"
                    ]
                        [vc_column_inner 
                            right_padding_tablet="33vw" 
                            right_padding_phone="0" 
                            column_element_direction_desktop="default" 
                            column_element_spacing="default" 
                            desktop_text_alignment="default" 
                            tablet_text_alignment="default" 
                            phone_text_alignment="default" 
                            background_color_opacity="1" 
                            background_hover_color_opacity="1" 
                            column_backdrop_filter="none" 
                            column_shadow="none" 
                            column_border_radius="none" 
                            column_link_target="_self" 
                            overflow="visible" 
                            gradient_direction="left_to_right" 
                            overlay_strength="0.3" 
                            width="1/1" 
                            tablet_width_inherit="default" 
                            animation_type="default" 
                            bg_image_animation="none" 
                            border_type="simple" 
                            column_border_width="none" 
                            column_border_style="solid" 
                            column_padding_type="advanced" 
                            gradient_type="default"
                        ]
                            [nectar_responsive_text 
                                font_size_min="14px" 
                                font_size_max="20px" 
                                font_size_desktop="1.1vw" 
                                font_line_height="1.5" 
                                text_direction="default"
                            ]
                                <p style="padding: 5px !important;">' . $venue_address . '</p><p style="padding: 5px !important;">Couch2Court® Date: ' . $date . '</p>
                            [/nectar_responsive_text]
                        [/vc_column_inner]
                    [/vc_row_inner]
                                <form class="c2c_details_c2c_id" method="post" action="/checkout">
                                    <input type="hidden" name="tasterID" value="' . $couch2court_id . '"/>
                                </form>
                    [vc_row_inner 
                        column_margin="default" 
                        column_direction="default" 
                        column_direction_tablet="default" 
                        column_direction_phone="default" 
                        bottom_padding="10px" 
                        text_align="left" 
                        row_position="default" 
                        row_position_tablet="inherit" 
                        row_position_phone="inherit" 
                        overflow="visible" 
                        pointer_events="all"
                        ]
                        [vc_column_inner 
                            column_padding="no-extra-padding" 
                            column_padding_tablet="inherit" 
                            column_padding_phone="inherit" 
                            column_padding_position="all" 
                            column_element_direction_desktop="default" 
                            column_element_spacing="default" 
                            desktop_text_alignment="default" 
                            tablet_text_alignment="default" 
                            phone_text_alignment="default" 
                            background_color_opacity="1" 
                            background_hover_color_opacity="1" 
                            column_backdrop_filter="none" 
                            column_shadow="none" 
                            column_border_radius="none" 
                            column_link_target="_self" 
                            overflow="visible" 
                            gradient_direction="left_to_right" 
                            overlay_strength="0.3" 
                            width="1/2" 
                            tablet_width_inherit="default" 
                            animation_type="default" 
                            bg_image_animation="none" 
                            border_type="simple" 
                            column_border_width="none" 
                            column_border_style="solid"
                            ]
                            [text-with-icon 
                                icon_type="font_icon" 
                                icon="icon-sun" 
                                color="Accent-Color"
                                ]' . $location . '
                            [/text-with-icon]
                        [/vc_column_inner]
                        [vc_column_inner 
                            column_padding="no-extra-padding" 
                            column_padding_tablet="inherit" 
                            column_padding_phone="inherit" 
                            column_padding_position="all" 
                            column_element_direction_desktop="default" 
                            column_element_spacing="default" 
                            desktop_text_alignment="default" 
                            tablet_text_alignment="default" 
                            phone_text_alignment="default" 
                            background_color_opacity="1" 
                            background_hover_color_opacity="1" 
                            column_backdrop_filter="none" 
                            column_shadow="none" 
                            column_border_radius="none" 
                            column_link_target="_self" 
                            overflow="visible" 
                            gradient_direction="left_to_right" 
                            overlay_strength="0.3" 
                            width="1/2" 
                            tablet_width_inherit="default" 
                            animation_type="default" 
                            bg_image_animation="none" 
                            border_type="simple" 
                            column_border_width="none" 
                            column_border_style="solid"
                            ]
                            [text-with-icon 
                                icon_type="font_icon" 
                                icon="icon-credit-card" 
                                color="Accent-Color"
                                ]£' . $Price . ' entry
                            [/text-with-icon]
                        [/vc_column_inner]
                    [/vc_row_inner]
                    [vc_row_inner 
                        column_margin="default" 
                        column_direction="default" 
                        column_direction_tablet="default" 
                        column_direction_phone="default" 
                        bottom_padding="10px" 
                        text_align="left" 
                        row_position="default" 
                        row_position_tablet="inherit" 
                        row_position_phone="inherit" 
                        overflow="visible" 
                        pointer_events="all"
                        ]
                        [vc_column_inner 
                            column_padding="no-extra-padding" 
                            column_padding_tablet="inherit" 
                            column_padding_phone="inherit" 
                            column_padding_position="all" 
                            column_element_direction_desktop="default" 
                            column_element_spacing="default" 
                            desktop_text_alignment="default" 
                            tablet_text_alignment="default" 
                            phone_text_alignment="default" 
                            background_color_opacity="1" 
                            background_hover_color_opacity="1" 
                            column_backdrop_filter="none" 
                            column_shadow="none" 
                            column_border_radius="none" 
                            column_link_target="_self" 
                            overflow="visible" 
                            gradient_direction="left_to_right" 
                            overlay_strength="0.3" 
                            width="1/2" 
                            tablet_width_inherit="default" 
                            animation_type="default" 
                            bg_image_animation="none" 
                            border_type="simple" 
                            column_border_width="none" 
                            column_border_style="solid"
                            ]
                            [text-with-icon 
                                icon_type="font_icon" 
                                icon="linecon-icon-calendar" 
                                color="Accent-Color"
                                ]' . $day . '
                            [/text-with-icon]
                        [/vc_column_inner]
                        [vc_column_inner 
                            column_padding="no-extra-padding" 
                            column_padding_tablet="inherit" 
                            column_padding_phone="inherit" 
                            column_padding_position="all" 
                            column_element_direction_desktop="default" 
                            column_element_spacing="default" 
                            desktop_text_alignment="default" 
                            tablet_text_alignment="default" 
                            phone_text_alignment="default" 
                            background_color_opacity="1" 
                            background_hover_color_opacity="1" 
                            column_backdrop_filter="none" 
                            column_shadow="none" 
                            column_border_radius="none" 
                            column_link_target="_self" 
                            overflow="visible" 
                            gradient_direction="left_to_right" 
                            overlay_strength="0.3" 
                            width="1/2" 
                            tablet_width_inherit="default" 
                            animation_type="default" 
                            bg_image_animation="none" 
                            border_type="simple" 
                            column_border_width="none" 
                            column_border_style="solid"]
                                [text-with-icon 
                                    icon_type="font_icon" 
                                    icon="linecon-icon-clock" 
                                    color="Accent-Color"]
                                    ' . $time . '
                                [/text-with-icon]
                            [/vc_column_inner]
                    [/vc_row_inner]
                    [vc_row_inner 
                        column_margin="default" 
                        column_direction="default" 
                        column_direction_tablet="default" 
                        column_direction_phone="default" 
                        bottom_padding="25px" 
                        text_align="left" 
                        row_position="default" 
                        row_position_tablet="inherit" 
                        row_position_phone="inherit" 
                        overflow="visible" 
                        pointer_events="all"]
                        [vc_column_inner 
                            el_class="checkout_button"
                            column_padding="no-extra-padding" 
                            column_padding_tablet="inherit" 
                            column_padding_phone="inherit" 
                            column_padding_position="all" 
                            column_element_direction_desktop="default" 
                            column_element_spacing="default" 
                            desktop_text_alignment="default" 
                            tablet_text_alignment="default" 
                            phone_text_alignment="default" 
                            background_color_opacity="1" 
                            background_hover_color_opacity="1" 
                            column_backdrop_filter="none" 
                            column_shadow="none" 
                            column_border_radius="none" 
                            column_link_target="_self" 
                            overflow="visible" 
                            gradient_direction="left_to_right" 
                            overlay_strength="0.3" 
                            width="1/1" 
                            tablet_width_inherit="default" 
                            animation_type="default" 
                            enable_animation="true" 
                            animation="fade-in" 
                            animation_easing="default" 
                            bg_image_animation="none" 
                            border_type="simple" 
                            column_border_width="none" 
                            column_border_style="solid" 
                            delay="200" 
                            column_padding_type="default" 
                            gradient_type="default"]
                            '.$limitedSpaces.'
                            ' . $buttonContent . '
                            [/vc_column_inner]
                            [/vc_row_inner]
                        [/vc_column]';
    return $content;
}