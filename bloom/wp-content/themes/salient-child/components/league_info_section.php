<?php
function league_info_section_content_vc_content($api_url)
{
    $data = fetch_api_data($api_url);

    if (!empty($data) && !empty($data['registration'])) {
        $league_name = esc_html($data['name']);
        $venue_name = esc_html($data['registration']['venue']['name']);
        $address = $data['registration']['venue']['line1'] . ', ' . $data['registration']['venue']['town'] . ', ' . $data['registration']['venue']['postcode'];
        $league_address = esc_html($address);
        $date = esc_html($data['registration']['day']);
        $time = strval($data['registration']['startTime'] . ' - ' . $data['registration']['endTime']);
        $location = strtoupper(esc_html($data['registration']['situated']));
        $price =  $data['registration']['fixtureCharge'];
        $formattedDate = date("jS F Y", strtotime($data['registration']['launchDate']));
        $day = (new DateTime($data['registration']['launchDate']))->format('l');
        if ($data['registration']['status'] == 'next') {
            $badge_text = "New Season Open!";
            $bottom_text = '<span class="new-season-starts text-lowercase full-stop">New Season Starts </span><span class="new-season-starts-date">' . $day . ' ' . $formattedDate . '</span>';
        } else if ($data['registration']['status'] == 'live') {
            $badge_text = "Live Season Ongoing";
            $bottom_text = '<span class="new-season-starts text-lowercase full-stop">The latest season is currently ongoing. You can enter a team for next season below or contact the local coordinator for more details.</span>';
        }
    }

    $content = '[vc_column top_padding_desktop="4vw" bottom_padding_desktop="1vw" left_padding_desktop="4vw" constrain_group_101="yes" right_padding_desktop="4vw" top_padding_tablet="8vw" left_padding_tablet="8vw" constrain_group_103="yes" right_padding_tablet="8vw" bottom_margin_tablet="20" column_element_direction_desktop="default" column_element_spacing="10px" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color="#ffffff" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="20px" column_link_target="_self" column_position="default" advanced_gradient_angle="0" gradient_direction="left_to_right" overlay_strength="0.3" width="1/3" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid" gradient_type="default" column_padding_type="advanced"]
                    [nectar_badge display_tag="label" badge_style="default" bg_color_type="custom" bg_color_custom="#00df74" text_color="#ffffff" padding="small" border_radius="20px" display="block" text="' . $badge_text . '" margin_bottom="20"][nectar_responsive_text font_size_desktop="1.8vw" font_size_tablet="22px" font_line_height="1.3" text_direction="default"]
        <h3>' . $league_name . '</h3>
        [/nectar_responsive_text][vc_row_inner column_margin="default" column_direction="default" column_direction_tablet="default" column_direction_phone="default" text_align="left" row_position="default" row_position_tablet="inherit" row_position_phone="inherit" overflow="visible" pointer_events="all"][vc_column_inner right_padding_tablet="33vw" right_padding_phone="0" column_element_direction_desktop="default" column_element_spacing="default" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" gradient_direction="left_to_right" overlay_strength="0.3" width="1/1" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid" column_padding_type="advanced" gradient_type="default"][nectar_responsive_text font_size_min="14px" font_size_max="20px" font_size_desktop="1.1vw" font_line_height="1.5" text_direction="default"]' . $venue_name . ', ' . $league_address . '[/nectar_responsive_text][/vc_column_inner][/vc_row_inner][divider line_type="No Line" custom_height="25px"][vc_row_inner column_margin="default" column_direction="default" column_direction_tablet="default" column_direction_phone="default" bottom_padding="25px" text_align="left" row_position="default" row_position_tablet="inherit" row_position_phone="inherit" overflow="visible" pointer_events="all"][vc_column_inner column_padding="no-extra-padding" column_padding_tablet="inherit" column_padding_phone="inherit" column_padding_position="all" column_element_direction_desktop="default" column_element_spacing="default" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" gradient_direction="left_to_right" overlay_strength="0.3" width="1/2" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid"][text-with-icon icon_type="font_icon" icon="linecon-icon-calendar" color="Extra-Color-3"]' . $day . '[/text-with-icon][/vc_column_inner][vc_column_inner column_padding="no-extra-padding" column_padding_tablet="inherit" column_padding_phone="inherit" column_padding_position="all" column_element_direction_desktop="default" column_element_spacing="default" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" gradient_direction="left_to_right" overlay_strength="0.3" width="1/2" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid"][text-with-icon icon_type="font_icon" icon="linecon-icon-clock" color="Extra-Color-3"]' . $time  . '[/text-with-icon][/vc_column_inner][/vc_row_inner][vc_row_inner column_margin="default" column_direction="default" column_direction_tablet="default" column_direction_phone="default" bottom_padding="25px" text_align="left" row_position="default" row_position_tablet="inherit" row_position_phone="inherit" overflow="visible" pointer_events="all"][vc_column_inner column_padding="no-extra-padding" column_padding_tablet="inherit" column_padding_phone="inherit" column_padding_position="all" column_element_direction_desktop="default" column_element_spacing="default" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" gradient_direction="left_to_right" overlay_strength="0.3" width="1/2" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid"][text-with-icon icon_type="font_icon" icon="icon-sun" color="Extra-Color-3"]' . $location . '[/text-with-icon][/vc_column_inner][vc_column_inner column_padding="no-extra-padding" column_padding_tablet="inherit" column_padding_phone="inherit" column_padding_position="all" column_element_direction_desktop="default" column_element_spacing="default" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" gradient_direction="left_to_right" overlay_strength="0.3" width="1/2" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid"][text-with-icon icon_type="font_icon" icon="icon-credit-card" color="Extra-Color-3"]£' . $price . ' per team[/text-with-icon][/vc_column_inner][/vc_row_inner][vc_row_inner column_margin="default" column_direction="default" column_direction_tablet="default" column_direction_phone="default" text_align="left" row_position="default" row_position_tablet="inherit" row_position_phone="inherit" overflow="visible" pointer_events="all"][vc_column_inner column_padding="no-extra-padding" column_padding_tablet="inherit" column_padding_phone="inherit" column_padding_position="all" column_element_direction_desktop="default" column_element_spacing="default" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" gradient_direction="left_to_right" overlay_strength="0.3" width="1/1" tablet_width_inherit="default" animation_type="default" enable_animation="true" animation="fade-in" animation_easing="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid" delay="200" column_padding_type="default" gradient_type="default"][nectar_responsive_text font_size_min="14px" font_size_max="20px" font_size_desktop="1.1vw" font_line_height="1.5" text_direction="default"]' . $bottom_text .  '[/nectar_responsive_text][nectar_cta btn_style="arrow-animation" heading_tag="h5" border_radius="50" button_color="extra-color-gradient-1" text_color="#ffffff" button_border_thickness="0px" link_type="regular" alignment="left" alignment_tablet="default" alignment_phone="default" constrain_group_3="yes" constrain_group_4="yes" display="block" display_tablet="inherit" display_phone="inherit" link_text="Enter your team here" margin_top="35" padding_top="15" padding_bottom="15" padding_left="35" padding_right="35" url="https://lockerroom.bloomnetball.co.uk/"][/vc_column_inner][/vc_row_inner][/vc_column]';

        return $content;
    }
