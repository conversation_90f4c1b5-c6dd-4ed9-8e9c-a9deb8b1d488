<?php
function league_teams_section_vc_content($api_url) {
    $data = fetch_api_data($api_url);

    $teams = !empty($data['live']['tables']) ? $data['live']['tables'] : array();

    $session_name = !empty($data['registration']['name']) ? esc_html($data['registration']['name']) : 'League Session';

    $content = '[vc_column el_class="league_table_section" top_padding_desktop="4vw" constrain_group_100="yes" bottom_padding_desktop="4vw" left_padding_desktop="4vw" constrain_group_101="yes" right_padding_desktop="4vw" top_padding_tablet="8vw" constrain_group_102="yes" bottom_padding_tablet="8vw" left_padding_tablet="8vw" constrain_group_103="yes" right_padding_tablet="8vw" bottom_margin_tablet="20" column_element_direction_desktop="default" column_element_spacing="0px" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color="#ffffff" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" column_shadow="none" column_border_radius="15px" column_link_target="_self" column_position="default" overflow="hidden" advanced_gradient_angle="0" gradient_direction="left_to_right" overlay_strength="0.3" width="7/12" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_color="rgba(10,10,10,0.1)" column_border_style="solid" gradient_type="default" column_padding_type="advanced"][vc_row_inner column_margin="default" column_direction="default" column_direction_tablet="default" column_direction_phone="default" text_align="left" row_position="default" row_position_tablet="inherit" row_position_phone="inherit" overflow="visible" pointer_events="all"][vc_column_inner right_padding_desktop="25%" right_padding_tablet="33vw" right_padding_phone="0" column_element_direction_desktop="default" column_element_spacing="10px" desktop_text_alignment="default" tablet_text_alignment="default" phone_text_alignment="default" background_color_opacity="1" background_hover_color_opacity="1" column_backdrop_filter="none" font_color="#0a0000" column_shadow="none" column_border_radius="none" column_link_target="_self" overflow="visible" advanced_gradient_angle="0" gradient_direction="left_to_right" overlay_strength="0.3" width="1/1" tablet_width_inherit="default" animation_type="default" bg_image_animation="none" border_type="simple" column_border_width="none" column_border_style="solid" gradient_type="default" column_padding_type="advanced"][nectar_responsive_text font_size_desktop="1.8vw" font_size_tablet="22px" font_line_height="1.3" text_direction="default"]
                <h3>' . $session_name . '</h3>
                [/nectar_responsive_text][nectar_responsive_text font_size_min="14px" font_size_max="20px" font_size_desktop="1.1vw" font_line_height="1.5" text_direction="default"]You can find out more information by visiting the <a href="https://lockerroom.bloomnetball.co.uk/">Locker Room</a>[/nectar_responsive_text][/vc_column_inner][/vc_row_inner]';

    $content .= '[vc_row_inner][vc_column_inner desktop_text_alignment="center" tablet_text_alignment="center" phone_text_alignment="center"][nectar_responsive_text font_size_desktop="1.5vw" font_line_height="1.2" text_direction="default"]
    <h4>League Teams Table</h4>
    [/nectar_responsive_text][/vc_column_inner][/vc_row_inner]';


    $content .= '[tabbed_section style="minimal_flexible" tab_color="Accent-Color" vs_content_animation="fade" vs_navigation_alignment="left" vs_navigation_width="regular" vs_navigation_spacing="15px" vs_tab_spacing="5%" icon_size="32" el_class="league_tabs"]';

    foreach ($teams as $division => $teamList) {
        $content .= '[tab icon_family="none" title="' . esc_html($division) . '"][vc_row_inner equal_height="yes" top_padding="1%" bottom_padding="3%"][vc_column_inner el_class="league_teams_tables"]';

        $content .= '[nectar_horizontal_list_item columns="11" 
            col_1_text_align="left" 
            col_2_text_align="center" 
            col_3_text_align="center" 
            col_4_text_align="center" 
            col_5_text_align="center" 
            col_6_text_align="center"
            col_7_text_align="center" 
            col_8_text_align="center" 
            col_9_text_align="center" 
            col_10_text_align="center" 
            col_11_text_align="center" 
            col_1_content="Teams" 
            col_2_content="P" 
            col_3_content="W" 
            col_4_content="D" 
            col_5_content="L" 
            col_6_content="F" 
            col_7_content="A" 
            col_8_content="GD" 
            col_9_content="Pts" 
            col_10_content="BP" 
            col_11_content="Tot"]';

        foreach ($teamList as $team) {
            $team_name = esc_html($team['teamID']);
            $team_played = intval($team['played']);
            $team_won = intval($team['won']);
            $team_drawn = intval($team['drawn']);
            $team_lost = intval($team['lost']);
            $team_for = intval($team['for']);
            $team_against = intval($team['against']);
            $team_gd = intval($team['gd']);
            $team_points = intval($team['points']);
            $team_bp = intval($team['bp']);
            $team_total = intval($team['total']);

            $content .= '[nectar_horizontal_list_item columns="11" 
            col_1_text_align="left" 
            col_2_text_align="center" 
            col_3_text_align="center" 
            col_4_text_align="center" 
            col_5_text_align="center" 
            col_6_text_align="center"
            col_7_text_align="center" 
            col_8_text_align="center" 
            col_9_text_align="center" 
            col_10_text_align="center" 
            col_11_text_align="center" 
            col_1_content="' . $team_name . '" 
            col_2_content="' . $team_played . '" 
            col_3_content="' . $team_won . '" 
            col_4_content="' . $team_drawn . '" 
            col_5_content="' . $team_lost . '" 
            col_6_content="' . $team_for . '" 
            col_7_content="' . $team_against . '" 
            col_8_content="' . $team_gd . '" 
            col_9_content="' . $team_points . '" 
            col_10_content="' . $team_bp . '" 
            col_11_content="' . $team_total . '"]';
        }

        $content .= '[/vc_column_inner][/vc_row_inner][/tab]';
    }

    $content .= '[/tabbed_section][/vc_column]';

    return $content;
}
