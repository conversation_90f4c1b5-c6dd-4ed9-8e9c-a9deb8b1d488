let allSessions = []; // Variable to store all sessions after the initial API load

// Initial API request to load all sessions
fetchData();

// Form search button logic
const searchButton = document.querySelector(".ff-btn-submit");

// Add event listener for search button click
searchButton.addEventListener("click", function (event) {
  event.preventDefault(); // Prevent form submission

  // Get search input value
  const postcodeOrTown = document
    .querySelector('input[name="input_text"]')
    .value.trim()
    .toLowerCase();

  // If input is not empty, filter the stored sessions based on user input
  if (postcodeOrTown !== "") {
    // Check if input is likely a postcode (contains digits)
    const isPostcode = /\d/.test(postcodeOrTown);

    if (isPostcode) {
      // If input is a postcode, get its coordinates and find nearest sessions
      getPostcodeLocation(postcodeOrTown).then((coords) => {
        if (coords) {
          // Find the 3 nearest sessions based on postcode location
          const nearestSessions = findNearestSessions(
            coords.lat,
            coords.lng,
            10
          );
          updateList(nearestSessions); // Show the nearest sessions
        } else {
          alert("Could not find location for the entered postcode.");
        }
      });
    } else {
      // If input is not a postcode, search by town
      const filteredSessions = allSessions.filter((session) => {
        const townMatch = session.town.toLowerCase().includes(postcodeOrTown);
        return townMatch; // Matches town names
      });

      if (filteredSessions.length > 0) {
        updateList(filteredSessions); // Show matching sessions by town
      } else {
        alert("No sessions found for the entered town.");
      }
    }
  } else {
    alert("Please enter a valid postcode or town.");
  }
});

// Function to fetch data from the live leagues API
function fetchData() {
  // Make API request
  fetch(`https://api.leagues4you.co.uk/liveLeagues`)
    .then((response) => response.json())
    .then((data) => {
      // Store the fetched sessions in allSessions variable
      allSessions = data;

      // Immediately show the first 3 sessions, don't wait for geolocation
      updateList(allSessions.slice(0, 10));

      // Check for geolocation permission asynchronously, show nearest sessions when available
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            // If user allows geolocation, find the 3 nearest sessions
            const nearestSessions = findNearestSessions(
              position.coords.latitude,
              position.coords.longitude,
              10
            );
            updateList(nearestSessions); // Show the 3 closest sessions
          },
          () => {
            // If geolocation is denied, we already showed the first 3 sessions
            console.log("Geolocation denied or unavailable");
          }
        );
      }
    })
    .catch((error) => {
      console.error("Error fetching data:", error);
    });
}

// Function to find the nearest sessions based on latitude and longitude
function findNearestSessions(lat, lng, count) {
  // Calculate distances for all sessions
  const sessionsWithDistance = allSessions.map((session) => {
    const sessionLat = session.coordinates.lat;
    const sessionLng = session.coordinates.lng;
    const distance = calculateDistance(lat, lng, sessionLat, sessionLng);

    return { ...session, distance }; // Add distance to the session object
  });

  // Sort sessions by distance and return the closest `count` sessions
  return sessionsWithDistance
    .sort((a, b) => a.distance - b.distance)
    .slice(0, count);
}

// Function to calculate the distance between two coordinates (Haversine formula)
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371; // Radius of the Earth in km
  const dLat = degreesToRadians(lat2 - lat1);
  const dLng = degreesToRadians(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(degreesToRadians(lat1)) *
      Math.cos(degreesToRadians(lat2)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in km
}

function degreesToRadians(degrees) {
  return degrees * (Math.PI / 180);
}

// Function to get latitude and longitude from postcode using a geocoding API
async function getPostcodeLocation(postcode) {
  const response = await fetch(
    `https://api.postcodes.io/postcodes/${encodeURIComponent(postcode)}`
  );
  const data = await response.json();

  if (data.status === 200) {
    const { latitude, longitude } = data.result;
    return { lat: latitude, lng: longitude };
  } else {
    return null;
  }
}

function removeNectarItems() {
  const nectarItems = document.querySelectorAll(".nectar-hor-list-item");
  nectarItems.forEach((item) => item.remove());
}

// Function to update the session list
function updateList(sessions) {
  // Clear the list container
  const wrappers = document.querySelectorAll(".wpb_wrapper");
  const listContainer = wrappers[12]; // Select the second wpb_wrapper
  removeNectarItems();

  const headerRow = document.createElement("div");
  headerRow.innerHTML = `
   	<div class="nectar-hor-list-item " data-hover-effect="none" data-br="0px" data-font-family="h4" data-color="accent-color" data-columns="3" data-column-layout="even"><div class="nectar-list-item" data-icon="false" data-text-align="left"><h4>Name</h4></div><div class="nectar-list-item" data-text-align="left"><h4>Nearest League</h4></div><div class="nectar-list-item" data-text-align="left"><h4>Email</h4></div></div>
  `;
  listContainer.appendChild(headerRow);

  // Loop through the sessions and create list items
  sessions.forEach((session) => {
    const coordinator = session.coordinator;
    const newItem = createNectarHorizontalListItem(session);

    // Append the new list item to the container
    listContainer.appendChild(newItem);
  });
}

// Function to create a Nectar Horizontal List Item with 3 columns
function createNectarHorizontalListItem(session) {
  const coordinator = session.coordinator;
  const fullName = `${coordinator?.fullName ?? "N/A"}`;
  const email = `${coordinator?.email ?? "N/A"}`;

  // Create a new div for the list item
  const newItem = document.createElement("div");
  newItem.classList.add("nectar-hor-list-item");
  newItem.setAttribute("data-hover-effect", "none");
  newItem.setAttribute("data-br", "0px");
  newItem.setAttribute("data-font-family", "h4");
  newItem.setAttribute("data-color", "accent-color");
  newItem.setAttribute("data-columns", "3"); // 3 columns for the layout
  newItem.setAttribute("data-column-layout", "even");

  // Column 1: Location
  const columnOne = createDiv("nectar-list-item", fullName);
  columnOne.style.textAlign = "left";

  console.log(session.leagues);

  // Column 2: leagues
  const leagueNames = session.leagues
    .map(
      (league) => `<a href="/social-leagues/${league.url}">${league.name}</a>`
    )
    .join("<br/> ");
  const columnTwo = createDiv(
    "nectar-list-item",
    `${leagueNames}<br/>${session.postcode}`
  );
  columnTwo.style.textAlign = "left";

  const columnThree = createDiv(
    "nectar-list-item",
    `<a href="mailto:${email}">${email}</a>`
  );
  columnThree.style.textAlign = "left";

  // Append columns to the new item
  newItem.appendChild(columnOne);
  newItem.appendChild(columnTwo);
  newItem.appendChild(columnThree);

  return newItem;
}

// Helper function to create a div with a specific class and text content
function createDiv(className, textContent) {
  const div = document.createElement("div");
  div.className = className;
  div.innerHTML = textContent;
  return div;
}

function createAnchor(className, href, innerHTML, color = "") {
  const anchor = document.createElement("a");
  anchor.className = className;
  anchor.href = href;
  anchor.innerHTML = innerHTML;
  anchor.style.color = color;
  return anchor;
}
