let allSessions = []; // Variable to store all sessions after the initial API load

// Initial API request to load all sessions (search=full)
fetchData("full");

// Form search button logic
const searchButton = document.querySelector(".ff-btn-submit");

// Add event listener for search button click
searchButton.addEventListener("click", function (event) {
  event.preventDefault(); // Prevent form submission

  // Get search input value
  const postcodeOrTown = document
    .querySelector('input[name="input_text"]')
    .value.trim()
    .toLowerCase();

  // If input is not empty, filter the stored sessions based on user input
  if (postcodeOrTown !== "") {
    const filteredSessions = filterSessions(postcodeOrTown); // Filter stored sessions
    updateList(filteredSessions); // Update the list with filtered sessions
  } else {
    //alert('Please enter a postcode or town!');
    updateList(allSessions); // Revert to the default session list
  }
});

// Function to fetch data from API
function fetchData(searchText) {
  // Make API request
  fetch(`/api.php?search=${encodeURIComponent(searchText)}`)
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Store the fetched sessions in allSessions variable
        allSessions = data.sessions;

        // Initially display all sessions
        updateList(allSessions);
      } else {
        alert("No sessions found!");
      }
    })
    .catch((error) => {
      console.error("Error fetching data:", error);
    });
}

// Function to filter sessions based on search input
function filterSessions(searchText) {
  return allSessions.filter((session) => {
    // Check if any part of the session's venue, town, or postcode matches the search text
    const venue = session.venue.name.toLowerCase();
    const town = session.venue.town.toLowerCase();
    const postcode = session.venue.postcode.toLowerCase();

    return (
      venue.includes(searchText) ||
      town.includes(searchText) ||
      postcode.includes(searchText)
    );
  });
}

function removeNectarItems() {
  const nectarItems = document.querySelectorAll(".nectar-hor-list-item");
  nectarItems.forEach((item) => item.remove());
}

// Function to update the session list
function updateList(sessions) {
  // Clear the list container
  const listContainer = document.querySelector(".wpb_wrapper");
  removeNectarItems();

  // Loop through the filtered sessions and create list items
  sessions.forEach((session) => {
    const location = `<b>${session.venue.name}</b><br/> ${session.venue.town}, ${session.venue.postcode}`;
    const price = session.charges.cost;
    const formattedPrice = parseFloat(price).toString();
    const dateAndPrice = `${session.timing.dateFormatted}<br>${session.timing.timeFormatted}<br>£${formattedPrice}`;
    const id = session.id;
    const newItem = createNectarHorizontalListItem(
      location,
      dateAndPrice,
      session
    );

    // Append the new list item to the container
    listContainer.appendChild(newItem);
  });
}

// Function to create a Nectar Horizontal List Item with 5 column
function createNectarHorizontalListItem(location, dateAndPrice, session) {
  const isMobile = isMobileView(); // Dynamically determine mobile view
  let c2c_url = `${window.location.origin}/couch2court-details/${session.slug}`;
  const newItem = document.createElement("div");

  // Set common attributes for the container
  newItem.classList.add(
    isMobile ? "nectar-hor-list-container" : "nectar-hor-list-item",
    "has-btn",
    "c2c_list_js"
  );
  newItem.setAttribute("data-hover-effect", "none");
  newItem.setAttribute("data-br", "0px");
  newItem.setAttribute("data-font-family", "h4");
  newItem.setAttribute("data-color", "accent-color");
  newItem.setAttribute("data-columns", "5");
  newItem.setAttribute("data-column-layout", "even");
  if (isMobile) {
    newItem.style.padding = "20px 0px";
  } else {
    newItem.style.display = "flex";
    newItem.style.justifyContent = "space-between";
  }

  // Column 1: Location
  const columnOne = session.name.includes("WILDCARD")
    ? createDiv("nectar-list-item", location)
    : createAnchor("nectar-list-item", c2c_url, location);
  columnOne.style.boxSizing = "border-box";
  columnOne.style.textAlign = isMobile ? "left" : "inherit";
  if (!isMobile) columnOne.style.width = "20%";

  // Column 2: Date, time, and price
  const columnTwo = createDiv("nectar-list-item", dateAndPrice);
  columnTwo.style.boxSizing = "border-box";
  columnTwo.style.textAlign = isMobile ? "left" : "center";
  if (!isMobile) columnTwo.style.width = "20%";

  // Column 3: Coordinator information
  const firstName = session.coordinator.name.split(" ")[0];
  const coordinatorInfo = `${firstName} is running the ${session.venue.town} Couch2Court®. If you have any more burning questions, you can <a href="mailto:${session.coordinator.email}">contact them here</a>.`;
  const columnThree = document.createElement("div");
  columnThree.classList.add("nectar-list-item");
  columnThree.classList.add("c2c-coordinator-info");
  columnThree.setAttribute("data-text-align", "left");
  columnThree.innerHTML = coordinatorInfo;
  columnThree.style.boxSizing = "border-box";
  if (!isMobile) columnThree.style.width = "30%";

  // Column 4: Book now button or Fully Booked message
  const columnFour = document.createElement("div");
  columnFour.classList.add("nectar-list-item");
  columnFour.setAttribute("data-text-align", isMobile ? "left" : "center");
  columnFour.style.boxSizing = "border-box";
  if (!isMobile) columnFour.style.width = "15%";

  if (session.fullyBooked) {
    const fullyBookedMessage = document.createElement("div");
    fullyBookedMessage.textContent = "Fully Booked";
    fullyBookedMessage.style.color = "red";
    fullyBookedMessage.style.fontWeight = "bold";
    columnFour.appendChild(fullyBookedMessage);
  } else {
    const form = document.createElement("form");
    form.method = "POST";
    form.action = "/checkout";

    const hiddenInput = document.createElement("input");
    hiddenInput.type = "hidden";
    hiddenInput.name = "tasterID";
    hiddenInput.value = session.id;

    const bookButton = document.createElement("button");
    bookButton.type = "submit";
    bookButton.textContent = "Checkout";
    bookButton.classList.add("btn", "btn-primary");

    form.appendChild(hiddenInput);
    form.appendChild(bookButton);
    columnFour.appendChild(form);
  }

  // Column 5: More details link
  const columnFive = createAnchor("nectar-list-item", c2c_url, "more details");
  columnFive.style.boxSizing = "border-box";
  columnFive.style.color = "#00df74";
  if (!isMobile) columnFive.style.width = "15%";

  // Append columns based on the layout
  if (isMobile) {
    newItem.appendChild(columnOne);
    newItem.appendChild(columnFour);
    newItem.appendChild(columnTwo);
    newItem.appendChild(columnFive);
    newItem.appendChild(columnThree);
  } else {
    newItem.appendChild(columnOne);
    newItem.appendChild(columnTwo);
    newItem.appendChild(columnThree);
    newItem.appendChild(columnFour);
    newItem.appendChild(columnFive);
  }

  return newItem;
}

function isMobileView() {
  return window.innerWidth <= 575;
}

// Helper function to create a div with a specific class and text content
function createDiv(className, textContent) {
  const div = document.createElement("div");
  div.className = className;
  div.innerHTML = textContent;
  return div;
}

function createAnchor(className, href, innerHTML) {
  const anchor = document.createElement("a");
  anchor.className = className;
  anchor.href = href;
  anchor.innerHTML = innerHTML;
  anchor.style.color = "black";
  return anchor;
}
