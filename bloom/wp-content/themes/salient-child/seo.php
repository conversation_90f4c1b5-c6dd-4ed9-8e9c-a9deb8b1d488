<?php
// Custom Title for Social Leagues
add_filter('wds_title', 'custom_social_leagues_meta_title');
function custom_social_leagues_meta_title($title) {
    if (is_page('social-leagues')) {
        // Get the league slug from the query variable
        $league_slug = get_query_var('league_slug');

        // If no slug is present, return the default title
        if (!$league_slug) {
            return $title;
        }

        // Construct the API URL
        $api_url = "https://public.v2.api.leagues4you.co.uk/league-info/$league_slug";

        // Fetch data using the caching-enabled function
        $data = fetch_api_data($api_url);

        // Extract the league name if data is available
        $league_name = !empty($data['name']) ? esc_html($data['name']) : 'League Name Not Found';

        // Set the custom title
        return "{$league_name} | Bloom Netball";
    }

    return $title;
}

// Custom Meta Description for Social Leagues
add_filter('wds_metadesc', 'custom_social_leagues_meta_description');
function custom_social_leagues_meta_description($description) {
    if (is_page('social-leagues')) {
        // Get the league slug from the query variable
        $league_slug = get_query_var('league_slug');

        // If no slug is present, return the default description
        if (!$league_slug) {
            return $description;
        }

        // Construct the API URL
        $api_url = "https://public.v2.api.leagues4you.co.uk/league-info/$league_slug";

        // Fetch data using the caching-enabled function
        $data = fetch_api_data($api_url);

        // Extract the league name if data is available
        $league_name = !empty($data['name']) ? esc_html($data['name']) : 'League Name Not Found';

        // Set the custom meta description
        return "Play, grow, flourish in our social {$league_name}. Perfect for players new to netball or returning after time away from the game.";
    }

    return $description;
}
