<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<link rel='stylesheet' id='custom-typekit-css-css' href='https://use.typekit.net/nvs8mrr.css?ver=1.0.18' type='text/css' media='all' />

<?php
$sports = json_decode(file_get_contents("https://api.leagues4you.co.uk/sports"), true);
?>

<body class="<?= basename(__FILE__, ".php") ?>">

    <?php
    $lat = 52.3555;
    $lng = -1.1743;
    $zoom = 7;
    if (isset($_GET['lat']) && $_GET['lat']) {
        $lat = $_GET['lat'];
        $lng = $_GET['lng'];
        $zoom = 10;
    }

    if (isset($_GET['location']) && $_GET['location']) {
        // $searchUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=".urlencode($_GET['location']).",UK&key=AIzaSyA1cyX8_stu_Qhl-_f6BVYhXxGxMN5pErc";
        $searchUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($_GET['location']) . ",UK&key=AIzaSyDpeui0fOCNqWx5dDQCtJLJMm0biUUYRsc";
        // echo "<!-- Request {$_GET['location']}-->";
        // echo "<!-- Search URL $searchUrl -->";
        $searchRlt = json_decode(file_get_contents($searchUrl), true);
        if (isset($searchRlt['results'][0]['geometry']['location'])) {
            $lat = $searchRlt['results'][0]['geometry']['location']['lat'];
            $lng = $searchRlt['results'][0]['geometry']['location']['lng'];
            $zoom = 10;
        }
        $url = "https://api.leagues4you.co.uk/websearchlocation";
        $ch = curl_init($url);
        # Setup request to send json via POST.
        $payload = json_encode([
            "location" => $_GET['location'],
            "sport" => $_GET['sport'],
            "ip" => $_SERVER['REMOTE_ADDR'],
            "agent" => $_SERVER['HTTP_USER_AGENT'],
            "lat" => $lat,
            "lng" => $lng
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        # Return response instead of printing.
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        # Send request.
        $result = curl_exec($ch);
        curl_close($ch);
        // echo "<!-- Result ";
        // print_r($searchRlt);
        // echo " -->";
    } ?>
    <script>
        console.log("Lat <?= $lat ?>, Lng <?= $lng ?>");
        var centerLatLng = {
            lat: <?= $lat ?>,
            lng: <?php echo $lng; ?>
        }
    </script>
    <main style="min-height: 100vh;">
        <div class="row leagues-map row-top-padding d-none">
            <!--- filter search-->
            <div class="col-md-4 col-sm-12 grey-bg">
                <br>
                <h3>FIND YOUR NEAREST LEAGUE</h3>
                <br>
                <form action="/league-search/" method="get">
                    <div class="form-group">
                        <input type="text" name="location" class="form-control mb-3" placeholder="Enter town or postcode" value="<?= isset($_GET['location']) ? $_GET['location'] : null ?>">
                        <select id="chosenSport" name="sport" class="sport">
                            <option value="">All sports</option><?php
                                                                if (isset($sports) && $sports) {
                                                                    foreach ($sports as $sport) { ?>
                                    <option value="<?= $sport['id'] ?>" <?php if (isset($_GET['sport']) && $_GET['sport'] == $sport['id']) echo ' selected'; ?>><?= $sport['name'] ?></option><?php
                                                                                                                                                                                            }
                                                                                                                                                                                        } ?>
                        </select>
                    </div>
                    <input type="submit" id="load-btn" class="btn btn-primary load-btn mb-2" value="Submit">
                </form>
            </div>
        </div>
        <!--- end filter search-->
        <!-- map -->
        <div id="map" style="height: 100vh;"></div>
        <!-- end map -->
    </main>
    <!-- Modal -->
    <div class="modal" tabindex="-1" role="dialog" id="leagueModal">
        <div class="modal-dialog leagueModal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div>
                        <h4 class="modal-title"></h4>
                        <h5 class="modal-address"></h5>
                    </div>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"></span>
                    </button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs mb-2" id="myTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link d-none" id="netball-tab" data-toggle="tab" href="#netball-panel" role="tab" aria-controls="netball-panel" aria-selected="true">NETBALL</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-none" id="mixednetball-tab" data-toggle="tab" href="#mixednetball-panel" role="tab" aria-controls="mixednetball-panel" aria-selected="false">MIXED NETBALL</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-none" id="outdoorrounders-tab" data-toggle="tab" href="#outdoorrounders-panel" role="tab" aria-controls="outdoorrounders-panel" aria-selected="false">OUTDOOR ROUNDERS</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-none" id="indoorrounders-tab" data-toggle="tab" href="#indoorrounders-panel" role="tab" aria-controls="indoorrounders-panel" aria-selected="false">INDOOR ROUNDERS</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link d-none" id="ladiesfootball-tab" data-toggle="tab" href="#ladiesfootball-panel" role="tab" aria-controls="ladiesfootball-panel" aria-selected="false">LADIES FOOTBALL</a>
                        </li>
                    </ul>
                    <div class="tabs tab-content">
                        <div class="tab-pane fade active show" id="netball-panel" role="tabpanel" aria-labelledby="netball-tab">
                            <div class="days-of-the-week">
                                <div class="day">
                                    <h6>Mon</h6>
                                    <div class="day-status modal-mo"></div>
                                </div>
                                <div class="day">
                                    <h6>Tue</h6>
                                    <div class="day-status modal-tu"></div>
                                </div>
                                <div class="day">
                                    <h6>Wed</h6>
                                    <div class="day-status modal-we"></div>
                                </div>
                                <div class="day">
                                    <h6>Thu</h6>
                                    <div class="day-status modal-th"></div>
                                </div>
                                <div class="day active">
                                    <h6>Fri</h6>
                                    <div class="day-status modal-fr"></div>
                                </div>
                                <div class="day">
                                    <h6>Sat</h6>
                                    <div class="day-status modal-sa"></div>
                                </div>
                                <div class="day">
                                    <h6>Sun</h6>
                                    <div class="day-status modal-su"></div>
                                </div>
                            </div>
                            <!--<div class="col-md-12 col-sm-12 mt-2 venue-thumbnail">-->
                            <!--    <img class="modal-sportImage" src="" alt="Venue image">-->
                            <!--</div>-->
                        </div>
                        <div class="tab-pane fade" id="mixednetball-panel" role="tabpanel" aria-labelledby="mixednetball-tab">
                            <div class="days-of-the-week">
                                <div class="day">
                                    <h6>Mon</h6>
                                    <div class="day-status modal-mo"></div>
                                </div>
                                <div class="day">
                                    <h6>Tue</h6>
                                    <div class="day-status modal-tu"></div>
                                </div>
                                <div class="day">
                                    <h6>Wed</h6>
                                    <div class="day-status modal-we"></div>
                                </div>
                                <div class="day">
                                    <h6>Thu</h6>
                                    <div class="day-status modal-th"></div>
                                </div>
                                <div class="day active">
                                    <h6>Fri</h6>
                                    <div class="day-status modal-fr"></div>
                                </div>
                                <div class="day">
                                    <h6>Sat</h6>
                                    <div class="day-status modal-sa"></div>
                                </div>
                                <div class="day">
                                    <h6>Sun</h6>
                                    <div class="day-status modal-su"></div>
                                </div>
                            </div>
                            <!--<div class="col-md-12 col-sm-12 mt-2 venue-thumbnail">-->
                            <!--    <img class="modal-sportImage" src="" alt="Venue image">-->
                            <!--</div>-->
                        </div>
                        <div class="tab-pane fade" id="outdoorrounders-panel" role="tabpanel" aria-labelledby="outdoorrounders-tab">
                            <div class="days-of-the-week">
                                <div class="day">
                                    <h6>Mon</h6>
                                    <div class="day-status modal-mo"></div>
                                </div>
                                <div class="day">
                                    <h6>Tue</h6>
                                    <div class="day-status modal-tu"></div>
                                </div>
                                <div class="day">
                                    <h6>Wed</h6>
                                    <div class="day-status modal-we"></div>
                                </div>
                                <div class="day">
                                    <h6>Thu</h6>
                                    <div class="day-status modal-th"></div>
                                </div>
                                <div class="day active">
                                    <h6>Fri</h6>
                                    <div class="day-status modal-fr"></div>
                                </div>
                                <div class="day">
                                    <h6>Sat</h6>
                                    <div class="day-status modal-sa"></div>
                                </div>
                                <div class="day">
                                    <h6>Sun</h6>
                                    <div class="day-status modal-su"></div>
                                </div>
                            </div>
                            <!--<div class="col-md-12 col-sm-12 mt-2 venue-thumbnail">-->
                            <!--    <img class="modal-sportImage" src="" alt="Venue image">-->
                            <!--</div>-->
                        </div>
                        <div class="tab-pane fade" id="indoorrounders-panel" role="tabpanel" aria-labelledby="indoorrounders-tab">
                            <div class="days-of-the-week">
                                <div class="day">
                                    <h6>Mon</h6>
                                    <div class="day-status modal-mo"></div>
                                </div>
                                <div class="day">
                                    <h6>Tue</h6>
                                    <div class="day-status modal-tu"></div>
                                </div>
                                <div class="day">
                                    <h6>Wed</h6>
                                    <div class="day-status modal-we"></div>
                                </div>
                                <div class="day">
                                    <h6>Thu</h6>
                                    <div class="day-status modal-th"></div>
                                </div>
                                <div class="day active">
                                    <h6>Fri</h6>
                                    <div class="day-status modal-fr"></div>
                                </div>
                                <div class="day">
                                    <h6>Sat</h6>
                                    <div class="day-status modal-sa"></div>
                                </div>
                                <div class="day">
                                    <h6>Sun</h6>
                                    <div class="day-status modal-su"></div>
                                </div>
                            </div>
                            <!--<div class="col-md-12 col-sm-12 mt-2 venue-thumbnail">-->
                            <!--    <img class="modal-sportImage" src="" alt="Venue image">-->
                            <!--</div>-->
                        </div>

                        <div class="tab-pane fade" id="ladiesfootball-panel" role="tabpanel" aria-labelledby="ladiesfootball-tab">
                            <div class="days-of-the-week">
                                <div class="day">
                                    <h6>Mon</h6>
                                    <div class="day-status modal-mo"></div>
                                </div>
                                <div class="day">
                                    <h6>Tue</h6>
                                    <div class="day-status modal-tu"></div>
                                </div>
                                <div class="day">
                                    <h6>Wed</h6>
                                    <div class="day-status modal-we"></div>
                                </div>
                                <div class="day">
                                    <h6>Thu</h6>
                                    <div class="day-status modal-th"></div>
                                </div>
                                <div class="day active">
                                    <h6>Fri</h6>
                                    <div class="day-status modal-fr"></div>
                                </div>
                                <div class="day">
                                    <h6>Sat</h6>
                                    <div class="day-status modal-sa"></div>
                                </div>
                                <div class="day">
                                    <h6>Sun</h6>
                                    <div class="day-status modal-su"></div>
                                </div>
                            </div>
                            <!--<div class="col-md-12 col-sm-12 mt-2 venue-thumbnail">-->
                            <!--    <img class="modal-sportImage" src="" alt="Venue image">-->
                            <!--</div>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBS3yImTCvOh4PaCfu7ij_bcTwspjzOWJA"></script>
    <script>
        var map;
        var options = {
            zoom: <?= $zoom ?>, // map zoom level
            center: new google.maps.LatLng(centerLatLng), // map centre
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            streetViewControl: false,
            mapTypeControl: false,
        }
        map = new google.maps.Map(document.getElementById("map"), options);

        function loadMarkers(venueList) {
            if (!venueList) return;
            var markerList = markers = [];
            for (i = 0; i < venueList.length; i++) {
                var venue = venueList[i];
                var markerTitle = "";

                var address = '';
                if (venue.address1) address += venue.address1;
                if (venue.town) address += (address) ? ", " + venue.town : venue.town;
                if (venue.postcode) address += (address) ? ", " + venue.postcode : venue.postcode;
                var sports = [];
                var selectedSport = null;
                for (var l in venue.leagues) {
                    let sportName = sports[venue.leagues[l].sport.name];
                    let league_url = window.location.origin + '/social-leagues/' + venue.leagues[l].url
                    if (!sports[venue.leagues[l].sport.name]) {
                        var sport = {
                            "name": venue.leagues[l].sport.name,
                            "img": venue.leagues[l].sport.img,
                            "urlref": venue.leagues[l].sport.urlref,
                            "url": league_url,
                            "days": {
                                "mo": false,
                                "tu": false,
                                "we": false,
                                "th": false,
                                "fr": false,
                                "sa": false,
                                "su": false
                            }
                        };
                        for (var d in venue.leagues[l].days) {
                            if (venue.leagues[l].days[d] === true) {
                                // sport.days[d] = venue.leagues[l].name.replace(/ /g,"-").replace(/&/,"and");
                                sport.days[d] = venue.leagues[l].id;
                                markerTitle += venue.leagues[l].name + ", " +
                                    venue.name;
                            }
                        }
                        sports.push(sport);
                    }
                }
                var markerLocation = '/wp-content/themes/salient-child/images/map-pin.png';
                var marker = new google.maps.Marker({
                    position: new google.maps.LatLng(venue.coordinates.lat, venue.coordinates.lng),
                    // title: venue.name,
                    title: markerTitle,
                    address: address,
                    markerId: venue.id,
                    sports: sports,
                    icon: markerLocation,
                    map: map
                });
                markers[venue.id] = marker;
                markerList.push(venue.id);
                google.maps.event.addListener(marker, 'click', (function(marker) {
                    return function() {
                        jQuery(".modal-title").text(marker.title);
                        jQuery(".modal-address").text(marker.address);
                        jQuery(".days-of-the-week .day-status").empty();
                        jQuery(".days-of-the-week .day-status").removeClass("active");
                        jQuery("#myTab .nav-link").addClass("d-none");
                        var tabPanes = document.getElementsByClassName("tab-pane");
                        for (var t in tabPanes) {
                            if (tabPanes[t].classList) {
                                tabPanes[t].classList.remove("show");
                                tabPanes[t].classList.remove("active");
                            }
                        }
                        var tabItemSection = document.getElementById("myTab");
                        var activated;
                        for (var s in marker.sports) {
                            // sportsName = marker.sports[s].name.replace(" ","");
                            var sportsname = marker.sports[s].urlref;
                            var tabName = marker.sports[s].urlref + "-tab";
                            var panelName = marker.sports[s].urlref + "-panel";
                            console.log("SportsName: ", sportsname, "TabName: ", tabName, "PanelName: ", panelName);

                            // jQuery("#"+panelName+" .modal-sportImage").attr("src",marker.sports[s].img);
                            for (var d in marker.sports[s].days) {
                                if (marker.sports[s].days[d]) {
                                    jQuery("#" + panelName + " .modal-" + d).addClass("active");
                                    // console.log("Panel marked Active","#"+panelName+" .modal-"+d);
                                    // jQuery( "#"+panelName+" .modal-"+d ).append('<a href="/league-info/?leagueid='+marker.sports[s].days[d]+'">&check;</a>');
                                    jQuery("#" + panelName + " .modal-" + d).append('<a target="_blank" href="' + marker.sports[s].url + '">&check;</a>');
                                }
                            }
                            var tab = document.getElementById(tabName);
                            var panel = document.getElementById(panelName);
                            if (!activated) {
                                activated = tabName;
                                // console.log("Tab marked Active",tabName);
                                // console.log("Activating",activated);
                                tab.classList.add("active");
                                tab.setAttribute("aria-selected", true);
                                panel.classList.add("active");
                                panel.classList.add("show");
                            } else if (tabName != activated) {
                                // console.log("Tab not Active",tabName);
                                // console.log("Deactivating",tabName);
                                tab.setAttribute("aria-selected", false);
                                panel.classList.remove("active");
                                panel.classList.remove("show");
                                tab.classList.remove("active");
                            }
                            if (tab && tab.classList) tab.classList.remove("d-none");
                            if (panel && panel.classList) panel.classList.remove("d-none");
                        }
                        jQuery("#leagueModal").modal('show');
                    }
                })(marker));
            }
        }

        function fetchVenues() {
            var url = "https://api.leagues4you.co.uk/liveLeagues"
            fetch(url)
                .then(response => response.json())
                .then(returnedLeagues => {
                    if (document.getElementById("chosenSport").value) {
                        filteredLeagues = [];
                        for (let r in returnedLeagues) {
                            for (let l in returnedLeagues[r].leagues) {
                                if (returnedLeagues[r].leagues[l].sport.id == document.getElementById("chosenSport").value) filteredLeagues.push(returnedLeagues[r]);
                            }
                        }
                        return filteredLeagues;
                    } else return returnedLeagues;
                })
                .then((leagueList) => {
                    loadMarkers(leagueList);
                })
        }

        fetchVenues();
    </script>

    <script src="/wp-content/themes/salient-child/js/jquery-3.5.1.slim.min.js"></script>
    <script src="/wp-content/themes/salient-child/js/bootstrap.4.5.2.min.js"></script>
    <script src="/wp-content/themes/salient-child/js/popper.min.js"></script>
    <link rel='stylesheet' id='mukta-font-css' href='https://fonts.googleapis.com/css2?family=Mukta%3Awght%40400%3B600%3B700&#038;display=swap&#038;ver=6.1.1' type='text/css' media='all' />
    <link rel='stylesheet' id='custom3-css' href='/wp-content/themes/salient-child/css/map-style.css?ver=1.1.4' type='text/css' media='all' />
    <link rel='stylesheet' id='fontawesome-css' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css?ver=6.1.1' type='text/css' media='all' />