.checkout-container {
    font-family: "neue-kabel";
}


.checkout-container>h1 {
    font-size: 3.5rem;
    font-style: normal;
    font-weight: 400;
    line-height: 72px;
    font-family: "neue-kabel";
    text-transform: none;
}

.h5-title {
    font-size: 22px;
    font-style: normal;
    font-weight: 600;
    line-height: 37.4px;
    font-family: "neue-kabel";
    text-transform: none;
    color: #333;
}


.text-space {
    font-family: "neue-kabel";
    color: #333;
    text-transform: none;
    font-weight: 400;
}

.booking-form {
    background-color: #F9F9F9;
}

.booking-form>.row>.col-md-6>label,
input::placeholder {
    font-family: "neue-kabel";
    font-size: 1.1rem;
    padding: 10px 0px 10px 0px;
    font-weight: 100;
}

input {
    border: 1px solid #999;
    background: #FFF;
    box-shadow: 0px 0px 60px 0px rgba(6, 30, 98, 0.08);
    padding: 7px 25px;
}

.booking-form>.row>.col-md-6 {
    display: flex;
    flex-direction: column;
}

.additional-participants {
    width: 46px;
    height: 40px;
    border: 1px solid #999;
    padding: 5px 0px 5px 0px;
    text-indent: 15px;
}


.checkout-price {
    border-bottom: 1px solid #999;
    padding-left: 5px;
}

.border-purple {
    border: 1px solid #760890;
    font-family: "neue-kabel";
}

/* add image to facilities-list li */
.facilities-list li {
    background-image: url(/images/circle-check-regular.svg);
    background-repeat: no-repeat;
    background-position: left;
    list-style-type: none;
    padding-left: 35px;
}


/* add spacing between facilities-list li */
.facilities-list li:not(:last-child) {
    margin-bottom: 10px;
}

/* Loading */
.spinner-border {
    display: block;
    position: fixed;
    top: calc(50% - (58px / 2));
    right: calc(50% - (58px / 2));
    color: red;
}

#backdrop {
    position: absolute;
    top: 0;
    width: 100vw;
    height: 200vh;
    z-index: 999;
    background-color: rgb(0, 0, 0, 0.2);
}

/* Stripe Elements CSS */


.modal-backdrop{
    z-index: -1 ! important;
}


/* Mobile */
@media only screen and (max-width: 600px) {
    .checkout-container>h1 {
        font-size: 2.5rem;
        line-height: normal;
    }

    .h5-title {
        font-size: 1.1rem;
    }

    .text-space {
        font-size: 0.9rem;
    }

    .checkout-price {
        padding-left: 0px;
    }

    #backdrop {
        height: 250vh;
    }
}