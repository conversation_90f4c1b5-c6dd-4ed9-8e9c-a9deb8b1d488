@import url("https://fonts.googleapis.com/css2?family=Cabin:wght@400;700&family=Oxygen:wght@400;700&display=swap");

:root {
    --dark-blue: #171e37;
    --dark-purple: #760890;
    --dark-grey: #444;
    --dark-pink: #ac56c6;
    --medium-pink: #e4098f;
    --font-primary: "Oxygen", sans-serif;
}

::-webkit-scrollbar {
    width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

body {
    font-family: var(--font-primary);
}

a {
    color: inherit;
}

.text-lowercase {
    text-transform: lowercase;
}

.full-stop::after {
    content: ".";
}

.nav-btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.btn-purple {
    background-color: var(--dark-purple);
    color: white;
}

main h1 {
    text-align: center;
    font-weight: bold;
}

#topNav {
    background-color: var(--dark-blue);
}

#topNav .nav-item .nav-link.active {
    border-bottom: medium solid var(--dark-purple);
    padding-bottom: 4px;
}

.find-nearby-leagues-form {
    display: flex;
    justify-content: center;
}

.find-nearby-leagues-select {
    border: medium solid var(--dark-pink);
    padding: 0 20px;
}

.league-grid-container {
    display: flex;
    justify-content: center;
    margin-bottom: 100px;
}

.league-grid {
    display: grid;
    grid-template-columns: 300px 300px 300px;
    grid-template-rows: repeat(2, 225px);
    gap: 10px;
}

@media screen and (max-width: 800px) {
    .league-grid {
        grid-template-columns: 300px;
    }
    .venue-info-centre-name {
        line-height: 1.25;
    }
}

.league-grid img,
.league-grid iframe {
    height: 175px;
    width: 100%;
}

.register-for-next-season {
    position: fixed;
    bottom: 5px;
    left: 0;
    right: 0;
    height: 50px;
    background-color: var(--dark-pink);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5em;
    font-weight: bold;
    opacity: 0.85;
}

.enter-here {
    display: flex;
    flex-direction: column;
    background-color: var(--dark-blue);
    color: white;
    align-items: center;
    justify-content: center;
    height: 50px;
}

.enter-here-link {
    border: 0;
    background-color: var(--dark-pink);
    color: white;
    padding: 0.15em 2.5em;
    border-radius: 0.25em;
    transition: all linear 300ms;
    font-weight: bold;
    text-decoration: none;
}

.enter-here-link:hover {
    color: var(--dark-blue);
}

.venue-info {
    background-color: var(--dark-blue);
    color: white;
    display: flex;
    flex-direction: column;
    font-size: 0.8em;
    /* background-color: blue; */
}

.venue-info-centre {
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.venue-info-centre-name {
    font-size: 1.8em;
    text-align: center;
}

.venue-info-centre-address {
    font-size: 1em;
    margin-bottom: 0;
}

.venue-info-details {
    flex: 1;
    background-image: url("https://leagues4you.co.uk/wp-content/uploads/2022/07/netball_court_2.jpg");
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    padding: 1em;
}

.venue-info-details .details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.venue-info-details .details-grid-item {
    display: flex;
    align-items: center;
    /* justify-content: center; */
}

.venue-info-details .details-grid-item i {
    margin: 0 0.5em;
}

.venue-info .venue-info-starts {
    height: 50px;
    background-color: var(--dark-blue);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.venue-info .venue-info-starts .new-season-starts {
    font-weight: bold;
    font-size: 1.2em;
}

.venue-info .venue-info-starts .new-season-starts-date {
    font-size: 0.7em;
}

.join-a-team {
    display: flex;
    flex-direction: column;
}

.join-here {
    height: 50px;
    background-color: var(--dark-blue);
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    font-weight: bold;
    transition: all linear 300ms;
}

.join-here:hover {
    color: var(--dark-pink);
}

.join-here i {
    margin-right: 0.5em;
    font-size: 1.25em;
}

.league-info {
    padding: 1em;
    background-color: var(--dark-blue);
    display: flex;
    flex-direction: column;
}

.league-info h2 {
    color: white;
    font-size: 1.25em;
    font-weight: bold;
    text-transform: lowercase;
    margin-bottom: 0;
}

.league-info ul {
    color: var(--medium-pink);
    list-style: none;
    font-weight: bold;
    padding-left: 0;
    margin-bottom: 0;
    flex: 1;
    line-height: 1.3;
    display: flex;
    flex-direction: column;
    justify-items: center;
    justify-content: center;
}

.league-info-link {
    background-color: transparent;
    color: white;
    border: thin solid white;
    font-weight: bold;
    align-self: flex-start;
    text-decoration: none;
    padding: 0.15em 1em;
    transition: all linear 300ms;
}

.league-info-link:hover {
    color: var(--dark-pink);
    border-color: var(--dark-pink);
}

.leagues4you-video {
    background-color: var(--dark-blue);
}

.leagues4you-video div {
    color: white;
    display: flex;
    text-transform: lowercase;
    justify-content: center;
    align-items: center;
    height: 35px;
    font-weight: bold;
}

.leagues4you-video div i {
    margin-right: 0.5em;
    font-size: 1.5em;
}

.register-for-next-season a {
    text-decoration: none;
}

.register-for-next-season a:hover {
    color: var(--dark-blue);
}

.league-table {
    margin: 10px auto;
    max-width: 920px;
}

.league-table-info {
    display: flex;
}

.league-table .league-table-header {
    text-transform: lowercase;
    display: flex;
    justify-content: center;
    background-color: var(--dark-blue);
    /* padding: 1em; */
    color: white;
    font-weight: bold;
    font-size: 1.25em;
}

.league-table .league-table-controls {
    margin: 10px 0;
}

.league-table .league-table-controls button {
    background-color: var(--medium-pink);
    color: white;
    border: none;
    border-radius: 0.5em;
    font-size: 0.8em;
    padding: 0.15em 1em;
    margin: 0 0.25em;
}

.league-table .league-table-controls button.active {
    background-color: var(--dark-blue);
}

.your-coordinator {
    color: #fff;
    font-size: 1.7em;
}

.league-search-container {
    display: flex;
    justify-content: center;
    align-items: center;
}
.league-search-form {
    width: 225px;
    position: relative;
}
.league-search-input {
    border: thin solid #ccc;
    border-radius: 1em;
    padding: 0.25em 1em;
    font-size: 0.8em;
    width: 100%;
}
.league-search-input::placeholder {
    margin-left: 1em;
}
.league-search-btn {
    position: absolute;
    right: 15px;
    border: none;
    background-color: transparent;
}

@media screen and (max-width: 768px) {
    .league-table {
        margin: 1em;
    }
}
.grid-cell {
    height: 225px;
}

.contact-coordinator {
    border-radius: 13px;
    margin-top: 18px;
    background-color: #760890;
}
.contact-coordinator a {
    text-decoration: none;
    color: inherit;
}
.contact-coordinator a:hover {
    text-decoration: none;
    color: inherit;
}
.message_parent {
    position: relative;
    width: 100%;
    height: 100%; /* Adjust as needed */
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black overlay */
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Styles for the span element */
.message {
    font-size: 24px; /* Adjust as needed */
    color: white; /* Text color */
    text-align: center; /* Center align text */
    padding: 20px; /* Add padding for spacing */
    background-color: #333; /* Background color */
    border-radius: 10px; /* Rounded corners */
}

.c2c_title{
    text-transform:capitalize; 
    font-size: small; 
    border: 2px solid black;
    display:inline-block; 
    border-radius: 16px;
    padding: 0px 4px;
}
.c2c_title a{
    text-decoration: none;
    color: inherit;
}