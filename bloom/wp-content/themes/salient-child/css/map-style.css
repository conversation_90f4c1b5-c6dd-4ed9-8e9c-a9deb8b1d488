/* 
	Purple: #760890;
	Dark Purple: #171e37;
	Pink: #00B6AC;
	Grey: #2d3436;
*/

/* Contents

1) Navigation
2) Carousel
3) Buttons
4) Search Bar
5) Typography
6) Map
7) Forms
8) league-info page
9) couch2court
10) contact-us page
11) league modal
12) facilities
13) footer
14) media queries
5) Misc

 */

 sup {
  font-size: 100% !important;
  top: 0em !important;
}

.tooltip-inner {
  background: #00b6ac;
}

.row {
  margin-right: 0px !important;
  margin-left: 0px !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  text-transform: uppercase;
  font-family: "neue-kabel" !important;
}

.countdown-timer {
  padding: 10px;
  width: 100%;
  background-color: rgba(0, 182, 172, 0.8);
  border-radius: 6px;
  font-family: "neue-kabel", sans-serif;
  text-transform: lowercase;
  min-width: 200px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.countdown-text {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  margin: 0px 1px;
}

.countdown-timer small {
  font-size: 12px;
}

.login-button:focus {
  border-color: inherit;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.fullHeight {
  height: 80vh;
}

.map-canvas {
  position: relative;
  background: #f9f9f9;
  padding-right: 0;
  padding-left: 0px;
}

.row-top-padding {
  margin-top: 4.5em;
}

@media (max-width: 576px) {
  .row-top-padding {
    margin-top: 4rem;
  }
}

.bg-secondary {
  background: #171e37 !important;
}

.bg-turquoise {
  background-color: #00b6ac;
}

.text-turquoise .fab {
  color: #00b6ac !important;
}

.align-middle {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

/* 1) Navigation 
------------------------------------------------------------------*/
.navbar:hover,
.navbar.dark-background {
  background-color: #171e37 !important;
  -webkit-transition: 0.7s ease-in-out;
  -o-transition: 0.7s ease-in-out;
  transition: 0.7s ease-in-out;
}

.navbar-brand img,
.footer-brand img {
  max-width: 240px;
}

ul.navbar-nav li {
  border-bottom: 4px solid transparent;
}

ul.navbar-nav li.active {
  border-bottom: 4px solid purple;
}

/* .nav-link, */
.nav-link:hover {
  color: #ffffff;
  font-weight: bold;
  text-shadow: 1px 1px 4px #333;
  /* font-size: 1.1em; */
  /* letter-spacing: 1px; */
}

.nav-link:hover {
  color: #00b6ac;
}

.nav-tabs {
  border: none !important;
  color: #fff !important;
}

.nav-tabs .nav-item {
  /* background-color: #171e37; */
  margin: 5px;
}

.nav-tabs .nav-link {
  text-shadow: none;
  font-size: 14px;
  border: none !important;
  background-color: rgba(23, 30, 55, 0.6);
  text-transform: uppercase;
}

.nav-tabs .nav-link.active {
  position: relative;
  background-color: #171e37;
  color: #fff !important;
}

.nav-tabs .nav-link.active::before {
  content: "";
  position: absolute;
  bottom: -30px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  border-top: 15px solid #171e37;
  border-right: 15px solid transparent;
  border-bottom: 15px solid transparent;
  border-left: 15px solid transparent;
}

.container-fluid {
  padding: 0;
}

.current-page {
  color: #00b6ac;
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255,255,255, 1)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 8h24M4 16h24M4 24h24'/%3E%3C/svg%3E");
}

.custom-toggler.navbar-toggler {
  border-color: transparent;
}

.navbar-collapse.left.collapse.show {
  background: #171e37;
}

#navbarSupportedContent .nav-link {
  text-shadow: none;
  color: rgba(255, 255, 255, 1);
}

.navbar .fab {
  cursor: pointer;
}

.login-button {
  position: relative;
  min-width: 94px;
  margin-top: -12px;
}

.inner_text_register {
  animation: register_button_slide_in;
  -webkit-animation: register_button_slide_in;
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}

@-webkit-keyframes register_button_slide_in {
  from {
    margin-left: -50%;
    z-index: -1;
  }

  to {
    margin-left: 0;
    z-index: 1;
  }
}

@keyframes register_button_slide_in {
  from {
    margin-left: -50%;
    z-index: -1;
  }

  to {
    margin-left: 0;
    z-index: 1;
  }
}

.inner_text_login {
  animation: login_button_slide_in;
  -webkit-animation: login_button_slide_in;
  -webkit-animation-duration: 1.5s;
  animation-duration: 1.5s;
}

@-webkit-keyframes login_button_slide_in {
  from {
    margin-right: -50%;
    z-index: -1;
  }

  to {
    margin-right: 0;
    z-index: 1;
  }
}

@keyframes login_button_slide_in {
  from {
    margin-right: -50%;
    z-index: -1;
  }

  to {
    margin-right: 0;
    z-index: 1;
  }
}

/* 2) Carousel 
------------------------------------------------------------------*/
/* .carousel {
  height: 100%;
  position: relative;
}

.carousel.slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(23, 30, 55, 0.2) !important;
  z-index: 1;
}

.carousel-item {
  position: relative;
  height: 100%;
  min-height: 350px;
  background: no-repeat center center scroll;
  background-size: cover;
}

.carousel-item:nth-of-type(2)::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: rgba(23, 30, 55, 0.5) !important;
}

.carousel-item img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
} */


/* .carousel-caption-1 {
	left: 0px;
	transform: translateX(-50%);
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
} */

/* .carousel-caption:nth-child(3) {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.carousel-caption h3 {
  font-size: 4em;
  text-transform: uppercase;
}
.caption-small-tagline {
  font-weight: bold;
  font-size: 24px;
}

.carousel-inner {
  height: 100%;
}

.we-need-you-badge {
  position: absolute;
  top: 0vh;
  right: 0px;
} */

/* 3) Buttons 
------------------------------------------------------------------*/

.btn,
.btn-primary {
  border-radius: 0;
}

.btn-default {
  background: #007bff;
  border: 1px solid #007bff;
  color: #ffffff;
}

.btn-primary {
  background: #760890;
  border: 1px solid #760890;
  color: #ffffff;
  text-transform: uppercase;
}

.btn-danger {
  background: #00b6ac;
  border: 1px solid #00b6ac;
  text-transform: uppercase;
}

.btn-turquoise {
  background: #00b6ac;
  color: #fff;
}

.btn-danger:hover {
  background: #760890;
  border: 1px solid #760890;
  color: #fff !important;
}

.btn-primary:hover,
.btn-primary:active,
.btn-primary:focus {
  background: #171e37;
  border: 1px solid #171e37;
}

.btn-primary:hover {
  color: #ffffff;
}

.btn-primary.submit-btn:hover {
  background: #760890;
  border: 1px solid #760890;
}

/* 4) Search Bar
------------------------------------------------------------------*/

.search-league-bar {
  text-align: center;
  background: #171e37;
  padding-top: 1em;
  padding-bottom: 0.6em;
  color: #ffffff;
  min-height: 20vh;
}

/* 5) Typography 
------------------------------------------------------------------*/

* {
  /* font-family: "Mukta", sans-serif; */
  font-family: "neue-kabel";
}

i.fab {
  color: #ffffff;
  padding-right: 0.5em;
}

h3 {
  text-transform: uppercase;
}

.subtitle {
  font-size: 80%;
  text-transform: uppercase;
  color: #760890;
  font-weight: bold;
}

/* 6) Map 
------------------------------------------------------------------*/

#map-canvas {
  margin: 0;
  width: 100%;
  min-height: 600px;
}

#map {
  width: 100%;
  height: 250px;
}

.location-section-address p {
  padding: 0;
  margin: 0;
}

.league-marker-info {
  list-style-type: none;
  margin: 0;
  padding: 0;
  margin-bottom: 1em;
}

.league-marker-info li {
  max-width: 600px;
  line-height: 1.6em;
  font-size: 1.2em;
}

.league-marker-info li:first-child {
  font-size: 1.1em;
  color: #760890;
  font-weight: bold;
  text-transform: uppercase;
  line-height: 1.8em;
}

.league-marker-info li:nth-child(2) {
  color: #00b6ac;
}

#more-info-btn {
  font-size: 0.9em;
}

.gm-style .gm-style-iw-c {
  border-radius: 0;
}

div.col-8.map-canvas {
  padding-left: 0;
}

.findALeague nav,
.contactPage nav {
  background: #171e37;
}

.number-of-divisions {
  width: 30px !important;
  height: 30px !important;
  border-radius: 50%;
  background: #00b6ac;
}

.text-pink {
  color: #00b6ac;
}

.leagues-map {
  position: relative;
}

.leagues-map h3 {
  padding-top: 0.8em;
}

.modal-content {
  border-radius: 0;
}

.search-results-data a {
  color: #760890;
}

.modal-header {
  background: #8A8BDC;
  color: #ffffff;
  border-radius: 0;
}

.modal-footer {
  background: #760890;
  color: #ffffff;
  border-radius: 0;
}

.close,
.close:hover {
  color: #ffffff;
  text-shadow: none;
}

.modal-body i {
  color: #760890;
  margin-right: 0.7em;
}

.modal-body {
  background-color: #f5f1e8 !important;
}


.grey-bg {
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  left: 20px;
  -webkit-transform: translateY(50%);
  -ms-transform: translateY(50%);
  transform: translateY(50%);
  z-index: 999;
}

@media (max-width: 600px) {
  .grey-bg {
    left: 0;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
  }
}

select {
  padding: 2%;
  width: 100%;
}

/* 7) Forms
------------------------------------------------------------------*/

input:not([type]),
input[type="text"],
input[type="password"],
input[type="date"],
input[type="datetime"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="email"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="time"],
input[type="url"],
input[type="color"],
textarea {
  border-radius: 0;
}

/*  8) league-info page 
------------------------------------------ */
.leagueInfo {
  background: url("../img/league-info-background.jpg") center no-repeat;
  background-size: cover;
  min-height: 100vh;
}

.leagueInfo::before {
  content: "";
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 105%;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: -1;
}

.leagueInfo-container {
  margin-top: 15vh !important;
}

.venue-name-and-adress {
  background: #171e37;
}

.dropdown-select {
  background: #760890;
}

.social-icons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.social-icons .fab {
  color: #000 !important;
}

.info-about-venue {
  background: rgba(255, 255, 255, 0.6);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.info-details-day,
.info-details-price,
.info-details-time,
.info-details-surface {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 1fr 2fr;
  grid-template-columns: 1fr 1fr 2fr;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  color: #171e37 !important;
  padding: 20px;
}

.info-details div:nth-child(odd) {
  background: #dadada;
}

.info-details div:nth-child(even) {
  background: #ffff;
}

.info-details-countdown-clock-details {
  /* background: url('../img/league-info-background.jpg') no-repeat; */
  /* background-size: cover;
	background-position: center; */
  height: 95%;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: #fff;
  background-color: rgba(23, 30, 55);
  padding: 0 1rem;
}

.info-details-time {
  background: #f9f9f9;
}

#previous-table-control,
#current-table-control,
#upcoming-fixtures,
#latest-results-control {
  background-color: rgba(0, 182, 172, 0.6) !important;
}

#previous-table-control.active,
#current-table-control.active,
#upcoming-fixtures.active,
#latest-results-control.active {
  background-color: rgba(0, 182, 172, 1) !important;
}

#previous-table-control::before,
#current-table-control::before,
#upcoming-fixtures::before,
#latest-results-control::before {
  border-top: 15px solid rgba(0, 182, 172, 0.6) !important;
}

#previous-table-control.active::before,
#current-table-control.active::before,
#upcoming-fixtures.active::before,
#latest-results-control.active::before {
  border-top: 15px solid rgba(0, 182, 172, 1) !important;
}

/* table.upcoming-fixtures tbody tr td:nth-of-type(1),
table.upcoming-fixtures tbody tr td:nth-of-type(3),
table.latest-results tbody tr td:nth-of-type(1),
table.latest-results tbody tr td:nth-of-type(3) {
	font-weight: bold;
} */

/* Generic Page Banner */
.hero-banner {
  position: relative;
  height: 60vh;
  color: #fff;
  margin-bottom: 1rem;
}

.hero-banner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(23, 30, 55, 0.4);
}

.hero-banner img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: right;
  object-position: right;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.hero-banner div {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;

  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

.hero-banner div h1 {
  font-size: 4em;
}

.darkContent {
  background: url("../img/C2C_background.jpg") no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  z-index: 1;
  color: #fff;
}

/*  9) couch2court
------------------------------------*/

#couch2court {
  background: url("../img/C2C_background.jpg") no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  z-index: 1;
}

.couch2court-hero {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  height: 60vh;
}

.couch2court-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(23, 30, 55, 0.4);
}

.couch2court-hero img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: right;
  object-position: right;
  width: 100%;
  height: 100%;
}

.couch2court-hero-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

.couch2court-hero-title div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.couch2court-hero-title h1 {
  font-size: 4em;
}

/* .c2c-top-content {
	padding-top: 15vh !important;
} */

.c2c-top-content h2 {
  text-transform: unset;
}

.c2c-top-content img {
  width: 175px;
  height: 125px;
}

.c2c-top-content-faqs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  /* height: 350px; */
  margin-bottom: 20px;
  overflow: hidden;
  position: relative;
}

.faqs-wrapper {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 10px 1fr 10px 1fr;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
  margin-right: -50px;
  /*Maximumwidthofscrollbar*/
  padding-right: 50px;
  -webkit-transition: -webkit-transform 0.4s ease;
  transition: -webkit-transform 0.4s ease;
  transition: transform 0.4s ease;
  transition: transform 0.4s ease, -webkit-transform 0.4s ease;
  -webkit-transition: transform 0.4s ease;
  -moz-transition: transform 0.4s ease;
  -ms-transition: transform 0.4s ease;
  -o-transition: transform 0.4s ease;
}

.faq-card {
  background: #fff;
  padding: 10px;
  transition: 0.4s ease;
  -webkit-transition: 0.4s ease;
  -moz-transition: 0.4s ease;
  -ms-transition: 0.4s ease;
  -o-transition: 0.4s ease;
}

.faq-card figure {
  line-height: 1.2em;
  height: 2.4em;
  overflow: hidden;
  position: relative;
}

.faq-card.active {
  -ms-grid-column: 1;
  -ms-grid-column-span: 3;
  grid-column: 1/4;
}

.faq-card.active figure {
  height: 100%;
}

.faq-card.active figure::after {
  display: none;
}

.faq-card button.minimize {
  display: none;
}

.faq-card.active button.minimize {
  display: block;
}

.faq-card.active button.expand {
  display: none;
}

.faq-card figure::after {
  content: "";
  text-align: right;
  position: absolute;
  bottom: 0;
  right: 0;
  width: 70%;
  height: 1em;
  background: -o-linear-gradient(left,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1) 50%);
  background: -webkit-gradient(linear,
      left top, right top,
      from(rgba(255, 255, 255, 0)),
      color-stop(50%, rgba(255, 255, 255, 1)));
  background: linear-gradient(to right,
      rgba(255, 255, 255, 0),
      rgba(255, 255, 255, 1) 50%);
}

.faq-controls {
  height: 340px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-right: 20px;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
}

.faq-controls .fa {
  cursor: pointer;
}

.faq-card h6 {
  text-transform: capitalize;
}

.faq-card button {
  background: none;
  margin: 0;
  padding: 0;
  border: none;
  color: #0035f5;
  outline: none;
}

/* 10) contact-us page 
----------------------------------- */
.contactPage .container {
  padding-top: 10vh;
}

.contact-us-hero-section {
  height: 80vh;
}

.contact-us-hero-section img {
  width: 100%;
  height: 100%;
  /* object-fit: cover; */
}

.contact-us-options {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  justify-content: space-evenly;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.contact-us-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  margin: 10px;
}

.contact-us-options .fab {
  color: #000;
}

/* 11) league modal
------------------------------------ */

#signUpModal .modal-content {
  background-color: rgba(255, 255, 255, 0.8);
}

.tab-control-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.tab-control {
  position: relative;
  border-right: 1px solid #333;
  padding: 2px 15px !important;
  cursor: pointer;
}

.tab-control.active-link::before {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0px;
  background: #760890;
  width: 90%;
  height: 2px;
  margin: 0px 5% 0px 5%;
}

.tab-control-links:not(.tab-control:first-child) {
  border-right: 2px solid #171e37;
  margin-left: 5px;
}

.days-of-the-week {
  display: -ms-grid;
  display: grid;
  /* -ms-grid-columns: (1fr)[7]; */
  /* -ms-grid-columns: (1fr)[7]; */
  grid-template-columns: repeat(7, 1fr);
  /* to change according to the screen size*/
}

.table.days-of-the-week .day:nth-child(odd) {
  background-color: #ececec;
}

.day {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 5px;
}

.day-status {
  border-radius: 50px;
  background: #ccc;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 50px;
  width: 100%;
  font-size: 40px;
}

.day-status.active {
  background: #005c3c;
  font-size: 20px;
}

/* .day-status.active:hover {
  background: #00b6ac;
} */

.nav-tabs {
  display: none;
}

.day-status a {
  text-decoration: none;
  color: #fff;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.venue-thumbnail img {
  width: 100%;
  max-height: 400px;
  -o-object-fit: cover;
  object-fit: cover;
}

.venue-price-heading {
  color: #bbb;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.venue-price-pricing {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.venue-price {
  color: #760890;
  font-weight: bold;
}

/* 12) facilities 
------------------------------*/

.facilities-and-location {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.facilities-location-section {
  -webkit-box-flex: 2;
  -ms-flex: 2;
  flex: 2;
}

.facilities p {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 3fr;
  grid-template-columns: 1fr 3fr;
}

.facilities .fas {
  font-size: 30px;
}

/* 13) footer 
--------------------------------------- */
footer {
  background: #760890;
  /* margin-top: 1rem; */
}

footer a {
  color: #fff;
  display: inline-block;
  width: 100%;
}

footer a:hover {
  text-decoration: none;
  color: #fff;
}

.footer_links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  margin: 15px 0px;
}

.footer_links a:nth-child(2) {
  border-right: 1px solid #fff;
  border-left: 1px solid #fff;
}

/* 14) Media Queries
----------------------------------------------------------------- */
@media only screen and (min-width: 48em) {
  .map-container {
    /* float: left; */
    width: 100%;
  }

  .filter-container-lg {
    display: block;
    float: left;
    width: 20%;
  }

  .filter-container-lg>div {
    border: 1px solid #ccc;
    -webkit-box-shadow: 0px 0px 3px #bbb;
    box-shadow: 0px 0px 3px #bbb;
    width: 90%;
  }

  .filter-container-sm {
    display: none;
  }
}

@media only screen and (max-width: 61.93em) {
  .navbar {
    background: #171e37;
  }

  .carousel-caption h3 {
    font-size: 50px;
  }

  .fullHeight {
    height: 60vh auto;
  }

  .search-league-bar {
    height: 20vh auto;
  }

  .search-league-bar h4 {
    font-size: 20px;
  }
}

@media (max-width: 1100px) {
  .navbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: unset;
    -ms-flex-align: unset;
    align-items: unset;
  }

  .contactPage .container {
    padding-top: 25vh;
  }
}

@media (max-width: 990px) {

  .info-details-day,
  .info-details-price,
  .info-details-time,
  .info-details-surface {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: 1fr 1fr;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center;
  }

  .info-details-day h6,
  .info-details-price h6,
  .info-details-time h6,
  .info-details-surface h6 {
    -ms-grid-column: 1;
    -ms-grid-column-span: 2;
    grid-column: 1/3;
  }

  .countdown-timer {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .countdown-text {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
  }
}

@media (max-width: 850px) {
  .navbar-collapse.left.collapse {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .nav-icons-right {
    width: 100%;
  }
}

@media (max-width: 767px) {
  .navbar {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
}

@media (max-width: 768px) {
  .carousel-caption a {
    font-size: 16px;
  }

  .carousel-caption h3 {
    font-size: 40px;
  }

  .caption-small-tagline {
    text-align: center !important;
  }

  .days-of-the-week {
    /* -ms-grid-columns: (1fr)[4] !important; */
    grid-template-columns: repeat(4,
        1fr) !important;
    /* to change accordign to the screen size*/
  }

  .info-details-day,
  .info-details-price,
  .info-details-time,
  .info-details-surface {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  .info-details-day h6,
  .info-details-price h6,
  .info-details-time h6,
  .info-details-surface h6 {
    -ms-grid-row: 2;
    grid-row: 2;
    grid-column: auto;
  }

  .facilities {
    padding: 10px;
  }

  .grey-bg {
    left: 0;
  }

  .info-about-venue {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }

  .info-details-price>*,
  .info-details-time>*,
  .info-details-surface>* {
    margin: 0px auto !important;
  }

  .contactPage .container {
    padding-top: 10vh;
  }

  .contact-us-option {
    background-color: #f3f3f3;
    padding: 10px;
  }

  .footer-column-1,
  .footer-column-2 {
    border-right: transparent;
    border-bottom: 2px solid #fff;
  }

  .couch2court-hero-title h1 {
    font-size: 40px;
  }

  .faqs-wrapper {
    -ms-grid-columns: 1fr 10px 1fr;
    grid-template-columns: 1fr 1fr;
  }

  .faq-card.active {
    -ms-grid-column: 1;
    -ms-grid-column-span: 2;
    grid-column: 1/3;
  }

  .footer-column {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    margin-top: 50px !important;
  }
}

/* Small devices (landscape phones, 576px and up) */
@media only screen and (max-width: 36em) {
  .navbar-brand img {
    max-width: 8em;
  }

  .faqs-wrapper {
    -ms-grid-columns: 1fr;
    grid-template-columns: 1fr;
  }

  .faq-card.active {
    -ms-grid-column: 1;
    -ms-grid-column-span: 1;
    grid-column: 1/2;
  }
}

@media (max-width: 350px) {
  .carousel-caption h3 {
    font-size: 30px;
  }
}

/* 15) Misc
----------------------------------------------------------------- */

/* Clearfix */
.cf:before,
.cf:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}

.cf:after {
  clear: both;
}

.cf {
  *zoom: 1;
}