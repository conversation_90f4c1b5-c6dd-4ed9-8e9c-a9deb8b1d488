<?php
session_start();
get_header();
$nectar_options = get_nectar_theme_options();

if (!isset($_POST['tasterID']) && !isset($_SESSION['c2c_multi_booking_id'])) {
  header("Location: /couch2court");
  exit;
}
if (isset($_POST['tasterID'])) {
  $url = "https://{$prefixUrl}api.leagues4you.co.uk/taster-multibook?tasterID={$_POST['tasterID']}";
  $rlt = json_decode(file_get_contents($url), true);
  $_SESSION['c2c_multi_booking_id'] = $rlt['id'];
  $c2c_multi_booking = $rlt;
} else {
  $url = "https://{$prefixUrl}api.leagues4you.co.uk/taster-multibook?bookingID={$_SESSION['c2c_multi_booking_id']}";
  $c2c_multi_booking = json_decode(file_get_contents($url), true);
}

$confirmation = [
  "title" => "Congratulations",
  "text" => [
    "You are successfully booked for " . $c2c_multi_booking['taster']['name'],
    "We'll send you an email containing all the details shortly so keep an eye out for that."
  ]
];
?>
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">

<?php wp_head(); ?>

<link rel="stylesheet" href="/wp-content/themes/salient-child/css/checkout.css?v=1.0.1" />
<link rel="stylesheet" id="space-grotesk-font-css" href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;600;700&display=swap" type="text/css" media="all" />
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
  integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
  crossorigin="anonymous"></script>
<script src="https://js.stripe.com/v3"></script>
<script>
  document.querySelector("#top .container .row .col.span_3").classList.remove("col");
  document.addEventListener('DOMContentLoaded', () => {
    // Select the parent element with the specified classes
    const parentElement = document.querySelector('.nectar-global-section.nectar_hook_global_section_footer');
    if (parentElement) {
      // Find the child element with the 'container' class
      const targetContainer = parentElement.querySelector('.container');
      if (targetContainer) {
        targetContainer.classList.remove('container');
      } else {
        console.log('Container class not found within the parent.');
      }
    } else {
      console.log('Parent element not found.');
    }
  });
</script>
<?php
$venue = $c2c_multi_booking['taster']['venue'];
$facilities = $venue['facilities'];
$address2 = (!empty(trim($venue['address2']))) ? ", " . $venue['address2'] : '';
$venueAddress = $venue['address1'] . ",$address2 " . $venue['town'] . ", " . $venue['postcode'];
$coordinator =  $c2c_multi_booking['coordinator'];
?>

<body class="<?= basename(__FILE__, ".php") ?>" data-c2cid="<?= (isset($_SESSION['c2c_multi_booking_id'])) ? $_SESSION['c2c_multi_booking_id'] : null ?>">
  <div id="pageBg" class="pageBg"></div>

  <div id="backdrop" style="display: none;">
    <div class="text-center loading">
      <div class="spinner-border" role="status">
        <span class="sr-only">Loading...</span>
      </div>
    </div>
  </div>

  <main style="min-height: 75vh" class="container">

    <div class="d-none" id="confirmation">
      <h1 class="text-center"><?= $confirmation['title'] ?></h1>

      <div class="alert alert-success alert-dismissible fade show" role="alert">
        <p><?= implode("</p><p>", $confirmation['text']) ?></p>
        <!-- <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button> -->
      </div>


      <!-- Modal -->
      <div class="fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="confirmationModalLabel"><?= $confirmation['title'] ?></h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <p><?= implode("</p><p>", $confirmation['text']) ?></p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container checkout-container">
      <h1 class="text-center pt-5">Couch2Court® Booking Confirmation</h1>
      <!-- create two column-->
      <div class="row pt-5">
        <div class="col-6 has-text-align-left">
          <h5 class="h5-title">Booking Details</h5>
        </div>
        <div class="col-6 has-text-align-right">
          <a href="/find-a-couch2court-session/" class="text-space">Click to go back</a>
        </div>
      </div>
      <!-- Location and date -->
      <div class="row">
        <div class="col-md-12 has-text-align-left">
          <h5 class="text-space">
            <?= $c2c_multi_booking['taster']['name'] ?>
          </h5>
        </div>
      </div>
      <div class="row pt-2">
        <div class="col-md-7">
          <div class="booking-form p-4">
            <h5 class="text-space pb-3">Participant Details</h5>
            <div class="row">
              <div class="col-md-6">
                <label for="fname">First Name</label>
                <input type="text" onchange="onChangeForm()" id="c2c_booker.firstname" name="c2c_booker[firstname]" placeholder="Your name.." required value="<?= $c2c_multi_booking['firstname'] ?>" />
              </div>
              <div class="col-md-6">
                <label for="lname">Last Name</label>
                <input type="text" onchange="onChangeForm()" id="c2c_booker.lastname" name="c2c_booker[lastname]" placeholder="Your last name.." required value="<?= $c2c_multi_booking['lastname'] ?>" />
              </div>
            </div>
            <div class="row pt-3">
              <div class="col-md-6">
                <label for="fname">Your Email</label>
                <input type="email" onchange="onChangeForm()" name="c2c_booker[email]" id="c2c_booker.email" placeholder="Your email.." value="<?= $c2c_multi_booking['email'] ?>" required />
              </div>
              <div class="col-md-6">
                <label for="pname">Mobile</label>
                <input type="tel" onchange="onChangeForm()" name="c2c_booker[mobile]" id="c2c_booker.mobile" placeholder="Your phone.." required value="<?= $c2c_multi_booking['mobile'] ?>" />
              </div>

            </div>

            <h5 class="text-space pt-5 pb-3">Add a friend or relative!</h5>
            <div class="row">
              <div class="col-md-4">
                <button class="btn btn-dark btn-purple border-purple w-100" id="revealAddFriendBtn" type="button">
                  Add Participant
                </button>
              </div>
            </div>
            <div class="row d-none mt-2" id="addFriendForm">
              <div class="col-md-6">
                <input type="text" name="newAttendee[firstname]" id="newAttendee.firstname" class="form-control" placeholder="Firstname" required>
              </div>
              <div class="col-md-3">
                <button type="button" class="btn btn-danger w-100" onclick="document.getElementById('addFriendForm').classList.add('d-none');document.getElementById('revealAddFriendBtn').classList.remove('d-none');">Cancel</button>
              </div>
              <div class="col-md-3">
                <button onclick="addAttendee()" class=" btn btn-purple border-purple w-100 btn-dark">Add</button>
              </div>
            </div>

            <div class="row participant-section">
              <div class="col-md-12">
                <h5 class="text-space pb-3">Participants</h5>
                <ul class="participants">
                  <?php foreach ($c2c_multi_booking['attendees'] as $attendee) { ?>
                    <li class="attendee" style="display:flex;justify-content:space-between;"><?= $attendee['firstname'] ?>
                      <button onclick="removeAttendee(<?= $attendee['id'] ?>)" name="removeAttendee" class="removeAttendee btn btn-danger"><i class="fas fa-trash"></i></button>
                    </li>
                  <?php } ?>
                </ul>
              </div>
            </div>


            <div class="row pt-4">
              <div class="col-12">
                <!-- coupon code-->
                <div class="coupon d-flex flex-column gap-3">
                  <label for="coupon">Coupon Code:</label>
                  <input type="text" name="c2c_booker[discountCode]" id="c2c_booker.discountCode" placeholder="Enter coupon code" value="<?= $c2c_multi_booking['discountCode'] ?>" />
                </div>
              </div>
              <div class="pt-3 col-12 col-sm-3">
                <div class=""><!---row-->
                  <input class="btn btn-purple border-purple" type="button" value="APPLY" id="applyCoupon" onclick="onChangeForm(true)" />
                </div>
              </div>
            </div>
          </div>

          <h5 class="text-space pt-4">Total Cost</h5>
          <div class="col-12 border-3 text-space">
            <h5 class="text-space pt-1 checkout-price">
              <?php if ($c2c_multi_booking['fullPrice']) { ?>
                <del>£<?= $c2c_multi_booking['fullPrice']; ?></del>
              <?php } ?>
              £<?= $c2c_multi_booking['charge']; ?>
            </h5>
          </div>

          <!-- Checkout -->

          <div class="row d-none" id="stripe-payment-form">
            <div id="payment-request-button">
              <!-- A Stripe Element will be inserted here. -->
            </div>

            <div id="payment-form-container" class="mt-2">
              <form id="paymentForm" method="post" action="<?= $_SERVER['REQUEST_URI'] ?>">
                <div id="card-element" class="form-control row"></div>
                <p id="card-error" role="alert"></p>
                <p class="result-message hidden"></p>
                <input type="hidden" name="completed_pi" value="<?= $c2c_multi_booking['stripePaymentIntentID'] ?>">
                <input type="hidden" id="clientSecret" value="<?= $c2c_multi_booking['stripeClientSecret'] ?>">
                <input type="hidden" id="stripeLoaded" value="false">
              </form>
            </div>
          </div>

          <div class="row d-none" id="stripe-payment-form2">
            <div class="col-md-9">
              <div class="mb-2">
                <input type="checkbox" id="termsCheckbox">
                <label class="form-check-label" for="termsCheckbox">
                  I have read and agree to the <a href="https://www.bloomnetball.co.uk/c2c-terms-conditons/" target="_blank">Terms & Conditions</a>
                </label>
              </div>
            </div>
            <div class="col-md-3 col-sm-12">
              <button id="book-now" class="btn btn-purple ms-auto w-100 float-md-end border-purple btn-dark">
                <div class="spinner hidden" id="spinner"></div>
                <span id="button-text">BOOK NOW</span>
              </button>
            </div>
          </div>

          <div class="row" id="clickToPay" style="margin-bottom: 50px;">
            <div class="col-md-12">
              <!-- Click for payment -->
              <button class="btn btn-secondary btn-purple border-purple w-100" onclick="checkHiddeForm();checkHiddeForm2();">Click to pay</button>
            </div>
          </div>
        </div>

        <div class="col-md-5 p-4 d-flex flex-column gap-3">
          <div>
            <h5 class="text-space">Questions about booking?</h5>
            <p class="text-space"><?= $coordinator['email']; ?></p>
          </div>

          <div>
            <h5 class="text-space">Venue Address</h5>
            <p class="text-space w-50 pt-2">
              <?= $venueAddress ?>
            </p>
            <?php if ($facilities) { ?>
              <ul class="text-space facilities-list">
                <?php foreach ($facilities as $facility) { ?>
                  <li><?= $facility['name'] ?></li>
                <?php } ?>
              </ul>
            <?php } ?>
          </div>
        </div>
      </div>
    </div>
  </main>

  <?php include("footer2.php"); ?>



  <script>
    function toggleParticipantsVisibility() {
      const participantsList = document.querySelector(".participants");
      const participantsSection = document.querySelector(".participant-section");

      if (participantsList && participantsList.children.length === 0) {
        participantsSection.style.display = "none";
      } else {
        participantsSection.style.display = "block";
      }
    }

    toggleParticipantsVisibility();

    const checkHiddeForm = () => {
      // if clientSecret is not null, hide clickToPay and show stripe form
      if (document.getElementById("clientSecret").value) {
        document.getElementById("clickToPay").classList.add("d-none");
      }
    }
    const checkHiddeForm2 = () => {
      if (!document.getElementById("clientSecret").value) {
        alert("Please complete the form before paying");
      }
    }
    checkHiddeForm();
    const formatPrice = (price) => {
      if (price) {
        let formatedPrice = parseFloat(price);
        formatedPrice = formatedPrice.toFixed(2);
        return formatedPrice;
      }
      return 0;
    };


    const updateLoading = () => {
      if (document.getElementById("backdrop").style.display == "none") {
        document.getElementById("backdrop").style.display = "flex";
      } else {
        document.getElementById("backdrop").style.display = "none";
      }
    }
    const checkoutSuccess = () => {
      var confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'), {
        keyboard: false
      });
      confirmationModal.show();
      document.getElementById('confirmation').classList.remove('d-none');
      document.querySelector('.checkout-container').classList.add('d-none');
      // Request to with fetch 
      fetch("/api.php?confirmBooking=true", {
          method: "GET",
          headers: {
            "Content-Type": "application/json"
          }
        })
        .then(response => response.json())
        .then(data => {})
        .catch(error => {});
    }

    const startStripe = (price) => {
      const stripe = Stripe('<?= $c2c_multi_booking['stripePublicKey'] ?>');
      const clientSecret = document.querySelector("#clientSecret").value;
      const stripeLoaded = document.querySelector("#stripeLoaded")
      const checkbox = document.getElementById("termsCheckbox");

      if (!clientSecret) return null;

      // Show stripe form 
      document.getElementById("stripe-payment-form").classList.remove("d-none");
      document.getElementById("stripe-payment-form2").classList.remove("d-none");

      var paymentRequest = stripe.paymentRequest({
        country: 'GB',
        currency: 'gbp',
        total: {
          label: 'Total',
          amount: parseInt(formatPrice(price * 100)),
        },
        requestPayerName: true,
        requestPayerEmail: true,
        // requestPayerPhone: true,
      });


      var elements = stripe.elements();

      const prButton = elements.create('paymentRequestButton', {
        paymentRequest,
      });

      (async () => {
        // Check the availability of the Payment Request API first.
        const result = await paymentRequest.canMakePayment();
        if (result) {
          prButton.mount('#payment-request-button');
        } else {
          document.getElementById('payment-request-button').style.display = 'none';
        }
      })();

      if (stripeLoaded.value == "true") {
        return;
      }

      // // Apple Pay 
      paymentRequest.on('paymentmethod', async (ev) => {
        // Confirm the PaymentIntent without handling potential next actions (yet).
        console.log(0)
        const {
          paymentIntent,
          error: confirmError
        } = await stripe.confirmCardPayment(
          clientSecret, {
            payment_method: ev.paymentMethod.id
          }, {
            handleActions: false
          }
        );

        console.log(1);
        if (confirmError) {
          // Report to the browser that the payment failed, prompting it to
          // re-show the payment interface, or show an error message and close
          // the payment interface.
          console.log(2, ev);
          ev.complete('fail');
          console.log(3, ev);
        } else {
          // Report to the browser that the confirmation was successful, prompting
          // it to close the browser payment method collection interface.
          console.log(4, ev);
          ev.complete('success');
          console.log(5, ev);
          // Check if the PaymentIntent requires any actions and, if so, let Stripe.js
          // handle the flow. If using an API version older than "2019-02-11"
          // instead check for: `paymentIntent.status === "requires_source_action"`.
          if (paymentIntent.status === "requires_action") {
            // Let Stripe.js handle the rest of the payment flow.
            console.log(6);
            const {
              error
            } = await stripe.confirmCardPayment(clientSecret);
            console.log(7);
            if (error) {
              console.log(8);
              alert("Apple or Google Payment failed")
              // The payment failed -- ask your customer for a new payment method.
            } else {
              console.log(9);
              checkoutSuccess();
              console.log(10);
              // The payment has succeeded.
            }
          } else {
            console.log(11);
            checkoutSuccess();
            console.log(12);
          }
          console.log(13);
        }
        console.log(14);
      });


      var style = {
        base: {
          color: "#32325d",
          fontFamily: 'Arial, sans-serif',
          fontSmoothing: "antialiased",
          fontSize: "16px",
          "::placeholder": {
            color: "#32325d"
          }
        },
        invalid: {
          fontFamily: 'Arial, sans-serif',
          color: "#fa755a",
          iconColor: "#fa755a"
        }
      };
      var card = elements.create("card", {
        style: style,
        hidePostalCode: true,
      });
      // Stripe injects an iframe into the DOM
      card.mount("#card-element");
      card.on("change", function(event) {
        // Disable the Pay button if there are no card details in the Element
        document.querySelector("button").disabled = event.empty;
        document.querySelector("#card-error").textContent = event.error ? event.error.message : "";
      });
      const bookNow = document.getElementById("book-now");
      bookNow.addEventListener("click", function(event) {
        event.preventDefault();
        if (!checkbox.checked) {
          alert("⚠️ You must agree to the Terms & Conditions before proceeding with the payment.");
          return false;
        }
        // Complete payment when the submit button is clicked
        payWithCard(stripe, card, clientSecret);
      });
      stripeLoaded.value = "true";
      console.log(stripeLoaded);
      var payWithCard = function(stripe, card, clientSecret) {
        loading(true);
        stripe
          .confirmCardPayment(clientSecret, {
            payment_method: {
              card: card
            }
          })
          .then(function(result) {
            if (result.error) {
              // Show error to your customer
              showError(result.error.message);
              alert("Payment failed")
              console.log(result);
            } else {
              // The payment succeeded!
              checkoutSuccess();
            }
          });
      };
      // Show the customer the error from Stripe if their card fails to charge
      var showError = function(errorMsgText) {
        loading(false);
        var errorMsg = document.querySelector("#card-error");
        errorMsg.textContent = errorMsgText;
        setTimeout(function() {
          errorMsg.textContent = "";
        }, 4000);
      };
      // Show a spinner on payment submission
      var loading = function(isLoading) {
        // Loading implementation
      };
    }
    startStripe(<?= $c2c_multi_booking['charge'] ?>);
  </script>
  <script>
    function revealAddFriend() {
      document.getElementById("addFriendForm").classList.remove("d-none");
      document.getElementById("revealAddFriendBtn").classList.add("d-none");
    }

    function scrollToForm() {
      // document.getElementById("wheres-my-nearest-couch2court").scrollIntoView();
    }
    let revealAddFriendBtn = document.getElementById("revealAddFriendBtn");
    if (revealAddFriendBtn) revealAddFriendBtn.addEventListener("click", revealAddFriend);
    <?= (isset($c2c_multi_booking)) ? 'scrollToForm();' : null ?>

    console.log("IP", '<?= $_SERVER['REMOTE_ADDR'] ?>');

    const updatePrice = (response) => {
      document.querySelector(".checkout-price").innerHTML = `£${formatPrice(response.data.charge)}`;
      document.querySelector("#clientSecret").value = response.data.stripeClientSecret;
      // Mount stripe again 
      startStripe(response.data.charge);
      updateLoading();
      if (document.querySelector("#clientSecret").value) document.getElementById("clickToPay").classList.add("d-none");
    }
    // if firstname, lastname, email and mobile not null update with ajax
    const onChangeForm = (applyCoupon = false) => {
      const firstname = document.getElementById("c2c_booker.firstname").value;
      const lastname = document.getElementById("c2c_booker.lastname").value;
      const email = document.getElementById("c2c_booker.email").value;
      const mobile = document.getElementById("c2c_booker.mobile").value;
      const discountCode = document.getElementById("c2c_booker.discountCode").value;

      if (applyCoupon) {
        if (!firstname || !lastname || !email || !mobile) return alert("Please complete the form before applying a coupon code");
      }

      if (!firstname || !lastname || !email || !mobile) return;
      if (mobile.length < 6) return;
      updateLoading();
      const xhr = new XMLHttpRequest();
      xhr.open("POST", "/api.php", true);
      xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
      xhr.onload = function() {
        if (this.status == 200) {
          const response = JSON.parse(this.responseText);
          updatePrice(response);
        }
      }
      xhr.send(`c2c_booker[firstname]=${firstname}&c2c_booker[lastname]=${lastname}&c2c_booker[email]=${email}&c2c_booker[mobile]=${mobile}&c2c_booker[discountCode]=${discountCode}`);
    }


    const addAttendee = () => {
      const newAttendeeFirstname = document.getElementById("newAttendee.firstname").value;
      if (!newAttendeeFirstname) return alert("Please enter a first name");
      // Create ajax request to add new attendee
      updateLoading();
      const xhr = new XMLHttpRequest();
      xhr.open("POST", "/api.php", true);
      xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
      xhr.onload = function() {
        // update list of attendees from data.attendees
        const response = JSON.parse(this.responseText);
        if (response.error) return alert(response.error);
        // clear list of attendees
        document.querySelector(".participants").innerHTML = "";

        response.data.attendees.map(function(attendee) {
          const li = document.createElement("li");
          li.classList.add("attendee");
          li.style.display = "flex";
          li.style.justifyContent = "space-between";

          li.innerHTML = `${attendee.firstname} <button onclick="removeAttendee(${attendee.id})" name="removeAttendee" class="removeAttendee btn btn-danger"><i class="fas fa-trash"></i></button>`;
          document.querySelector(".participants").appendChild(li);
        });
        updatePrice(response);

        // set input value to null and hide form
        document.getElementById("newAttendee.firstname").value = "";
        toggleParticipantsVisibility();

      }
      xhr.send(`newAttendee[firstname]=${newAttendeeFirstname}`);

    }



    // remove attende 
    function removeAttendee(attendeeID) {
      const xhr = new XMLHttpRequest();
      updateLoading();
      xhr.open("POST", "/api.php", true);
      xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
      xhr.onload = function() {
        // remove attendee from list
        const response = JSON.parse(this.responseText);
        document.querySelector(`.attendee button[name="removeAttendee"][onclick="removeAttendee(${attendeeID})"]`).parentElement.remove();
        updatePrice(response);
        toggleParticipantsVisibility();
      }
      xhr.send(`removeAttendee=${attendeeID}`);

    }
  </script>
</body>

</html>
<div id="fws_673def26b0b26" data-column-margin="default" data-midnight="dark" class="wpb_row vc_row-fluid vc_row full-width-content vc_row-o-equal-height vc_row-flex vc_row-o-content-top  right_padding_8pct left_padding_8pct top_padding_tablet_6pct bottom_padding_tablet_6pct" style="padding-top: 28px; padding-bottom: 28px; ">
  <div class="row-bg-wrap" data-bg-animation="none" data-bg-animation-delay="" data-bg-overlay="false">
    <div class="inner-wrap row-bg-layer">
      <div class="row-bg viewport-desktop using-bg-color" style="background-color: #f4ece2; "></div>
    </div>
  </div>
  <div class="row_col_wrap_12 col span_12 dark left">
    <div class="vc_col-sm-6 wpb_column column_container vc_column_container col no-extra-padding force-tablet-text-align-center force-phone-text-align-center inherit_tablet inherit_phone " data-padding-pos="all" data-has-bg-color="false" data-bg-color="" data-bg-opacity="1" data-animation="" data-delay="0">
      <div class="vc_column-inner">
        <div class="wpb_wrapper">

          <div class="wpb_text_column wpb_content_element ">
            <div class="wpb_wrapper">
              <p><b>© </b><span class="nectar-current-year">2025</span> Bloom Netball</p>
            </div>
          </div>

        </div>
      </div>
    </div>

    <div class="vc_col-sm-6 wpb_column column_container vc_column_container col no-extra-padding force-tablet-text-align-center force-phone-text-align-center inherit_tablet inherit_phone " data-padding-pos="all" data-has-bg-color="false" data-bg-color="" data-bg-opacity="1" data-animation="" data-delay="0">
      <div class="vc_column-inner">
        <div class="wpb_wrapper">
          <div id="fws_673def26b10e4" data-midnight="" data-column-margin="default" class="wpb_row vc_row-fluid vc_row inner_row  top_padding_tablet_10px" style="">
            <div class="row-bg-wrap">
              <div class="row-bg"></div>
            </div>
            <div class="row_col_wrap_12_inner col span_12  right">
              <div class="vc_col-sm-12 wpb_column column_container vc_column_container col child_column no-extra-padding el_spacing_0px inherit_tablet inherit_phone " data-padding-pos="all" data-has-bg-color="false" data-bg-color="" data-bg-opacity="1" data-animation="" data-delay="0">
                <div class="vc_column-inner">
                  <div class="wpb_wrapper">
                    <div class="nectar-cta  alignment_tablet_center alignment_phone_default display_tablet_inherit display_phone_inherit " data-color="default" data-using-bg="false" data-display="inline" data-style="underline" data-alignment="right" data-text-color="std">
                      <p> <span class="link_wrap" style="padding-right: 25px; padding-left: 25px;"><a class="link_text" role="button" href="https://staging1.bloomnetball.co.uk/terms-conditions/">Terms &amp; Conditions</a></span></p>
                    </div>
                    <div class="nectar-cta  alignment_tablet_center alignment_phone_default display_tablet_inherit display_phone_inherit " data-color="default" data-using-bg="false" data-display="inline" data-style="underline" data-alignment="right" data-text-color="std">
                      <p> <span class="link_wrap" style="padding-right: 25px; padding-left: 25px;"><a class="link_text" role="button" href="https://staging1.bloomnetball.co.uk/privacy-policy/">Privacy Policy</a></span></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
