<?php
$sessionSlug = get_query_var('c2c_session', false);

?>
<body class="<?= basename(__FILE__, ".php") ?>" data-c2cid="<?= (isset($_SESSION['c2c_multi_booking_id'])) ? $_SESSION['c2c_multi_booking_id'] : null ?>">
<link rel="stylesheet" href="/wp-content/themes/salient-child/css/c2c.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css"
        integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" type="text/css" href="/wp-content/themes/salient-child/css/c2c-details.css">
    <?php 
            global $wp;
        $url =  home_url( $wp->request );
        $buttonText = "Book Now!";
        $slug = end(explode('/', $url));
        //echo $slug;exit;
        $details = json_decode(file_get_contents("http://{$prefixUrl}api.leagues4you.co.uk/c2cSession/{$slug}"), true);


        function venueAddress($venue) {
            return $venue['address1'] . ", " . $venue['town'] . ", " . $venue['postcode'];
        }
        function formatLaunchDate($date) {
            $thisDate = new DateTime($date);
            $thisDate = $thisDate->format('d-m-Y');
            return $thisDate;
        }

        $bookHereImg = 'https://leagues4you.co.uk/wp-content/uploads/2021/08/img_llmz6_IMG5233.jpg';
        $joinFbGroupImg = 'https://leagues4you.co.uk/wp-content/uploads/2021/08/Netball-C2C-Fun.jpeg';
        $hardcodedYoutubeLink = 'https://www.youtube.com/embed/BnTIB2T94Fc';
        $hardcodedFbPageLink = 'https://www.facebook.com/leagues4you/';
    ?>

    <main style="min-height: 75vh;">

        <div class="col-md-12 col-sm-12 text-dark mt-4 text-center">

            <h4 class="text-center fw-bolder fs-1" style="text-transform: none; padding: 1rem 1rem 0rem 1rem; margin:0"><?php echo $details['venue']['name'] .  " Couch2Court®" ?? 'League not found';?></h4>
            <span class="text-center fw-bolder c2c_title">
                <a href="/couch2court/">
                    <i class="fa fa-arrow-left" aria-hidden="true"></i>
                    go back
                </a>
            </span>
        </div>

        <?php if(isset($details) && !empty($details)) { ?>
            <div class="league-grid-container">
                <div class="league-grid mt-4">
                    <div class="grid-cell team-entry">
                        <img src="<?= $bookHereImg ?>" alt="'Enter a team at ' <?= $details['venue']['name'] ?>">
                        <div class="enter-here">
                            <form action="/checkout" method="post" style="margin-bottom: -1;">
                                <button type="submit" name="tasterID" value="<?=$_GET['taster_session_id']?>" class="cbook-now">
                                    <?= $buttonText ?>
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="grid-cell venue-info">
                        <div class="venue-info-centre">
                            <div class="venue-info-centre-name text-capitalize">
                                <span>
                                    <?= trim($details['venue']['name'])?>
                                </span>
                            </div>
                            <address class="venue-info-centre-address">
                                <span>
                                    <?= venueAddress($details['venue']) ?>
                                </span>
                            </address>
                        </div>

                        <div class="venue-info-details">
                            <div class="details-grid-item details-grid-day">
                                <i class="fa-solid fa-calendar-days"></i>
                                    <span>
                                        <?=  formatLaunchDate($details['tasterSession']['date']) ?>
                                    </span>
                            </div>

                            <div class="details-grid-item details-grid-time">
                                <i class="fa-solid fa-clock"></i>
                                <span>
                                    <?=  date('H:i', strtotime($details['tasterSession']['time'])) ?>
                                </span>
                            </div>

                            <div class="details-grid-item details-grid-indoorOutdoor">
                                <i class="fa-solid fa-location-pin"></i>
                                <span class="text-capitalize">
                                    <?= $details['tasterSession']['location'] ?>
                                </span>
                            </div>

                            <div class="details-grid-item details-grid-fixtureCharge">
                                <i class="fa-solid fa-credit-card"></i>
                                <span>
                                    &pound;<?= $details['tasterSession']['charge'] ?>
                                </span>
                            </div>
                        </div>

                        <div class="venue-info-starts">
                            <span class="new-season-starts">Limited Spaces Remaining</span>
                        </div>
                    </div>

                    <div class="grid-cell join-a-team">
                        <img src="<?= $joinFbGroupImg ?>" alt="'Play at ' + <?= $details['venue']['name'] ?>">
                        <a class="join-here" href="<?= ensure_http_protocol($details['tasterSession']['fb_page'] ?? ($details['league']['league_fb_group'] ?? 'https://www.facebook.com/leagues4you/')) ?>"
                            target="_blank">
                            <i class="fa-brands fa-facebook"></i>
                            <span>join our group</span>
                        </a>
                    </div>

                    <div class="grid-cell league-info">
                        <?php if ($details['coordinator']): ?>
                            <div class="container your-coordinator">
                                <div class="row">
                                    <span>Your Coordinator is </span>
                                </div>
                                <div class="row" style="justify-content: center; align-items: center;">
                                    <div class="-profile-name col-6" style="color:#fff">
                                        <?= $details['coordinator']['firstname'] ?>
                                    </div>
                                    <div class="league-table-data-coodinator-profile-image  col-6">
                                        <?php if(isset($details['coordinator']['profilePictureUrl']) && !empty(isset($details['coordinator']['profilePictureUrl']))){ ?>
                                            <img src="<?= $details['coordinator']['profilePictureUrl'] ?>" class="rounded-circle" style="height: auto;"
                                                alt="<?= $details['coordinator']['firstname'] ?>">
                                        <?php } ?>
                                    </div>
                                </div>
                                <div class="row justify-content-center">
                                    <?php if ($details['coordinator']['email']): ?>
                                        <div class="contact-coordinator">
                                            <a href="mailto:<?= $details['coordinator']['email'] ?>" class="league-table-daa-coordinator-profile-link">
                                                <i class="fa-solid fa-envelope-circle-check"></i>
                                                <span>Contact <?= $details['coordinator']['firstname'] ?></span>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>

                            </div>                            
                        <?php endif; ?>

                    </div>

                    <div class="grid-cell leagues4you-video">
                        <iframe width="1273" height="716" src="<?= $hardcodedYoutubeLink ?>" 
                            title="leagues4you - We Rise By Lifting Others" 
                            frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen>
                        </iframe>
                        <div>
                            <i class="fa-brands fa-youtube"></i>
                            <span class="full-stop">We rise by lifting others</span>
                        </div>
                    </div>

                    <div class="grid-cell leagues4you-facebook">
                        <div class="leagues4you-facebook-header" style="height: 95px;">
                            <img src="https://leagues4you.co.uk/wp-content/uploads/2022/07/lets-get-social.jpg" alt="" style="height:90px;">
                        </div>
                        <div class="leagues4-you-facebook-feed">
                            <a href="<?= $hardcodedFbPageLink ?>" style="display: inline-block;height:130px; width: 300px;">
                                <img src="https://leagues4you.co.uk/wp-content/uploads/2022/07/facebook_bg_300x130.jpg" alt="leagues4you on Facebook" style="height: 100%; width: 100%;">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php }else { ?>
            <div class="message_parent">
                <span class="message">League Not found</span>   
            </div>
        <?php } ?>



        <div class="cfaq-page mt-4 p-5" id="more-about-couch2court">
            <div class="container mt-4">
                <h2 class="cfaq-title has-text-align-left">FAQS</h2>

                <div class="accordion mt-5" id="accordionExample">
                    <div class="accordion mt-5" id="accordionExample">
                        <?php foreach ($faqs as $index => $faq) : ?>
                            <div class="accordion-item2">
                                <h2 class="accordion-header" id="heading<?= $index ?>">
                                    <button class="accordion-button <?= $index != 0 ? 'collapsed' : '' ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?= $index ?>" aria-expanded="true" aria-controls="collapse<?= $index ?>">
                                        <?= $faq['name'] ?>
                                    </button>
                                </h2>
                                <div id="collapse<?= $index ?>" class="accordion-collapse collapse <?= $index === 0 ? 'show' : '' ?>" aria-labelledby="heading<?= $index ?>" data-bs-parent="#accordionExample">
                                    <div class="accordion-body">
                                        <?= $faq['description'] ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                </div>
                <p class="text-center faq-footer-text pt-5">
                    Any more questions please get in touch </br>
                    <a href="mailto:<EMAIL>" class="text-white"><EMAIL></a>
                </p>
            </div>
        </div>
    </main>

    <?php include("footer2.php"); ?>
    <script>
        function revealAddFriend() {
            document.getElementById("addFriendForm").classList.remove("d-none");
            document.getElementById("revealAddFriendBtn").classList.add("d-none");
        }

        function scrollToForm() {
            document.getElementById("wheres-my-nearest-couch2court").scrollIntoView();
        }
        let revealAddFriendBtn = document.getElementById("revealAddFriendBtn");
        if (revealAddFriendBtn) revealAddFriendBtn.addEventListener("click", revealAddFriend);
        <?= (isset($c2c_multi_booking)) ? 'scrollToForm();' : null ?>
        <?php
        if (isset($confirmation)) { ?>
            var confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'), {
                keyboard: false
            });
            confirmationModal.show();
        <?php
        } ?>
        console.log("IP", '<?= $_SERVER['REMOTE_ADDR'] ?>');
    </script>
    <?php wp_footer() ?>

</body>

</html>