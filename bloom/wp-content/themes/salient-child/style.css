/*
Theme Name: Salient Child Theme
Description: This is a custom child theme for Salient
Theme URI:   https://themeforest.net/item/salient-responsive-multipurpose-theme/4363266
Author: ThemeNectar
Author URI:  https://themeforest.net/user/themenectar
Template: salient
Version: 1.0
*/

.nectar-badge[data-bg-color-custom="#3452ff"] .nectar-badge__inner {
  background-color: #3452ff !important;
}

.wpb_column.right_padding_desktop_25pct > .vc_column-inner {
  padding-right: 25%;
}

.img-with-aniamtion-wrap.br_br_125px .img-with-animation,
.img-with-aniamtion-wrap.br_br_125px .inner,
.img-with-aniamtion-wrap.br_br_125px .hover-wrap {
  border-bottom-right-radius: 125px;
}
.img-with-aniamtion-wrap.bl_br_125px .img-with-animation,
.img-with-aniamtion-wrap.bl_br_125px .inner,
.img-with-aniamtion-wrap.bl_br_125px .hover-wrap {
  border-bottom-left-radius: 125px;
}
.img-with-aniamtion-wrap.tr_br_125px .img-with-animation,
.img-with-aniamtion-wrap.tr_br_125px .inner,
.img-with-aniamtion-wrap.tr_br_125px .hover-wrap {
  border-top-right-radius: 125px;
}
.img-with-aniamtion-wrap.tl_br_125px .img-with-animation,
.img-with-aniamtion-wrap.tl_br_125px .inner,
.img-with-aniamtion-wrap.tl_br_125px .hover-wrap {
  border-top-left-radius: 125px;
}
.wpb_column[data-cfc="true"] h1,
.wpb_column[data-cfc="true"] h2,
.wpb_column[data-cfc="true"] h3,
.wpb_column[data-cfc="true"] h4,
.wpb_column[data-cfc="true"] h5,
.wpb_column[data-cfc="true"] h6,
.wpb_column[data-cfc="true"] p {
  color: inherit;
}
.nectar-cta.has-icon .link_wrap i {
  margin-right: 0.7em;
  line-height: 1;
  font-size: 1.3em;
}
.nectar-cta.has-icon .link_wrap {
  display: flex;
  align-items: center;
}
.nectar-cta.has-icon .im-icon-wrap,
.nectar-cta.has-icon .im-icon-wrap * {
  display: block;
}
.linecon-icon-calendar::before,
.linecon-icon-clock::before {
  font-family: "linecons" !important;
}

@media only screen and (max-width: 999px) {
  .wpb_column.top_margin_tablet_25px {
    margin-top: 25px !important;
  }
  .vc_column-inner
    .wpb_wrapper
    .wpb_row.vc_row-fluid.vc_row.inner_row
    .row_col_wrap_12_inner.col.span_12
    .vc_column-inner {
    margin-bottom: 25px !important;
  }
  .row_col_wrap_12.col.span_12.dark.left > *:not(:first-child) {
    padding-top: 20px;
  }
}

ul.wpb_tabs_nav.ui-tabs-nav.clearfix {
  margin-bottom: 15px !important;
  display: flex;
  flex-wrap: nowrap;
}
ul.wpb_tabs_nav.ui-tabs-nav.clearfix li.tab-item.active-tab {
  float: none;
  flex-grow: 1;
  display: block;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
ul.wpb_tabs_nav.ui-tabs-nav.clearfix li.tab-item.active-tab a.active-tab {
  background-color: transparent;
  text-align: center;
  color: inherit;
  border: none;
  font-size: 1.2em;
  padding: 25px 30px;
}
.vc_row.inner_row.bottom_position_desktop_-8pct {
  bottom: -8%;
}

.vc_row.inner_row.row_position_absolute {
  position: absolute;
}
.vc_row.inner_row.min_width_desktop_152pct {
  min-width: 152%;
}

body
  .container-wrap
  .vc_row-fluid[data-column-margin="20px"]
  .wpb_column:not(.child_column),
body .container-wrap .inner_row[data-column-margin="20px"] .child_column {
  padding-left: 10px;
  padding-right: 10px;
}
.wpb_column[data-border-radius="20px"],
.wpb_column[data-border-radius="20px"] > .vc_column-inner,
.wpb_column[data-border-radius="20px"] > .vc_column-inner > .column-link,
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-bg-overlay-wrap,
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-image-bg-wrap[data-bg-animation="zoom-out-reveal"],
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-image-bg-wrap
  .column-image-bg,
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-image-bg-wrap[data-n-parallax-bg="true"],
.wpb_column[data-border-radius="20px"] > .n-sticky > .vc_column-inner,
.wpb_column[data-border-radius="20px"]
  > .n-sticky
  > .vc_column-inner
  > .column-bg-overlay-wrap {
  border-radius: 20px;
}
.vc_column-inner
  .wpb_wrapper
  .img-with-aniamtion-wrap.tl_br_125px.tr_br_125px.bl_br_125px.br_br_125px
  .inner
  .hover-wrap
  .hover-wrap-inner
  img.img-with-animation.skip-lazy {
  max-height: 287px;
}

.nectar-cta[data-style="arrow-animation"] .link_wrap .link_text > .text {
  color: #fff;
}

.nectar-cta[data-style="arrow-animation"] svg {
  color: #fff;
}

.nectar-cta[data-style="arrow-animation"] .line {
  background-color: #fff !important;
}

img.wp-smiley,
img.emoji {
  display: inline !important;
  border: none !important;
  box-shadow: none !important;
  height: 1em !important;
  width: 1em !important;
  margin: 0 0.07em !important;
  vertical-align: -0.1em !important;
  background: none !important;
  padding: 0 !important;
}
.safe-svg-cover {
  text-align: center;
}
.safe-svg-cover .safe-svg-inside {
  display: inline-block;
  max-width: 100%;
}
.safe-svg-cover svg {
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  width: 100%;
}
html body[data-header-resize="1"] .container-wrap,
html
  body[data-header-format="left-header"][data-header-resize="0"]
  .container-wrap,
html body[data-header-resize="0"] .container-wrap,
body[data-header-format="left-header"][data-header-resize="0"] .container-wrap {
  padding-top: 0;
}
.main-content > .row > #breadcrumbs.yoast {
  padding: 20px 0;
}
.safe-svg-cover {
  text-align: center;
}
.safe-svg-cover .safe-svg-inside {
  display: inline-block;
  max-width: 100%;
}
.safe-svg-cover svg {
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  width: 100%;
}
html body[data-header-resize="1"] .container-wrap,
html
  body[data-header-format="left-header"][data-header-resize="0"]
  .container-wrap,
html body[data-header-resize="0"] .container-wrap,
body[data-header-format="left-header"][data-header-resize="0"] .container-wrap {
  padding-top: 0;
}
.main-content > .row > #breadcrumbs.yoast {
  padding: 20px 0;
}

#header-outer .nectar-ext-menu-item .image-layer-outer,
#header-outer .nectar-ext-menu-item .image-layer,
#header-outer .nectar-ext-menu-item .color-overlay,
#slide-out-widget-area .nectar-ext-menu-item .image-layer-outer,
#slide-out-widget-area .nectar-ext-menu-item .color-overlay,
#slide-out-widget-area .nectar-ext-menu-item .image-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.nectar-ext-menu-item .inner-content {
  position: relative;
  z-index: 10;
  width: 100%;
}
.nectar-ext-menu-item .image-layer {
  background-size: cover;
  background-position: center;
  transition: opacity 0.25s ease 0.1s;
}
.nectar-ext-menu-item .image-layer video {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
#header-outer nav .nectar-ext-menu-item .image-layer:not(.loaded) {
  background-image: none !important;
}
#header-outer nav .nectar-ext-menu-item .image-layer {
  opacity: 0;
}
#header-outer nav .nectar-ext-menu-item .image-layer.loaded {
  opacity: 1;
}
.nectar-ext-menu-item span[class*="inherit-h"] + .menu-item-desc {
  margin-top: 0.4rem;
}
#mobile-menu .nectar-ext-menu-item .title,
#slide-out-widget-area .nectar-ext-menu-item .title,
.nectar-ext-menu-item .menu-title-text,
.nectar-ext-menu-item .menu-item-desc {
  position: relative;
}
.nectar-ext-menu-item .menu-item-desc {
  display: block;
  line-height: 1.4em;
}
body #slide-out-widget-area .nectar-ext-menu-item .menu-item-desc {
  line-height: 1.4em;
}
#mobile-menu .nectar-ext-menu-item .title,
#slide-out-widget-area .nectar-ext-menu-item:not(.style-img-above-text) .title,
.nectar-ext-menu-item:not(.style-img-above-text) .menu-title-text,
.nectar-ext-menu-item:not(.style-img-above-text) .menu-item-desc,
.nectar-ext-menu-item:not(.style-img-above-text) i:before,
.nectar-ext-menu-item:not(.style-img-above-text) .svg-icon {
  color: #fff;
}
#mobile-menu .nectar-ext-menu-item.style-img-above-text .title {
  color: inherit;
}
.sf-menu li ul li a .nectar-ext-menu-item .menu-title-text:after {
  display: none;
}
.menu-item .widget-area-active[data-margin="default"] > div:not(:last-child) {
  margin-bottom: 20px;
}
.nectar-ext-menu-item__button {
  display: inline-block;
  padding-top: 2em;
}
#header-outer
  nav
  li:not([class*="current"])
  > a
  .nectar-ext-menu-item
  .inner-content.has-button
  .title
  .menu-title-text {
  background-image: none;
}
.nectar-ext-menu-item__button .nectar-cta:not([data-color="transparent"]) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}
.nectar-ext-menu-item .color-overlay {
  transition: opacity 0.5s cubic-bezier(0.15, 0.75, 0.5, 1);
}
.nectar-ext-menu-item:hover .hover-zoom-in-slow .image-layer {
  transform: scale(1.15);
  transition: transform 4s cubic-bezier(0.1, 0.2, 0.7, 1);
}
.nectar-ext-menu-item:hover .hover-zoom-in-slow .color-overlay {
  transition: opacity 1.5s cubic-bezier(0.15, 0.75, 0.5, 1);
}
.nectar-ext-menu-item .hover-zoom-in-slow .image-layer {
  transition: transform 0.5s cubic-bezier(0.15, 0.75, 0.5, 1);
}
.nectar-ext-menu-item .hover-zoom-in-slow .color-overlay {
  transition: opacity 0.5s cubic-bezier(0.15, 0.75, 0.5, 1);
}
.nectar-ext-menu-item:hover .hover-zoom-in .image-layer {
  transform: scale(1.12);
}
.nectar-ext-menu-item .hover-zoom-in .image-layer {
  transition: transform 0.5s cubic-bezier(0.15, 0.75, 0.5, 1);
}
.nectar-ext-menu-item {
  display: flex;
  text-align: left;
}
#slide-out-widget-area .nectar-ext-menu-item .title,
#slide-out-widget-area .nectar-ext-menu-item .menu-item-desc,
#slide-out-widget-area .nectar-ext-menu-item .menu-title-text,
#mobile-menu .nectar-ext-menu-item .title,
#mobile-menu .nectar-ext-menu-item .menu-item-desc,
#mobile-menu .nectar-ext-menu-item .menu-title-text {
  color: inherit !important;
}
#slide-out-widget-area .nectar-ext-menu-item,
#mobile-menu .nectar-ext-menu-item {
  display: block;
}
#slide-out-widget-area .nectar-ext-menu-item .inner-content,
#mobile-menu .nectar-ext-menu-item .inner-content {
  width: 100%;
}
#slide-out-widget-area.fullscreen-alt .nectar-ext-menu-item,
#slide-out-widget-area.fullscreen .nectar-ext-menu-item {
  text-align: center;
}
#header-outer .nectar-ext-menu-item.style-img-above-text .image-layer-outer,
#slide-out-widget-area
  .nectar-ext-menu-item.style-img-above-text
  .image-layer-outer {
  position: relative;
}
#header-outer .nectar-ext-menu-item.style-img-above-text,
#slide-out-widget-area .nectar-ext-menu-item.style-img-above-text {
  flex-direction: column;
}
#header-space {
  background-color: #f5f1e8;
}
@media only screen and (min-width: 1000px) {
  body #ajax-content-wrap.no-scroll {
    min-height: calc(100vh - 130px);
    height: calc(100vh - 130px) !important;
  }
}
@media only screen and (min-width: 1000px) {
  #page-header-wrap.fullscreen-header,
  #page-header-wrap.fullscreen-header #page-header-bg,
  html:not(.nectar-box-roll-loaded)
    .nectar-box-roll
    > #page-header-bg.fullscreen-header,
  .nectar_fullscreen_zoom_recent_projects,
  #nectar_fullscreen_rows:not(.afterLoaded) > div {
    height: calc(100vh - 129px);
  }
  .wpb_row.vc_row-o-full-height.top-level,
  .wpb_row.vc_row-o-full-height.top-level > .col.span_12 {
    min-height: calc(100vh - 129px);
  }
  html:not(.nectar-box-roll-loaded)
    .nectar-box-roll
    > #page-header-bg.fullscreen-header {
    top: 130px;
  }
  .nectar-slider-wrap[data-fullscreen="true"]:not(.loaded),
  .nectar-slider-wrap[data-fullscreen="true"]:not(.loaded) .swiper-container {
    height: calc(100vh - 128px) !important;
  }
  .admin-bar .nectar-slider-wrap[data-fullscreen="true"]:not(.loaded),
  .admin-bar
    .nectar-slider-wrap[data-fullscreen="true"]:not(.loaded)
    .swiper-container {
    height: calc(100vh - 128px - 32px) !important;
  }
}
.admin-bar[class*="page-template-template-no-header"]
  .wpb_row.vc_row-o-full-height.top-level,
.admin-bar[class*="page-template-template-no-header"]
  .wpb_row.vc_row-o-full-height.top-level
  > .col.span_12 {
  min-height: calc(100vh - 32px);
}
body[class*="page-template-template-no-header"]
  .wpb_row.vc_row-o-full-height.top-level,
body[class*="page-template-template-no-header"]
  .wpb_row.vc_row-o-full-height.top-level
  > .col.span_12 {
  min-height: 100vh;
}
@media only screen and (max-width: 999px) {
  .using-mobile-browser
    #nectar_fullscreen_rows:not(.afterLoaded):not([data-mobile-disable="on"])
    > div {
    height: calc(100vh - 100px);
  }
  .using-mobile-browser .wpb_row.vc_row-o-full-height.top-level,
  .using-mobile-browser .wpb_row.vc_row-o-full-height.top-level > .col.span_12,
  [data-permanent-transparent="1"].using-mobile-browser
    .wpb_row.vc_row-o-full-height.top-level,
  [data-permanent-transparent="1"].using-mobile-browser
    .wpb_row.vc_row-o-full-height.top-level
    > .col.span_12 {
    min-height: calc(100vh - 100px);
  }
  html:not(.nectar-box-roll-loaded)
    .nectar-box-roll
    > #page-header-bg.fullscreen-header,
  .nectar_fullscreen_zoom_recent_projects,
  .nectar-slider-wrap[data-fullscreen="true"]:not(.loaded),
  .nectar-slider-wrap[data-fullscreen="true"]:not(.loaded) .swiper-container,
  #nectar_fullscreen_rows:not(.afterLoaded):not([data-mobile-disable="on"])
    > div {
    height: calc(100vh - 47px);
  }
  .wpb_row.vc_row-o-full-height.top-level,
  .wpb_row.vc_row-o-full-height.top-level > .col.span_12 {
    min-height: calc(100vh - 47px);
  }
  body[data-transparent-header="false"] #ajax-content-wrap.no-scroll {
    min-height: calc(100vh - 47px);
    height: calc(100vh - 47px);
  }
}
#nectar_fullscreen_rows {
  background-color: transparent;
}
body
  .container-wrap
  .wpb_row[data-column-margin="20px"]:not(.full-width-section):not(
    .full-width-content
  ) {
  margin-bottom: 20px;
}
body .container-wrap .vc_row-fluid[data-column-margin="20px"] > .span_12,
body
  .container-wrap
  .vc_row-fluid[data-column-margin="20px"]
  .full-page-inner
  > .container
  > .span_12,
body
  .container-wrap
  .vc_row-fluid[data-column-margin="20px"]
  .full-page-inner
  > .span_12 {
  margin-left: -10px;
  margin-right: -10px;
}
body
  .container-wrap
  .vc_row-fluid[data-column-margin="20px"]
  .wpb_column:not(.child_column),
body .container-wrap .inner_row[data-column-margin="20px"] .child_column {
  padding-left: 10px;
  padding-right: 10px;
}
.container-wrap
  .vc_row-fluid[data-column-margin="20px"].full-width-content
  > .span_12,
.container-wrap
  .vc_row-fluid[data-column-margin="20px"].full-width-content
  .full-page-inner
  > .span_12 {
  margin-left: 0;
  margin-right: 0;
  padding-left: 10px;
  padding-right: 10px;
}
.single-portfolio
  #full_width_portfolio
  .vc_row-fluid[data-column-margin="20px"].full-width-content
  > .span_12 {
  padding-right: 10px;
}
@media only screen and (max-width: 999px) and (min-width: 691px) {
  .vc_row-fluid[data-column-margin="20px"]
    > .span_12
    > .one-fourths:not([class*="vc_col-xs-"]),
  .vc_row-fluid
    .vc_row-fluid.inner_row[data-column-margin="20px"]
    > .span_12
    > .one-fourths:not([class*="vc_col-xs-"]) {
    margin-bottom: 20px;
  }
}
#ajax-content-wrap .vc_row.bottom_margin_0px {
  margin-bottom: 0;
}
#ajax-content-wrap .vc_row.left_padding_5pct .row_col_wrap_12,
.nectar-global-section .vc_row.left_padding_5pct .row_col_wrap_12 {
  padding-left: 5%;
}
#ajax-content-wrap .vc_row.right_padding_5pct .row_col_wrap_12,
.nectar-global-section .vc_row.right_padding_5pct .row_col_wrap_12 {
  padding-right: 5%;
}
.vc_row.inner_row.min_width_desktop_152pct {
  min-width: 152%;
}
.vc_row.inner_row.row_position_absolute {
  position: absolute;
}
.vc_row.inner_row.bottom_position_desktop_-8pct {
  bottom: -8%;
}
.vc_row.inner_row.row_position_relative {
  position: relative;
}
.vc_row.inner_row.top_position_desktop_10pct {
  top: 10%;
}
#ajax-content-wrap .vc_row.inner_row.left_padding_25vw .row_col_wrap_12_inner,
.nectar-global-section
  .vc_row.inner_row.left_padding_25vw
  .row_col_wrap_12_inner {
  padding-left: 25vw;
}
#ajax-content-wrap .vc_row.inner_row.right_padding_25vw .row_col_wrap_12_inner,
.nectar-global-section
  .vc_row.inner_row.right_padding_25vw
  .row_col_wrap_12_inner {
  padding-right: 25vw;
}
@media only screen, print {
  .wpb_column.top_padding_desktop_4vw > .vc_column-inner {
    padding-top: 4vw;
  }
  .wpb_column.right_padding_desktop_4vw > .vc_column-inner {
    padding-right: 4vw;
  }
  .wpb_column.bottom_padding_desktop_1vw > .vc_column-inner {
    padding-bottom: 1vw;
  }
  .wpb_column.left_padding_desktop_4vw > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.top_padding_tablet_8vw > .vc_column-inner {
    padding-top: 8vw;
  }
  body .wpb_column.right_padding_tablet_8vw > .vc_column-inner {
    padding-right: 8vw;
  }
  body .wpb_column.left_padding_tablet_8vw > .vc_column-inner {
    padding-left: 8vw;
  }
}
@media only screen, print {
  .wpb_column.top_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-top: 4vw;
  }
  .wpb_column.right_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-right: 4vw;
  }
  .wpb_column.bottom_padding_desktop_1vw > .n-sticky > .vc_column-inner {
    padding-bottom: 1vw;
  }
  .wpb_column.left_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.top_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-top: 8vw;
  }
  body .wpb_column.right_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-right: 8vw;
  }
  body .wpb_column.left_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-left: 8vw;
  }
}
.wpb_column[data-border-radius="20px"],
.wpb_column[data-border-radius="20px"] > .vc_column-inner,
.wpb_column[data-border-radius="20px"] > .vc_column-inner > .column-link,
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-bg-overlay-wrap,
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-image-bg-wrap[data-bg-animation="zoom-out-reveal"],
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-image-bg-wrap
  .column-image-bg,
.wpb_column[data-border-radius="20px"]
  > .vc_column-inner
  > .column-image-bg-wrap[data-n-parallax-bg="true"],
.wpb_column[data-border-radius="20px"] > .n-sticky > .vc_column-inner,
.wpb_column[data-border-radius="20px"]
  > .n-sticky
  > .vc_column-inner
  > .column-bg-overlay-wrap {
  border-radius: 20px;
}
@media only screen, print {
  .wpb_column.top_padding_desktop_4vw > .vc_column-inner {
    padding-top: 4vw;
  }
  .wpb_column.right_padding_desktop_4vw > .vc_column-inner {
    padding-right: 4vw;
  }
  .wpb_column.bottom_padding_desktop_4vw > .vc_column-inner {
    padding-bottom: 4vw;
  }
  .wpb_column.left_padding_desktop_4vw > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.top_padding_tablet_8vw > .vc_column-inner {
    padding-top: 8vw;
  }
  body .wpb_column.right_padding_tablet_8vw > .vc_column-inner {
    padding-right: 8vw;
  }
  body .wpb_column.bottom_padding_tablet_8vw > .vc_column-inner {
    padding-bottom: 8vw;
  }
  body .wpb_column.left_padding_tablet_8vw > .vc_column-inner {
    padding-left: 8vw;
  }
}
@media only screen, print {
  .wpb_column.top_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-top: 4vw;
  }
  .wpb_column.right_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-right: 4vw;
  }
  .wpb_column.bottom_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-bottom: 4vw;
  }
  .wpb_column.left_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.top_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-top: 8vw;
  }
  body .wpb_column.right_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-right: 8vw;
  }
  body .wpb_column.bottom_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-bottom: 8vw;
  }
  body .wpb_column.left_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-left: 8vw;
  }
}
.wpb_column[data-cfc="true"] h1,
.wpb_column[data-cfc="true"] h2,
.wpb_column[data-cfc="true"] h3,
.wpb_column[data-cfc="true"] h4,
.wpb_column[data-cfc="true"] h5,
.wpb_column[data-cfc="true"] h6,
.wpb_column[data-cfc="true"] p {
  color: inherit;
}
@media only screen, print {
  .wpb_column.top_padding_desktop_4vw > .vc_column-inner {
    padding-top: 4vw;
  }
  .wpb_column.right_padding_desktop_4vw > .vc_column-inner {
    padding-right: 4vw;
  }
  .wpb_column.bottom_padding_desktop_1vw > .vc_column-inner {
    padding-bottom: 1vw;
  }
  .wpb_column.left_padding_desktop_4vw > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.top_padding_tablet_8vw > .vc_column-inner {
    padding-top: 8vw;
  }
  body .wpb_column.right_padding_tablet_4vw > .vc_column-inner {
    padding-right: 4vw;
  }
  body .wpb_column.left_padding_tablet_4vw > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen, print {
  .wpb_column.top_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-top: 4vw;
  }
  .wpb_column.right_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-right: 4vw;
  }
  .wpb_column.bottom_padding_desktop_1vw > .n-sticky > .vc_column-inner {
    padding-bottom: 1vw;
  }
  .wpb_column.left_padding_desktop_4vw > .n-sticky > .vc_column-inner {
    padding-left: 4vw;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.top_padding_tablet_8vw > .n-sticky > .vc_column-inner {
    padding-top: 8vw;
  }
  body .wpb_column.right_padding_tablet_4vw > .n-sticky > .vc_column-inner {
    padding-right: 4vw;
  }
  body .wpb_column.left_padding_tablet_4vw > .n-sticky > .vc_column-inner {
    padding-left: 4vw;
  }
}
.wpb_column.el_spacing_0px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 0;
}
.wpb_column[data-border-radius="15px"],
.wpb_column[data-border-radius="15px"] > .vc_column-inner,
.wpb_column[data-border-radius="15px"] > .vc_column-inner > .column-link,
.wpb_column[data-border-radius="15px"]
  > .vc_column-inner
  > .column-bg-overlay-wrap,
.wpb_column[data-border-radius="15px"]
  > .vc_column-inner
  > .column-image-bg-wrap[data-bg-animation="zoom-out-reveal"],
.wpb_column[data-border-radius="15px"]
  > .vc_column-inner
  > .column-image-bg-wrap
  .column-image-bg,
.wpb_column[data-border-radius="15px"]
  > .vc_column-inner
  > .column-image-bg-wrap[data-n-parallax-bg="true"],
.wpb_column[data-border-radius="15px"] > .n-sticky > .vc_column-inner,
.wpb_column[data-border-radius="15px"]
  > .n-sticky
  > .vc_column-inner
  > .column-bg-overlay-wrap {
  border-radius: 15px;
}
.wpb_column.el_spacing_20px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 20px;
}
.wpb_column.child_column.tl_br_20px > .vc_column-inner > div[class*="-wrap"],
.wpb_column.child_column.tl_br_20px > .vc_column-inner {
  border-top-left-radius: 20px;
  overflow: hidden;
}
@media only screen, print {
  .wpb_column.right_padding_desktop_25pct > .vc_column-inner {
    padding-right: 25%;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.right_padding_tablet_33vw > .vc_column-inner {
    padding-right: 33vw;
  }
}
@media only screen and (max-width: 690px) {
  html body .wpb_column.right_padding_phone_0px > .vc_column-inner {
    padding-right: 0;
  }
}
@media only screen, print {
  .wpb_column.right_padding_desktop_25pct > .n-sticky > .vc_column-inner {
    padding-right: 25%;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.right_padding_tablet_33vw > .n-sticky > .vc_column-inner {
    padding-right: 33vw;
  }
}
@media only screen and (max-width: 690px) {
  html body .wpb_column.right_padding_phone_0px > .n-sticky > .vc_column-inner {
    padding-right: 0;
  }
}
.wpb_column.child_column.el_spacing_10px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 10px;
}
.img-with-aniamtion-wrap.tl_br_125px .img-with-animation,
.img-with-aniamtion-wrap.tl_br_125px .inner,
.img-with-aniamtion-wrap.tl_br_125px .hover-wrap {
  border-top-left-radius: 125px;
}
.img-with-aniamtion-wrap.tr_br_125px .img-with-animation,
.img-with-aniamtion-wrap.tr_br_125px .inner,
.img-with-aniamtion-wrap.tr_br_125px .hover-wrap {
  border-top-right-radius: 125px;
}
.img-with-aniamtion-wrap.bl_br_125px .img-with-animation,
.img-with-aniamtion-wrap.bl_br_125px .inner,
.img-with-aniamtion-wrap.bl_br_125px .hover-wrap {
  border-bottom-left-radius: 125px;
}
.img-with-aniamtion-wrap.br_br_125px .img-with-animation,
.img-with-aniamtion-wrap.br_br_125px .inner,
.img-with-aniamtion-wrap.br_br_125px .hover-wrap {
  border-bottom-right-radius: 125px;
}
.nectar-cta.has-icon .link_wrap {
  display: flex;
  align-items: center;
}
.nectar-cta.has-icon .link_wrap i {
  margin-right: 0.7em;
  line-height: 1;
  font-size: 1.3em;
}
.nectar-cta.has-icon .link_wrap .link_text {
  line-height: 1;
}
.nectar-cta.has-icon .link_wrap i svg {
  width: 1.3em;
  fill: currentColor;
}
.nectar-cta.has-icon .im-icon-wrap,
.nectar-cta.has-icon .im-icon-wrap * {
  display: block;
}
.nectar-text-inline-images {
  position: relative;
  opacity: 0;
  transition: opacity 0.2s ease;
}
@media only screen and (max-width: 999px) {
  .nectar-text-inline-images--rm-mobile-animation {
    transition: none;
  }
}
body .nectar-text-inline-images a {
  color: inherit;
}
.nectar-text-inline-images .nectar-text-inline-images__inner > *:last-child {
  margin-bottom: 0;
}
.nectar-text-inline-images__marker {
  display: inline-block;
  position: relative;
  min-width: 10px;
  clip-path: inset(6%);
}
body .row .nectar-text-inline-images__marker img {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  max-width: none;
  width: auto;
}
.nectar-text-inline-images__marker video {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.nectar-text-inline-images--calculated {
  opacity: 1;
}
@keyframes nectarClipFade {
  0% {
    opacity: 0;
    clip-path: circle(10%);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    clip-path: circle(42%);
  }
}
.nectar-text-inline-images--animation_circle_fade_in
  .nectar-text-inline-images__marker {
  clip-path: none;
}
.nectar-text-inline-images--animation_circle_fade_in img {
  clip-path: circle(15%);
  opacity: 0;
}
.nectar-text-inline-images--animation_circle_fade_in
  .nectar-text-inline-images__marker.animated-in
  img {
  animation: nectarClipFade 1.8s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}
@media only screen and (min-width: 1000px) {
  body .row .nectar-text-inline-images.font_size_desktop_4vw * {
    font-size: 4vw;
    line-height: 1.4;
  }
}
@media only screen, print {
  .nectar-text-inline-images.right_margin_desktop_-6pct
    .nectar-text-inline-images__marker {
    margin-right: -6%;
  }
}
.nectar-badge__inner {
  display: inline-block;
  line-height: 1;
  color: #fff;
  border-radius: 20px;
}
.nectar-badge.position_desktop_absolute {
  white-space: nowrap;
}
.padding-amount-small .nectar-badge__inner {
  padding: 0.5em 1em;
}
.nectar-badge[data-bg-color-custom="#3452ff"] .nectar-badge__inner {
  background-color: #3452ff;
}
.nectar-badge.text-color-ffffff .nectar-badge__inner {
  color: #ffffff;
}
.nectar-lottie-wrap {
  line-height: 0;
}
.nectar-lottie-wrap .nectar-lottie {
  width: 100%;
  height: 100%;
}
.wpb_wrapper.tabbed {
  position: relative;
}
@media only screen, print {
  .nectar-lottie-wrap.width_desktop_80pct {
    width: 80%;
  }
}
.nectar-lottie-wrap.alignment_center {
  display: flex;
  margin: 0 auto;
  justify-content: center;
}
#ajax-content-wrap .nectar-responsive-text * {
  margin-bottom: 0;
  color: inherit;
}
#ajax-content-wrap .nectar-responsive-text[class*="font_size"] * {
  font-size: inherit;
  line-height: inherit;
}
.nectar-responsive-text.nectar-link-underline-effect a {
  text-decoration: none;
}
@media only screen, print {
  #ajax-content-wrap .font_size_desktop_1-8vw.nectar-responsive-text,
  .font_size_desktop_1-8vw.nectar-responsive-text {
    font-size: 1.8vw;
  }
}
@media only screen, print {
  #ajax-content-wrap .font_size_desktop_1-8vw.nectar-responsive-text,
  .font_size_desktop_1-8vw.nectar-responsive-text {
    font-size: 1.8vw;
  }
}
@media only screen and (max-width: 999px) {
  #ajax-content-wrap .font_size_tablet_22px.nectar-responsive-text,
  .font_size_tablet_22px.nectar-responsive-text {
    font-size: 22px;
  }
}
@media only screen and (max-width: 999px) {
  #ajax-content-wrap
    .container-wrap
    .font_size_tablet_22px.nectar-responsive-text,
  .container-wrap .font_size_tablet_22px.nectar-responsive-text {
    font-size: 22px;
  }
}
#ajax-content-wrap .font_line_height_1-3.nectar-responsive-text {
  line-height: 1.3;
}
@media only screen, print {
  body
    #ajax-content-wrap
    .font_size_desktop_1-1vw.font_size_min_14px.font_size_max_20px.nectar-responsive-text,
  body
    .font_size_desktop_1-1vw.font_size_min_14px.font_size_max_20px.nectar-responsive-text {
    font-size: min(20px, max(14px, 1.1vw));
  }
}
@media only screen, print {
  body
    #ajax-content-wrap
    .font_size_desktop_1-1vw.font_size_min_14px.font_size_max_20px.nectar-responsive-text,
  body
    .font_size_desktop_1-1vw.font_size_min_14px.font_size_max_20px.nectar-responsive-text {
    font-size: min(20px, max(14px, 1.1vw));
  }
}
#ajax-content-wrap .font_line_height_1-5.nectar-responsive-text {
  line-height: 1.5;
}
.toggles--minimal-shadow .toggle > .toggle-title a {
  color: inherit;
}
.toggles--minimal-shadow .toggle.default > .toggle-title a:hover,
.toggles--minimal-shadow .toggle.default.open > .toggle-title a {
  color: #000;
}
.span_12.light .toggles--minimal-shadow .toggle.default > .toggle-title a:hover,
.span_12.light .toggles--minimal-shadow .toggle.default.open > .toggle-title a {
  color: #fff;
}
.toggles--minimal-shadow .toggle > .toggle-title i:before,
.toggles--minimal-shadow .toggle > .toggle-title i:after {
  background-color: #888;
}
.toggles--minimal-shadow .toggle.default.open > .toggle-title i:after,
.toggles--minimal-shadow .toggle.default.open > .toggle-title i:before,
.toggles--minimal-shadow .toggle.default:hover > .toggle-title i:after,
.toggles--minimal-shadow .toggle.default:hover > .toggle-title i:before {
  background-color: #000;
}
.toggles--minimal-shadow .toggle.default.open > .toggle-title i,
.toggles--minimal-shadow .toggle.default:hover > .toggle-title i {
  border-color: #000;
}
.span_12.light
  .toggles--minimal-shadow
  .toggle.default.open
  > .toggle-title
  i:after,
.span_12.light
  .toggles--minimal-shadow
  .toggle.default.open
  > .toggle-title
  i:before,
.span_12.light
  .toggles--minimal-shadow
  .toggle.default:hover
  > .toggle-title
  i:after,
.span_12.light
  .toggles--minimal-shadow
  .toggle.default:hover
  > .toggle-title
  i:before {
  background-color: #fff;
}
.span_12.light .toggles--minimal-shadow .toggle.default.open > .toggle-title i,
.span_12.light
  .toggles--minimal-shadow
  .toggle.default:hover
  > .toggle-title
  i {
  border-color: #fff;
}
.toggles--minimal-shadow
  .toggle[data-inner-wrap="true"]
  > div
  .inner-toggle-wrap {
  padding: 0 0 30px 0;
}
.toggles--minimal-shadow .toggle > .toggle-title a {
  padding: 30px 70px 30px 0;
  transition: color 0.15s ease;
}
.toggles--minimal-shadow .toggle {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}
body .toggles--minimal-shadow .toggle {
  margin-bottom: 0;
  padding: 0 40px;
  position: relative;
  transition: border-color 0.15s ease;
}
div[data-style*="minimal"] .toggle.open {
  border-color: transparent;
}
.toggles--minimal-shadow .toggle:before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  width: 100%;
  height: 100%;
  box-shadow: 0 90px 70px 0 rgba(0, 0, 0, 0.04),
    0 40px 35px 0 rgba(0, 0, 0, 0.03), 0 25px 15px 0 rgba(0, 0, 0, 0.03),
    0 11px 7px 0 rgba(0, 0, 0, 0.03), 0 2px 5px 0 rgba(0, 0, 0, 0.03);
  transition: opacity 0.15s ease;
  opacity: 0;
}
div[data-style*="minimal"] .toggle.open:before {
  opacity: 1;
  transition: opacity 0.45s cubic-bezier(0.3, 0.4, 0.2, 1);
}
.toggles--minimal-shadow .toggle > .toggle-title i:before {
  content: " ";
  top: 14px;
  left: 6px;
  margin-top: -2px;
  width: 14px;
  height: 2px;
  position: absolute;
  transition: transform 0.45s cubic-bezier(0.3, 0.4, 0.2, 1),
    background-color 0.15s ease;
}
.toggles--minimal-shadow .toggle > .toggle-title i:after {
  content: " ";
  top: 6px;
  left: 14px;
  width: 2px;
  margin-left: -2px;
  height: 14px;
  position: absolute;
  transition: transform 0.45s cubic-bezier(0.3, 0.4, 0.2, 1),
    background-color 0.15s ease;
}
.light .toggles--minimal-shadow .toggle {
  border-color: rgba(255, 255, 255, 0.2);
}
div[data-style*="minimal"].toggles--minimal-shadow .toggle i {
  transition: transform 0.45s cubic-bezier(0.3, 0.4, 0.2, 1),
    border-color 0.15s ease;
}
div[data-style*="minimal"] .toggle.open .toggle-title i {
  transform: rotate(90deg);
}
div[data-style*="minimal"] .toggle.open .toggle-title i:before {
  -ms-transform: scale(0, 1);
  transform: scale(0, 1);
  -webkit-transform: scale(0, 1);
}
div[data-style*="minimal"] .toggle.open .toggle-title i:after {
  -ms-transform: scale(1, 1);
  transform: scale(1, 1);
  -webkit-transform: scale(1, 1);
}
body .toggles--minimal-shadow .toggle > .toggle-title i:before,
body .toggles--minimal-shadow .toggle > .toggle-title i:after {
  background-color: #202020;
}
body
  .dark
  div[data-style*="minimal"].toggles--minimal-shadow
  .toggle:not(.open):not(:hover)
  > .toggle-title
  i {
  border-color: #202020;
}
@media only screen and (max-width: 999px) {
  .wpb_column.bottom_margin_tablet_20px {
    margin-bottom: 20px !important;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.right_padding_tablet_33vw > .n-sticky > .vc_column-inner {
    padding-right: 33vw;
  }
}
@media only screen and (max-width: 690px) {
  html body .wpb_column.right_padding_phone_0px > .n-sticky > .vc_column-inner {
    padding-right: 0;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.right_padding_tablet_33vw > .vc_column-inner {
    padding-right: 33vw;
  }
}
@media only screen and (max-width: 690px) {
  html body .wpb_column.right_padding_phone_0px > .vc_column-inner {
    padding-right: 0;
  }
}
@media only screen and (max-width: 999px) {
  .divider-wrap.height_tablet_60vw > .divider {
    height: 60vw !important;
  }
}
@media only screen and (max-width: 999px) {
  .nectar-cta.display_tablet_inherit {
    display: inherit;
  }
}
@media only screen and (max-width: 999px) {
  body .row .nectar-text-inline-images.font_size_tablet_4vw * {
    font-size: 4vw;
    line-height: 1.4;
  }
}
@media only screen and (max-width: 690px) {
  #ajax-content-wrap .vc_row.left_padding_phone_14px .row_col_wrap_12 {
    padding-left: 14px !important;
  }
}
@media only screen and (max-width: 690px) {
  .nectar-cta.display_phone_inherit {
    display: inherit;
  }
}
@media only screen and (max-width: 690px) {
  #ajax-content-wrap .vc_row.right_padding_phone_14px .row_col_wrap_12 {
    padding-right: 14px !important;
  }
}
@media only screen and (max-width: 690px) {
  body .row .nectar-text-inline-images.font_size_phone_5vw * {
    font-size: 5vw;
    line-height: 1.4;
  }
}
.nectar-shape-divider-wrap {
  position: absolute;
  top: auto;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 150px;
  z-index: 3;
  transform: translateZ(0);
}
.post-area.span_9 .nectar-shape-divider-wrap {
  overflow: hidden;
}
.nectar-shape-divider-wrap[data-front="true"] {
  z-index: 50;
}
.nectar-shape-divider-wrap[data-style="waves_opacity"] svg path:first-child {
  opacity: 0.6;
}
.nectar-shape-divider-wrap[data-style="curve_opacity"] svg path:nth-child(1),
.nectar-shape-divider-wrap[data-style="waves_opacity_alt"]
  svg
  path:nth-child(1) {
  opacity: 0.15;
}
.nectar-shape-divider-wrap[data-style="curve_opacity"] svg path:nth-child(2),
.nectar-shape-divider-wrap[data-style="waves_opacity_alt"]
  svg
  path:nth-child(2) {
  opacity: 0.3;
}
.nectar-shape-divider {
  width: 100%;
  left: 0;
  bottom: -1px;
  height: 100%;
  position: absolute;
}
.nectar-shape-divider-wrap.no-color .nectar-shape-divider {
  fill: #fff;
}
@media only screen and (max-width: 999px) {
  .nectar-shape-divider-wrap:not([data-using-percent-val="true"])
    .nectar-shape-divider {
    height: 75%;
  }
  .nectar-shape-divider-wrap[data-style="clouds"]:not(
      [data-using-percent-val="true"]
    )
    .nectar-shape-divider {
    height: 55%;
  }
}
@media only screen and (max-width: 690px) {
  .nectar-shape-divider-wrap:not([data-using-percent-val="true"])
    .nectar-shape-divider {
    height: 33%;
  }
  .nectar-shape-divider-wrap[data-style="clouds"]:not(
      [data-using-percent-val="true"]
    )
    .nectar-shape-divider {
    height: 33%;
  }
}
#ajax-content-wrap
  .nectar-shape-divider-wrap[data-height="1"]
  .nectar-shape-divider,
#ajax-content-wrap
  .nectar-shape-divider-wrap[data-height="1px"]
  .nectar-shape-divider {
  height: 1px;
}
.nectar-shape-divider-wrap[data-position="top"] {
  top: -1px;
  bottom: auto;
}
.nectar-shape-divider-wrap[data-position="top"] {
  transform: rotate(180deg);
}
#ajax-content-wrap .vc_row.left_padding_8pct .row_col_wrap_12,
.nectar-global-section .vc_row.left_padding_8pct .row_col_wrap_12 {
  padding-left: 8%;
}
#ajax-content-wrap .vc_row.right_padding_8pct .row_col_wrap_12,
.nectar-global-section .vc_row.right_padding_8pct .row_col_wrap_12 {
  padding-right: 8%;
}
#ajax-content-wrap .vc_row.inner_row.right_padding_40pct .row_col_wrap_12_inner,
.nectar-global-section
  .vc_row.inner_row.right_padding_40pct
  .row_col_wrap_12_inner {
  padding-right: 40%;
}
body .container-wrap .vc_row-fluid[data-column-margin="50px"] > .span_12,
body
  .container-wrap
  .vc_row-fluid[data-column-margin="50px"]
  .full-page-inner
  > .container
  > .span_12,
body
  .container-wrap
  .vc_row-fluid[data-column-margin="50px"]
  .full-page-inner
  > .span_12 {
  margin-left: -25px;
  margin-right: -25px;
}
body
  .container-wrap
  .vc_row-fluid[data-column-margin="50px"]
  .wpb_column:not(.child_column),
body .container-wrap .inner_row[data-column-margin="50px"] .child_column {
  padding-left: 25px;
  padding-right: 25px;
}
.container-wrap
  .vc_row-fluid[data-column-margin="50px"].full-width-content
  > .span_12,
.container-wrap
  .vc_row-fluid[data-column-margin="50px"].full-width-content
  .full-page-inner
  > .span_12 {
  margin-left: 0;
  margin-right: 0;
  padding-left: 25px;
  padding-right: 25px;
}
.single-portfolio
  #full_width_portfolio
  .vc_row-fluid[data-column-margin="50px"].full-width-content
  > .span_12 {
  padding-right: 25px;
}
@media only screen and (max-width: 999px) and (min-width: 691px) {
  .vc_row-fluid[data-column-margin="50px"]
    > .span_12
    > .one-fourths:not([class*="vc_col-xs-"]),
  .vc_row-fluid
    .vc_row-fluid.inner_row[data-column-margin="50px"]
    > .span_12
    > .one-fourths:not([class*="vc_col-xs-"]) {
    margin-bottom: 50px;
  }
}
.wpb_column.border_style_solid > .vc_column-inner,
.wpb_column.border_style_solid > .n-sticky > .vc_column-inner {
  border-style: solid;
}
.col.padding-2-percent > .vc_column-inner,
.col.padding-2-percent > .n-sticky > .vc_column-inner {
  padding: calc(600px * 0.03);
}
@media only screen and (max-width: 690px) {
  .col.padding-2-percent > .vc_column-inner,
  .col.padding-2-percent > .n-sticky > .vc_column-inner {
    padding: calc(100vw * 0.03);
  }
}
@media only screen and (min-width: 1000px) {
  .col.padding-2-percent > .vc_column-inner,
  .col.padding-2-percent > .n-sticky > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.02);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-2-percent
    > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.01);
  }
}
@media only screen and (min-width: 1425px) {
  .col.padding-2-percent > .vc_column-inner {
    padding: calc(1245px * 0.02);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-2-percent
    > .vc_column-inner {
    padding: calc(1245px * 0.01);
  }
}
.full-width-content .col.padding-2-percent > .vc_column-inner {
  padding: calc(100vw * 0.02);
}
@media only screen and (max-width: 999px) {
  .full-width-content .col.padding-2-percent > .vc_column-inner {
    padding: calc(100vw * 0.03);
  }
}
@media only screen and (min-width: 1000px) {
  .full-width-content
    .column_container:not(.vc_col-sm-12)
    .col.padding-2-percent
    > .vc_column-inner {
    padding: calc(100vw * 0.01);
  }
}
body #ajax-content-wrap .col[data-padding-pos="bottom"] > .vc_column-inner,
#ajax-content-wrap
  .col[data-padding-pos="bottom"]
  > .n-sticky
  > .vc_column-inner {
  padding-right: 0;
  padding-top: 0;
  padding-left: 0;
}
.wpb_column.child_column.el_spacing_10px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 10px;
}
.col.padding-5-percent > .vc_column-inner,
.col.padding-5-percent > .n-sticky > .vc_column-inner {
  padding: calc(600px * 0.06);
}
@media only screen and (max-width: 690px) {
  .col.padding-5-percent > .vc_column-inner,
  .col.padding-5-percent > .n-sticky > .vc_column-inner {
    padding: calc(100vw * 0.06);
  }
}
@media only screen and (min-width: 1000px) {
  .col.padding-5-percent > .vc_column-inner,
  .col.padding-5-percent > .n-sticky > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.05);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-5-percent
    > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.025);
  }
}
@media only screen and (min-width: 1425px) {
  .col.padding-5-percent > .vc_column-inner {
    padding: calc(1245px * 0.05);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-5-percent
    > .vc_column-inner {
    padding: calc(1245px * 0.025);
  }
}
.full-width-content .col.padding-5-percent > .vc_column-inner {
  padding: calc(100vw * 0.05);
}
@media only screen and (max-width: 999px) {
  .full-width-content .col.padding-5-percent > .vc_column-inner {
    padding: calc(100vw * 0.06);
  }
}
@media only screen and (min-width: 1000px) {
  .full-width-content
    .column_container:not(.vc_col-sm-12)
    .col.padding-5-percent
    > .vc_column-inner {
    padding: calc(100vw * 0.025);
  }
}
body #ajax-content-wrap .col[data-padding-pos="left"] > .vc_column-inner,
#ajax-content-wrap
  .col[data-padding-pos="left"]
  > .n-sticky
  > .vc_column-inner {
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.wpb_column.child_column.el_spacing_0px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 0;
}
.nectar-split-heading .heading-line {
  display: block;
  overflow: hidden;
  position: relative;
}
.nectar-split-heading .heading-line > div {
  display: block;
  transform: translateY(200%);
  -webkit-transform: translateY(200%);
}
.nectar-split-heading h1 {
  margin-bottom: 0;
}
@media only screen and (min-width: 1000px) {
  .nectar-split-heading[data-custom-font-size="true"] h1,
  .nectar-split-heading[data-custom-font-size="true"] h2,
  .row .nectar-split-heading[data-custom-font-size="true"] h3,
  .row .nectar-split-heading[data-custom-font-size="true"] h4,
  .row .nectar-split-heading[data-custom-font-size="true"] h5,
  .row .nectar-split-heading[data-custom-font-size="true"] h6,
  .row .nectar-split-heading[data-custom-font-size="true"] i {
    font-size: inherit;
    line-height: inherit;
  }
}
.nectar-split-heading.font_line_height_1-6 {
  line-height: 1.6 !important;
}
.nectar-split-heading.font_line_height_1-6 * {
  line-height: 1.6 !important;
}
@media only screen, print {
  #ajax-content-wrap .font_size_1-05vw.nectar-split-heading,
  .font_size_1-05vw.nectar-split-heading {
    font-size: 1.05vw !important;
  }
}
@media only screen, print {
  #ajax-content-wrap .font_size_1-05vw.nectar-split-heading,
  .font_size_1-05vw.nectar-split-heading {
    font-size: 1.05vw !important;
  }
}
@media only screen and (max-width: 999px) {
  #ajax-content-wrap .font_size_tablet_16px.nectar-split-heading,
  .font_size_tablet_16px.nectar-split-heading {
    font-size: 16px !important;
  }
}
@media only screen and (max-width: 999px) {
  #ajax-content-wrap
    .container-wrap
    .font_size_tablet_16px.nectar-split-heading,
  .container-wrap .font_size_tablet_16px.nectar-split-heading {
    font-size: 16px !important;
  }
}
#ajax-content-wrap .font_line_height_1-6.nectar-split-heading {
  line-height: 1.6;
}
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h1,
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h2,
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h3,
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h4 {
  margin: 0 auto;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"]:not(
    .markup-generated
  ) {
  opacity: 0;
}
@media only screen and (max-width: 999px) {
  .nectar-split-heading[data-m-rm-animation="true"] {
    opacity: 1 !important;
  }
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"] > * > span {
  position: relative;
  display: inline-block;
  overflow: hidden;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"] span {
  vertical-align: bottom;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"] span,
.nectar-split-heading[data-animation-type="line-reveal-by-space"]:not(
    .markup-generated
  )
  > * {
  line-height: 1.2;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-stagger="true"]:not(
    [data-text-effect*="letter-reveal"]
  )
  span
  .inner {
  transition: transform 1.2s cubic-bezier(0.25, 1, 0.5, 1),
    opacity 1.2s cubic-bezier(0.25, 1, 0.5, 1);
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"] span .inner {
  position: relative;
  display: inline-block;
  -webkit-transform: translateY(1.3em);
  transform: translateY(1.3em);
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"]
  span
  .inner.animated {
  -webkit-transform: none;
  transform: none;
  opacity: 1;
}
#ajax-content-wrap .nectar-split-heading[data-text-effect="none"] {
  opacity: 1;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-align="left"] {
  display: flex;
  justify-content: flex-start;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-align="center"] {
  display: flex;
  justify-content: center;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-align="right"] {
  display: flex;
  justify-content: flex-end;
}
@media only screen and (max-width: 999px) {
  .nectar-split-heading[data-animation-type="line-reveal-by-space"][data-m-align="left"] {
    display: flex;
    justify-content: flex-start;
  }
  .nectar-split-heading[data-animation-type="line-reveal-by-space"][data-m-align="center"] {
    display: flex;
    justify-content: center;
  }
  .nectar-split-heading[data-animation-type="line-reveal-by-space"][data-m-align="right"] {
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-width: 999px) {
  .wpb_column.child_column.bottom_margin_tablet_0px {
    margin-bottom: 0 !important;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.force-tablet-text-align-left,
  body .wpb_column.force-tablet-text-align-left .col {
    text-align: left !important;
  }
  body .wpb_column.force-tablet-text-align-right,
  body .wpb_column.force-tablet-text-align-right .col {
    text-align: right !important;
  }
  body .wpb_column.force-tablet-text-align-center,
  body .wpb_column.force-tablet-text-align-center .col,
  body .wpb_column.force-tablet-text-align-center .vc_custom_heading,
  body .wpb_column.force-tablet-text-align-center .nectar-cta {
    text-align: center !important;
  }
  .wpb_column.force-tablet-text-align-center .img-with-aniamtion-wrap img {
    display: inline-block;
  }
}
@media only screen and (max-width: 999px) {
  .nectar-cta.display_tablet_inherit {
    display: inherit;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.inner_row.bottom_padding_tablet_10pct {
    padding-bottom: 10% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.bottom_padding_tablet_6pct {
    padding-bottom: 6% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.top_padding_tablet_12pct {
    padding-top: 12% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.bottom_padding_tablet_12pct {
    padding-bottom: 12% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.top_padding_tablet_6pct {
    padding-top: 6% !important;
  }
}
@media only screen and (max-width: 999px) {
  .nectar-split-heading.font_size_tablet_16px * {
    font-size: inherit !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.inner_row.top_padding_tablet_10px {
    padding-top: 10px !important;
  }
}
@media only screen and (max-width: 999px) {
  body .nectar-cta.alignment_tablet_center,
  body .nectar-next-section-wrap.alignment_tablet_center {
    text-align: center;
  }
}
@media only screen and (max-width: 690px) {
  body
    .wpb_row
    .wpb_column.child_column.padding-3-percent_phone
    > .vc_column-inner,
  body
    .wpb_row
    .wpb_column.child_column.padding-3-percent_phone
    > .n-sticky
    > .vc_column-inner {
    padding: calc(690px * 0.03);
  }
}
@media only screen and (max-width: 690px) {
  #ajax-content-wrap
    .vc_row.inner_row.left_padding_phone_10pct
    .row_col_wrap_12_inner {
    padding-left: 10% !important;
  }
}
@media only screen and (max-width: 690px) {
  .wpb_column.child_column.bottom_margin_phone_0px {
    margin-bottom: 0 !important;
  }
}
@media only screen and (max-width: 690px) {
  .wpb_column.child_column.bottom_margin_phone_40px {
    margin-bottom: 40px !important;
  }
}
@media only screen and (max-width: 690px) {
  html body .wpb_column.force-phone-text-align-left,
  html body .wpb_column.force-phone-text-align-left .col {
    text-align: left !important;
  }
  html body .wpb_column.force-phone-text-align-right,
  html body .wpb_column.force-phone-text-align-right .col {
    text-align: right !important;
  }
  html body .wpb_column.force-phone-text-align-center,
  html body .wpb_column.force-phone-text-align-center .col,
  html body .wpb_column.force-phone-text-align-center .vc_custom_heading,
  html body .wpb_column.force-phone-text-align-center .nectar-cta {
    text-align: center !important;
  }
  .wpb_column.force-phone-text-align-center .img-with-aniamtion-wrap img {
    display: inline-block;
  }
}
@media only screen and (max-width: 690px) {
  .nectar-cta.display_phone_inherit {
    display: inherit;
  }
}
@media only screen and (max-width: 690px) {
  #ajax-content-wrap
    .vc_row.inner_row.right_padding_phone_10pct
    .row_col_wrap_12_inner {
    padding-right: 10% !important;
  }
}
.screen-reader-text,
.nectar-skip-to-content:not(:focus) {
  border: 0;
  clip: rect(1px, 1px, 1px, 1px);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute !important;
  width: 1px;
  word-wrap: normal !important;
}
.row .col img:not([srcset]) {
  width: auto;
}
.row .col img.img-with-animation.nectar-lazy:not([srcset]) {
  width: 100%;
}
@media only screen and (min-width: 1000px) {
  .nectar-scrolling-tabs.font_size_4vw .tab-nav-heading {
    font-size: 2.5vw !important;
    line-height: 1.2 !important;
  }
}

.divider img {
  border-radius: 25px !important;
}

.nectar-google-map,
.wpb_wrapper > .nectar-google-map {
  border-radius: 15px !important;
  margin-bottom: 0px !important;
}

.league_tabs {
  margin-bottom: 20px !important;
}

body[data-form-submit="regular"] .container-wrap button[type="submit"],
body[data-form-submit="regular"] .container-wrap input[type="submit"] {
  padding: 8px 11px !important;
}

form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit {
  background-color: #005c3c !important;
  border-color: #005c3c !important;
}

form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit:hover {
  color: #fff !important;
  box-shadow: none !important;
  -ms-transform: none !important;
  transform: none !important;
  -webkit-transform: none !important;
  opacity: 1 !important;
}

form.fluent_form_7 .wpf_has_custom_css.ff-btn-submit:hover,
form.fluent_form_8 .wpf_has_custom_css.ff-btn-submit:hover {
  border-color: #25d17b !important;
  color: #fff !important;
}

form.fluent_form_7 .wpf_has_custom_css.ff-btn-submit,
form.fluent_form_8 .wpf_has_custom_css.ff-btn-submit {
  color: #fff !important;
}

form.fluent_form_8 .ff-el-group {
  padding-bottom: 25px !important;
}

form.fluent_form_8 .ff-btn-submit:not(.ff_btn_no_style):hover {
  border-color: #25d17b !important;
  color: #fff !important;
}

form.fluent_form_8 .ff-btn-submit:not(.ff_btn_no_style) {
  color: #fff !important;
}

.nectar-progress-bar p {
  font-size: 1.1vw;
  line-height: 1.6vw;
  padding-top: 20px;
  font-style: italic !important;
}
.wpb_animate_when_almost_visible {
  opacity: 1;
}
form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit {
  background-color: #1a7efb;
  border-color: #1a7efb;
  color: #ffffff;
  min-width: 100%;
}
form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit:hover {
  background-color: #ffffff;
  border-color: #1a7efb;
  color: #1a7efb;
  min-width: 100%;
}
.vc_custom_1729073712045 {
  margin-bottom: 10px !important;
}
.vc_custom_1728201264411 {
  margin-bottom: 10px !important;
}
.vc_custom_1636697263705 {
  margin-bottom: 10px !important;
}


@media print {
  #wpadminbar {
    display: none;
  }
}
img.wp-smiley,
img.emoji {
  display: inline !important;
  border: none !important;
  box-shadow: none !important;
  height: 1em !important;
  width: 1em !important;
  margin: 0 0.07em !important;
  vertical-align: -0.1em !important;
  background: none !important;
  padding: 0 !important;
}

.safe-svg-cover {
  text-align: center;
}
.safe-svg-cover .safe-svg-inside {
  display: inline-block;
  max-width: 100%;
}
.safe-svg-cover svg {
  height: 100%;
  max-height: 100%;
  max-width: 100%;
  width: 100%;
}

@font-face {
  font-family: "Open Sans";
  src: url("https://staging1.bloomnetball.co.uk/wp-content/themes/salient/css/fonts/OpenSans-Light.woff")
    format("woff");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: "Open Sans";
  src: url("https://staging1.bloomnetball.co.uk/wp-content/themes/salient/css/fonts/OpenSans-Regular.woff")
    format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: "Open Sans";
  src: url("https://staging1.bloomnetball.co.uk/wp-content/themes/salient/css/fonts/OpenSans-SemiBold.woff")
    format("woff");
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: "Open Sans";
  src: url("https://staging1.bloomnetball.co.uk/wp-content/themes/salient/css/fonts/OpenSans-Bold.woff")
    format("woff");
  font-weight: 700;
  font-style: normal;
}
html body[data-header-resize="1"] .container-wrap,
html
  body[data-header-format="left-header"][data-header-resize="0"]
  .container-wrap,
html body[data-header-resize="0"] .container-wrap,
body[data-header-format="left-header"][data-header-resize="0"] .container-wrap {
  padding-top: 0;
}
.main-content > .row > #breadcrumbs.yoast {
  padding: 20px 0;
}

#wpadminbar [id*="wp-admin-bar-salient-hook-locations"] .ab-item {
  display: flex;
  align-items: center;
}
#wpadminbar #wp-admin-bar-salient-hook-locations .ab-icon:before {
  content: "\f177";
}
#wpadminbar #wp-admin-bar-salient-hook-locations-active .ab-icon:before {
  content: "\f530";
}

.salient-hook-location .row-bg-wrap .row-bg {
  transition: box-shadow 0.35s ease, border-color 0.35s ease;
  border: 1px dashed var(--nectar-accent-color);
  border-radius: 10px;
}
.salient-hook-location .row-bg-wrap .inner-wrap {
  transform: none !important;
}
.salient-hook-location:hover .row-bg-wrap .row-bg {
  border: 1px dashed transparent;
  box-shadow: 0 0 0px 4px inset var(--nectar-accent-color);
}

.salient-hook-location__content {
  padding: 10px;
}

.salient-hook-location__content,
.row .salient-hook-location__content a span,
.row .salient-hook-location__content a i {
  color: #000;
}
.row .salient-hook-location__content a {
  display: inline-flex;
  gap: 5px;
  font-weight: 700;
  font-size: 13px;
  align-items: center;
  text-decoration: none;
  line-height: 1.3;
  transform: scale(0.9);
  transition: transform 0.35s ease, opacity 0.35s ease;
  opacity: 0;
}
.salient-hook-location__content > span {
  display: block;
  transform: translateY(50%);
  transition: transform 0.35s ease, opacity 0.35s ease;
}
.salient-hook-location__content a i {
  top: 0;
}
.salient-hook-location:hover .salient-hook-location__content > span {
  transform: translateY(0);
}
.salient-hook-location:hover .salient-hook-location__content a {
  transform: translateY(0) scale(1);
  opacity: 1;
}

@media only screen and (min-width: 1000px) {
  .nectar-scrolling-tabs.font_size_4vw .tab-nav-heading {
    font-size: 2.5vw !important;
    line-height: 1.2 !important;
  }
}

.divider img {
  border-radius: 25px !important;
}

.nectar-google-map,
.wpb_wrapper > .nectar-google-map {
  border-radius: 15px !important;
  margin-bottom: 0px !important;
}

.league_tabs {
  margin-bottom: 20px !important;
}

body[data-form-submit="regular"] .container-wrap button[type="submit"],
body[data-form-submit="regular"] .container-wrap input[type="submit"] {
  padding: 8px 11px !important;
  border-radius: 50px !important;
}

form.fluent_form_7 .wpf_has_custom_css.ff-btn-submit {
  padding: 11px 11px !important;
  border-radius: 50px !important;
}

form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit,
form.fluent_form_12 .ff-btn-submit:not(.ff_btn_no_style),
form.fluent_form_13 .ff-btn-submit:not(.ff_btn_no_style),
form.fluent_form_4 .ff-btn-submit:not(.ff_btn_no_style),
form.fluent_form_11 .wpf_has_custom_css.ff-btn-submit,
form.fluent_form_7 .wpf_has_custom_css.ff-btn-submit,
form.fluent_form_14 .wpf_has_custom_css.ff-btn-submit {
  background-color: #00df74 !important;
  border-color: #00df74 !important;
  color: #fff !important;
  border-radius: 50px !important;
}

form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit:hover,
form.fluent_form_12 .ff-btn-submit:not(.ff_btn_no_style):hover,
form.fluent_form_13 .ff-btn-submit:not(.ff_btn_no_style):hover,
form.fluent_form_4 .ff-btn-submit:not(.ff_btn_no_style):hover,
form.fluent_form_11
  .wpf_has_custom_css.ff-btn-submit:hoverm
  form.fluent_form_7
  .wpf_has_custom_css.ff-btn-submit:hover,
form.fluent_form_14 .wpf_has_custom_css.ff-btn-submit:hover {
  color: #fff !important;
  box-shadow: none !important;
  -ms-transform: none !important;
  transform: none !important;
  -webkit-transform: none !important;
  opacity: 1 !important;
}

form.fluent_form_7 .wpf_has_custom_css.ff-btn-submit:hover,
form.fluent_form_8 .wpf_has_custom_css.ff-btn-submit:hover {
  border-color: #25d17b !important;
  color: #fff !important;
}

form.fluent_form_7 .wpf_has_custom_css.ff-btn-submit,
form.fluent_form_8 .wpf_has_custom_css.ff-btn-submit {
  color: #fff !important;
}

form.fluent_form_8 .ff-el-group {
  padding-bottom: 25px !important;
}

form.fluent_form_8 .ff-btn-submit:not(.ff_btn_no_style):hover {
  border-color: #25d17b !important;
  color: #fff !important;
}

form.fluent_form_8 .ff-btn-submit:not(.ff_btn_no_style) {
  color: #fff !important;
}

.nectar-progress-bar p {
  font-size: 1.1vw;
  line-height: 1.6vw;
  padding-top: 20px;
  font-style: italic !important;
}

.ff-btn-next {
  background-color: #00df74 !important;
  border-color: #00df74 !important;
  padding: 6px 20px !important;
}

ff-btn-next:hover {
  border-color: #00df74 !important;
  color: #fff !important;
}

.ff-btn-prev {
  padding: 6px 10px !important;
}

.ff-el-progress-bar {
  background-color: #8a8bdc !important;
}

.fluentform .ff-el-tooltip svg {
  fill: #8a8bdc !important;
}

.nectar-scrolling-tabs[data-tab-spacing="40%"] .scrolling-tab-content > div {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

#header-outer:not([data-format="centered-menu-under-logo"]):not(
    [data-format="centered-menu-bottom-bar"]
  )
  #top
  .slide-out-widget-area-toggle[data-custom-color="true"]
  a:before,
#header-outer:not([data-format="centered-menu-under-logo"]):not(
    [data-format="centered-menu-bottom-bar"]
  )
  #top
  nav
  > ul
  > li[class*="button_"]
  > a:before {
  border-radius: 50px !important;
}
.wpb_animate_when_almost_visible {
  opacity: 1;
}

.nectar-shape-divider-wrap {
  position: absolute;
  top: auto;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 150px;
  z-index: 3;
  transform: translateZ(0);
}
.post-area.span_9 .nectar-shape-divider-wrap {
  overflow: hidden;
}
.nectar-shape-divider-wrap[data-front="true"] {
  z-index: 50;
}
.nectar-shape-divider-wrap[data-style="waves_opacity"] svg path:first-child {
  opacity: 0.6;
}

.nectar-shape-divider-wrap[data-style="curve_opacity"] svg path:nth-child(1),
.nectar-shape-divider-wrap[data-style="waves_opacity_alt"]
  svg
  path:nth-child(1) {
  opacity: 0.15;
}
.nectar-shape-divider-wrap[data-style="curve_opacity"] svg path:nth-child(2),
.nectar-shape-divider-wrap[data-style="waves_opacity_alt"]
  svg
  path:nth-child(2) {
  opacity: 0.3;
}
.nectar-shape-divider {
  width: 100%;
  left: 0;
  bottom: -1px;
  height: 100%;
  position: absolute;
}
.nectar-shape-divider-wrap.no-color .nectar-shape-divider {
  fill: #fff;
}
@media only screen and (max-width: 999px) {
  .nectar-shape-divider-wrap:not([data-using-percent-val="true"])
    .nectar-shape-divider {
    height: 75%;
  }
  .nectar-shape-divider-wrap[data-style="clouds"]:not(
      [data-using-percent-val="true"]
    )
    .nectar-shape-divider {
    height: 55%;
  }
}
@media only screen and (max-width: 690px) {
  .nectar-shape-divider-wrap:not([data-using-percent-val="true"])
    .nectar-shape-divider {
    height: 33%;
  }
  .nectar-shape-divider-wrap[data-style="clouds"]:not(
      [data-using-percent-val="true"]
    )
    .nectar-shape-divider {
    height: 33%;
  }
}

#ajax-content-wrap
  .nectar-shape-divider-wrap[data-height="1"]
  .nectar-shape-divider,
#ajax-content-wrap
  .nectar-shape-divider-wrap[data-height="1px"]
  .nectar-shape-divider {
  height: 1px;
}
.nectar-shape-divider-wrap[data-position="top"] {
  top: -1px;
  bottom: auto;
}
.nectar-shape-divider-wrap[data-position="top"] {
  transform: rotate(180deg);
}
#ajax-content-wrap .vc_row.left_padding_8pct .row_col_wrap_12,
.nectar-global-section .vc_row.left_padding_8pct .row_col_wrap_12 {
  padding-left: 8%;
}
#ajax-content-wrap .vc_row.right_padding_8pct .row_col_wrap_12,
.nectar-global-section .vc_row.right_padding_8pct .row_col_wrap_12 {
  padding-right: 8%;
}
#ajax-content-wrap .vc_row.inner_row.right_padding_40pct .row_col_wrap_12_inner,
.nectar-global-section
  .vc_row.inner_row.right_padding_40pct
  .row_col_wrap_12_inner {
  padding-right: 40%;
}
body .container-wrap .vc_row-fluid[data-column-margin="50px"] > .span_12,
body
  .container-wrap
  .vc_row-fluid[data-column-margin="50px"]
  .full-page-inner
  > .container
  > .span_12,
body
  .container-wrap
  .vc_row-fluid[data-column-margin="50px"]
  .full-page-inner
  > .span_12 {
  margin-left: -25px;
  margin-right: -25px;
}

body
  .container-wrap
  .vc_row-fluid[data-column-margin="50px"]
  .wpb_column:not(.child_column),
body .container-wrap .inner_row[data-column-margin="50px"] .child_column {
  padding-left: 25px;
  padding-right: 25px;
}
.container-wrap
  .vc_row-fluid[data-column-margin="50px"].full-width-content
  > .span_12,
.container-wrap
  .vc_row-fluid[data-column-margin="50px"].full-width-content
  .full-page-inner
  > .span_12 {
  margin-left: 0;
  margin-right: 0;
  padding-left: 25px;
  padding-right: 25px;
}
.single-portfolio
  #full_width_portfolio
  .vc_row-fluid[data-column-margin="50px"].full-width-content
  > .span_12 {
  padding-right: 25px;
}

@media only screen and (max-width: 999px) and (min-width: 691px) {
  .vc_row-fluid[data-column-margin="50px"]
    > .span_12
    > .one-fourths:not([class*="vc_col-xs-"]),
  .vc_row-fluid
    .vc_row-fluid.inner_row[data-column-margin="50px"]
    > .span_12
    > .one-fourths:not([class*="vc_col-xs-"]) {
    margin-bottom: 50px;
  }
}
.wpb_column.border_style_solid > .vc_column-inner,
.wpb_column.border_style_solid > .n-sticky > .vc_column-inner {
  border-style: solid;
}

.col.padding-2-percent > .vc_column-inner,
.col.padding-2-percent > .n-sticky > .vc_column-inner {
  padding: calc(600px * 0.03);
}

@media only screen and (max-width: 690px) {
  .col.padding-2-percent > .vc_column-inner,
  .col.padding-2-percent > .n-sticky > .vc_column-inner {
    padding: calc(100vw * 0.03);
  }
}

@media only screen and (min-width: 1000px) {
  .col.padding-2-percent > .vc_column-inner,
  .col.padding-2-percent > .n-sticky > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.02);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-2-percent
    > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.01);
  }
}

@media only screen and (min-width: 1425px) {
  .col.padding-2-percent > .vc_column-inner {
    padding: calc(1245px * 0.02);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-2-percent
    > .vc_column-inner {
    padding: calc(1245px * 0.01);
  }
}

.full-width-content .col.padding-2-percent > .vc_column-inner {
  padding: calc(100vw * 0.02);
}

@media only screen and (max-width: 999px) {
  .full-width-content .col.padding-2-percent > .vc_column-inner {
    padding: calc(100vw * 0.03);
  }
}
@media only screen and (min-width: 1000px) {
  .full-width-content
    .column_container:not(.vc_col-sm-12)
    .col.padding-2-percent
    > .vc_column-inner {
    padding: calc(100vw * 0.01);
  }
}
body #ajax-content-wrap .col[data-padding-pos="bottom"] > .vc_column-inner,
#ajax-content-wrap
  .col[data-padding-pos="bottom"]
  > .n-sticky
  > .vc_column-inner {
  padding-right: 0;
  padding-top: 0;
  padding-left: 0;
}
.wpb_column.child_column.el_spacing_10px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 10px;
}
#ajax-content-wrap .col[data-padding-pos="top"] > .vc_column-inner,
#ajax-content-wrap .col[data-padding-pos="top"] > .n-sticky > .vc_column-inner {
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
}

.col.padding-5-percent > .vc_column-inner,
.col.padding-5-percent > .n-sticky > .vc_column-inner {
  padding: calc(600px * 0.06);
}

@media only screen and (max-width: 690px) {
  .col.padding-5-percent > .vc_column-inner,
  .col.padding-5-percent > .n-sticky > .vc_column-inner {
    padding: calc(100vw * 0.06);
  }
}

@media only screen and (min-width: 1000px) {
  .col.padding-5-percent > .vc_column-inner,
  .col.padding-5-percent > .n-sticky > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.05);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-5-percent
    > .vc_column-inner {
    padding: calc((100vw - 180px) * 0.025);
  }
}

@media only screen and (min-width: 1425px) {
  .col.padding-5-percent > .vc_column-inner {
    padding: calc(1245px * 0.05);
  }
  .column_container:not(.vc_col-sm-12)
    .col.padding-5-percent
    > .vc_column-inner {
    padding: calc(1245px * 0.025);
  }
}

.full-width-content .col.padding-5-percent > .vc_column-inner {
  padding: calc(100vw * 0.05);
}

@media only screen and (max-width: 999px) {
  .full-width-content .col.padding-5-percent > .vc_column-inner {
    padding: calc(100vw * 0.06);
  }
}
@media only screen and (min-width: 1000px) {
  .full-width-content
    .column_container:not(.vc_col-sm-12)
    .col.padding-5-percent
    > .vc_column-inner {
    padding: calc(100vw * 0.025);
  }
}
body #ajax-content-wrap .col[data-padding-pos="left"] > .vc_column-inner,
#ajax-content-wrap
  .col[data-padding-pos="left"]
  > .n-sticky
  > .vc_column-inner {
  padding-right: 0;
  padding-top: 0;
  padding-bottom: 0;
}
.wpb_column.child_column.el_spacing_0px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 0px;
}
.nectar-split-heading .heading-line {
  display: block;
  overflow: hidden;
  position: relative;
}
.nectar-split-heading .heading-line > div {
  display: block;
  transform: translateY(200%);
  -webkit-transform: translateY(200%);
}

.nectar-split-heading h1 {
  margin-bottom: 0;
}
@media only screen and (min-width: 1000px) {
  .nectar-split-heading[data-custom-font-size="true"] h1,
  .nectar-split-heading[data-custom-font-size="true"] h2,
  .row .nectar-split-heading[data-custom-font-size="true"] h3,
  .row .nectar-split-heading[data-custom-font-size="true"] h4,
  .row .nectar-split-heading[data-custom-font-size="true"] h5,
  .row .nectar-split-heading[data-custom-font-size="true"] h6,
  .row .nectar-split-heading[data-custom-font-size="true"] i {
    font-size: inherit;
    line-height: inherit;
  }
}
.nectar-split-heading.font_line_height_1-6 {
  line-height: 1.6 !important;
}
.nectar-split-heading.font_line_height_1-6 * {
  line-height: 1.6 !important;
}
@media only screen, print {
  #ajax-content-wrap .font_size_1-05vw.nectar-split-heading,
  .font_size_1-05vw.nectar-split-heading {
    font-size: 1.05vw !important;
  }
}
@media only screen, print {
  #ajax-content-wrap .font_size_1-05vw.nectar-split-heading,
  .font_size_1-05vw.nectar-split-heading {
    font-size: 1.05vw !important;
  }
}
@media only screen and (max-width: 999px) {
  #ajax-content-wrap .font_size_tablet_16px.nectar-split-heading,
  .font_size_tablet_16px.nectar-split-heading {
    font-size: 16px !important;
  }
}
@media only screen and (max-width: 999px) {
  #ajax-content-wrap
    .container-wrap
    .font_size_tablet_16px.nectar-split-heading,
  .container-wrap .font_size_tablet_16px.nectar-split-heading {
    font-size: 16px !important;
  }
}
#ajax-content-wrap .font_line_height_1-6.nectar-split-heading {
  line-height: 1.6;
}
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h1,
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h2,
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h3,
.centered-text
  .nectar-split-heading[data-animation-type="line-reveal-by-space"]
  h4 {
  margin: 0 auto;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"]:not(
    .markup-generated
  ) {
  opacity: 0;
}
@media only screen and (max-width: 999px) {
  .nectar-split-heading[data-m-rm-animation="true"] {
    opacity: 1 !important;
  }
}

.nectar-split-heading[data-animation-type="line-reveal-by-space"] > * > span {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.nectar-split-heading[data-animation-type="line-reveal-by-space"] span {
  vertical-align: bottom;
}

.nectar-split-heading[data-animation-type="line-reveal-by-space"] span,
.nectar-split-heading[data-animation-type="line-reveal-by-space"]:not(
    .markup-generated
  )
  > * {
  line-height: 1.2;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-stagger="true"]:not(
    [data-text-effect*="letter-reveal"]
  )
  span
  .inner {
  transition: transform 1.2s cubic-bezier(0.25, 1, 0.5, 1),
    opacity 1.2s cubic-bezier(0.25, 1, 0.5, 1);
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"] span .inner {
  position: relative;
  display: inline-block;
  -webkit-transform: translateY(1.3em);
  transform: translateY(1.3em);
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"]
  span
  .inner.animated {
  -webkit-transform: none;
  transform: none;
  opacity: 1;
}
#ajax-content-wrap .nectar-split-heading[data-text-effect="none"] {
  opacity: 1;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-align="left"] {
  display: flex;
  justify-content: flex-start;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-align="center"] {
  display: flex;
  justify-content: center;
}
.nectar-split-heading[data-animation-type="line-reveal-by-space"][data-align="right"] {
  display: flex;
  justify-content: flex-end;
}
@media only screen and (max-width: 999px) {
  .nectar-split-heading[data-animation-type="line-reveal-by-space"][data-m-align="left"] {
    display: flex;
    justify-content: flex-start;
  }
  .nectar-split-heading[data-animation-type="line-reveal-by-space"][data-m-align="center"] {
    display: flex;
    justify-content: center;
  }
  .nectar-split-heading[data-animation-type="line-reveal-by-space"][data-m-align="right"] {
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-width: 999px) {
  .wpb_column.child_column.bottom_margin_tablet_0px {
    margin-bottom: 0px !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.bottom_padding_tablet_6pct {
    padding-bottom: 6% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.inner_row.top_padding_tablet_10px {
    padding-top: 10px !important;
  }
}
@media only screen and (max-width: 999px) {
  .nectar-cta.display_tablet_inherit {
    display: inherit;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.inner_row.bottom_padding_tablet_10pct {
    padding-bottom: 10% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.top_padding_tablet_12pct {
    padding-top: 12% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.bottom_padding_tablet_12pct {
    padding-bottom: 12% !important;
  }
}
@media only screen and (max-width: 999px) {
  .vc_row.top_padding_tablet_6pct {
    padding-top: 6% !important;
  }
}
@media only screen and (max-width: 999px) {
  .nectar-split-heading.font_size_tablet_16px * {
    font-size: inherit !important;
  }
}
@media only screen and (max-width: 999px) {
  body .nectar-cta.alignment_tablet_center,
  body .nectar-next-section-wrap.alignment_tablet_center {
    text-align: center;
  }
}
@media only screen and (max-width: 999px) {
  body .wpb_column.force-tablet-text-align-left,
  body .wpb_column.force-tablet-text-align-left .col {
    text-align: left !important;
  }

  body .wpb_column.force-tablet-text-align-right,
  body .wpb_column.force-tablet-text-align-right .col {
    text-align: right !important;
  }

  body .wpb_column.force-tablet-text-align-center,
  body .wpb_column.force-tablet-text-align-center .col,
  body .wpb_column.force-tablet-text-align-center .vc_custom_heading,
  body .wpb_column.force-tablet-text-align-center .nectar-cta {
    text-align: center !important;
  }

  .wpb_column.force-tablet-text-align-center .img-with-aniamtion-wrap img {
    display: inline-block;
  }
}
@media only screen and (max-width: 690px) {
  .wpb_column.child_column.bottom_margin_phone_40px {
    margin-bottom: 40px !important;
  }
}
@media only screen and (max-width: 690px) {
  body
    .wpb_row
    .wpb_column.child_column.padding-3-percent_phone
    > .vc_column-inner,
  body
    .wpb_row
    .wpb_column.child_column.padding-3-percent_phone
    > .n-sticky
    > .vc_column-inner {
    padding: calc(690px * 0.03);
  }
}
@media only screen and (max-width: 690px) {
  #ajax-content-wrap
    .vc_row.inner_row.left_padding_phone_10pct
    .row_col_wrap_12_inner {
    padding-left: 10% !important;
  }
}
@media only screen and (max-width: 690px) {
  html body .wpb_column.force-phone-text-align-left,
  html body .wpb_column.force-phone-text-align-left .col {
    text-align: left !important;
  }

  html body .wpb_column.force-phone-text-align-right,
  html body .wpb_column.force-phone-text-align-right .col {
    text-align: right !important;
  }

  html body .wpb_column.force-phone-text-align-center,
  html body .wpb_column.force-phone-text-align-center .col,
  html body .wpb_column.force-phone-text-align-center .vc_custom_heading,
  html body .wpb_column.force-phone-text-align-center .nectar-cta {
    text-align: center !important;
  }

  .wpb_column.force-phone-text-align-center .img-with-aniamtion-wrap img {
    display: inline-block;
  }
}
@media only screen and (max-width: 690px) {
  .wpb_column.child_column.bottom_margin_phone_0px {
    margin-bottom: 0px !important;
  }
}
@media only screen and (max-width: 690px) {
  .nectar-cta.display_phone_inherit {
    display: inherit;
  }
}
@media only screen and (max-width: 690px) {
  #ajax-content-wrap
    .vc_row.inner_row.right_padding_phone_10pct
    .row_col_wrap_12_inner {
    padding-right: 10% !important;
  }
}
form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit {
  background-color: #1a7efb;
  border-color: #1a7efb;
  color: #ffffff;
  min-width: 100%;
}
form.fluent_form_9 .wpf_has_custom_css.ff-btn-submit:hover {
  background-color: #ffffff;
  border-color: #1a7efb;
  color: #1a7efb;
  min-width: 100%;
}

.vc_custom_1729073712045 {
  margin-bottom: 10px !important;
}
.vc_custom_1731845203850 {
  margin-bottom: 10px !important;
}
.vc_custom_1731845208844 {
  margin-bottom: 10px !important;
}

#ajax-content-wrap .nectar-cta.border_radius_50px .link_wrap {
  border-radius: 50px !important;
}

.nectar-badge[data-bg-color-custom="#00df74"] .nectar-badge__inner {
  background-color: #00df74;
}
.wpb_column.el_spacing_10px
  > .vc_column-inner
  > .wpb_wrapper
  > div:not(:last-child) {
  margin-bottom: 10px;
}
.img-with-aniamtion-wrap.mask_shape_custom.mask_size_contain > .inner {
  -webkit-mask-size: contain;
}

.img-with-aniamtion-wrap.mask_shape_custom > .inner {
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center center;
}

.img-with-aniamtion-wrap.br_br_150px .img-with-animation,
.img-with-aniamtion-wrap.br_br_150px .inner,
.img-with-aniamtion-wrap.br_br_150px .hover-wrap {
  border-bottom-right-radius: 150px;
}
.img-with-aniamtion-wrap.br_br_150px .img-with-animation,
.img-with-aniamtion-wrap.br_br_150px .inner,
.img-with-aniamtion-wrap.br_br_150px .hover-wrap {
  border-bottom-left-radius: 150px;
}

.img-with-aniamtion-wrap.br_br_150px .img-with-animation,
.img-with-aniamtion-wrap.br_br_150px .inner,
.img-with-aniamtion-wrap.br_br_150px .hover-wrap {
  border-top-right-radius: 150px;
}

.img-with-aniamtion-wrap.br_br_150px .img-with-animation,
.img-with-aniamtion-wrap.br_br_150px .inner,
.img-with-aniamtion-wrap.br_br_150px .hover-wrap {
  border-top-left-radius: 150px;
}

.vc_column-inner
  .wpb_wrapper
  .img-with-aniamtion-wrap.center.tl_br_150px.tr_br_150px.bl_br_150px.img-with-aniamtion-wrap.br_br_150px
  .inner
  .hover-wrap
  .hover-wrap-inner
  img.img-with-animation.skip-lazy {
  width: 190px;
}

.league_teams_tables .vc_column-inner .wpb_wrapper {
  display: grid;
  grid-template-columns: repeat(11, 1fr); /* 11 columns for your data */
  background-color: #fff;
  overflow: hidden; /* Prevent overflow issues */
}

/* Treat each item as part of the grid */
.league_teams_tables .vc_column-inner .wpb_wrapper .nectar-hor-list-item {
  display: contents;
}

/* Style individual list items */
.league_teams_tables .vc_column-inner .wpb_wrapper .nectar-list-item {
  padding: 10px;
  text-align: center;
  word-wrap: break-word; /* Prevent text overflow */
}

/* First row with a dark background */
.league_teams_tables
  .vc_column-inner
  .wpb_wrapper
  .nectar-hor-list-item:first-child
  .nectar-list-item {
  background-color: #262626;
  color: #fff; /* Ensure text is readable on dark background */
  font-weight: bold;
}

/* Alternating row colors starting from the second row */
.league_teams_tables
  .vc_column-inner
  .wpb_wrapper
  .nectar-hor-list-item:nth-child(odd):not(:first-child)
  .nectar-list-item {
  background-color: #f0f0f0; /* Gray for odd rows */
}

.league_teams_tables
  .vc_column-inner
  .wpb_wrapper
  .nectar-hor-list-item:nth-child(even):not(:first-child)
  .nectar-list-item {
  background-color: #fff; /* White for even rows */
}

/* Add border to rows */
.league_teams_tables .vc_column-inner .wpb_wrapper .nectar-hor-list-item {
  border-bottom: 1px solid #ddd; /* Border for each row */
}

/* Make the first column left-aligned and wider */
.league_teams_tables
  .vc_column-inner
  .wpb_wrapper
  .nectar-list-item[data-text-align="left"] {
  text-align: left;
  font-weight: bold;
  width: 15vw; /* Adjust this value as needed */
  display: inline-block; /* Ensures the width is applied */
}

/* Optional: Style for hover effects */
.league_teams_tables
  .vc_column-inner
  .wpb_wrapper
  .nectar-hor-list-item:not(:first-child):hover
  .nectar-list-item {
  background-color: #f0f8ff; /* Light blue on hover */
}

/* Ensure consistent width for league_tabs container */
.league_teams_tables {
  width: 100%;
  max-width: 1200px; /* Adjust as needed */
  margin: auto; /* Center the content */
}

@media (min-width: 1000px) and (max-width: 1400px) {
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item[data-text-align="left"] {
    width: 10vw; /* Adjust first column */
    padding: 5px;
  }
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item:not([data-text-align="left"]) {
    padding: 5px;
    font-size: 14px;
  }
}

/* Breakpoint for tablets (screen width <= 1024px) */
@media screen and (max-width: 1000px) {
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item[data-text-align="left"] {
    width: 25vw; /* Adjust first column */
  }
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item:not([data-text-align="left"]) {
    padding: 5px;
  }
}

/* Breakpoint for small tablets and large phones (screen width <= 768px) */
@media screen and (max-width: 768px) {
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item[data-text-align="left"] {
    width: 23vw; /* Adjust first column */
  }
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item:not([data-text-align="left"]) {
    padding: 5px;
  }
}

/* Breakpoint for small phones (screen width <= 480px) */
@media screen and (max-width: 500px) {
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item[data-text-align="left"] {
    width: 20vw;
    padding: 5px;
  }
  .league_teams_tables
    .vc_column-inner
    .wpb_wrapper
    .nectar-list-item:not([data-text-align="left"]) {
    padding: 5px;
  }
  .league_teams_tables {
    font-size: 15px;
  }
  .league_tabs .wpb_wrapper {
    margin: -12px !important;
  }
}
.nectar-color-accent-color,
label span,
body [class^="icon-"].icon-default-style,
.blog-recent[data-style*="classic_enhanced"] .post-meta a:hover i,
.masonry.classic_enhanced .post .post-meta a:hover i,
.comment-list .comment-meta a:hover,
.comment-list .comment-meta a:focus,
.comment-author a:hover,
.comment-author a:focus,
.post .post-header h2 a,
.post .post-header a:hover,
.post .post-header a:focus,
#single-below-header a:hover,
#single-below-header a:focus,
.comment-list .pingback .comment-body > a:hover,
[data-style="list_featured_first_row"] .meta-category a,
[data-style="list_featured_first_row"] .meta-category a,
.nectar-fancy-box[data-style="color_box_hover"][data-color="accent-color"]
  .icon-default-style,
div[data-style="minimal"] .toggle:hover > .toggle-title a,
div[data-style="minimal"] .toggle.open > .toggle-title a,
#footer-outer #copyright li a i:hover,
.ascend .comment-list .reply a,
body.material
  .widget:not(.nectar_popular_posts_widget):not(.recent_posts_extra_widget)
  li
  a:hover,
body.material
  #sidebar
  .widget:not(.nectar_popular_posts_widget):not(.recent_posts_extra_widget)
  li
  a:hover,
body.material
  #footer-outer
  .widget:not(.nectar_popular_posts_widget):not(.recent_posts_extra_widget)
  li
  a:hover,
#top nav .sf-menu .current_page_item > a .sf-sub-indicator i,
#top nav .sf-menu .current_page_ancestor > a .sf-sub-indicator i,
.sf-menu > .current_page_ancestor > a > .sf-sub-indicator i,
.material .widget .tagcloud a,
#single-below-header a:hover [class^="icon-"],
.wpcf7-form .wpcf7-not-valid-tip,
#header-outer .nectar-menu-label {
  color: #005c3c !important;
}

@media only screen and (min-width: 1000px) {
  body .row .nectar-text-inline-images.font_size_desktop_4vw * {
    font-size: 3vw !important;
    line-height: 1.4;
  }
}
@media (max-width: 770px) {
  .nectar-hor-list-item.has-btn.c2c_list_js{
    padding-right: 0px !important;
  }
}
@media only screen and (max-width: 425.98px) {
  .nectar-hor-list-item.has-btn.c2c_list_js{
    padding-right: 0px !important;
    display: none !important;
  }
}


.nectar-hor-list-container {
  display: grid;
  grid-template-columns: 1fr auto; /* Two main columns */
  grid-template-rows: auto auto auto; /* Three rows */
  gap: 1rem; /* Add space between items */
  align-items: center; /* Align items vertically */
  justify-content: space-between; /* Distribute items */
}
.nectar-hor-list-container:nth-child(1) {
  grid-column: 1 / span 2; /* First item spans both columns */
  text-align: left;
}

.nectar-hor-list-container:nth-child(2),
.nectar-hor-list-container:nth-child(3),
.nectar-hor-list-container:nth-child(4),
.nectar-hor-list-container:nth-child(5) {
  grid-column: 2; /* Align subsequent items to the second column */
  text-align: right;
}

.nectar-hor-list-container:nth-child(4) {
  justify-self: end; /* Align the Checkout button further right */
}

.nectar-hor-list-container:nth-child(5) {
  /* justify-self: end;  */
  /* Align "More Details" link to the far right */
}

@media only screen and (max-width: 768px) {
    .mobile_padding_left .nectar-cta .link_wrap {
        padding-left: 15px !important;
    }
    .c2c-coordinator-info{
      display:none !important;
    }
}