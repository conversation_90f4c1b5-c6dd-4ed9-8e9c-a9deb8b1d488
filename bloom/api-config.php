<?php
// if url containt staging then return staging if not return null
$prefixUrl = strpos($_SERVER['HTTP_HOST'], 'staging') !== false ? 'staging.' : null;

$faqs = array(
    array(
        "name" => "What actually happens at a Couch2Court session?",
        "description" => "First a member of the Leagues4you team will greet you with a bucket full of encouragement! Then during the session you’ll take part in fun filled warm up games. As the evening continues you’ll progress into match play at your pace, with lots of helpful advice and rule reminders. By the end of the night your netball confidence will be through the roof!"
    ),
    array(
        "name" => "Will I be able to take part, I haven’t played Netball for sometime?",
        "description" => "Whether it's been a few years or 40 years, our Couch2Court sessions are the perfect way for you to jump back into the world of netball! As an average 63% of Couch2Court participators hadn’t played netball for over 10 years before coming to a Couch2Court session. You won’t be alone we promise!"
    ),
    array(
        "name" => "I’m out of shape, will I be able to do it?",
        "description" => "The simple answer is YES! This really is a place for everybody, no matter your shape, size or level of fitness. You can take the session at your own pace, take breaks when you need to and the nice thing about netball is there’s a position for everyone! If you don’t fancy running around the whole court then pick up a GS or GK bib!"
    ),
    array(
        "name" => "I feel like I won’t keep up, is there an age limit?",
        "description" => "There is no upper age limit at leaguese4you, in fact, we’re pretty proud of the fact that in some of our leagues we have grandma’s playing alongside their granddaughters! It’s never too late to start your journey back to the courts."
    ),
    array(
        "name" => "How do you decide on the teams?",
        "description" => "During our couch2court sessions you’ll find yourself having fun with a group of likeminded ladies and the teams grow from the activities you take part in during the session. There’s no fear of being “picked last” here."
    ),
    array(
        "name" => "Do I need to know the rules?",
        "description" => "In short, no! We cover the basic rules during the session and from there on in the umpires will help you pick up the rules as you go along."
    ),
    array(
        "name" => "I’m pregnant, can I attend?",
        "description" => "Playing netball has many health benefits, and current research suggests that a moderate amount of exercise during pregnancy can have beneficial effects. Netball is however a contact sport so we recommend you consult with your own appropriately qualified medical practitioner to inform them, on a voluntary basis, of your pregnancy and to obtain individual and specific professional medical advice before participating in netball."
    ),
    array(
        "name" => "I’m returning from an injury, can I attend?",
        "description" => "Absolutely! We find that our couch2court sessions can act as the perfect stepping stone for those coming back from injury. We’d obviously also advise that you seek medical advice when returning to physical activity following an injury."
    ),
    array(
        "name" => "If I come with friends, will I be able to play with them?",
        "description" => "Netball is better with friends! We’ll absolutely make sure that you form a team with the friends you come along with, and if you don’t quite have enough for a full team we’ll simply find you some extra friends too!"
    ),
    array(
        "name" => "Can I come on my own?",
        "description" => "Our Couch2Court sessions are open to individuals, in fact 6/10 come on their own. Small groups of friends, and full netball teams are also welcome."
    ),
    array(
        "name" => "What do I wear and is there anything I need to bring?",
        "description" => "Wear whatever you feel comfortable in, this isn't a netball catwalk! The only item of clothing we insist on are sports trainers. Don't forget to bring your own water bottle and confirmation of your booking!"
    ),
    array(
        "name" => "I’ve got some other questions, is there anyone I can contact?",
        "description" => "Sure, if you have a general question you can email us here or find us on Facebook and drop us a message there if that’s more your thing."
    )
);


$quotes = array(
    array(
        "quote" => "‘I had so much fun at the Couch2Court - Emma and the umpires are so friendly and there are all ages, fitness levels and body types taking part and loving it!’",
        "author" => "Helen - Bristol"
    ),
    array(
        "quote" => "“AMAZING!! Thank you all for such a warm welcome. Its very daunting doing new things like this on your own but I promised myself this year would be the year I stopped just being a mum, wife and nurse, that I would find a little something to be me again. I loved it. Definitely will be a regular.”",
        "author" => "Gemma, West Midlands"
    ),
    array(
        "quote" => "“Thank you all for another great night! I've really got the bug for netball again! It's ignited the fire in me.”",
        "author" => "Helen, West Midlands"
    ),
    array(
        "quote" => "“I've really enjoyed Couch2Court, I've now got the netball bug. I'm not sporty, and I am learning the rules but I've met some fantastic ladies these past 2 weeks.”",
        "author" => "Kathryn, West Midlands"
    ),
    array(
        "quote" => "“Within 15 minutes of being there I felt comfortable and motivated. I’ve met wonderful, lovely ladies on our team and other teams and it’s been a laugh a minute. Everyone has been encouraging and patient and I look forward to it every week”",
        "author" => "Amy, Bridgwater"
    ),
    array(
        "quote" => "Definitely a little scary going somewhere new on your own, especially as an adult, but straight away was made to feel welcome and comfortable. I had so much fun and remember why I used to love playing netball at school.”",
        "author" => "Sophie, West Midlands"
    ),
    array(
        "quote" => "“Really enjoyed tonight - No feeling self-conscious, no feeling like you’re not good enough just loads of fun and laughter.”",
        "author" => "Lisa, Somerset"
    )
);

// Fuzzy search function
function fuzzySearchArray($array, $name, $threshold = 3)
{
    if(!isset($name)) return $array;

   $results = [];

    foreach ($array as $item) {
        if (stripos($item['venue']['postcode'], $name) !== false || stripos($item['venue']['town'], $name) !== false) {
            $results[] = $item;
        }
    }

    return $results;
}
//$searchText = $_POST['search'] ? htmlspecialchars(trim($_POST['search'])) : null;

function response($data)
{
    header("Content-Type: application/json");
    http_response_code(200);
    echo json_encode($data);
    exit;
}

function convertDateToYMD($date) {
    // Create a DateTime object from the input date
    $dateObject = DateTime::createFromFormat('d/m/Y', $date);
    
    // Check if the date is valid
    if ($dateObject === false) {
        return false; // Return false if the date is invalid
    }
    
    // Convert the date to 'Y-m-d' format
    return $dateObject->format('Y-m-d');
}