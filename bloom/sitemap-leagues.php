<?php
// Set headers to indicate XML content
header("Content-Type: application/xml; charset=UTF-8");

// API URL to fetch the data
$apiUrl = "https://api.leagues4you.co.uk/leagues";

// Fetch data from the API
$response = file_get_contents($apiUrl);
if ($response === FALSE) {
    die("Failed to fetch data from the API.");
}

// Decode the JSON response into a PHP array
$data = json_decode($response, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Failed to parse JSON: " . json_last_error_msg());
}

// Start the XML sitemap
echo '<?xml version="1.0" encoding="UTF-8"?>';
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

// Loop through each league and add it to the sitemap
foreach ($data as $league) {
    $slug = htmlspecialchars($league['url']); // Escape special characters
    $url = "https://www.bloomnetball.co.uk/social-leagues/" . $slug;

    echo "<url>";
    echo "<loc>{$url}</loc>";
    echo "<changefreq>weekly</changefreq>";
    echo "<priority>0.8</priority>";
    echo "</url>";
}

// Close the XML sitemap
echo '</urlset>';
