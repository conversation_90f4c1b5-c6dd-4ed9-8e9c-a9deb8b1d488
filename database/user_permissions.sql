CREATE TABLE `user_groups` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted` datetime DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_group_permissions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `group_id` int(11) NOT NULL,
    `route` varchar(255) NOT NULL,
    `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `group_id` (`group_id`),
    CONSTRAINT `user_group_permissions_ibfk_1` FOREI<PERSON>N KEY (`group_id`) REFERENCES `user_groups` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user_group_members` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `group_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `deleted` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `group_id` (`group_id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `user_group_members_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `user_groups` (`id`),
    CONSTRAINT `user_group_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;