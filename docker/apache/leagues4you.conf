<VirtualHost *:80>
    ServerName local.hub.leagues4you.co.uk
    DocumentRoot /var/www/html/hub
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.lockerroom.leagues4you.co.uk
    DocumentRoot /var/www/html/lockerroom
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.leagues4you.co.uk
    DocumentRoot /var/www/html/www
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.dashboard.leagues4you.co.uk
    DocumentRoot /var/www/html/dashboard
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.cdn.leagues4you.co.uk
    DocumentRoot /var/www/html/cdn
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.admin.v2.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api/v2/admin
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.user.v2.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api/v2/user
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.public.v2.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api/v2/public
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.admin.v3.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api/v3/admin
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.user.v3.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api/v3/user
    DirectoryIndex index.htm index.html index.php
</VirtualHost>

<VirtualHost *:80>
    ServerName local.public.v3.api.leagues4you.co.uk
    DocumentRoot /var/www/html/api/v3/public
    DirectoryIndex index.htm index.html index.php
</VirtualHost>