FROM php:8.1.13-apache

# Install necessary packages
RUN apt-get update && \
    apt-get install -y \
        libpng-dev \
        libjpeg-dev \
        libpq-dev \
        libzip-dev \
        zip \
        unzip

# Enable Apache modules
RUN a2enmod rewrite

# Install PHP extensions
RUN docker-php-ext-configure gd --with-jpeg && \
    docker-php-ext-install gd mysqli pdo_mysql pdo_pgsql pgsql zip

# Set document root
ENV APACHE_DOCUMENT_ROOT /var/www/html

# Set permissions for application files
RUN chown -R www-data:www-data /var/www/html

COPY apache/leagues4you.conf /etc/apache2/sites-available/leagues4you.conf
RUN a2ensite leagues4you.conf
RUN a2enmod ssl

# Start Apache server
CMD ["apache2-foreground"]
