<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
    <title>Chat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
</head>

<body>
    <div id="app">
        <div class="container">
            <h2>{{ title }} </h2>
            <p>{{ status }}</p>
            <input class="form-control" type="text" id="name" v-model="name" placeholder="Name">
            <textarea class="form-control my-2" v-model="message" id="chat-box" placeholder="Message"></textarea>
            <button v-if="!connected && name" class="btn btn-sm btn-info" type="button"
                v-on:click="sendConnection">Connect</button>
            <button v-if="connected" class="btn btn-sm btn-warning" type="button"
                v-on:click="sendDisconnection">Disconnect</button>
            <button class="btn btn-sm btn-success" type="button" v-on:click="sendMessage">Send</button>
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="message in messages">
                        <th>{{ message.name }}</th>
                        <th>{{ message.message }}</th>
                        <th>{{ message.type }}</th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
        crossorigin="anonymous"></script>
    <script>
        var app = new Vue({
            el: '#app',
            data: {
                title: 'Chat App',
                websocket: null,
                connected: false,
                messages: [],
                message: null,
                name: null,
                channels: [],
                channelID: 1,
            },
            computed: {
                status: function () {
                    return (this.connected) ? "Connected" : "Disconnected";
                }
            },
            methods: {
                sendMessage: function () {
                    if (!this.message) return;
                    let payload = {
                        name: this.name,
                        message: this.message,
                        channelID: this.channelID
                    };
                    this.message = null;
                    this.websocket.send(JSON.stringify(payload));
                    console.log(payload);
                },
                sendConnection: function () {
                    this.websocket = new WebSocket("ws://chat.leagues4you.co.uk:8090");
                    this.websocket.onopen = function (event) {
                        app.connected = true;
                    };
                    this.websocket.onmessage = function (event) {
                        let message = JSON.parse(event.data);
                        if (message.type == "usermsg") app.messages.push(message);
                        // console.log("Received", message);
                    };
                },
                sendDisconnection: function () {
                    this.websocket.close();
                },
            },
        })
    </script>
    <script>
        // function showMessage(messageHTML) {
        //     $('#chat-box').append(messageHTML);
        // }

        // $(document).ready(function () {
        //     var websocket = new WebSocket("ws://chat.leagues4you.co.uk:8090");
        //     websocket.onopen = function (event) {
        //         showMessage("<div class='chat-connection-ack'>Connection is established!</div>");
        //     }
        //     websocket.onmessage = function (event) {
        //         var Data = JSON.parse(event.data);
        //         showMessage("<div class='" + Data.message_type + "'>" + Data.message + "</div>");
        //         $('#chat-message').val('');
        //     };

        //     websocket.onerror = function (event) {
        //         showMessage("<div class='error'>Problem due to some Error</div>");
        //     };
        //     websocket.onclose = function (event) {
        //         showMessage("<div class='chat-connection-ack'>Connection Closed</div>");
        //     };

        //     $('#frmChat').on("submit", function (event) {
        //         event.preventDefault();
        //         $('#chat-user').attr("type", "hidden");
        //         var messageJSON = {
        //             chat_user: $('#chat-user').val(),
        //             chat_message: $('#chat-message').val()
        //         };
        //         websocket.send(JSON.stringify(messageJSON));
        //     });
        // });
    </script>
</body>

</html>