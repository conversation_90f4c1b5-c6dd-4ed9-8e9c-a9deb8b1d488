.clickable {
  cursor: pointer;
}

button:disabled {
  cursor: no-drop;
}

.accordion {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
}

tfoot {
  background-color: red;
}

tfoot th {
  color: white;
}

.week-table tr:nth-child(even) {
  background-color: #f2f2f2;
}

.week-table tr:hover {
  background-color: #ddd;
}

.week-th {
  background-color: #04aa6d;
  color: white;
}

.accordion button {
  background-color: #ffc107;
  color: #333;
  cursor: pointer;
  padding: 10px;
  text-align: left;
  border: none;
  outline: none;
  transition: 0.4s;
  font-weight: 700;
}

.accordion button:hover {
  background-color: #ddd;
}

.accordion button.active {
  background-color: #28a745;
  color: white;
}

.accordion-panel {
  display: none;
  padding: 10px;
  border-top: 1px solid #ccc;
}

.accordion-panel.show {
  display: flex;
  flex-direction: column;
}

.table {
  margin-top: 10px;
  border-collapse: collapse;
  width: 100%;
}

.table th,
.table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.table th {
  background-color: #28a745;
  color: white;
}