   /* Main container styling */
   .incident-container {
       background-color: #f8f9fa;
       border-radius: 8px;
       padding: 20px;
       box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
   }

   /* Filter section styling */
   .filter-section {
       background-color: white;
       border-radius: 8px;
       padding: 15px;
       margin-bottom: 20px;
       box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
   }

   .filter-title {
       font-size: 1.1rem;
       font-weight: 600;
       margin-bottom: 15px;
       color: #495057;
   }

   /* Table styling */
   .incident-table {
       background-color: white;
       border-radius: 8px;
       overflow: hidden;
       box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
   }

   .incident-table thead {
       background-color: #f1f3f5;
   }

   .incident-table th {
       font-weight: 600;
       color: #495057;
       border-top: none;
       border-bottom: 2px solid #dee2e6;
       padding: 12px 15px;
   }

   .incident-table td {
       padding: 12px 15px;
       vertical-align: middle;
   }

   .incident-table tbody tr {
       transition: background-color 0.2s;
   }

   .incident-table tbody tr:hover {
       background-color: #f8f9fa;
   }

   /* Alternating row colors */
   .incident-table tbody tr:nth-child(even) {
       background-color: #f8f9fa;
   }

   /* Status badges */
   .status-badge {
       padding: 5px 10px;
       border-radius: 50px;
       font-size: 0.85rem;
       font-weight: 500;
       display: inline-block;
       min-width: 90px;
       text-align: center;
   }

   .status-open {
       background-color: #d4edda;
       color: #155724;
   }

   .status-pending {
       background-color: #fff3cd;
       color: #856404;
   }

   .status-closed {
       background-color: #f8d7da;
       color: #721c24;
   }

   /* Severity badges */
   .severity-badge {
       padding: 5px 10px;
       border-radius: 50px;
       font-size: 0.85rem;
       font-weight: 500;
       display: inline-block;
       min-width: 80px;
       text-align: center;
   }

   .severity-green {
       background-color: #d4edda;
       color: #155724;
   }

   .severity-amber {
       background-color: #fff3cd;
       color: #856404;
   }

   .severity-red {
       background-color: #f8d7da;
       color: #721c24;
   }

   /* Action dropdown styling */
   .action-dropdown .dropdown-toggle {
       border-radius: 4px;
       padding: 0.375rem 0.75rem;
   }

   .action-dropdown .dropdown-menu {
       min-width: 200px;
       padding: 0.5rem 0;
       box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
       border: none;
   }

   .action-dropdown .dropdown-item {
       padding: 0.5rem 1rem;
       display: flex;
       align-items: center;
       gap: 8px;
   }

   .action-dropdown .dropdown-item i {
       width: 16px;
       text-align: center;
   }

   .action-dropdown .dropdown-item:hover {
       background-color: #f8f9fa;
   }

   /* Pagination styling */
   .pagination-container {
       margin-top: 20px;
       display: flex;
       justify-content: center;
   }

   .pagination .page-link {
       color: #6c757d;
       border: none;
       margin: 0 3px;
       border-radius: 4px;
   }

   .pagination .page-item.active .page-link {
       background-color: #007bff;
       color: white;
   }

   /* Summary info */
   .summary-info {
       margin-top: 10px;
       color: #6c757d;
       font-size: 0.9rem;
   }

   /* Responsive adjustments */
   @media (max-width: 768px) {
       .incident-table {
           display: block;
           overflow-x: auto;
       }
   }