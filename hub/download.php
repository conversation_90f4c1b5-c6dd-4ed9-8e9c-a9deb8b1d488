<?php
include (__DIR__."/app/Init.php");
if (isset($_POST['download']) && $_POST['download'] && file_exists($_POST['download'])) {
    $ext = substr($_POST['download'],strrpos($_POST['download'],".")+1);
    $inlineExtensions = ["pdf"];
    $disposition = (in_array($ext,$inlineExtensions)) ? "inline" : "attachment";
    // exit($disposition);
    // $disposition = "attachment";
    // header("Content-Type: application/octetstream");
    header("Content-Type: application/$ext");
    header("Content-Transfer-Encoding: Binary");
    header("Content-Length:" . filesize($_POST['download']));
    header("Content-Disposition: $disposition; filename='".basename($_POST['download'])."'");
    // header("Content-Disposition: $disposition; filename="".basename($_POST['download'])."\"");
    readfile($_POST['download']);
    exit(0);   
}