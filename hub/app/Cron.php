<?php
exit;
include("Init.php");
global $config;
// Daily
$logFolder = \Tools\CheckFolder($config['system']['path']."app/logs/cronDaily/");
$logFile = date('Ymd').".csv";
if (!file_exists($logFolder.$logFile)) {
    $log[] = "Running Daily Cron at " . date('Y-m-d H:i:s');
    Venue::CalculateDistances();
    $billing = Fixture::Billable();
    if ($billing) Email::Issue("Daily Billing", $billing,["<EMAIL>" => "Bloomnetball"]);
    // $log = array_merge($log,$rlt);
    // $subject = "Daily Cron " . $config['system']['url'];
    $cardChecks = Team::PaymentCardSweep();
    if ($cardChecks) Email::Issue("Daily Card Check", $cardChecks,["<EMAIL>" => "Bloomnetball"]);
    Db::Backup();
    // Email::Issue($subject, $log,[$config['system']['internalEmail'] => $config['system']['internalEmail']]);
    $fp = fopen($logFolder.$logFile,"a");
    fputcsv($fp,$log); fclose($fp);
}