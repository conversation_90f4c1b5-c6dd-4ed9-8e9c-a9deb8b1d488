# System Documentation Notes
## Bootstrap : index.php
- Calls app/init.php
    - Error Reporting
    - Config
    - Cookie settings and session_start
    - include app/Core and app/Procedures functions files
    - ClassAutoloader
    - Route\Set
        - Explodes URL
- HTML <head>
- menu (Guest or Admin)
- Route\Display (Passing $GLOBALS['url'])
    - Uses $GLOBALS Controller, Action & Variables
    - Calls Controller Class/Method
- Scripts and End HTML

## Cron
- Venue::CalculateDistances()
- Fixture::Billable()
- Team::PaymentCardSweep()
- Db::Backup()
- Logs events

## Fixture::Billable()
Intended to simply raise invoices that are added to the Customer Statement.
- Selects Fixtures that 
    - have a Booking scheduled for today or earlier
    - that have not already had an Invoice allocated to either Home or Away team
- For each located Fixture
    - Run Fixture->createInvoices()
