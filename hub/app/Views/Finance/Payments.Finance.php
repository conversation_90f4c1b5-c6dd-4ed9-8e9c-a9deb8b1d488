<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" role="dialog" aria-labelledby="paymentModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
  <form action="/Finance/Payments" method="post">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="paymentModalLabel">Payment</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <input type="hidden" name="payment[ledger]" value="S">
        <input type="hidden" name="payment[type]" value="P">
        <label for="payment.category">Category</label>
        <select name="payment[category]" id="payment.category" class="form-control">
            <option value="P" selected>Payment</option>
            <option value="R">Refund</option>
        </select>
        <label for="payment.taxDate">Date</label>
        <input type="date" name="payment[taxDate]" id="payment.taxDate" class="form-control" value="<?php echo date('Y-m-d');?>" required>
        <label for="payment.description">Description</label>
        <input type="text" name="payment[description]" id="payment.description" class="form-control" required>
        <label for="payment.teamID">Team</label>
        <select name="payment[teamID]" id="payment.teamID" class="form-control" required>
            <option value="">...</option><?php
            foreach ($data['teams'] as $team) {?>
                <option value="<?php echo $team->id;?>"<?php if (isset($data['teamID']) && $data['teamID']==$team->id) echo ' selected';?>><?php echo $team->fullName();?></option><?php
            }?>
        </select>
        <label for="payment.total">Value</label>
        <input type="text" name="payment[total]" id="payment.total" class="form-control" required>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        <button type="submit" class="btn btn-primary">Save Payment</button>
      </div>
    </div>
    </form>
  </div>
</div>
<div class="container-fluid">
    <h3>Payments | <button type="button" class="btn btn-sm btn-warning" data-toggle="modal" data-target="#paymentModal">New</button></h3>
    <form action="/Finance/Payments" method="post" class="d-flex flex-column flex-md-row">
        <select name="teamID" id="teamID" class="form-control" onchange="form.submit();">
            <option value="">Team</option><?php
            foreach ($data['teams'] as $team) { ?>
              <option value="<?php echo $team->id;?>"<?php if (isset($data['teamID']) && $data['teamID']==$team->id) echo ' selected';?>><?php echo $team->fullName();?></option><?php
            } ?>
        </select>
        <select name="typeID" id="typeID" class="form-control" onchange="form.submit();">
            <option value="">Type</option>
            <option value="P"<?php if (isset($data['typeID']) && $data['typeID']=="P") echo ' selected';?>>Payment Rcvd</option>
            <option value="R"<?php if (isset($data['typeID']) && $data['typeID']=="R") echo ' selected';?>>Refund Issd</option>
        </select>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Date</th>
                <th>Type</th>
                <th>For</th>
                <th>Description</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody><?php
        $subtotal = 0;
        foreach ($data['transactions'] as $transaction) {
          if (isset($data['typeID']) && $data['typeID']!=$transaction->getCategoryID()) continue;
          $subtotal += $transaction->getTotal(); ?>
            <tr>
                <td><?php echo $transaction->id;?></td>
                <td><?php echo date('d/m/Y',strtotime($transaction->getTaxDate()));?></td>
                <td><?php echo $transaction->getCategoryName();?></td>
                <td><?php echo $transaction->getTeam();?></td>
                <td><?php echo $transaction->getDescription();?></td>
                <td class="text-right"><?php echo number_format($transaction->getTotal(),2);?></td>
            </tr><?php
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="5">Total</th>
                <th class="text-right"><?php echo number_format($subtotal,2);?></th>
            </tr>
        </tfoot>
    </table>
</div>