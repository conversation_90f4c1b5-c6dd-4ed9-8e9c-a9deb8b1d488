<div class="container-fluid">
    <h1>Customer Balances</h1>
    <form action="" method="post">
        <select name="filter" id="filter" class="form-control" onchange="form.submit()">
            <option value="">Show All</option>
            <option value="1">Owing Only</option>
        </select>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>Team</th>
                <th>League</th>
                <th>Treasurer</th>
                <th>Card</th>
                <th class="text-right">Balance</th>
            </tr>
        </thead>
        <tbody><?php
        $total = 0;
        foreach ($data['balances'] as $teamID => $balance) { 
            if (isset($_POST['filter']) && $_POST['filter'] && $balance <=0) continue;
            $total += $balance;
            $team = new Team($teamID);
            $treasurer = ($team->getTreasurerID()) ? new User($team->getTreasurerID()) : null;
            $paymentMethod = ($team->getStripePaymentMethodID()) ? true : false;
            ?>
            <tr>
                <td><a href="/Finance/Statement/<?php echo $teamID;?>"><?php echo $team;?></a></td>
                <td><?php echo $team->getLeagueName();?></td>
                <td><?php echo $treasurer;?></td>
                <td class="text-center text-light <?php echo ($paymentMethod) ? "bg-success" : "bg-danger";?>"><?php echo ($paymentMethod) ? "&check;" : "&cross;";?></td>
                <td class="text-right"><?php echo $balance;?></td>
            </tr><?php
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="4">Total</th>
                <th class="text-right"><?php echo $total;?></th>
            </tr>
        </tfoot>
    </table>
</div>