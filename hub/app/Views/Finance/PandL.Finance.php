<div class="container-fluid">
    <h1>P&L Report</h1>
    <form class="form-inline" action="" method="post">
        <select name="regionID" id="regionID" class="form-control">
            <option value="">All Regions</option><?php
            foreach ($data['regions'] as $region) { ?>
                <option value="<?= $region->id ?>"<?php if (isset($_POST['regionID']) && $_POST['regionID']==$region->id) echo ' selected';?>><?= $region ?></option><?php
            } ?>
        </select>
        <input type="date" name="startDate" id="startDate" class="form-control" value="<?= $_POST['startDate'] ?>">
        <input type="date" name="endDate" id="endDate" class="form-control" value="<?= $_POST['endDate'] ?>">
        <button type="submit" class="btn btn-sm btn-primary">Filter</button>
    </form>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Season</th>
                <th>League</th>
                <th class="text-center">Team Count</th>
                <th class="text-center">Utilisation</th>
                <th class="text-right">Inv &pound;</th>
                <th class="text-right">Chr &pound;</th>
            </tr>
        </thead>
        <tbody><?php
        $st = ["income" => 0, "expenditure" => 0, "teams" => 0,"seasons" => 0,"utilisation" => 0];
        foreach ($data['opReport'] as $o) {
            $season = new Season($o['seasonID']);
            $league = new League($season->leagueID);
            if (isset($_POST['regionID']) && $_POST['regionID'] && $_POST['regionID'] != $league->regionID) continue;
            $financials = $season->financials($_POST['startDate'],$_POST['endDate']);
            $st['seasons'] ++;
            $st['teams'] += ($teamCount=$season->teamCount());
            $st['utilisation'] += ($utilisation=$season->Utilisation());
            $st['income']+=$financials['invoiced'];
            $st['expenditure']+=$financials['charged'];
             ?>
            <tr>
                <td title="Season ID <?= $season->id ?>"><a href="/User/League/<?= $league->id ?>"><?= $season ?></a></td>
                <td title="League ID <?= $league->id ?>"><?= $league ?></td>
                <td class="text-center"><?= $teamCount ?></td>
                <td class="text-center"><?= $utilisation ?></td>
                <td class="text-right"><?= (int)$financials['invoiced'] ?></td>
                <td class="text-right"><?= (int)$financials['charged'] ?></td>
            </tr>
<?php
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th>Totals</th>
                <th><?= $st['seasons'] ?> Seasons</th>
                <th class="text-center"><?= $st['teams'] ?></th>
                <th class="text-center"><?= ($st['utilisation']) ? (int)($st['utilisation']/$st['seasons']) : null ?></th>
                <th class="text-right"><?= (int)$st['income'] ?></th>
                <th class="text-right"><?= (int)$st['expenditure'] ?></th>
            </tr>
        </tfoot>
    </table>
</div>