<!-- Transation Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1" role="dialog" aria-labelledby="transactionModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <form action="/Finance/Transactions" method="post">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="transactionModalLabel">Transaction</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <input type="hidden" name="transaction[ledger]" value="S">
          <input type="hidden" name="transaction[type]" value="T">
          <label for="transaction.category">Category</label>
          <select name="transaction[category]" id="transaction.category" class="form-control">
            <option value="I" selected>Invoice</option>
            <option value="C">Credit</option>
          </select>
          <label for="transaction.taxDate">Date</label>
          <input type="date" name="transaction[taxDate]" id="transaction.taxDate" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
          <label for="transaction.description">Description</label>
          <input type="text" name="transaction[description]" id="transaction.description" class="form-control" required>

          <div class="form-group">
            <label for="leagueSearch">League</label>
            <div class="input-group">
              <input type="text" id="leagueSearch" class="form-control" placeholder="Search leagues..." autocomplete="off">
              <input type="hidden" name="transaction[leagueID]" id="transaction.leagueID" required>
            </div>
            <div id="leagueResults" class="autocomplete-results"></div>
          </div>

          <div class="form-check mb-3">
            <input type="checkbox" class="form-check-input" id="modalLiveTeams" checked>
            <label class="form-check-label" for="modalLiveTeams">Show Only Live Teams</label>
          </div>

          <label for="transaction.teamIDs">Teams <span id="teamCount" class="text-muted">(0)</span></label>
          <div class="mb-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" id="selectAllTeams">Select All Teams</button>
            <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllTeams">Deselect All</button>
          </div>
          <div id="teamsCheckboxContainer" class="form-control p-2" style="height: 200px; overflow-y: auto;">
            <div class="text-center py-2">Select a league first...</div>
          </div>

          <label for="transaction.total">Value</label>
          <input type="text" name="transaction[total]" id="transaction.total" class="form-control" required>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button type="submit" class="btn btn-primary">Save changes</button>
        </div>
      </div>
    </form>
  </div>
</div>
<div class="container-fluid">
  <h3>Transactions | <button type="button" class="btn btn-sm btn-warning" data-toggle="modal" data-target="#transactionModal">New</button></h3>
  <form action="/Finance/Transactions" method="post" class="d-flex flex-column flex-md-row">
    <select name="teamID" id="teamID" class="form-control" onchange="form.submit();">
      <option value="">Team</option><?php
                                    foreach ($data['teams'] as $team) { ?>
        <option value="<?php echo $team->id; ?>" <?php if (isset($data['teamID']) && $data['teamID'] == $team->id) echo ' selected'; ?>><?php echo $team->fullName(); ?></option><?php
                                                                                                                                                                                } ?>
    </select>
    <select name="typeID" id="typeID" class="form-control" onchange="form.submit();">
      <option value="">All Transactions</option>
      <option value="I" <?php if (isset($_POST['typeID']) && $_POST['typeID'] == "I") echo ' selected'; ?>>Invoices</option>
      <option value="C" <?php if (isset($_POST['typeID']) && $_POST['typeID'] == "C") echo ' selected'; ?>>Credits</option>
    </select>
  </form>
  150
  <table class="table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Date</th>
        <th>Team</th>
        <th>Description</th>
        <th class="text-right">Total</th>
      </tr>
    </thead>
    <tbody><?php
            $subtotal = 0;
            foreach ($data['transactions'] as $transaction) {
              if (isset($_POST['typeID']) && $_POST['typeID'] && $_POST['typeID'] != $transaction->getCategoryID()) continue;
              $subtotal += $transaction->getTotal(); ?>
        <tr>
          <td><?php echo $transaction->id; ?></td>
          <td><?php echo date('d/m/Y', strtotime($transaction->getTaxDate())); ?></td>
          <td><?php echo $transaction->getTeam(); ?></td>
          <td><?php echo $transaction->getDescription(); ?></td>
          <td class="text-right"><?php echo number_format($transaction->getTotal() ?? 0, 2); ?></td>
        </tr><?php
            } ?>
    </tbody>
    <tfoot>
      <tr>
        <th colspan="4">Total</th>
        <th class="text-right"><?php echo number_format($subtotal, 2); ?></th>
      </tr>
    </tfoot>
  </table>
</div>
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const form = document.querySelector("#transactionModal form");
    const submitButton = form.querySelector("button[type='submit']");
    const selectAllBtn = document.getElementById('selectAllTeams');
    const deselectAllBtn = document.getElementById('deselectAllTeams');
    const teamsContainer = document.getElementById('teamsCheckboxContainer');
    const leagueSearch = document.getElementById('leagueSearch');
    const leagueResults = document.getElementById('leagueResults');
    const leagueIDInput = document.getElementById('transaction.leagueID');
    const modalLiveTeams = document.getElementById('modalLiveTeams');
    const teamCount = document.getElementById('teamCount');

    // Load leagues for autocomplete
    let leagues = <?php echo json_encode(array_map(function ($league) {
                    return ['id' => $league->id, 'name' => $league->name];
                  }, $data['leagues'])); ?>;

    // Form submission validation
    form.addEventListener("submit", function(event) {
      // Check if at least one team is selected
      const selectedTeams = teamsContainer.querySelectorAll('input[type="checkbox"]:checked');

      if (selectedTeams.length === 0) {
        // Prevent form submission
        event.preventDefault();

        // Show alert
        alert("Please select at least one team before saving.");

        // Return focus to the teams container
        teamsContainer.scrollIntoView({
          behavior: 'smooth'
        });
        return false;
      }

      // If validation passes, disable submit button to prevent double submission
      submitButton.disabled = true;
    });

    // League search input event
    leagueSearch.addEventListener('input', function() {
      const searchText = this.value.toLowerCase();

      if (searchText.length < 2) {
        leagueResults.style.display = 'none';
        return;
      }

      // Filter leagues based on search text
      const filteredLeagues = leagues.filter(league =>
        league.name.toLowerCase().includes(searchText)
      );

      // Display results
      displayLeagueResults(filteredLeagues);
    });

    // Handle clicking outside the autocomplete
    document.addEventListener('click', function(e) {
      if (e.target !== leagueSearch && e.target !== leagueResults) {
        leagueResults.style.display = 'none';
      }
    });

    // Function to display league results
    function displayLeagueResults(results) {
      leagueResults.innerHTML = '';

      if (results.length === 0) {
        leagueResults.style.display = 'none';
        return;
      }

      results.forEach(league => {
        const div = document.createElement('div');
        div.className = 'autocomplete-item';
        div.textContent = league.name;
        div.dataset.id = league.id;

        div.addEventListener('click', function() {
          leagueSearch.value = league.name;
          leagueIDInput.value = league.id;
          leagueResults.style.display = 'none';

          // Load teams for the selected league
          loadTeamsByLeague(league.id);
        });

        leagueResults.appendChild(div);
      });

      leagueResults.style.display = 'block';
    }

    // Select all teams
    selectAllBtn.addEventListener('click', function() {
      const checkboxes = teamsContainer.querySelectorAll('input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        checkbox.checked = true;
      });
    });

    // Deselect all teams
    deselectAllBtn.addEventListener('click', function() {
      const checkboxes = teamsContainer.querySelectorAll('input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        checkbox.checked = false;
      });
    });

    // Live teams filter change
    modalLiveTeams.addEventListener('change', function() {
      if (leagueIDInput.value) {
        loadTeamsByLeague(leagueIDInput.value);
      }
    });
  });

  function loadTeamsByLeague(leagueID) {
    const teamsContainer = document.getElementById('teamsCheckboxContainer');
    const teamCount = document.getElementById('teamCount');
    const liveOnly = document.getElementById('modalLiveTeams').checked;

    if (!leagueID) {
      teamsContainer.innerHTML = '<div class="text-center py-2">Select a league first...</div>';
      teamCount.textContent = '(0)';
      return;
    }

    // Show loading indicator
    teamsContainer.innerHTML = '<div class="text-center py-2">Loading teams...</div>';
    teamCount.textContent = '(0)';

    // Fetch teams for the selected league
    fetch('/Api/teamsByLeague/' + leagueID + '?liveOnly=' + liveOnly)
      .then(response => response.json())
      .then(data => {
        teamsContainer.innerHTML = '';

        if (data.length === 0) {
          teamsContainer.innerHTML = '<div class="text-center py-2">No teams found</div>';
          teamCount.textContent = '(0)';
          return;
        }

        // Update team count
        teamCount.textContent = `(${data.length})`;

        // Add search box for teams
        const searchDiv = document.createElement('div');
        searchDiv.className = 'mb-2';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control mb-2';
        searchInput.placeholder = 'Search teams...';

        searchInput.addEventListener('input', function() {
          const searchText = this.value.toLowerCase();
          const teamItems = teamsContainer.querySelectorAll('.form-check');

          teamItems.forEach(item => {
            const label = item.querySelector('label');
            if (label.textContent.toLowerCase().includes(searchText)) {
              item.style.display = '';
            } else {
              item.style.display = 'none';
            }
          });
        });

        searchDiv.appendChild(searchInput);
        teamsContainer.appendChild(searchDiv);

        // Create team checkboxes
        data.forEach(team => {
          const checkboxDiv = document.createElement('div');
          checkboxDiv.className = 'form-check';

          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.className = 'form-check-input';
          checkbox.name = 'transaction[teamIDs][]';
          checkbox.value = team.id;
          checkbox.id = 'team_' + team.id;

          const label = document.createElement('label');
          label.className = 'form-check-label';
          label.htmlFor = 'team_' + team.id;
          label.textContent = team.name;

          checkboxDiv.appendChild(checkbox);
          checkboxDiv.appendChild(label);
          teamsContainer.appendChild(checkboxDiv);
        });
      })
      .catch(error => {
        console.error('Error loading teams:', error);
        teamsContainer.innerHTML = '<div class="text-center py-2">Error loading teams</div>';
      });
  }
</script>
<style>
  .autocomplete-results {
    position: absolute;
    border: 1px solid #ddd;
    border-top: none;
    z-index: 99;
    max-height: 200px;
    overflow-y: auto;
    background-color: white;
    width: 100%;
    display: none;
  }

  .autocomplete-item {
    padding: 8px 12px;
    cursor: pointer;
  }

  .autocomplete-item:hover {
    background-color: #f1f1f1;
  }

  .selected {
    background-color: #e9ecef;
  }
</style>