<div class="container-fluid">
    <h3>Statement</h3>
    <form action="/Finance/Statement" method="post" class="d-flex flex-column flex-md-row">
        <select name="teamID" id="teamID" class="form-control" onchange="form.submit();">
            <option value="">Team</option><?php
            foreach ($data['teams'] as $team) { ?>
              <option value="<?php echo $team->id;?>"<?php if (isset($data['teamID']) && $data['teamID']==$team->id) echo ' selected';?>><?php echo $team->fullName();?></option><?php
            } ?>
        </select>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Type</th>
                <th>Date</th>
                <th>Description</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody><?php
        $subtotal = 0;
        if ($data['transactions']) {
        foreach ($data['transactions'] as $transaction) { 
            $subtotal += $transaction->getTotal(); ?>
            <tr>
                <td><?php echo $transaction->id;?></td>
                <td><?php echo $transaction->getCategoryName();?></td>
                <td><?php echo date('d/m/Y',strtotime($transaction->getTaxDate()));?></td>
                <td><?php echo $transaction->getDescription();?></td>
                <td class="text-right"><?php echo number_format($transaction->getTotal(),2);?></td>
            </tr><?php
        } 
        }?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="4">Total</th>
                <th class="text-right"><?php echo number_format($subtotal,2);?></th>
            </tr>
        </tfoot>
    </table>
</div>