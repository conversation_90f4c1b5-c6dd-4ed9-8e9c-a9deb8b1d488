<div class="container-fluid">
    <h3>Daily Figures</h3>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Date</th>
                <th class="text-center">C.Fwd</th>
                <th class="text-center">Fixt.</th>
                <th class="text-center">Owed</th>
                <th class="text-center">Intent</th>
                <th class="text-center">PreAuth</th>
                <th class="text-center">Capture</th>
            </tr>
        </thead>
        <tbody><?php
        $chartFixtures = $chartPreauth = $weekOnWeek = $dailyOwed = [];
        foreach ($data['billingTotals'] as $billingTotal) { 
            if (count($chartPreauth)<7) $chartPreauth[substr(date('D',strtotime($billingTotal['date'])),0,2)] = $billingTotal['preauth'];
            if (count($chartFixtures)<7) $chartFixtures[substr(date('D',strtotime($billingTotal['date'])),0,2)] = $billingTotal['fixtures'];
            if (count($weekOnWeek)<8 && date('D',strtotime($billingTotal['date'])) == date('D')) $weekOnWeek[date('d/m/Y',strtotime($billingTotal['date']))] = $billingTotal['fixtures'];
            $dailyOwed[date('d/m/Y',strtotime($billingTotal['date']))] = $billingTotal['debt'];
            ?>
            <tr style="<?= (date('D')==date('D',strtotime($billingTotal['date']))) ? 'background-color: #FAFAFA' : null?>">
                <td><?= date('D d F',strtotime($billingTotal['date'])) ?></td>
                <td class="text-center"><?= ($billingTotal['debt'] - $billingTotal['fixtures']) ?></td>
                <td class="text-center"><?= $billingTotal['fixtures']; ?></td>
                <td class="text-center"><?= $billingTotal['debt'] ?></td>
                <td class="text-center"><?= $billingTotal['intent'] ?></td>
                <td class="text-center"><?= $billingTotal['preauth'] ?></td>
                <td class="text-center"><?= $billingTotal['capture'] ?></td>
            </tr><?php
        } ?>
        </tbody>
    </table>
    <div class="row">
        <div class="col">
            <canvas id="chartFixtures" width="200" height="200"></canvas>
        </div>
        <div class="col">
            <canvas id="chartPreauth" width="200" height="200"></canvas>
        </div>
    </div>
    <div class="row">
        <div class="col" style="height: 400px; width:400px;">
            <canvas id="weekOnWeek" width="400" height="400"></canvas>
        </div>
        <div class="col" style="height: 400px; width:400px;">
            <canvas id="dailyOwed" width="400" height="400"></canvas>
        </div>
    </div>
</div><?php
/*
<script>
    var chartFixtures = document.getElementById('chartFixtures').getContext('2d');
    var myChart = new Chart(chartFixtures, {
        type: 'bar',
        data: {
            labels: [<?= "'".implode("','",array_keys($chartFixtures))."'" ?>],
            datasets: [{
                label: 'Daily Fixture Charges',
                data: [<?= implode(",",$chartFixtures) ?>],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 206, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                    'rgba(255, 159, 64, 0.2)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    var chartPreauth = document.getElementById('chartPreauth').getContext('2d');
    var myChart = new Chart(chartPreauth, {
        type: 'bar',
        data: {
            labels: [<?= "'".implode("','",array_keys($chartPreauth))."'" ?>],
            datasets: [{
                label: 'Daily Pre-Auth',
                data: [<?= implode(",",$chartPreauth) ?>],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 206, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                    'rgba(255, 159, 64, 0.2)'
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    var weekOnWeek = document.getElementById('weekOnWeek').getContext('2d');
    var myChart = new Chart(weekOnWeek, {
        type: 'line',
        data: {
            labels: [<?= "'".implode("','",array_keys($weekOnWeek))."'" ?>],
            datasets: [{
                label: 'Week on Week',
                data: [<?= implode(",",array_reverse($weekOnWeek))?>],
                fill: false,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        }
    });
    var dailyOwed = document.getElementById('dailyOwed').getContext('2d');
    var myChart = new Chart(dailyOwed, {
        type: 'line',
        data: {
            labels: [<?= "'".implode("','",array_keys($dailyOwed))."'" ?>],
            datasets: [{
                label: 'Daily Debt',
                data: [<?= implode(",",array_reverse($dailyOwed))?>],
                fill: false,
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        }
    });
</script> */