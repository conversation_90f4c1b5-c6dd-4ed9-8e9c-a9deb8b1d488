<link rel='stylesheet' type='text/css' href='/css/hub.leagues4you.css'>
<script src="/js/fixtureexport.js"></script>

<div class="container-fluid">
    <h3>Fixture Report</h3>
    <form method="post" action="">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="startDate">Start Date</label>
                    <input type="date" name="startDate" id="startDate" class="form-control" value="<?php echo $_POST['startDate'] ?? ''; ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="endDate">End Date</label>
                    <input type="date" name="endDate" id="endDate" class="form-control" value="<?php echo $_POST['endDate'] ?? ''; ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="coordinatorID">Coordinator</label>
                    <select name="coordinatorID" id="coordinatorID" class="form-control">
                        <option value="">All Coordinators</option>
                        <?php foreach ($coordinators as $coordinatorID => $coordinator) { ?>
                            <option value="<?= $coordinatorID ?>" <?php echo (isset($_POST['coordinatorID']) && $_POST['coordinatorID'] == $coordinatorID) ? "selected" : ""; ?>>
                                <?= $coordinator ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-primary">Go</button>
            <button id="exportButton" class="btn btn-success" type="button">Export</button>
        </div>
    </form>

    <table class="table">
        <thead>
            <tr>
                <th class="week-th">League</th>
                <?php foreach ($weeks as $week) : ?>
                    <th class="text-center week-th">
                        <?= $week['start'] ?> to <?= $week['end'] ?>
                    </th>
                <?php endforeach; ?>
                <th class="text-center week-th">Total Fixture &pound;</th>
            </tr>
        </thead>
        <tbody class="week-table">
            <?php
            $marginRanges = [
                66 => "bg-success",
                56 => "bg-warning",
                1 => "bg-danger",
            ];
            $totalLeagues = $totalBookingCharges = $totalProfit = 0;
            $totalAllFixtureCharges = 0; // Variable to store total charges for all leagues
            $i = 0;
            foreach ($results as $league => $d) {
                $leagueName = $league;
                $totalFixtureCharges = 0; // Reset total fixture charges for each league
                $totalBookingCharges = 0;
                $totalLeagues++;
            ?>
                <tr>
                    <td><?= $leagueName ?></td>
                    <?php foreach ($weeks as $key => $week) : ?>
                        <?php
                        $startandEnd = $week['start'] . ' - ' . $week['end'];
                        $fixtureCharges = $other[$leagueName][$startandEnd] ?? 0;
                        $totalFixtureCharges += $fixtureCharges;
                        $bookingCost =
                            $book[$leagueName][$startandEnd] ?? 0;
                        $profit = $fixtureCharges - $bookingCost;

                        ?>
                        <td class="text-center" id="week<?= $i . $key ?>">
                            <span class="fixture-cost">£<?= $fixtureCharges ?></span>
                            <br>
                            <span class="booking-cost">£<?= $bookingCost ?></span>
                            <br>
                            Profit :<span class="profit"> £<?= $profit ?></span>
                        </td>



                    <?php endforeach; ?>
                    <td class="text-center">
                        <span class="total<?= $i ?>">£<?= $totalFixtureCharges ?></span>
                    </td>
                </tr>
            <?php
                $totalAllFixtureCharges += $totalFixtureCharges; // Accumulate charges for all leagues
                $i++;
            } ?>
        </tbody>
        <tfoot>
            <tr>
                <th><?= $totalLeagues ?> Leagues</th>
                <?php foreach ($weeks as $week) : ?>
                    <th class="text-center"></th>
                <?php endforeach; ?>
                <th class="text-center">£<?= $totalAllFixtureCharges ?></th>
                <!-- Display total charges for all leagues -->
            </tr>
        </tfoot>
    </table>



    <?php
    if (isset($messages)) {
        Tools::Dump($messages);
    }
    ?>
</div>