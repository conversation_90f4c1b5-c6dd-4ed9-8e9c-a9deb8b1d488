<?php
$leagues = League::Listing();
?>
<style>
    body {
        font-family: sans-serif;
        font-size: 13px;
    }
    input, button {
        border: thin solid #ddd;
        font-family: sans-serif;
    }
    input {
        padding: .25rem;
    }
    button {
        padding: .4rem;
    }
    table {
        border-collapse: collapse;
        font-size: 13px;
    }
    table th, table td {
        border: thin solid #ddd;
        padding: .25rem .4rem;
        text-align:left;
    }
    .text-center { text-align: center;}
    .bg-danger { background-color: red; color: #fff;}
    .bg-warning { background-color: orange; color: #fff;}
</style>
<?php
if (!isset($_POST['startDate']) || !$_POST['startDate']) {
    $date = date('Y-m-d',strtotime("-1 days"));
    while (date("N",strtotime($date)) != 1) {
        $date = date('Y-m-d',strtotime("-1 day",strtotime($date)));
    }
    $_POST['startDate'] = $date;
}
if (!isset($_POST['endDate']) || !$_POST['endDate']) {
    $_POST['endDate'] = date('Y-m-d',strtotime("+6 days",strtotime($date)));
}
?>
<form action="" method="post">
    <label for="startDate">Start</label>
    <input type="date" name="startDate" id="startDate" value="<?php echo $_POST['startDate'];?>">
    <label for="endDate">End</label>
    <input type="date" name="endDate" id="endDate" value="<?php echo $_POST['endDate'];?>">
    <button type="submit">Go</button>
</form>
<table>
    <thead>
        <tr>
            <th>League</th>
            <th class="text-center">Teams</th>
            <th>Day</th>
            <th class="text-center">Book. Mins</th>
            <th class="text-center">Book. Cost</th>
            <th class="text-center">Fixt. Mins</th>
            <th class="text-center">Fixt. Chrg</th>
            <th class="text-center">Billing</th>
            <th class="text-center">Credit</th>
            <th class="text-center">Outstanding</th>
            <th class="text-center">Utilisation</th>
            <th class="text-center">Profit</th>
            <th class="text-center">Margin</th>
        </tr>
    </thead>
    <tbody><?php
    $totals = ["bookingCost" => 0,  "billing" => 0, "credit" => 0, "uncollected" => 0, "fixtureCharges" => 0, "bookingMinutes" => 0, "fixtureMinutes" => 0, "teamTotal" => 0, "teamAvg" => 0, "cost" => 0];
    foreach ($leagues as $league) { 
        $seasons = Season::leagueDefaults($league);
        if (!isset($seasons['live']) || !$seasons['live']->isLocked() || $seasons['live']->getLaunchDate() > date('Y-m-d')) continue;
        $booking = Booking::CostsBetween($_POST['startDate'],$_POST['endDate'],$league);
        $fixture = Fixture::LeagueTotals($_POST['startDate'],$_POST['endDate'],$league);
        $profit = ($fixture) ? $fixture['charge'] - $booking['cost'] : -$booking['cost'];
        $margin = ($fixture) ? round(($profit / $fixture['charge'])*100) : null;
        $totals['bookingCost'] += $booking['cost'];
        if ($fixture) $totals['fixtureCharges'] += $fixture['charge'];
        $totals['bookingMinutes'] += $booking['minutes'];
        if ($fixture) $totals['fixtureMinutes'] += $fixture['minutes'];
        $totals['teamTotal'] += (($teamCount=$seasons['live']->teamCount()));
        $billing = StripePayment::SeasonTotalInPeriod($seasons['live'],$_POST['startDate'],$_POST['endDate']);
        $transactions = Finance::SeasonTotalInPeriod($seasons['live'],$_POST['startDate'],$_POST['endDate']);
        $credit = (isset($transactions['c'])) ? $transactions['c'] : 0;
        $totals['credit'] += $credit;
        $uncollected = ($fixture) ? $fixture['charge'] - $billing : 0;
        $totals['billing'] += $billing;
        $totals['uncollected'] += $uncollected;
        if ($margin <= 78) {
            $bg = "danger";
        } elseif ($margin <= 83) {
            $bg = "warning";
        } else $bg = null;
        ?>
        <tr>
            <td title="<?php echo $seasons['live'];?>. Starting <?php echo $seasons['live']->getLaunchDate('d/m/Y');?>"><?php echo $league->getName();?></td>
            <td class="text-center"><?php echo $teamCount;?></td>
            <td><?php echo substr($league->getPlanningDay(),0,3);?></td>
            <td class="text-center"><?php if ($booking) echo $booking['minutes'];?></td>
            <td class="text-center"><?php if ($booking) echo "&pound;" . (int)$booking['cost'];?></td>
            <td class="text-center"><?php if ($fixture) echo $fixture['minutes'];?></td>
            <td class="text-center"><?php if ($fixture) echo "&pound;" . (int)$fixture['charge'];?></td>
            <td class="text-center"><?php echo "&pound;" . (int)$billing;?></td>
            <td class="text-center"><?php echo "&pound;" . (int)$credit;?></td>
            <td class="text-center"><?php echo "&pound;" . (int)$uncollected;?></td>
            <td class="text-center"><?php echo ($fixture) ? round(($fixture['minutes'] / $booking['minutes'])*100) : null;?></td>
            <td class="text-center"><?php echo "&pound;" . round($profit);?></td>
            <td class="text-center<?php if ($bg) echo " bg-$bg";?>"><?php echo $margin;?></td>
        </tr><?php
    }?>
    </tbody><?php
    $totalProfit = ($totals['fixtureCharges']) ? $totals['fixtureCharges'] - $totals['bookingCost'] : -$totals['bookingCost'];
    $overallMargin = ($totals['fixtureCharges']) ? round(($totalProfit / $totals['fixtureCharges'])*100) : null;
    ?>
    <tfoot>
        <tr>
            <th>Totals</th>
            <th class="text-center"><?php echo $totals['teamTotal'];?></th>
            <td>&nbsp;</td>
            <th class="text-center"><?php echo $totals['bookingMinutes'];?></th>
            <th class="text-center"><?php echo "&pound;" . (int)$totals['bookingCost'];?></th>
            <th class="text-center"><?php echo $totals['fixtureMinutes'];?></th>
            <th class="text-center"><?php echo "&pound;" . (int)$totals['fixtureCharges'];?></th>
            <th class="text-center"><?php echo "&pound;" . (int)$totals['billing'];?></th>
            <th class="text-center"><?php echo "&pound;" . (int)$totals['credit'];?></th>
            <th class="text-center"><?php echo "&pound;" . (int)$totals['uncollected'];?></th>
            <th class="text-center"><?php echo ($totals['fixtureMinutes']) ? round(($totals['fixtureMinutes'] / $totals['bookingMinutes'])*100) : null;?></th>
            <th class="text-center"><?php echo "&pound;" . (int)$totalProfit;?></th>
            <th class="text-center"><?php echo (int)$overallMargin;?></th>
        </tr>
    </tfoot>
</table>