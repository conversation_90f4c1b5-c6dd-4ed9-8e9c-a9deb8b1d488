<div class="container-fluid">
    <h3>Aged Debtors</h3>
    <table class="table">
        <thead>
            <tr>
                <th>Team</th>
                <th>League</th>
                <th>Date</th>
                <th>Days</th>
                <th class="text-right">Pending</th>
                <th class="text-right">Balance</th>
            </tr>
        </thead>
        <tbody><?php
        $total =  $pendingST = 0;
        foreach ($data['debtors'] as $teamID => $debtor) { 
            $team = new Team($teamID);
            $league = new League($team->leagueID);
            if ($debtor['agedDays']<=1) continue;
            $total += $debtor['balance'];
            $pendingST += $debtor['pending'];
            ?>
            <tr>
                <td><a href="/Finance/Statement/<?= $teamID ?>"><?= $team ?></a></td>
                <td><a href="/User/League/<?= $league->id ?>"><?= $league ?></a></td>
                <td><?= date('d/m/Y',strtotime($debtor['agedDate'])) ?></td>
                <td><?= $debtor['agedDays'] ?></td>                
                <td class="text-right"><?= $debtor['pending'] ?></td>
                <td class="text-right"><?= $debtor['balance'] ?></td>
            </tr><?php
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="4">Total</th>
                <th class="text-right"><?= $pendingST ?></th>
                <th class="text-right"><?= $total ?></th>
            </tr>
        </tfoot>
    </table>
</div>