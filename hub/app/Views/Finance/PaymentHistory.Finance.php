<style>
    .filterForm > * {
        margin: .5rem;
    }
</style>
<div class="container-fluid">
    <h3>Payment History</h3>
    <form action="" method="post" class="d-flex align-items-center filterForm">
        <label for="teamID">Team</label>
        <select name="teamID" id="teamID" class="form-control form-control-sm">
            <option value="">...</option><?php
            foreach ($data['teams'] as $team) { ?>
                <option value="<?php echo $team->id;?>"<?php if (isset($_POST['teamID']) && $_POST['teamID']==$team->id) echo ' selected';?>><?php echo $team->fullName();?></option><?php
            } ?>
        </select>
        <label for="startDate">From</label>
        <input type="date" name="startDate" id="startDate" class="form-control form-control-sm">
        <label for="endDate">To</label>
        <input type="date" name="endDate" id="endDate" class="form-control form-control-sm">
        <button type="submit" class="btn btn-sm btn-primary">Get</button>
    </form>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Date / Time</th>
                <th>Ref</th>
                <th>Status</th>
                <th>Note</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody><?php
        if (isset($data['stripePayments']) && $data['stripePayments']) {
        foreach ($data['stripePayments'] as $sp) { ?>
            <tr>
                <td><?php echo date('h:i d/m/Y',strtotime($sp['created']));?></td>
                <td><?php echo $sp['stripePaymentIntentID'];?></td>
                <td><?php echo ucwords($sp['stripePaymentIntentStatus']);?></td>
                <td><?php echo $sp['failText'];?></td>
                <td class="text-right"><?php echo $sp['total'];?></td>
            </tr><?php
        } 
        }?>
        </tbody>
    </table>
</div>