<link rel='stylesheet' type='text/css' href='/css/hub.leagues4you.css'>
<script src="/js/marginexport.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.5/xlsx.full.min.js"></script>


<div class="container-fluid">
    <h3>Margin Report</h3>
    <form method="post" action="">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="startDate">Start Date</label>
                    <input type="date" name="startDate" id="startDate" class="form-control" value="<?php echo $_POST['startDate'] ?? ''; ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="endDate">End Date</label>
                    <input type="date" name="endDate" id="endDate" class="form-control" value="<?php echo $_POST['endDate'] ?? ''; ?>">
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label for="coordinatorID">Coordinator</label>
                    <select name="coordinatorID" id="coordinatorID" class="form-control">
                        <option value="">All Coordinators</option>
                        <?php foreach ($coordinators as $coordinatorID => $coordinator) { ?>
                            <option value="<?= $coordinatorID ?>" <?php echo (isset($_POST['coordinatorID']) && $_POST['coordinatorID'] == $coordinatorID) ? "selected" : ""; ?>>
                                <?= $coordinator ?>
                            </option>
                        <?php } ?>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <button type="submit" class="btn btn-primary">Go</button>
            <button id="exportButton" class="btn btn-success">Export </button>

        </div>
    </form>

    <table class="table">
        <thead>
            <tr>
                <th>League</th>
                <th class="text-center">Booked &pound;</th>
                <th class="text-center">Booked Avg/Hrly</th>
                <th class="text-center">Coordinator</th>
                <th class="text-center">Match &pound;</th>
                <th class="text-center">Fixture &pound;</th>
                <th class="text-center">Profit &pound;</th>
                <th class="text-center">Margin %</th>
                <th class="text-center">Utilisation %</th>
            </tr>
        </thead>
        <tbody> <?php
                $marginRanges = [
                    66 => "bg-success",
                    56 => "bg-warning",
                    1 => "bg-danger",
                ];
                $totalLeagues = $totalBookingCharges = $totalFixtureCharges = $totalProfit = 0;
                foreach ($leagues as $leagueID => $d) {

                    if (isset($_POST['coordinatorID']) && $_POST['coordinatorID'] && (!isset($d['coordinatorID']) || $_POST['coordinatorID'] != $d['coordinatorID'])) {
                        continue;
                    }

                    if (!isset($d['coordinatorID']) || !$d['coordinatorID']) {
                        $league = $leagueID ? new League($leagueID) : null;
                        echo "<tr class=\"bg-dark text-light\"><th colspan=\"9\">Cannot display " . $league . " correctly.</th></tr>";
                        continue;
                    }

                    $leagueName = (isset($d['name'])) ? $d['name'] : null;
                    $teamCount = (isset($d['teamCount'])) ? $d['teamCount'] : null;
                    $bookingDuration = $d['bookingDuration'];
                    $fixtureDuration = (isset($d['fixtureDuration'])) ? $d['fixtureDuration'] : 0;
                    $matchFee = (isset($d['matchFee'])) ? $d['matchFee'] : 0;
                    $matchFees[] = $matchFee;
                    $utilisation = round(($fixtureDuration / $bookingDuration) * 100, 2);
                    $utilisations[] = $utilisation;
                    $bookingCost = (isset($d['bookingCost'])) ? round($d['bookingCost'], 2) : 0;
                    $bookingAvg = round($d['bookingCost'] / ($d['bookingDuration'] / 60), 2);
                    $fixtureCharges = (isset($d['fixtureCharge'])) ? $d['fixtureCharge'] : 0;
                    $profit = $fixtureCharges - $bookingCost;
                    $margin = ($fixtureCharges) ? round(($profit / $fixtureCharges) * 100, 2) : 0;
                    $totalLeagues++;
                    $totalBookingCharges += $bookingCost;
                    $totalFixtureCharges += $fixtureCharges;
                    $totalProfit += $profit;

                    $mColour = null;
                    foreach ($marginRanges as $marginMin => $marginColour) {
                        if ($margin >= $marginMin) {
                            $mColour = $marginColour;
                            break;
                        }
                    }
                    $uColour = ($utilisation >= 88) ? "bg-success" : "bg-danger";
                    $coordinator = new User($d['coordinatorID']); ?>

                <tr>
                    <td><?= $leagueName ?> (<?= $teamCount ?>)</td>
                    <td class="text-center"><?= $bookingCost ?></td>
                    <td class="text-center"><?= $bookingAvg ?></td>
                    <td class="text-center"><?= $coordinator ?></td>
                    <td class="text-center"><?= $matchFee ?></td>
                    <td class="text-center"><?= $fixtureCharges ?></td>
                    <td class="text-center"><?= $profit ?></td>
                    <td class="text-center <?= $mColour ?>"><?= $margin ?></td>
                    <td class="text-center <?= $uColour ?>"><?= $utilisation ?></td>
                </tr><?php
                    }

                    $matchFeeAvg = round(array_sum($matchFees) / count($matchFees), 2);
                    $utilisationAvg = round(array_sum($utilisations) / count($utilisations), 2);
                    $totalMargin = round(($totalProfit / $totalFixtureCharges) * 100, 2); ?>

        </tbody>
        <tfoot>
            <tr>
                <th><?= $totalLeagues ?> Leagues</th>
                <th class="text-center"><?= $totalBookingCharges ?></th>
                <th>&nbsp;</th>
                <th>&nbsp;</th>
                <th class="text-center"><?= $matchFeeAvg ?></th>
                <th class="text-center"><?= $totalFixtureCharges ?></th>
                <th class="text-center"><?= $totalProfit ?></th>
                <th class="text-center"><?= $totalMargin ?></th>
                <th class="text-center"><?= $utilisationAvg ?></th>
            </tr>
        </tfoot>
    </table>
    <?php
    if (isset($messages)) {
        Tools::Dump($messages);
    } ?>
</div>