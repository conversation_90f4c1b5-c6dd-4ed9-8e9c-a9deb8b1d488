<div class="container-fluid">
    <h1>Pending Payments</h1><?php
    if (\User::isAuthor()) {?>
    <form action="" method="post">
        <label for="date">Date</label>
        <input type="date" name="date" id="date" class="form-control" value="<?= $_POST['date'] ?>" onchange="form.submit()">
    </form><?php
    } ?>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Team</th>
                <th>League</th>
                <th>Stripe ID</th>
                <th colspan="2">Status</th>
                <th class="text-right">Owed</th>
                <th>...</th>
            </tr>
        </thead>
        <tbody><?php
        $totals = [];
        foreach ($data['pending'] as $p) { 
            $team = new Team($p->getTeamID());
            if (!isset($totals[$p->stripePaymentIntentStatus])) $totals[$p->stripePaymentIntentStatus] = 0;
            $totals[$p->stripePaymentIntentStatus] += $p->getTotal(); ?>
            <tr data-stripepaymentintentid="<?= $p->getStripePaymentIntentID() ?>">

                <td><a href="Finance/Statement/<?= $team->id ?>"><?= $team ?><?= ($team->lockerRoomVersion) ? " v{$team->lockerRoomVersion}" : null ?></a></td>

                <td><?= $team->getLeagueName() ?></td>

                <td><?= $p->getStripePaymentIntentID() ?></td>

                <td>
                  <?= ($status=$p->getStripePaymentIntentStatus()) ?><?= (isset($pi) && $pi->status != $status) ? "Update" : null ?></td>
                
                <td><?= $p->outcomeValue ?> <?= $p->outcomeReason ?></td>

               <td class="text-right paymentCharge" style="cursor: pointer;"
                data-id="<?php echo $p->id;?>"
                data-teamame="<?= $team->__toString() ?>"
                data-total="<?= $p->getTotal() ?>"><?= $p->getTotal() ?></td>

                <td>
                    <form action="Finance/PendingPayments" method="post" onsubmit="return confirm('Are you sure?');">
                        <input type="hidden" name="pendingPaymentID" value="<?php echo $p->id;?>">
                        <input type="hidden" name="action" value="removePendingPayment">
                        <button class="btn btn-sm" type="submit"><i class="fas fa-window-close text-danger" title="Remove" style="cursor: pointer;"></i></button>
                    </form>
            </tr><?php   
        } ?>
        </tbody>
        <tfoot><?php
        foreach ($totals as $category => $total) { ?>
          <tr>
            <th colspan="6"><?= $category ?></th>
            <th class="text-right"><?= number_format($total,2) ?></th>
          </tr><?php
        } ?>
        </tfoot>
    </table>
</div>