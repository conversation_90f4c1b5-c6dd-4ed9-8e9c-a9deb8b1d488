<div class="container">
    <h1>Login</h1>
    <form id="loginForm">
        <div class="loginMessage bg-danger text-white d-none p-3" id="loginMessage"></div>
        <label for="auth[username]">Username</label>
        <input type="email" name="auth[username]" id="auth[username]" class="form-control" placeholder="<EMAIL>" required>
        <label for="auth[password]">Password</label>
        <input type="password" name="auth[password]" id="auth[password]" class="form-control" placeholder="Your password">
        <br>
        <button id="loginBtn" type="submit" class="btn btn-primary">Login</button>
    </form>

    <!-- 2FA Activation Form (Initially Hidden) -->
    <form id="activationForm" class="d-none">
        <div class="loginMessage bg-danger text-white d-none p-3" id="activationMessage" style="max-width: 200px;"></div>
        <label for="auth[code]">Activation Code</label>
        <input type="text" name="auth[code]" id="auth[code]" class="form-control" placeholder="Enter 2FA Code">
        <div class="form-check mt-2">
            <input type="checkbox" class="form-check-input" id="rememberDevice" name="rememberDevice">
            <label class="form-check-label" for="rememberDevice">Remember this device for 30 days</label>
        </div>
        <br>
        <button id="verifyBtn" type="submit" class="btn btn-success">Verify</button>
    </form>

    <a href="/Home/Password" class="btn btn-sm btn-warning" style="margin-top: 1rem;">Password Reminder</a>
</div>

<script>
    let tempToken = ""; // Temporary token storage for 2FA

    function disableButton(button) {
        button.disabled = true;
        button.innerText = "Processing...";
    }

    function enableButton(button, originalText) {
        button.disabled = false;
        button.innerText = originalText;
    }

    function Login(e) {
        e.preventDefault();

        var loginEmail = document.getElementById("auth[username]");
        var loginPassword = document.getElementById("auth[password]");
        var loginButton = document.getElementById("loginBtn");

        if (!loginEmail || !loginEmail.value || !loginPassword || !loginPassword.value) return;

        disableButton(loginButton);

        var url = "<?= getDomainPrefix(); ?>public.v2.api.leagues4you.co.uk/authenticate/";
        fetch(url, {
                method: 'POST',
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    email: loginEmail.value,
                    password: loginPassword.value
                }),
                credentials: 'include' // Add this to include cookies in the request
            })
            .then(response => response.json())
            .then(json => {
                if (json.success) {
                    if (json.requiresAuth) {
                        // Show 2FA form
                        document.getElementById("loginForm").classList.add("d-none");
                        document.getElementById("activationForm").classList.remove("d-none");
                    } else {
                        // Direct login with device token
                        document.cookie = `jwt=${json.token}; path=${json.cookie.path}; domain=${json.cookie.domain}; expires=${json.cookie.expires}`;
                        window.location = "/User";
                    }
                } else {
                    var loginMessage = document.getElementById("loginMessage");
                    loginMessage.innerHTML = json.message || json.error;
                    loginMessage.classList.remove("d-none");
                    enableButton(loginButton, "Login");
                }
            })
            .catch(err => {
                console.error("Login error:", err);
                enableButton(loginButton, "Login");
            });
    }

    function VerifyCode(e) {
        e.preventDefault();

        var activationCode = document.getElementById("auth[code]");
        var loginEmail = document.getElementById("auth[username]");
        var rememberDevice = document.getElementById("rememberDevice");
        var verifyButton = document.getElementById("verifyBtn");

        if (!activationCode || !activationCode.value) return;

        disableButton(verifyButton);

        var url = "<?= getDomainPrefix(); ?>public.v2.api.leagues4you.co.uk/activation/";
        fetch(url, {
                method: 'POST',
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    email: loginEmail.value,
                    code: activationCode.value,
                    rememberDevice: rememberDevice.checked
                }),
                credentials: 'include' // Add this to include cookies in the request
            })
            .then(response => response.json())
            .then(json => {
                if (json?.success) {
                    // Set JWT cookie
                    document.cookie = `jwt=${json.success.token}; path=${json.success.cookie.path}; domain=${json.success.cookie.domain}; expires=${json.success.cookie.expires}`;

                    // Set device token cookie if provided
                    if (json.success.deviceToken) {
                        document.cookie = `device_token=${json.success.deviceToken}; path=/; domain=.leagues4you.co.uk; expires=${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toUTCString()}`;
                    }

                    window.location = "/User";
                } else {
                    var activationMessage = document.getElementById("activationMessage");
                    activationMessage.innerHTML = json.error || "Invalid 2FA code";
                    activationMessage.classList.remove("d-none");
                    enableButton(verifyButton, "Verify");
                }
            })
            .catch(err => {
                console.error("Verification error:", err);
                enableButton(verifyButton, "Verify");
            });
    }

    document.getElementById("loginBtn").addEventListener("click", Login);
    document.getElementById("verifyBtn").addEventListener("click", VerifyCode);
</script>