<link rel="stylesheet" href="/css/hub.riskassessment.css">
<div class="container-fluid" style="margin-top: 5%;">

    <div class="body-container">
        <div>
            <label for="venueFilter">Venue:</label>
            <select id="venueFilter">
                <option value="">All</option>
                <!-- Add options dynamically based on the available venues -->
                <?php foreach ($data["risks"]["success"]["rows"] as $risk) : ?>
                    <option value="<?= $risk["venue"] ?>"><?= $risk["venue"] ?></option>
                <?php endforeach; ?>
            </select>
            <label for="publishedFilter">Risk Assessment Published:</label>
            <select id="publishedFilter">
                <option value="">All</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
            </select>

            <label for="controlFilter">Amber or Red Control Measures:</label>
            <select id="controlFilter">
                <option value="">All</option>
                <option value="Yes">Yes</option>
                <option value="No">No</option>
            </select>

            <button style="margin-left: 3%;" id="exportButton" class="btn btn-primary">Export CSV</button>
        </div>

        <table id="riskTable" class="table-responsive my-4">
            <thead>
                <tr>
                    <th>Venue</th>
                    <th>Coordinator</th>
                    <th>Risk Assessment Published</th>
                    <th>Any red or Amber control measures?</th>
                    <th>Assessment Publish Date</th>
                    <th>Upcoming Review Date</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($data["risks"]["success"]["rows"] as $risk) : ?>
                    <tr>
                        <?php
                        $decodedRisk = [];
                        if ($risk["Risk"] !== null) {
                            $decodedRisk = json_decode($risk["Risk"] ?? '{}', true);
                        }

                        // Define an array of relevant fields for multiplication
                        $fieldsToMultiply = [
                            "staff",
                            "medical_fitness",
                            "condition",
                            "obstruction",
                            "manual",
                            "fire",
                            "weather",
                            "parking",
                            "electricity",
                            "asbestos",
                            "sports",
                            "jewellery",
                            "insurance",
                            "violent",
                            "crowd",
                            "medical_treatment",
                            "welfare",
                        ];

                        // Initialize flag to check if any field meets the condition
                        $conditionMet = false;

                        // Check each field for the condition (8-15 or 16-25)
                        foreach ($fieldsToMultiply as $field) {
                            $likelihoodKey = $field . "_likelihood";
                            $severityKey = $field . "_severity";

                            // Check if the keys exist before accessing them
                            if (isset($decodedRisk[$likelihoodKey], $decodedRisk[$severityKey])) {
                                $fieldMultiplication =
                                    (float)$decodedRisk[$likelihoodKey] *
                                    (float)$decodedRisk[$severityKey];

                                // Check if the condition is met (8-15 or 16-25)
                                if (
                                    $fieldMultiplication >= 8 &&
                                    $fieldMultiplication <= 15
                                ) {
                                    $conditionMet = true;
                                    break;
                                } elseif (
                                    $fieldMultiplication >= 16 &&
                                    $fieldMultiplication <= 25
                                ) {
                                    $conditionMet = true;
                                    break;
                                }
                            }
                        }

                        // Output 'Yes' or 'No' based on the condition
                        $redAmberControl = $conditionMet ? "Yes" : "No";
                        $riskPublished = $risk["Risk_Assessment_Published"] == "Yes";
                        ?>

                        <td><?= $risk["venue"] ?? "N/A" ?></td>
                        <td><?= $risk["coordinator"] ?? "N/A" ?></td>
                        <td><?= $riskPublished ? "Yes" : "No" ?></td>

                        <td id="red_amber_control"><?= $redAmberControl ?></td>
                        <?php if ($riskPublished) : ?>
                            <td><?= isset($risk["Assessment_Publish_Date"]) ? date("d-m-Y", strtotime($risk["Assessment_Publish_Date"])) : "N/A" ?></td>
                            <td><?= date("d-m-Y", strtotime($risk["Assessment_Publish_Date"] . " +1 year")) ?? "N/A" ?></td>
                        <?php else : ?>
                            <td>N/A</td>
                            <td>N/A</td>
                        <?php endif; ?>
                        <td><?= $risk["Risk_Status"]; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<script src="/js/riskoverallratingtoggle.js"></script>
<link rel='stylesheet' type='text/css' href='/css/hub.leagues4you.css'>