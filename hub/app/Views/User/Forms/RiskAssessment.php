<table class="table table-bordered" id="export-pdf" style="visibility: visible;">
    <thead>
        <tr>
            <th scope="col" style="width: 25%;">Date of Assessment</th>
            <th scope="col" style="width: 25%;">Assessor</th>
            <th scope="col" style="width: 50%;">Additional Info</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td style="width: 25%;"><?php echo $dateOfAssessment; ?></td>
            <td style="width: 25%;"><?php echo $exportUserName; ?></td>
            <td style="width: 50%;"><?php echo isset($risk['additional_info']) ? $risk['additional_info'] : ''; ?></td>
        </tr>
    </tbody>
</table>


<div class="table-responsive my-4">
    <table class='table table-bordered'>
        <thead class="table-success">
            <tr>
                <th>What are the hazards?</th>
                <th>How might people be harmed?</th>
                <th>Likelihood</th>
                <th>Severity</th>
                <th>Overall Risk Rating</th>
                <th>Control Measures</th>
                <th>Likelihood</th>
                <th>Severity</th>
                <th>Overall Risk Rating</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Staff or Adult/Child ratio</td>
                <td>Unsupervised children may be injured or go missing.</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>Clubs/Teams must provide suitable supervision for children. The venue
                    <select required style=" width: 14%; " name="form[staff_select]" class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['staff_select']) && $risk['staff_select'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['staff_select']) && $risk['staff_select'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select>have staff to man entry and exit points.
                </td>
                <td><input id="staff_likelihood" type="text" name="form[staff_likelihood]" value="<?php echo isset($risk['staff_likelihood']) ? $risk['staff_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input id="staff_severity" type="text" name="form[staff_severity]" value="<?php echo isset($risk['staff_severity']) ? $risk['staff_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='overall_risk_rating'>
                    <?php
                    // Check if both values are set
                    if (isset($risk['staff_likelihood']) && isset($risk['staff_severity'])) {
                        // Multiply the values
                        $result = $risk['staff_likelihood'] * $risk['staff_severity'];

                        // Display the result
                        echo $result;
                    }
                    ?>
                </td>
            </tr>


            <tr>
                <td>Medical Fitness</td>
                <td>Legal liability for BLOOM Netball Ltd. Illness and/or injury for participants</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>Clubs/Teams/Captains must provide suitable guidance for those with medical
                    conditions which make them unsuitable for play and/or exclude them from league
                    matches while unfit. <br> A first aid provision is available at all matches.
                </td>
                <td><input type="text" id='medical_fitness_likelihood' name="form[medical_fitness_likelihood]" value="<?php echo isset($risk['medical_fitness_likelihood']) ? $risk['medical_fitness_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='medical_fitness_severity' name="form[medical_fitness_severity]" value="<?php echo isset($risk['medical_fitness_severity']) ? $risk['medical_fitness_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='medical_fitness_risk_rating'>
                    <?php
                    // Check if both values are set
                    if (isset($risk['medical_fitness_likelihood']) && isset($risk['medical_fitness_severity'])) {
                        // Multiply the values
                        $result = $risk['medical_fitness_likelihood'] * $risk['medical_fitness_severity'];
                        echo $result;
                    }
                    ?>
                </td>
            </tr>

            <tr>
                <td>Condition and access of play area, i.e. Slippery, wet, uneven, dirty, dog
                    faeces, litter, glass, large bricks and stones</td>
                <td>Slips, Trips, Falls and obstruction of access may cause illness and/or injury
                </td>
                <td>3</td>
                <td>4</td>
                <td>12</td>
                <td>All players are advised that that BLOOM Netball ltd have no control over the
                    safety of the premises it hires, or persons admitted to the venues it hires, and
                    it is the sole responsibility of the team captain to ensure their teams players
                    do not start any game unless they are satisfied that the surface and other
                    conditions are satisfactory and safe for the activity in question having due
                    regard for the participants. <br> BLOOM Netball officials will inspect the playing
                    surface and playing conditions and will report any issues prior to games
                    commencing to the league coordinator. <br> A first aid provision is available at
                    all matches. </td>
                <td><input type="text" id='condition_likelihood' name="form[condition_likelihood]" value="<?php echo isset($risk['condition_likelihood']) ? $risk['condition_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='condition_severity' name="form[condition_severity]" value="<?php echo isset($risk['condition_severity']) ? $risk['condition_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='condition_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['condition_likelihood']) && isset($risk['condition_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['condition_severity'] * (float)$risk['condition_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>
            <tr>
                <td>Obstructions, i.e. Benches, Chairs, Equipment, and Bags surrounding the play
                    area</td>
                <td>Slips, Trips, and falls may cause injury</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>A designated area for clubs, teams, players, and spectators to leave belongings
                    so that they are not obstructing play areas, entrances, exits, gangways, etc is
                    identified and enforced by the umpire. This is located <input type="text" name="form[obstruction_area]" value="<?php echo isset($risk['obstruction_area']) ? $risk['obstruction_area'] : ''; ?>" placeholder="Enter area"> <br> The umpire must ensure that play areas,
                    entrances, exits, gangways, etc, are not obstructed and work with the relevant
                    parties to move any obstructions present.<br> A first aid provision is available
                    at all matches. </td>
                <td><input type="text" id='obstruction_likelihood' name="form[obstruction_likelihood]" value="<?php echo isset($risk['obstruction_likelihood']) ? $risk['obstruction_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='obstruction_severity' name="form[obstruction_severity]" value="<?php echo isset($risk['obstruction_severity']) ? $risk['obstruction_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='obstruction_risk_rating'><?php
                                                    // Check if both values are set
                                                    if (isset($risk['obstruction_likelihood']) && isset($risk['obstruction_likelihood'])) {
                                                        // Multiply the values
                                                        $result = (float)$risk['obstruction_severity'] * (float)$risk['obstruction_severity'];

                                                        // Display the result
                                                        echo $result;
                                                    }
                                                    ?></td>
            </tr>

            <tr>
                <td>Manual Handling</td>
                <td>Manual handling can cause injury in some cases</td>
                <td>3</td>
                <td>3</td>
                <td>9</td>
                <td>All umpires are provided a suitable bag to carry match equipment. <br> No
                    employees, contractors or customers are required to move or set up any
                    equipment. <br> A first aid provision is available at all matches. </td>
                <td><input type="text" id='manual_likelihood' name="form[manual_likelihood]" value="<?php echo isset($risk['manual_likelihood']) ? $risk['manual_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='manual_severity' name="form[manual_severity]" value="<?php echo isset($risk['manual_severity']) ? $risk['manual_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='manual_risk_rating'><?php
                                            // Check if both values are set
                                            if (isset($risk['manual_likelihood']) && isset($risk['manual_likelihood'])) {
                                                // Multiply the values
                                                $result = (float)$risk['manual_severity'] * (float)$risk['manual_severity'];

                                                // Display the result
                                                echo $result;
                                            }
                                            ?></td>
            </tr>

            <tr>
                <td>Fire Evacuation procedures</td>
                <td>Fire may cause severe harm and/or death</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>The site <select style=" width: 14%; " required name="form[fire_select1]" class="dropdown-select">

                        <option value="has" <?php echo (isset($risk['fire_select1']) && $risk['fire_select1'] === 'has') ? 'selected' : ''; ?>>
                            has</option>
                        <option value="hasnot" <?php echo (isset($risk['fire_select1']) && $risk['fire_select1'] === 'hasnot') ? 'selected' : ''; ?>>
                            does not have</option>
                    </select> working fire detection installations (alarm and smoke detectors),
                    which is tested and serviced regularly. <br><br> Fire fighting equipment <select style=" width: 14%; " name="form[fire_select2]" required class="dropdown-select">

                        <option value="is" <?php echo (isset($risk['fire_select2']) && $risk['fire_select2'] === 'is') ? 'selected' : ''; ?>>
                            is</option>
                        <option value="isnot" <?php echo (isset($risk['fire_select2']) && $risk['fire_select2'] === 'isnot') ? 'selected' : ''; ?>>
                            is not</option>
                    </select> in place, in date, and in a usable condition. <br><br> Emergency
                    lighting <select style=" width: 14%; " required name="form[fire_select3]" class="dropdown-select">

                        <option value="is" <?php echo (isset($risk['fire_select3']) && $risk['fire_select3'] === 'is') ? 'selected' : ''; ?>>
                            is</option>
                        <option value="isnot" <?php echo (isset($risk['fire_select3']) && $risk['fire_select3'] === 'isnot') ? 'selected' : ''; ?>>
                            is not</option>
                    </select> in good working order. <br> <br> Emergency exits <select style=" width: 14%; " name="form[fire_select4]" required class="dropdown-select">

                        <option value="are" <?php echo (isset($risk['fire_select4']) && $risk['fire_select4'] === 'are') ? 'selected' : ''; ?>>
                            are</option>
                        <option value="arenot" <?php echo (isset($risk['fire_select4']) && $risk['fire_select4'] === 'arenot') ? 'selected' : ''; ?>>
                            are not</option>
                    </select> unobstructed, clearly signposted, and have illuminated signage as
                    required. <br> <br> The site <select style=" width: 14%; " required name="form[fire_select5]" class="dropdown-select">

                        <option value="has" <?php echo (isset($risk['fire_select5']) && $risk['fire_select5'] === 'has') ? 'selected' : ''; ?>>
                            has</option>
                        <option value="hasnot" <?php echo (isset($risk['fire_select5']) && $risk['fire_select5'] === 'hasnot') ? 'selected' : ''; ?>>
                            does not have</option>
                    </select> emergency evacuation procedures in place, which are clearly displayed.
                </td>
                <td><input type="text" id='fire_likelihood' name="form[fire_likelihood]" value="<?php echo isset($risk['fire_likelihood']) ? $risk['fire_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='fire_severity' name="form[fire_severity]" value="<?php echo isset($risk['fire_severity']) ? $risk['fire_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='fire_risk_rating'><?php
                                            // Check if both values are set
                                            if (isset($risk['fire_likelihood']) && isset($risk['fire_likelihood'])) {
                                                // Multiply the values
                                                $result = (float)$risk['fire_severity'] * (float)$risk['fire_severity'];

                                                // Display the result
                                                echo $result;
                                            }
                                            ?></td>
            </tr>

            <tr>
                <td>Weather conditions</td>
                <td>Dehydration, heatstroke, sunburn Hypothermia</td>
                <td>4</td>
                <td>5</td>
                <td>20</td>
                <td>BLOOM Netball and clubs, teams, captains to provide information to players and
                    attendees prior to the event, considering both the weather and venue. For
                    example, wearing warm clothing or bringing sun lotion. <br> No league matches
                    will operate where there is a red weather warning in place. <br> A first aid
                    provision is available at all matches. </td>
                <td><input type="text" id='weather_likelihood' name="form[weather_likelihood]" value="<?php echo isset($risk['weather_likelihood']) ? $risk['weather_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='weather_severity' name="form[weather_severity]" value="<?php echo isset($risk['weather_severity']) ? $risk['weather_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='weather_risk_rating'><?php
                                                if (isset($risk['weather_likelihood']) && isset($risk['weather_likelihood'])) {
                                                    $result = (float)$risk['fire_severity'] * (float)$risk['fire_severity'];
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>

            <tr>
                <td>Parking</td>
                <td>Incidents and accidents caused by vehicles or slips and trips</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>The venue <select style="width: 14%; " name="form[parking_select1]" required class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['parking_select1']) && $risk['parking_select1'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['parking_select1']) && $risk['parking_select1'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select>ensure that the car park surface is in good condition. <br> Vehicle and
                    pedestrian routes/flows and car park and site entrance/exits <select style="width: 14%; " name="form[parking_area]" required class="dropdown-select">

                        <option value="are" <?php echo (isset($risk['parking_area']) && $risk['parking_area'] === 'are') ? 'selected' : ''; ?>>
                            are</option>
                        <option value="arenot" <?php echo (isset($risk['parking_area']) && $risk['parking_area'] === 'arenot') ? 'selected' : ''; ?>>
                            are not</option>
                    </select> <br>clearly marked. <br> <br>The car park <select style=" width: 14%; " name="form[parking_select2]" required class="dropdown-select">

                        <option value="is" <?php echo (isset($risk['parking_select2']) && $risk['parking_select2'] === 'is') ? 'selected' : ''; ?>>
                            is</option>
                        <option value="isnot" <?php echo (isset($risk['parking_select2']) && $risk['parking_select2'] === 'isnot') ? 'selected' : ''; ?>>
                            is not</option>
                    </select> well-lit. <br> BLOOM Netball and clubs, teams, captains communicates
                    with attendees and provide details relating to the availability of parking.</td>
                <td><input type="text" id='parking_likelihood' name="form[parking_likelihood]" value="<?php echo isset($risk['parking_likelihood']) ? $risk['parking_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='parking_severity' name="form[parking_severity]" value="<?php echo isset($risk['parking_severity']) ? $risk['parking_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='parking_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['parking_likelihood']) && isset($risk['parking_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['parking_severity'] * (float)$risk['parking_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>

            <tr>
                <td>Electricity and gas</td>
                <td>Incidents and accidents may cause injury or death</td>
                <td>4</td>
                <td>5</td>
                <td>20</td>
                <td>The venue <select style=" width: 14%; " name="form[electricity_select1]" required class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['electricity_select1']) && $risk['electricity_select1'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['electricity_select1']) && $risk['electricity_select1'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select> ensure that electric and gas installations are correctly installed,
                    modified or repaired, then inspected and tested by an electrician, Gas Safe
                    engineer or other suitably qualified person before being put into use, and
                    inspected and tested at suitable (occasional) intervals by an electrician, Gas
                    Safe engineer or other suitably qualified person. <br> The venue <select style=" width: 14%; " name="form[electricity_select2]" required class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['electricity_select2']) && $risk['electricity_select2'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['electricity_select2']) && $risk['electricity_select2'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select> ensure that all portable electrical equipment has been tested at
                    regular intervals and deemed safe.</td>
                <td><input type="text" id='electricity_likelihood' name="form[electricity_likelihood]" value="<?php echo isset($risk['electricity_likelihood']) ? $risk['electricity_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='electricity_severity' name="form[electricity_severity]" value="<?php echo isset($risk['electricity_severity']) ? $risk['electricity_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='electricity_risk_rating'><?php
                                                    // Check if both values are set
                                                    if (isset($risk['electricity_likelihood']) && isset($risk['electricity_likelihood'])) {
                                                        // Multiply the values
                                                        $result = (float)$risk['electricity_severity'] * (float)$risk['electricity_severity'];

                                                        // Display the result
                                                        echo $result;
                                                    }
                                                    ?></td>
            </tr>
            <tr>
                <td>Asbestos</td>
                <td>Illness caused by exposure, which can lead to death</td>
                <td>2</td>
                <td>5</td>
                <td>10</td>
                <td>The venue <select style=" width: 14%; " name="form[asbestos_select1]" required class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['asbestos_select1']) && $risk['asbestos_select1'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['asbestos_select1']) && $risk['asbestos_select1'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select> ensure that any asbestos on site is identified, contained, and remains
                    in good condition.</td>
                <td><input type="text" id='asbestos_likelihood' name="form[asbestos_likelihood]" value="<?php echo isset($risk['asbestos_likelihood']) ? $risk['asbestos_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='asbestos_severity' name="form[asbestos_severity]" value="<?php echo isset($risk['asbestos_severity']) ? $risk['asbestos_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='asbestos_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['asbestos_likelihood']) && isset($risk['asbestos_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['asbestos_severity'] * (float)$risk['asbestos_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>

            <tr>
                <td>Sports equipment</td>
                <td>Minor to severe injury caused by defective equipment or lack of suitable
                    equipment</td>
                <td>3</td>
                <td>4</td>
                <td>12</td>
                <td>BLOOM Netball and clubs, teams, captains must ensure that players wear suitable
                    footwear. <br> The equipment owner (nets, etc.) <select style=" width: 14%; " name="form[sports_select1]" required class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['sports_select1']) && $risk['sports_select1'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['sports_select1']) && $risk['sports_select1'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select> ensure it is regularly inspected and maintained to a good standard.
                    <br> Equipment not needed on the court must be stored away from the court area.
                </td>
                <td><input type="text" id='sports_likelihood' name="form[sports_likelihood]" value="<?php echo isset($risk['sports_likelihood']) ? $risk['sports_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='sports_severity' name="form[sports_severity]" value="<?php echo isset($risk['sports_severity']) ? $risk['sports_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='sports_risk_rating'><?php
                                            // Check if both values are set
                                            if (isset($risk['sports_likelihood']) && isset($risk['sports_likelihood'])) {
                                                // Multiply the values
                                                $result = (float)$risk['sports_severity'] * (float)$risk['sports_severity'];

                                                // Display the result
                                                echo $result;
                                            }
                                            ?></td>
            </tr>

            <tr>
                <td>Jewellery, nails and hair</td>
                <td>Minor injury when making physical contact</td>
                <td>3</td>
                <td>3</td>
                <td>9</td>
                <td>BLOOM Netball and Umpires inform players that fingernails should be kept short and
                    that no jewellery should be worn. <br> Medical alert bracelets and rings may be
                    covered with medical tape. <br> Players with long hair should ensure that their
                    hair is tied back.</td>
                <td><input type="text" id='jewellery_likelihood' name="form[jewellery_likelihood]" value="<?php echo isset($risk['jewellery_likelihood']) ? $risk['jewellery_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='jewellery_severity' name="form[jewellery_severity]" value="<?php echo isset($risk['jewellery_severity']) ? $risk['jewellery_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='jewellery_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['jewellery_likelihood']) && isset($risk['jewellery_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['jewellery_severity'] * (float)$risk['jewellery_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>

            <tr>
                <td>Insurance</td>
                <td>Legal liability (Impact in terms of legal liability and harm to reputation)</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>The venue <select style=" width: 14%; " name="form[insurance_select1]" required class="dropdown-select">

                        <option value="does" <?php echo (isset($risk['insurance_select1']) && $risk['insurance_select1'] === 'does') ? 'selected' : ''; ?>>
                            does</option>
                        <option value="doesnot" <?php echo (isset($risk['insurance_select1']) && $risk['insurance_select1'] === 'doesnot') ? 'selected' : ''; ?>>
                            does not</option>
                    </select> have sufficient public liability insurance. <br> BLOOM Netball has
                    sufficient public liability insurance.</td>
                <td><input type="text" id='insurance_likelihood' name="form[insurance_likelihood]" value="<?php echo isset($risk['insurance_likelihood']) ? $risk['insurance_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='insurance_severity' name="form[insurance_severity]" value="<?php echo isset($risk['insurance_severity']) ? $risk['insurance_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='insurance_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['insurance_likelihood']) && isset($risk['insurance_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['insurance_severity'] * (float)$risk['insurance_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>

            <tr>
                <td>Violent and aggressive attendees</td>
                <td>Injury or mental distress</td>
                <td>3</td>
                <td>4</td>
                <td>12</td>
                <td>BLOOM Netball promotes conduct that must be followed. Clubs, Teams and Captains
                    should communicate rules around conduct and behaviour to all attendees. <br>
                    Alcohol and the use of drugs will not be permitted. <br> Those under the
                    influence will be asked to leave and escorted from the premises if required.
                    <br> The umpires and/or BLOOM Netball staff must halt and consider whether to
                    cancel the event where appropriate. <br> The venue should provide security where
                    there is a known risk of violence.
                </td>
                <td><input type="text" id='violent_likelihood' name="form[violent_likelihood]" value="<?php echo isset($risk['violent_likelihood']) ? $risk['violent_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='violent_severity' name="form[violent_severity]" value="<?php echo isset($risk['violent_severity']) ? $risk['violent_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='violent_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['violent_likelihood']) && isset($risk['violent_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['violent_severity'] * (float)$risk['violent_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>


            <!-- Crowd control	 -->
            <tr>
                <td>Crowd control</td>
                <td>Injury due to crushing or obstruction</td>
                <td>3</td>
                <td>4</td>
                <td>12</td>
                <td>Spectators <select style=" width: 14%; " name="form[crowd_select1]" required class="dropdown-select">

                        <option value="are" <?php echo (isset($risk['crowd_select1']) && $risk['crowd_select1'] === 'are') ? 'selected' : ''; ?>>
                            are</option>
                        <option value="arenot" <?php echo (isset($risk['crowd_select1']) && $risk['crowd_select1'] === 'arenot') ? 'selected' : ''; ?>>
                            are not</option>
                    </select> permitted. Spectators area is located at <input type="text" name="form[crowd_area]" value="<?php echo isset($risk['crowd_area']) ? $risk['crowd_area'] : ''; ?>" placeholder="Enter area" \n>. <br> The venue can accommodate <input type="text" name="form[crowd_number]" value="<?php echo isset($risk['crowd_number']) ? $risk['crowd_number'] : ''; ?>" placeholder="Enter number"> in the area which games are hosted. Teams
                    waiting to play <input type="text" name="form[crowd_procedure]" value="<?php echo isset($risk['crowd_procedure']) ? $risk['crowd_procedure'] : ''; ?>" placeholder="Enter procedure"> (example - must wait outside the sports hall
                    until the current game has completed, or can wait to the side of the court in
                    the run off area identified) <br> The venue <select style=" width: 14%; " name="form[crowd_select2]" required class="dropdown-select">

                        <option value="has" <?php echo (isset($risk['crowd_select2']) && $risk['crowd_select2'] === 'has') ? 'selected' : ''; ?>>
                            has</option>
                        <option value="hasnot" <?php echo (isset($risk['crowd_select2']) && $risk['crowd_select2'] === 'hasnot') ? 'selected' : ''; ?>>
                            has not</option>
                    </select> signage to guide attendees around the site.</td>
                <td><input type="text" id='crowd_likelihood' name="form[crowd_likelihood]" value="<?php echo isset($risk['crowd_likelihood']) ? $risk['crowd_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='crowd_severity' name="form[crowd_severity]" value="<?php echo isset($risk['crowd_severity']) ? $risk['crowd_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='crowd_risk_rating'><?php
                                            // Check if both values are set
                                            if (isset($risk['crowd_likelihood']) && isset($risk['crowd_likelihood'])) {
                                                // Multiply the values
                                                $result = (float)$risk['crowd_severity'] * (float)$risk['crowd_severity'];

                                                // Display the result
                                                echo $result;
                                            }
                                            ?></td>
            </tr>
            <!-- Crowd control	 -->


            <!--  start Medical treatment	 -->
            <tr>
                <td>Medical treatment</td>
                <td>Injury and illness could cause distress and or become serious with a lack of
                    treatment</td>
                <td>3</td>
                <td>5</td>
                <td>15</td>
                <td>Clubs, teams, captains must gain consent from parents/guardians to provide
                    medical treatment to those under 18. <br> Attendees should provide their own
                    treatment (inhalers, EpiPens, etc.). <br> A first aid provision is available at
                    all matches.</td>
                <td><input type="text" id='medical_treatment_likelihood' name="form[medical_treatment_likelihood]" value="<?php echo isset($risk['medical_treatment_likelihood']) ? $risk['medical_treatment_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='medical_treatment_severity' name="form[medical_treatment_severity]" value="<?php echo isset($risk['medical_treatment_severity']) ? $risk['medical_treatment_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='medical_treatment_risk_rating'><?php
                                                        // Check if both values are set
                                                        if (isset($risk['medical_treatment_likelihood']) && isset($risk['medical_treatment_likelihood'])) {
                                                            // Multiply the values
                                                            $result = (float)$risk['medical_treatment_severity'] * (float)$risk['medical_treatment_severity'];

                                                            // Display the result
                                                            echo $result;
                                                        }
                                                        ?></td>
            </tr>
            <!--  end Medical treatment	 -->

            <!--  start Welfare facilities -->
            <tr>
                <td>Welfare facilities</td>
                <td>A lack of welfare facilities may cause illness and/or congestion, leading to
                    accidents</td>
                <td>3</td>
                <td>3</td>
                <td>9</td>
                <td>
                    The venue has the following facilities thus ensuring that adequate welfare
                    facilities are available:
                    <br>
                    <input type="checkbox" name="form[welfare_toilets]" value="toilets" <?php echo (isset($risk['welfare_toilets']) && $risk['welfare_toilets'] == 'toilets') ? 'checked' : ''; ?>>
                    Toilets
                    <br>
                    <input type="checkbox" name="form[welfare_drinking_water]" value="drinking_water" <?php echo (isset($risk['welfare_drinking_water']) && $risk['welfare_drinking_water'] == 'drinking_water') ? 'checked' : ''; ?>>
                    Drinking water
                    <br>
                    <input type="checkbox" name="form[welfare_rest_eat]" value="rest_eat_places" <?php echo (isset($risk['welfare_rest_eat']) && $risk['welfare_rest_eat'] == 'rest_eat_places') ? 'checked' : ''; ?>>
                    Places to rest and eat
                    <br>
                    <input type="checkbox" name="form[welfare_medical_treatment]" value="medical_treatment" <?php echo (isset($risk['welfare_medical_treatment']) && $risk['welfare_medical_treatment'] == 'medical_treatment') ? 'checked' : ''; ?>>
                    Places to receive medical treatment
                </td>

                <td><input type="text" id='welfare_likelihood' name="form[welfare_likelihood]" value="<?php echo isset($risk['welfare_likelihood']) ? $risk['welfare_likelihood'] : ''; ?>" placeholder="Enter Likelihood 0-5"></td>
                <td><input type="text" id='welfare_severity' name="form[welfare_severity]" value="<?php echo isset($risk['welfare_severity']) ? $risk['welfare_severity'] : ''; ?>" placeholder="Enter Severity  0-5"></td>
                <td id='welfare_risk_rating'><?php
                                                // Check if both values are set
                                                if (isset($risk['welfare_likelihood']) && isset($risk['welfare_likelihood'])) {
                                                    // Multiply the values
                                                    $result = (float)$risk['welfare_severity'] * (float)$risk['welfare_severity'];

                                                    // Display the result
                                                    echo $result;
                                                }
                                                ?></td>
            </tr>
            <!--  end Welfare facilities -->

        </tbody>
    </table>
    <div class="button-container">
        <button id="save-button">
            <?php echo isset($risk['id']) ? 'Update' : 'Save'; ?>
        </button>
        <?php if (isset($risk['id'])) : ?>
            <button class="publish-button]" value="4" disabled style="background-color: red; color:white">Delete</button>
            <button class="publish-button]" value="2" disabled>Publish</button>
        <?php endif; ?>
    </div>


</div>
<script src="/js/riskoverallratingtoggle.js"></script>
<style>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 8px;
        text-align: left;
        border: 1px solid black;
    }



    .dropdown-select {
        width: 100%;
    }

    .button-container {
        text-align: center;
        margin-top: 20px;
    }

    td:nth-child(7),
    td:nth-child(3),
    td:nth-child(4),
    td:nth-child(8) {
        background-color: rgba(70, 175, 70, 0.36);
    }

    /* Set background color for Overall Risk Rating column */

    td:nth-child(5) {
        background-color: #ffc10787;
    }

    .button-container button {
        padding: 10px 20px;
        background-color: #28a745;
        color: white;
        border: none;
        cursor: pointer;
    }
</style>