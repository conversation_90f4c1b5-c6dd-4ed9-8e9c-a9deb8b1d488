<div class="container-fluid">
    <h2>Schedule Report</h2>
    <form action="" class="form-inline" method="post">
        <p class="my-0 mr-1"><?= count($data['schedule']) ?> scheduled fixtures between</p>
        <input name="startDate" type="date" class="form-control form-control-sm" value="<?= $_POST['startDate'] ?>">
        <p class="my-0 mr-1">and</p>
        <input name="endDate" type="date" class="form-control form-control-sm" value="<?= $_POST['endDate'] ?>">
        <button class="btn btn-sm btn-primary mx-1">Update</button>
    </form>
    <!-- <div class="filterOptions">
        <select id="venues" class="form-control">

        </select>
    </div> -->
    <table class="table">
        <thead>
            <tr>
                <th>Time</th>
                <th>Date</th>
                <th>Home</th>
                <th>Away</th>
                <th>Venue</th>
                <th>Pitch</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['schedule'] as $schedule) { ?>
            <tr>
                <td><?= ($schedule->startTime) ? substr($schedule->startTime,0,5):null ?></td>
                <td><?= ($schedule->getBooking()->startDate) ? date('d/m/Y',strtotime($schedule->getBooking()->startDate)) : null ?></td>
                <td class="team"><?= $schedule->getFixture()->getHome() ?></td>
                <td class="team"><?= $schedule->getFixture()->getAway() ?></td>
                <td class="venue" data-venueid="<?= $schedule->getBooking()->getVenue()->id ?>"><?= $schedule->getBooking()->getVenue() ?></td>
                <td><?= $schedule->getBooking()->pitchCourt ?></td>
            </tr>
<?php
        } ?>
        </tbody>
    </table>
</div>
<script>
    var 
    function filterVenue(e) {

    }

</script>