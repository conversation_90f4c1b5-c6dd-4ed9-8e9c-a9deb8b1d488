<div class="container-fluid">
    <h1>Fixture Report</h1>
    <form action="/User/Fixtures" method="post" class="d-flex flex-column flex-sm-row align-items-center justify-content-between">
        <label for="startDate">Starting</label>
        <input type="date" name="startDate" id="startDate" class="form-control" value="<?php echo $data['startDate'];?>" required>
        <label for="endDate">Ending</label>
        <input type="date" name="endDate" id="endDate" class="form-control" value="<?php echo $data['endDate'];?>" required>
        <input type="checkbox" name="unbilledOnly" value="1"<?= (isset($_POST['unbilledOnly']) && $_POST['unbilledOnly']==1) ? " checked" : null ?>>
        <button type="submit" class="btn btn-primary">Update</button>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th class="text-center">Home</th>
                <th class="text-center">HT</th>
                <th class="text-center">Card</th>
                <th class="text-center">Card</th>
                <th class="text-center">AT</th>
                <th class="text-center">Away</th>
                <th>Venue</th>
                <th>League</th>
                <th>Time</th>
                <th>Charge</th>
            </tr>
        </thead>
        <tbody><?php
        $total = $unbilled = 0;
        foreach ($data['report'] as $v) { 
            if (isset($_POST['unbilledOnly']) && $_POST['unbilledOnly']==1 && $v['homeTrans'] && $v['home'] && $v['awayTrans']) continue;
            ?>
            <tr data-fixtureid="<?= $v['fixture']->id ?>">
                <td class="text-center"><?php echo $v['home'];?></td>
                <td class="text-center"><?php echo $v['homeTrans'];?></td>
                <td class="text-center text-light <?php echo ($v['home']->paymentMethodStatus()) ? "bg-success " : "bg-danger";?>"><?php echo ($v['home']->paymentMethodStatus()) ? "&check;" : "&cross;";?></td>
                <td class="text-center text-light <?php echo ($v['away']->paymentMethodStatus()) ? "bg-success" : "bg-danger";?>"><?php echo ($v['away']->paymentMethodStatus()) ? "&check;" : "&cross;";?></td>
                <td class="text-center"><?php echo $v['awayTrans'];?></td>
                <td class="text-center"><?php echo $v['away'];?></td>
                <td><?php echo $v['venue'];?></td>
                <td><?php echo $v['league'];?></td>
                <td title="<?php echo $v['booking']->getStartDate('D jS F');?>"><?php echo $v['schedule']->getStartTime();?></td>
                <td class="text-right"><?php echo $v['charges'];?></td>
            </tr>
<?php
            $total += $v['charges'];
            if (!$v['homeTrans']) $unbilled += ($v['charges']/2);
            if (!$v['awayTrans']) $unbilled += ($v['charges']/2);
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="9">Total</th>
                <th><?php echo $total;?></th>
            </tr>
            <tr>
                <th colspan="9">Unbilled</th>
                <th><?php echo $unbilled;?></th>
            </tr>
        </tfoot>
    </table>
</div>