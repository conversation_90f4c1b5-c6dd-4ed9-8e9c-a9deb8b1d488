<div class="container-fluid">
    <h1>League Report</h1>
    <p>Count of teams in each league. Includes Live teams only.</p>
    <div class="container-fluid">
        <table class="table">
            <thead>
                <tr>
                    <th>League</th>
                    <th>Co-ordinator</th>
                    <th>Day</th>
                    <th>Season</th>
                    <th>Launch</th>
                    <th>Surface</th>
                    <!-- <th class="text-center">Fixtures</th> -->
                    <th class="text-center">Teams</th>
                    <th class="text-right">Profit</th>
                </tr>
            </thead>
            <tbody><?php
            $teamCount = 0;
            $surfaces = \League::Surfaces();
            $statuses = \League::Statuses();
            $days = \League::Days();
            $profit = 0;
            foreach ($data['leagueReport'] as $leagueReport) {
                $teamCount += $leagueReport['total'];
                $defaultSeasons = \Season::leagueDefaults(new \League($leagueReport['id']));
                if (!$defaultSeasons['live']) continue;
                $fixtureCount = \Schedule::seasonTotal($defaultSeasons['live']);
                $pandl = $defaultSeasons['live']->ProfitLoss();
                $profit += $pandl['profit'];
                $league = new League($leagueReport['id']);
                $coordinator = new User($league->coordinator);
                if (isset($defaultSeasons['live']) && $defaultSeasons['live']->startBookingID) {
                    $seasonStartBooking = new \Booking($defaultSeasons['live']->startBookingID);
                } else $seasonStartBooking = null;
                ?>
                <tr>
                    <td><a href="User/League/<?= $leagueReport['id']?>"><?php echo $leagueReport['name'];?></a></td>
                    <td><?=  $coordinator ?></td>
                    <td><?= $days[$leagueReport['defaultDay']] ?></td>
                    <td colspan="5">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="2" class="bg-success">Live</td>
                    <td><?= $defaultSeasons['live'] ?></td>
                    <td>&nbsp;</td>
                    <td><?php echo (isset($surfaces[$leagueReport['playingsurface']]['name'])) ? $surfaces[$leagueReport['playingsurface']]['name'] : null;?></td>
                    <?php
                    /*
                    <td class="text-center"><?php echo $fixtureCount;?></td>
                    */
                    ?>
                    <td class="text-center"><?php echo $leagueReport['total'];?></td>
                    <td class="text-right" title="<?php foreach ($pandl as $k=>$v) echo "$k: $v\n";?>"><?php echo $pandl['profit'];?></td>
                </tr><?php
                if (isset( $defaultSeasons['next'])) {?>
                <tr>
                    <td colspan="2" class="bg-warning">Next</td>
                    <td><?= $defaultSeasons['next'] ?></td>
                    <td><?= date('d/m/Y',strtotime($league->launchDate)) ?></td>
                    <td colspan="5">&nbsp;</td>
                </tr>
                <?php
                }
            } ?>
            </tbody>
            <tfoot>
                <tr>
                    <th colspan="4">Total</th>
                    <th class="text-center"><?php echo $teamCount;?></th>
                    <th class="text-right"><?php echo $profit;?></th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>