<!-- Add this in the head section of your HTML -->
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.5/xlsx.full.min.js"></script>
<script src="/js/export.js"></script>
<script src="/js/openregiexport.js"></script>
<link rel='stylesheet' type='text/css' href='/css/hub.leagues4you.css'>
<link rel="stylesheet" type="text/css" href="/css/hub.liveTeamReport.css">





<div class=" container-fluid">
    <div style=" text-align: center; color: brown; ">
        <h1>Live Team Report </h1>
    </div>
    <div >
        <form method="GET" class="container" style="font-size: 20px;">
            <div class="row">
                <div class="col-4">
                    <label for="coordinator">Coordinator:</label>
                    <select name="coordinator" id="coordinator">
                        <option value="">All</option>
                        <?php foreach ($coordinators as $coordinatorId => $coordinator) : ?>
                            <?php $selected = ($coordinator->id == $selectedCoordinator) ? 'selected' : ''; ?>
                            <option value="<?php echo $coordinator->id; ?>" <?php echo $selected; ?>><?php echo $coordinator; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <span class="slider-button-title">WildCard: </span>
                <label class="switch">
                    <input type="checkbox" <?php if(isset($_GET['wildcard']) && $_GET['wildcard'] == 1) echo 'checked';?> value="1" name="wildcard" id="wildcard">
                    <span class="slider round"></span>
                </label>
                <div class="col-1">
                    <button class="btn btn-primary" type="submit" >Apply</button>
                </div>
                <div class="col-2">
                    <button class="btn btn-secondary" id="exportExcel" >Export to Excel</button>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="container-fluid">
    <table class="table">
        <thead>
            <tr>
                <th>League</th>
                <th>Season Name</th>
                <th>Teams Registered</th>
                <th>Playing Teams</th>
                <th>Wildcard Teams</th>
                <th>Last Booking Date</th>
                <th>Next Season Start Date</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($leagues as $league_id => $league) : ?>
                <?php $firstSeason = true; ?>
                <?php foreach ($league['seasons'] as $season_id => $season) : ?>
                    <tr>
                        <?php if ($firstSeason) : ?>
                            <td rowspan="<?php echo count($league['seasons']); ?>">
                                <?php echo htmlspecialchars($league['name']); ?>
                            </td>
                        <?php endif; ?>
                        <td><?php echo isset($season['season_name']) ? htmlspecialchars($season['season_name']) : ''; ?></td>
                        <td><?php echo isset($season['teams_registered']) ? htmlspecialchars($season['teams_registered']) : ''; ?></td>
                        <td><?php echo isset($season['playing_teams']) ? htmlspecialchars($season['playing_teams']) : 'N/A'; ?></td>
                        <td><?php echo isset($season['wildcard_teams']) ? htmlspecialchars($season['wildcard_teams']) : ''; ?></td>
                        <td><?php echo $season['final_fixture_date'] ? date('d/m/Y', strtotime($season['final_fixture_date'])) : 'none'; ?></td>
                        <td><?php echo $season['next_season_start_date'] ? date('d/m/Y', strtotime($season['next_season_start_date'])) : 'none'; ?></td>
                    </tr>
                    <?php $firstSeason = false; ?>
                <?php endforeach; ?>
            <?php endforeach; ?>
            <?php if (empty($leagues)) : ?>
                <tr>
                    <td colspan="6">No data available</td>
                </tr>
            <?php endif; ?>
        </tbody>
        <tfoot>
            <tr>
                <th>Total</th>
                <th></th>
                <th><?php echo htmlspecialchars($totalRegisteredTeams); ?></th>
                <th><?php echo htmlspecialchars($totalPlayingTeams); ?></th>
                <th><?php echo htmlspecialchars($totalWildcardTeams); ?></th>
                <th></th>
                <th></th>
            </tr>
        </tfoot>
    </table>
</div>