<?php $isTournament = $data['tasterSession']->is_tournament; ?>
<div id="spinner" class="spinner d-none" style="display:flex; align-items: center; justify-content: center; position: absolute; top:0; left:0; right:0; bottom:0;">
    <div class="spinner-border" style="width: 3rem; height: 3rem;" role="status">
    </div>
</div>
<link rel="stylesheet" type="text/css" href="/css/hub.liveTeamReport.css">
<div class="container-fluid">
    <h1>Taster Session | <a href="/User/TasterSessions" class="btn btn-sm btn-info">&lt;&minus; All Sessions</a></h1>
    <?php
    $currentVenue = array_filter($data['venues'], function ($venue) use ($data) {
        return $venue->id == $data['tasterSession']->venueID;
    });
    $currentVenue = reset($currentVenue);
    ?>
    <ul class="nav nav-tabs mt-4" id="myTab" role="tablist">
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab'] == "main") echo ' active'; ?>" id="main-tab" data-toggle="tab" href="#main" role="tab" aria-controls="main" aria-selected="<?php echo ($data['tab'] == "main") ? 'true' : 'false'; ?>">Main</a>
        </li><?php
                if ($data['tasterSession']->id) { ?>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "attendees") echo ' active'; ?>" id="attendees-tab" data-toggle="tab" href="#attendees" role="tab" aria-controls="attendees" aria-selected="<?php echo ($data['tab'] == "attendees") ? 'true' : 'false'; ?>">Attendees</a>
            </li><?php
                } ?>
    </ul>

    <div class="tab-content mt-4" id="myTabContent">

        <div class="tab-pane fade<?php if ($data['tab'] == "main") echo ' show active'; ?>" id="main" role="tabpanel" aria-labelledby="main">
            <h3 class="d-flex align-items-center m-0">
                <span>Main |</span>
                <form class="d-flex align-items-center">
                    <button type="button" class="btn btn-sm btn-warning mx-1" id="cancelSessionBtn" data-tasterid="<?= $data['tasterSession']->id ?>" <?= ($data['tasterSession']->cancelled) ? " disabled" : null ?>>Cancel</button>
                    <button type="button" class="btn btn-sm btn-danger" id="refundSessionBtn" data-tasterid="<?= $data['tasterSession']->id ?>" <?= ($data['tasterSession']->refunded) ? " disabled" : null ?>>Refund</button>
                </form>
            </h3>
            <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                <input type="hidden" name="taster[id]" value="<?= $data['tasterSession']->id ?>">

                <label for="taster.venueID">Venue</label>
                <select name="taster[venueID]" id="taster.venueID" class="form-control" required>
                    <option value="">Venue</option><?php
                                                    foreach ($data['venues'] as $venue) { ?>
                        <option value="<?= $venue->id ?>" <?= ($venue->id == $data['tasterSession']->venueID) ? " selected" : null ?>><?= $venue ?></option><?php
                                                                                                                                                        } ?>
                </select>

                <label for="taster.sportID">Sport</label>
                <select name="taster[sportID]" id="taster.sportID" class="form-control" required>
                    <option value="">Sport</option><?php
                                                    foreach ($data['sports'] as $sport) { ?>
                        <option value="<?= $sport->id ?>" <?= ($sport->id == $data['tasterSession']->sportID) ? " selected" : null ?>><?= $sport ?></option><?php
                                                                                                                                                        } ?>
                </select>

                <label for="taster.date">Date</label>
                <input type="date" name="taster[date]" id="taster.date" class="form-control" value="<?= $data['tasterSession']->date ?>" required>

                <label for="taster.time">Time</label>
                <input type="time" name="taster[time]" id="taster.town" class="form-control" value="<?= $data['tasterSession']->time ?>" required>

                <label for="taster.charge">Charge</label>
                <input type="text" name="taster[charge]" id="taster.charge" class="form-control" value="<?= $data['tasterSession']->charge ?>" required>

                <label for="taster.coordinatorID">Coordinator</label>
                <select name="taster[coordinatorID]" id="taster.coordinatorID" class="form-control" required onchange="handleCoordinatorChange()">
                    <option value="">Coordinator</option>
                    <?php foreach ($data['coordinators'] as $coordinator) { ?>
                        <option data-email="<?= $coordinator->email ?>"
                            data-tel="<?= $coordinator->mobile ?>"
                            value="<?= $coordinator->id ?>"
                            <?= ($coordinator->id == $data['tasterSession']->coordinatorID) ? ' selected' : null ?>>
                            <?= $coordinator ?>
                        </option>
                    <?php } ?>
                </select>

                <input type="hidden" name="taster[coordinatorEmail]" id="taster.coordinatorEmail" value="<?= $data['tasterSession']->coordinatorEmail ?>">
                <input type="hidden" name="taster[coordinatorName]" id="taster.coordinatorName" value="<?= $data['tasterSession']->coordinatorName ?>">
                <input type="hidden" name="taster[CoordinatorTel]" id="taster.CoordinatorTel" value="<?= $data['tasterSession']->coordinatorTel ?>">
                <input type="hidden" name="taster[slug]" id="taster.coordinatorSlug" value="<?= $data['tasterSession']->slug ?>">
                <?php if ($currentVenue->name != 'WILDCARD') { ?>
                    <label for="taster.fbPageLink">Facebook Page Link</label>
                    <input type="text" name="taster[fbPageLink]" id="taster.fbPageLink" class="form-control" value="<?= $data['tasterSession']->fbPageLink ?>">

                    <label for="taster.videoLink">Youtube Link</label>
                    <input type="text" name="taster[videoLink]" id="taster.videoLink" class="form-control " value="<?= $data['tasterSession']->videoLink ?>">
                <?php } ?>

                <label for="taster.notes">Welcome Notes</label>
                <small>This text will be included on confirmation emails.</small>
                <textarea name="taster[notes]" id="taster.notes" class="form-control" rows="5"><?= $data['tasterSession']->notes ?></textarea>

                <label for="taster.cancelNotes">Cancel Notes</label>
                <small>This text will be included on cancellation emails.</small>
                <textarea name="taster[cancelNotes]" id="taster.cancelNotes" class="form-control" rows="5"><?= $data['tasterSession']->cancelNotes ?></textarea>

                <div class="d-flex align-items-center pt-2">
                    <span class="slider-button-title">WildCard: </span>
                    <label class="switch">
                        <input type="checkbox" value="1" name="taster[is_wildcard]" id="is_wildcard" <?= ($data['tasterSession']->is_wildcard) ? ' checked' : null ?>>
                        <span class="slider round"></span>
                    </label>
                </div>
                <div class="d-flex align-items-center pt-2">
                    <span class="slider-button-title">Tournament: </span>
                    <label class="switch">
                        <input type="checkbox" value="1" name="taster[is_tournament]" id="is_tournament" <?= ($isTournament) ? ' checked' : null ?>>
                        <span class="slider round"></span>
                    </label>
                </div>
                <div class="d-flex align-items-center pt-2">
                    <span class="slider-button-title">Fully Booked: </span>
                    <label class="switch">
                        <input type="checkbox" value="1" name="taster[fullyBooked]" id="taster.fullyBooked" <?= ($data['tasterSession']->fullyBooked) ? ' checked' : null ?>>
                        <span class="slider round"></span>
                    </label>
                </div>

                <div id="tournamentFields">
                    <label for="taster.tournamentName">Tournament Name</label>
                    <input type="text" name="taster[tournamentName]" id="taster.tournamentName" class="form-control" value="<?= $data['tasterSession']->tournamentName ?>">

                    <label for="taster.numberOfWeeks" class="d-none">Number of Weeks</label>
                    <input type="number" name="taster[numberOfWeeks]" id="taster.numberOfWeeks" class="form-control d-none" value="<?= $data['tasterSession']->numberOfWeeks ?>" min="1" max="10">
                </div>

                <button type="submit" class="btn btn-primary mt-4">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "attendees") echo ' show active'; ?>" id="attendees" role="tabpanel" aria-labelledby="attendees">
            <h3 id="heading-title"> loading...
                <?php
                /* if (!$data['tasterSession']->cancelled) { ?><button id="cancelSessionBtn" class="btn btn-sm btn-warning">Cancel</button><?php }?><?php if ($data['tasterSession']->cancelled && !$data['tasterSession']->refunded) { ?><button id="refundSessionBtn" class="btn btn-sm btn-danger">Refund</button><?php }
            */ ?></h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Mobile</th>
                        <?php if ($isTournament) { ?>
                            <th>Team Name</th>
                        <?php } ?>
                        <th>Disc. Code</th>
                        <th>Paid</th>
                        <th>When</th>
                        <th class="text-center">...</th>
                    </tr>
                </thead>
                <tbody><?php
                        foreach ($data['attendees'] as $c2c_booking) {
                            $c2c_booking->getUser();
                            $totalAttendees = count($c2c_booking->attendees);
                            $showTotalAttendees = $totalAttendees ? " <span class='badge badge-primary' title='Total Attendees'>$totalAttendees</span>" : null;
                            $totalAttendesWithMain = $totalAttendees + 1;
                            if (!$c2c_booking->user) continue;

                            // Get team name from data field if exists
                            $teamName = '';
                            if ($c2c_booking->data) {
                                $colData = json_decode($c2c_booking->data, true);
                                $teamName = isset($colData['teamName']) ? $colData['teamName'] : '';
                            }
                        ?>
                        <tr title="Booking ID <?= $c2c_booking->id ?>">
                            <input type="hidden" id="totalAttendees_<?= $c2c_booking->stripePaymentIntentID ?>" value="<?= $totalAttendesWithMain ?>">
                            <td title="User ID <?= $c2c_booking->user->id ?>"><?= $c2c_booking->user->name() ?><?= $showTotalAttendees; ?></td>
                            <td><?= $c2c_booking->user->email ?></td>
                            <td><?= $c2c_booking->user->mobile ?></td>
                            <?php if ($isTournament) { ?>
                                <td><?= $teamName ?></td>
                            <?php } ?>
                            <td><?= $c2c_booking->discountCode ?></td>
                            <td class="stripePaymentIntentID" id="stripePaymentIntentID_<?= $c2c_booking->stripePaymentIntentID ?>" data-stripepaymentintentid="<?= $c2c_booking->stripePaymentIntentID ?>"><?= $c2c_booking->stripePaymentIntentID ?></td>
                            <td><?= date('H:ia d/m/Y', strtotime($c2c_booking->created)) ?></td>
                            <td class="text-center" title="Refund">
                                <form action="" method="post">
                                    <button type="submit" name="refundC2c" value="<?= $c2c_booking->id ?>" id="refund_<?= $c2c_booking->stripePaymentIntentID ?>" class="btn btn-sm btn-danger" disabled><i class="far fa-credit-card"></i></button>
                                </form>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>

    </div>
</div>
<script>
    // Define the named function
    function handleCoordinatorChange() {
        // Get references to select and input elements
        var selectCoordinator = document.getElementById('taster.coordinatorID');
        var inputCoordinatorName = document.getElementById('taster.coordinatorName');
        var inputCoordinatorEmail = document.getElementById('taster.coordinatorEmail');
        var inputCoordinatorTel = document.getElementById('taster.CoordinatorTel');

        // Check if the elements are found
        if (!selectCoordinator || !inputCoordinatorName || !inputCoordinatorEmail || !inputCoordinatorTel) {
            console.error('One or more elements not found');
            return;
        }

        // Get the selected option
        var selectedOption = selectCoordinator.options[selectCoordinator.selectedIndex];

        // Check if selectedOption is null
        if (!selectedOption) {
            console.error('Selected option is null');
            return;
        }

        // Get data attributes from the selected option
        var email = selectedOption.getAttribute('data-email');
        var tel = selectedOption.getAttribute('data-tel');
        var coordinatorName = selectedOption.textContent;

        // Set values of input fields
        inputCoordinatorName.value = coordinatorName.trim();
        inputCoordinatorEmail.value = email;
        inputCoordinatorTel.value = tel;
    }

    // Add the event listener to the select element
    document.addEventListener('DOMContentLoaded', function() {
        var selectCoordinator = document.getElementById('taster.coordinators');
        if (selectCoordinator) {
            selectCoordinator.addEventListener('change', handleCoordinatorChange);
        }
    });

    function readCookie(cname) {
        let name = cname + "=";
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                var rlt = c.substring(name.length, c.length);
                return rlt;
            }
        }
    }

    var jwt = readCookie('jwt');
    var spinner = document.getElementById("spinner");

    function CancelSession(e) {
        if (confirm("Are you sure you want to cancel <?= $data['tasterSession'] ?> ?") === true) {
            var url = "https://admin.v2.api.leagues4you.co.uk/taster-session-cancel/" + e.target.dataset.tasterid;
            spinner.classList.remove("d-none");
            fetch(url, {
                    headers: new Headers({
                        'method': 'get',
                        'authorization': 'Bearer ' + jwt,
                    }),
                })
                .then(response => response.json())
                .then(json => console.log(json))
                .then(() => spinner.classList.add("d-none"));
        }
    }

    function RefundSession(e) {
        if (confirm("Are you sure you want to refund <?= $data['tasterSession'] ?> ?\nThis CANNOT be reversed.") === true) {
            var url = "https://admin.v2.api.leagues4you.co.uk/taster-session-refund/" + e.target.dataset.tasterid;
            spinner.classList.remove("d-none");
            fetch(url, {
                    headers: new Headers({
                        'method': 'get',
                        'authorization': 'Bearer ' + jwt,
                    }),
                })
                .then(response => response.json())
                .then(json => console.log(json))
                .then(() => spinner.classList.add("d-none"));
        }
    }

    function CheckPaymentIntentStatuses() {
        let url = "https://admin.v2.api.leagues4you.co.uk/lastest-pi-statuses";
        let jwt = readCookie('jwt');
        let statuses = [];
        let stripePaymentIntentIdElements = document.querySelectorAll('.stripePaymentIntentID');
        for (let s in stripePaymentIntentIdElements) {
            if (stripePaymentIntentIdElements[s].dataset && stripePaymentIntentIdElements[s].dataset.stripepaymentintentid) statuses.push(stripePaymentIntentIdElements[s].dataset.stripepaymentintentid);
        }
        // console.log(url,jwt,statuses);
        fetch(
                url, {
                    method: 'POST', // *GET, POST, PUT, DELETE, etc.
                    headers: new Headers({
                        'authorization': 'Bearer ' + jwt,
                        'Content-Type': 'application/json'
                    }),
                    body: JSON.stringify(statuses)
                }
            )
            .then(response => response.json())
            .then(data => {
                let totalIncomplete = 0;
                let totalConfirmed = 0;
                let totalRefunded = 0;

                if (data['error']) {
                    console.error(data['error'])
                } else {
                    // console.log(data['success']);
                    for (let d in data['success']) {
                        const stripePaymentId = data['success'][d].stripePaymentIntentID;

                        let stripeElement = document.getElementById("stripePaymentIntentID_" + stripePaymentId);
                        const countAttendees = parseInt(document.getElementById("totalAttendees_" + stripePaymentId).value);
                        if (stripeElement) {
                            // console.log(data['success'][d]);
                            const getStatus = data['success'][d].internal_status;

                            stripeElement.innerHTML = getStatus;
                            if (data['success'][d].refundable == true) {
                                document.getElementById("refund_" + stripePaymentId).disabled = false;
                            }
                            if (getStatus == 'Incomplete') totalIncomplete += countAttendees;
                            if (getStatus == 'Refunded') totalRefunded += countAttendees;
                            if (getStatus.includes('Charged')) totalConfirmed += countAttendees;
                        }
                    }
                    // Set incomplete total
                    document.getElementById('heading-title').innerHTML = `${totalConfirmed} Confirmed (including guests) | ${totalIncomplete} Incomplete | ${totalRefunded} Refunded`;
                }
            });
    }

    var cancelBtn = document.getElementById("cancelSessionBtn");
    var refundBtn = document.getElementById("refundSessionBtn");
    if (cancelBtn) cancelBtn.addEventListener("click", CancelSession);
    if (refundBtn) refundBtn.addEventListener("click", RefundSession);
    CheckPaymentIntentStatuses();

    // Add tournament switch toggle handler
    document.addEventListener('DOMContentLoaded', function() {
        var tournamentSwitch = document.getElementById('is_tournament');
        var tournamentFields = document.getElementById('tournamentFields');

        if (tournamentSwitch && tournamentFields) {
            // Initial state
            tournamentFields.style.display = tournamentSwitch.checked ? 'block' : 'none';

            // Add change event listener
            tournamentSwitch.addEventListener('change', function() {
                tournamentFields.style.display = this.checked ? 'block' : 'none';
            });
        }
    });
</script>