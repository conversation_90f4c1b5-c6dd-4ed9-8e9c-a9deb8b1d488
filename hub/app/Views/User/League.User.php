<?php
if (isset($data['league']) && $data['league']->id) {?>
<!-- User Modal -->
<div class="modal" id="userModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">User</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="followers">
            <input type="hidden" name="user[id]" id="user.id" value="">
            <input type="hidden" name="user[leagueID]" id="user.leagueid" value="<?php echo $data['league']->id;?>">

            <label for="user.firstname">First Name</label>
            <input class="form-control" type="text" id="user.firstname" name="user[firstname]" value="<?php if (isset($data['user'])) echo $data['user']->getFirstname();?>">

            <label for="user.lastname">Last Name</label>
            <input class="form-control" type="text" id="user.lastname" name="user[lastname]" value="<?php if (isset($data['user'])) echo $data['user']->getLastname();?>">

            <label for="user.email">Email</label>
            <input class="form-control" type="email" id="user.email" name="user[email]" value="<?php if (isset($data['user'])) echo $data['user']->getEmail();?>">

            <label for="user.mobile">Mobile</label>
            <input class="form-control" type="tel" id="user.mobile" name="user[mobile]" value="<?php if (isset($data['user'])) $data['user']->getMobile();?>">

            <button type="submit" class="btn btn-success mt-2">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Season Modal -->
<div class="modal" id="seasonModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">New Season</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="season">
            <input type="hidden" id="season.leagueID" name="season[leagueID]" value="<?php echo $data['league']->getID();?>">
            <label for="editseason">Name</label>
            <input class="form-control" type="text" id="editseason.name" name="season[name]">
            <button type="submit" class="btn btn-success mt-2">Add Season</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Booking Modal -->
<div class="modal" id="bookingModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Booking</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="bookings">
            <input type="hidden" name="booking[id]" id="booking.id" value="">
            <input type="hidden" name="booking[leagueID]" id="booking.leagueid" value="<?php echo $data['league']->getID();?>">
              <label for="booking.venueid">Venue</label>
              <select class="form-control" name="booking[venueID]" id="booking.venueid">
                <option value>...</option><?php
                if (isset($data['venues'])) {
                    foreach ($data['venues'] as $venue) {
                    ?>
                    <option value="<?php echo $venue->getID();?>"><?php echo $venue;?></option><?php
                    } 
                } ?>
              </select>
              <label for="booking.pitchcourt">Pitch Court</label>
              <input
                type="text"
                name="booking[pitchCourt]"
                id="booking.pitchcourt"
                class="form-control"
              />
              <label for="booking.starttime">Start Time</label>
              <input
                type="time"
                name="booking[startTime]"
                id="booking.starttime"
                class="form-control"
              />
              <label for="booking.startdate">Start Date</label>
              <input
                type="date"
                name="booking[startDate]"
                id="booking.startdate"
                class="form-control"
              />
            <label for="booking.weeks">Length of Booking</label>
            <input
                type="number"
                name="booking[weeks]"
                id="booking.weeks"
                class="form-control"
                placeholder="in Weeks"
                min="2"
                max="52"
            />
              <label for="booking.duration">Duration</label>
              <input
                type="number"
                name="booking[duration]"
                id="booking.duration"
                class="form-control"
                placeholder="in Minutes"
              />
              <label for="booking.hourlyrate">Hourly Rate</label>
              <input
                type="text"
                name="booking[hourlyRate]"
                id="booking.hourlyrate"
                class="form-control"
              />
              <label for="booking.creditvalue">Credit</label>
              <input
                type="text"
                name="booking[creditValue]"
                id="booking.creditvalue"
                class="form-control"
              />
            <button type="submit" class="btn btn-success mt-2">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<?php
if (isset($data['season']) && $data['season']->id) {?>
<!-- Switch Booking Modal -->
<div class="modal" id="switchModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Switch</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p id="scheduleWording"></p>
        <form action="/User/League" method="post">
            <input name="seasonID" type="hidden" value="<?php echo $data['season']->id;?>">
            <input name="tab" type="hidden" value="schedule">
            <input name="schedule[old]" id="schedule.old" type="hidden">
            <select name="schedule[new]" id="schedule.new" class="form-control"></select>
            <button type="submit" class="btn btn-success mt-2">Switch Now</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Team Modal -->
<div class="modal" id="teamModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Team</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="clearTeamData()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="teams">
            <input type="hidden" name="seasonID" value="<?= $data['season']->id ?>">
            <input type="hidden" name="amendteam[id]" id="team.id" value="">

            <label for="team.name">Name</label>
            <input class="form-control" type="text" id="team.name" name="amendteam[name]" required>

            <button type="submit" class="btn btn-success mt-2">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Team Season - Division -->
<div class="modal" id="teamseasonDivisionModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Team Division</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="clearTeamSeasonDivisionData()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="teams">
            <input type="hidden" name="changeTeamDivision[id]" id="changeTeamDivision.id" value="">
            <label for="changeTeamDivision.divisionID">Division</label>
            <select class="form-control" name="changeTeamDivision[divisionID]" required>
                <option value>...</option><?php
                if (isset($data['divisions'])) {
                    foreach ($data['divisions'] as $division) {
                    ?>
                <option value="<?= $division->id;?>"><?= $division ?></option><?php
                    } 
                } ?>
            </select>
            <button type="submit" class="btn btn-success mt-2" name="action" value="changeTeamDivision">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- TeamSeason People -->
<div class="modal" id="teamSeasonCaptainModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="teamSeasonPeople.teamname"></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="teams">
            <input  type="hidden" name="teamSeasonPeople[teamSeasonID]" id="teamSeasonPeople.teamSeasonID" value="">

            <label for="teamSeasonPeople.captainID">Captain</label>
            <div class="form-group">
              <input type="text" class="form-control" id="teamSeasonPeople-captainID-search" placeholder="Search for a user">
              <select class="form-control" name="teamSeasonPeople[captainID]" id="teamSeasonPeople.captainID">
                <option value="">Loading...</option>
              </select>
            </div>

            <button type="submit" class="btn btn-success mt-2">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Division Modal -->
<div class="modal" id="divisionModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Division</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form action="" method="post">
            <input type="hidden" name="tab" value="divisions">
            <input  type="hidden" name="division[seasonID]" value="<?php echo $data['season']->getID();?>">
            <input  type="hidden" name="division[id]" id="division.id" value="">
            <label for="division.name">Name</label>
            <input class="form-control" type="text" id="division.name" name="division[name]" value="">
            <button type="submit" class="btn btn-success mt-2">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<!-- Schedule Modal -->
<div class="modal" id="scheduleModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Schedule</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <h5 id="fixtureName"></h5>
        <h6 id="fixtureTiming"></h6>
        <form action="" method="post">
            <input type="hidden" name="scheduling[id]" id="scheduling.id">
            <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
            <input type="hidden" name="tab" value="schedule">
            <hr>
            <label for="scheduling.bookingID">Change To</label>
            <select name="scheduling[bookingID]" id="scheduling.bookingID" class="form-control" data-fixtureid="" onchange="BookingSlots(event)"></select>
            <select name="scheduling[offset]" id="scheduling.offset" class="form-control"></select>
            <label for="scheduling.reason">Reason for Reschedule</label>
            <select name="scheduling[reason]" id="scheduling.reason" class="form-control">
                <option value="">Select Reason</option><?php
                foreach ($data['rescheduleReasons'] as $reason) { ?>
                <option value="<?= $reason['id'] ?>"><?= $reason['name'] ?></option><?php
                } ?>
            </select>
            <button type="submit" class="btn btn-primary" style="margin-top: 1rem;">Save</button>
        </form>
      </div>
    </div>
  </div>
</div>
<?php
}
} ?>
<div class="container-fluid">
    <h2>League Management</h2>
    <div class="row">
        <div class="col-xs-12 col-sm-6">
            <form action="/User/League" method="post">
                <select name="leagueID" id="leagueID" class="form-control" onchange="form.submit()">
                    <option value="">League</option><?php
                    if (isset($data['leagues'])) {
                        foreach ($data['leagues'] as $league) { ?>
                            <option value="<?php echo $league->getID();?>"<?php if (isset($data['league']) && $league->getID()==$data['league']->getID()) echo ' selected';?>><?php echo $league->getName();?></option><?php
                        }
                    }?>
                </select>
                <!-- <button type="button" class="btn btn-primary mx-2" data-toggle="modal" data-target="#newLeagueModal">New League</button> -->
            </form>
        </div><?php
        if (isset($data['league']) && $data['league']->getID()) {?>
        <div class="col-xs-12 col-sm-6">
            <form action="" method="post">
            <?php
                if (isset($data['seasons']) && $data['seasons']) {?>
                <select name="seasonID" id="" class="form-control" onchange="form.submit()">
                    <option value="">Season</option><?php
                    if (isset($data['seasons'])) {
                        foreach ($data['seasons'] as $season) { ?>
                            <option value="<?php echo $season->getID();?>"<?php if (isset($data['season']) && $season->getID()==$data['season']->getID()) echo ' selected';?>><?php echo $season->getName();?></option><?php
                        }
                    }?>
                </select><?php
                } ?>
            </form>
        </div><?php
        } ?>
    </div>
</div>
<div class="container-fluid">
    <!-- Nav Tabs -->
    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="main") echo ' active';?> " id="main-tab" data-toggle="tab" href="#main" role="tab" aria-controls="main" aria-selected="<?php echo ($data['tab']=="main") ? 'true': 'false';?>">Main</a>
        </li><?php        
        if (isset($data['season']) && $data['season']->getID()) {?>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?= ($data['tab']=="notes")?' active':null?>" id="notes-tab" data-toggle="tab" href="#notes" role="tab" aria-controls="notes" aria-selected="<?=($data['tab']=="website")?'true':'false'?>">Notes</a>
        </li><?php
        /*
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="website") echo ' active';?> " id="website-tab" data-toggle="tab" href="#website" role="tab" aria-controls="website" aria-selected="<?php echo ($data['tab']=="website") ? 'true': 'false';?>">Website</a>
        </li>
        */ ?>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="seasons") echo ' active';?> " id="seasons-tab" data-toggle="tab" href="#seasons" role="tab" aria-controls="seasons" aria-selected="<?php echo ($data['tab']=="seasons") ? 'true': 'false';?>">Season</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="bookings") echo ' active';?> " id="bookings-tab" data-toggle="tab" href="#bookings" role="tab" aria-controls="bookings" aria-selected="<?php echo ($data['tab']=="bookings") ? 'true': 'false';?>">Bookings <?php if (isset($data['bookings'])) echo '<span class="badge badge-success">'.count($data['bookings']).'</span>';?></a>
        </li>
        <?php
        /*
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="teams") echo ' active';?> " id="teams-tab" data-toggle="tab" href="#teams" role="tab" aria-controls="teams" aria-selected="<?php echo ($data['tab']=="teams") ? 'true': 'false';?>">Teams <?php if (isset($data['teams'])) echo '<span class="badge badge-success">'.count($data['teams']).'</span>';?></a>
        </li>
        */
        ?>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="teams2") echo ' active';?> " id="teams2-tab" data-toggle="tab" href="#teams2" role="tab" aria-controls="teams2" aria-selected="<?= ($data['tab']=="teams2") ? 'true': 'false' ?>">Teams <?= (isset($data['seasonsTeams'])) ? '<span class="badge badge-success">'.count($data['seasonsTeams']).'</span>' : null ?></a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="divisions") echo ' active';?> " id="divisions-tab" data-toggle="tab" href="#divisions" role="tab" aria-controls="divisions" aria-selected="<?php echo ($data['tab']=="divisions") ? 'true': 'false';?>">Divisions <?php if (isset($data['divisions'])) echo '<span class="badge badge-success">'.count($data['divisions']).'</span>';?></a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="fixtures") echo ' active';?> " id="fixtures-tab" data-toggle="tab" href="#fixtures" role="tab" aria-controls="fixtures" aria-selected="<?php echo ($data['tab']=="fixtures") ? 'true': 'false';?>">Fixtures <?php if (isset($data['fixtures'])) echo '<span class="badge badge-success">'.count($data['fixtures']).'</span>';?></a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="schedule") echo ' active';?> " id="schedule-tab" data-toggle="tab" href="#schedule" role="tab" aria-controls="schedule" aria-selected="<?php echo ($data['tab']=="schedule") ? 'true': 'false';?>">Schedule <?php if (isset($data['schedule'])) echo '<span class="badge badge-success">'.count($data['schedule']).'</span>';?></a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="results") echo ' active';?> " id="results-tab" data-toggle="tab" href="#results" role="tab" aria-controls="results" aria-selected="<?php echo ($data['tab']=="results") ? 'true': 'false';?>">Results</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="tables") echo ' active';?> " id="tables-tab" data-toggle="tab" href="#tables" role="tab" aria-controls="tables" aria-selected="<?php echo ($data['tab']=="tables") ? 'true': 'false';?>">Tables</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="statements") echo ' active';?> " id="statements-tab" data-toggle="tab" href="#statements" role="tab" aria-controls="statements" aria-selected="<?php echo ($data['tab']=="statements") ? 'true': 'false';?>">Statements</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="waitinglist") echo ' active';?> " id="waitinglist-tab" data-toggle="tab" href="#waitinglist" role="tab" aria-controls="waitinglist" aria-selected="<?php echo ($data['tab']=="waitinglist") ? 'true': 'false';?>">Waiting List <?= ($data['waitingList']) ? '<span class="badge badge-success">'.count($data['waitingList']).'</span>' : null ?></a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link<?php if ($data['tab']=="leaderboard") echo ' active';?> " id="leaderboard-tab" data-toggle="tab" href="#leaderboard" role="tab" aria-controls="leaderboard" aria-selected="<?php echo ($data['tab']=="leaderboard") ? 'true': 'false';?>">Leader Board <?= ($data['leaderboard']) ? '<span class="badge badge-success">'.count($data['leaderboard']).'</span>' : null ?></a>
        </li>
        <?php
        } ?>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content mt-2" id="myTabContent">

        <div class="tab-pane fade<?php if ($data['tab']=="main") echo ' show active';?>" id="main" role="tabpanel" aria-labelledby="main-tab">
            <h3>Main</h3>
            <form action="" method="post">
                <input type="hidden" name="league[id]" value="<?php if (isset($data['league'])) echo $data['league']->getID();?>">

                <label for="league.name">League Name</label>
                <input name="league[name]" id="league.name" type="text" placeholder="League Name" required="required" aria-required="true" class="form-control form-control-sm" value="<?php if (isset($data['league'])) echo \Tools::HtmlEscape($data['league']->getName());?>">

                <label for="league.regionID" class="d-block">Region</label>
                <select name="league[regionID]" id="league.regionID" required="required" aria-required="true" class="custom-select custom-select-sm">
                    <option value="">...</option><?php
                    if (isset($data['regions'])) {
                        foreach ($data['regions'] as $region) { ?>
                            <option value="<?php echo $region->getID();?>"<?php if (isset($data['league']) && $region->getID()==$data['league']->getRegionID()) echo ' selected';?>><?php echo $region->getName();?></option><?php
                        }
                    } ?>
                </select>

                <label for="league.sportID" class="d-block">Sport:</label>
                <select name="league[sportID]" id="league.sportID" required="required" aria-required="true" class="custom-select custom-select-sm">
                    <option value="">...</option><?php
                    if (isset($data['sports'])) {
                        foreach ($data['sports'] as $sport) { ?>
                            <option value="<?php echo $sport->getID();?>"<?php if (isset($data['league']) && $sport->getID()==$data['league']->getSportID()) echo ' selected';?>><?php echo $sport->getName();?></option><?php
                        }
                    } ?>
                </select>

                <label for="league.coordinator" class="d-block">Coordinator:</label>
                <select name="league[coordinator]" id="league.coordinator" required="required" aria-required="true" class="custom-select custom-select-sm">
                    <option value="">...</option><?php
                    if (isset($data['coordinators'])) {
                        foreach ($data['coordinators'] as $user) { ?>
                            <option value="<?php echo $user->id;?>"<?php if (isset($data['league']) && $user->id==$data['league']->getCoordinatorID()) echo ' selected';?>><?php echo $user->__toString();?></option><?php
                        }
                    } ?>
                </select>

                <label for="league.playerRequestGroup">Player Request Group</label>
                <input name="league[playerRequestGroup]" id="league.playerRequestGroup" type="text" placeholder="Player Request Group" class="form-control form-control-sm" value="<?= \Tools::HtmlEscape($data['league']->playerRequestGroup ?? '') ?>">

                <button type="submit" class="btn btn-primary" style="margin-top: 1rem;">Save</button>
            </form>
        </div>
        
        <div class="tab-pane fade<?= ($data['tab']=="notes")?' show active':null?>" id="notes" role="tabpanel" aria-labelledby="notes-tab">
            <h3>Notes</h3>
            <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                <label for="leagueNotes.notes">Note</label>
                <textarea name="leagueNotes[notes]" id="leagueNotes.notes" cols="30" rows="10" class="form-control"><?= $data['league']->notes ?></textarea>
                <button type="submit" name="leagueNotes[id]" value="<?= $data['league']->id ?>" class="btn btn-primary mt-2">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="website") echo ' show active';?>" id="website" role="tabpanel" aria-labelledby="website-tab">
            <h3>Website Settings</h3>
            <form action="/User/League" method="post">
                <input type="hidden" name="website[id]" value="<?php if (isset($data['league'])) echo $data['league']->getID();?>">
                
                <?php
                /*
                <label for="website.defaultVenueID" class="d-block">Default Venue:</label>
                <select name="website[defaultVenueID]" id="website.defaultVenueID" required="required" aria-required="true" class="custom-select custom-select-sm">
                    <option value="">...</option><?php
                    if (isset($data['venues'])) {
                        foreach ($data['venues'] as $venue) { ?>
                            <option value="<?php echo $venue->getID();?>"<?php if (isset($data['league']) && $venue->getID()==$data['league']->getDefaultVenueID()) echo ' selected';?>><?php echo $venue->getName();?></option><?php
                        }
                    } ?>
                </select>
                
                <label for="website.defaultDay" class="d-block">Default Day:</label>
                <select name="website[defaultDay]" id="website.defaultDay" aria-required="true" class="custom-select custom-select-sm">
                    <option value="">...</option>
                    <option value="mo"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="mo") echo ' selected';?>>Monday</option>
                    <option value="tu"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="tu") echo ' selected';?>>Tuesday</option>
                    <option value="we"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="we") echo ' selected';?>>Wednesday</option>
                    <option value="th"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="th") echo ' selected';?>>Thursday</option>
                    <option value="fr"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="fr") echo ' selected';?>>Friday</option>
                    <option value="sa"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="sa") echo ' selected';?>>Saturday</option>
                    <option value="su"<?php if (isset($data['league']) && $data['league']->getDefaultDay()=="su") echo ' selected';?>>Sunday</option>
                </select>
                */
                ?>
                <div class="custom-control custom-switch mt-2">
                    <input type="checkbox" class="custom-control-input" id="website.visible" name="website[visible]" value="1"<?php if (isset($data['league']) && $data['league']->isVisible()===true) echo ' checked';?>>
                    <label class="custom-control-label" for="website.visible">Show on Website</label>
                </div>
            <?php
            /*
                            <label for="website.status" class="d-block">Status:</label>
                            <select name="website[status]" id="website.status" class="form-control"><?php
                            foreach ($data['statuses'] as $statusID => $status) { ?>
                                <option value="<?php echo $statusID;?>"<?php if (isset($data['league']) && $data['league']->getStatus()==$statusID) echo ' selected';?>><?php echo $status;?></option><?php 
                            }?>
                            </select>
            */
            ?>
                <div class="launch">
                    <label for="website.launchDate">Launch Date</label>
                    <input class="form-control" type="date" name="website[launchDate]" id="website.launchDate" value="<?php if (isset($data['league'])) echo $data['league']->getLaunchDate();?>">
                    <label for="website.launchTime">Launch Time</label>
                    <input class="form-control" type="time" name="website[launchTime]" id="website.launchTime" value="<?php if (isset($data['league'])) echo $data['league']->getLaunchTime();?>">
                </div>

                <label for="website.playingSurface" class="d-block">Playing Surface:</label>
                <select name="website[playingSurface]" id="website.playingSurface" class="form-control">
                    <option value="">...</option>
                    <option value="1"<?php if (isset($data['league']) && $data['league']->getPlayingSurface()==1) echo ' selected';?>>Indoor</option>
                    <option value="2"<?php if (isset($data['league']) && $data['league']->getPlayingSurface()==2) echo ' selected';?>>Outdoor</option>
                    <option value="3"<?php if (isset($data['league']) && $data['league']->getPlayingSurface()==3) echo ' selected';?>>Indoor & Outdoor</option>
                </select>

                <div class="pricing">
                    <label for="website.totalPrice">Fixture &pound;</label>
                    <input class="form-control" type="text" name="website[totalPrice]" id="website.totalPrice" value="<?php if (isset($data['league'])) echo number_format($data['league']->getTotalPrice(),2);?>">
                </div>
                <button type="submit" class="btn btn-primary" style="margin-top: 1rem;" name="tab" value="website">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="seasons") echo ' show active';?>" id="seasons" role="tabpanel" aria-labelledby="seasons-tab">
            <h3>Season | <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#seasonModal">New</button></h3>
            <form action="" method="post">
                <input type="hidden" name="tab" value="seasons">
                <input type="hidden" name="season[leagueID]" value="<?php if (isset($data['league'])) echo $data['league']->getID();?>">
                <input type="hidden" name="season[id]" value="<?php if (isset($data['season'])) echo $data['season']->getID();?>">
                <div class="row">
                    <div class="col">
                        <label for="season.name" class="d-block">Name</label>
                        <input name="season[name]" id="season.name" type="text" placeholder="Name" required="required" aria-required="true" class="form-control form-control-sm" value="<?php if (isset($data['season'])) echo $data['season']->getName();?>">
                    </div>
                    <div class="col">
                        <label for="season.launchDate" class="d-block">Launch Date</label>
                        <input name="season[launchDate]" id="season.launchDate" type="date" placeholder="Launch Date" class="form-control form-control-sm" value="<?= $data['season']->launchDate ?>">
                    </div>
                    <?php
                    /*
                    <div class="col" style="align-self: center;">
                        <div class="row">
                            <div class="col" style="align-self: center;">
                                <div class="custom-control custom-switch mt-2">
                                    <input type="checkbox" class="custom-control-input" id="season.autoRollForward" name="season[autoRollForward]" value="1"<?php if (isset($data['season']) && $data['season']->autoRollForward == 1) echo ' checked';?>>
                                    <label class="custom-control-label" for="season.autoRollForward">Auto Roll Forward</label>
                                </div>
                            </div>
                            <div class="col" style="align-self: center;">
                                <div class="custom-control custom-switch mt-2">
                                    <input type="checkbox" class="custom-control-input" id="season.openForRegistration" name="season[openForRegistration]" value="1"<?php if (isset($data['season']) && $data['season']->openForRegistration == 1) echo ' checked';?>>
                                    <label class="custom-control-label" for="season.openForRegistration">Open For Registration</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    */
                    ?>
                </div>
                
                <div class="row">
                    <div class="col">
                        <label for="season.duration" class="d-block">Duration</label>
                        <input name="season[duration]" id="season.duration" type="number" placeholder="in minutes" required="required" aria-required="true" class="form-control form-control-sm" value="<?php if (isset($data['season'])) echo $data['season']->getDuration();?>">
                    </div>
                    <div class="col">
                        <label for="season.rounds" class="d-block">Rounds</label>
                        <input name="season[rounds]" id="season.rounds" type="number" placeholder="Rounds" required="required" aria-required="true" class="form-control form-control-sm" value="<?php if (isset($data['season'])) echo $data['season']->getRounds();?>">
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <label for="season.fixtureCharge" class="d-block">Fixture Charge</label>
                        <input name="season[fixtureCharge]" id="season.fixtureCharge" type="text" placeholder="Fixture Charge per Team" required="required" aria-required="true" class="form-control form-control-sm" value="<?php if (isset($data['season'])) echo $data['season']->getFixtureCharge();?>">
                    </div>
                    <div class="col">
                        <label for="season.officialCharge" class="d-block">Charge for Official</label>
                        <input name="season[officialCharge]" id="season.officialCharge" type="text" placeholder="Charge for Match Official" required="required" aria-required="true" class="form-control form-control-sm" value="<?php if (isset($data['season'])) echo $data['season']->getOfficialCharge();?>">
                    </div>
                </div><?php
                /*
                <div class="row">
                    <div class="col">
                        <label for="season.minTeams">Min Teams</label>
                        <input type="number" name="season[minTeams]" id="season.minTeams" class="form-control form-control" value="<?= $data['season']->minTeams ?>">
                    </div>
                    <div class="col">
                        <label for="season.maxTeams">Max Teams</label>
                        <input type="number" name="season[maxTeams]" id="season.maxTeams" class="form-control form-control" value="<?= $data['season']->maxTeams ?>">
                    </div>
                </div>
                */
                ?>
                <?php
                /*
                <div class="custom-control custom-switch">                
                    <input name="season[autoFixture]" type="checkbox" id="season.autoFixture" class="custom-control-input"<?php if (isset($data['season']) && $data['season']->getAutoFixture()==1) echo ' checked';?> value="1">
                    <label for="season.autoFixture" class="custom-control-label">AutoFixture</label>
                </div>
                <div class="custom-control custom-switch">
                    <input name="season[autoSchedule]" type="checkbox" id="season.autoSchedule" class="custom-control-input"<?php if (isset($data['season']) && $data['season']->getAutoSchedule()==1) echo ' checked';?> value="1">
                    <label for="season.autoSchedule" class="custom-control-label">AutoSchedule</label>
                </div>
                <div class="custom-control custom-switch">
                    <input name="season[autoBilling]" type="checkbox" id="season.autoBilling" class="custom-control-input"<?php if (isset($data['season']) && $data['season']->getAutoBilling()==1) echo ' checked';?> value="1">
                    <label for="season.autoBilling" class="custom-control-label">Auto Billing</label>
                </div>
                */
                ?>
                <div class="row">
                    <div class="col">
                        <label for="season.startBookingID" class="d-block">Start Booking</label>
                        <select class="form-control form-control-sm" name="season[startBookingID]" id="season.startBookingID">
                            <option value=""></option><?php
                            if (isset($data['bookings'])) {
                                foreach ($data['bookings'] as $booking) { 
                                    if ($data['season']->launchDate && strtotime($data['season']->launchDate) > strtotime($booking->startDate)) continue;
                                    ?>
                                    <option value="<?php echo $booking->id;?>"<?php if ($data['season']->getStartBookingID() == $booking->id) echo ' selected';?>><?php echo $booking->__toString();?></option><?php
                                }
                            }?>
                        </select>
                    </div>
                    <div class="col">
                        <label for="season.statusID" class="d-block">Status</label>
                        <select class="form-control form-control-sm" name="season[statusID]" id="season.statusID">
                            <option value="">Pending</option><?php
                            foreach ($data['seasonStatuses'] as $seasonStatus) { ?>
                                <option value="<?= $seasonStatus->id ?>"<?php if ($data['season']->statusID==$seasonStatus->id) echo ' selected';?>><?= $seasonStatus ?></option><?php
                            } ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <label for="season.venueID" class="d-block">Default Venue</label>
                        <select class="form-control form-control-sm" name="season[venueID]" id="season.venueID">
                            <option value=""></option><?php
                           if (isset($data['venues'])) {
                            foreach ($data['venues'] as $venue) {
                                $selected = (isset($data['season']) && $data['season']->venueID == $venue->id) ? ' selected' : null;
                                ?>
                                <option value="<?= $venue->id ?>"<?= $selected ?>><?= $venue->__toString() ?></option>
                                <?php
                            }
                        }?>
                        </select>
                    </div>
                    <div class="col">
                        <label for="season.defaultDay" class="d-block">Default Day:</label>
                        <select name="season[defaultDay]" id="season.defaultDay" class="custom-select custom-select-sm">
                            <option value="">...</option>
                            <option value="mo"<?= ($data['season']->defaultDay=="mo") ? ' selected' : null ?>>Monday</option>
                            <option value="tu"<?= ($data['season']->defaultDay=="tu") ? ' selected' : null ?>>Tuesday</option>
                            <option value="we"<?= ($data['season']->defaultDay=="we") ? ' selected' : null?>>Wednesday</option>
                            <option value="th"<?= ($data['season']->defaultDay=="th") ? ' selected' : null?>>Thursday</option>
                            <option value="fr"<?= ($data['season']->defaultDay=="fr") ? ' selected' : null?>>Friday</option>
                            <option value="sa"<?= ($data['season']->defaultDay=="sa") ? ' selected' : null?>>Saturday</option>
                            <option value="su"<?= ($data['season']->defaultDay=="su") ? ' selected' : null?>>Sunday</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <label for="season.playingSurface" class="d-block">Playing Surface:</label>
                        <select name="season[playingSurface]" id="season.playingSurface" class="form-control  form-control-sm">
                            <option value="">Select</option>
                            <option value="1"<?= ($data['season']->playingSurface == 1) ? ' selected' : null?>>Indoor</option>
                            <option value="2"<?= ($data['season']->playingSurface == 2) ? ' selected' : null?>>Outdoor</option>
                        </select>
                    </div>
                    <div class="col">
                        <label for="season.rollForward" class="d-block">Roll Forward:</label>
                        <select name="season[rollForward]" id="season.rollForward" class="form-control  form-control-sm">
                            <option value="">Pending</option>
                            <option value="1"<?= ($data['season']->rollForward == 1) ? ' selected' : null?>>Yes</option>
                            <option value="2"<?= ($data['season']->rollForward == 2) ? ' selected' : null?>>No</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <label for="season.timeStarts" class="d-block">Time Starts</label>
                        <input type="time" name="season[timeStarts]" id="season.timeStarts" class="form-control  form-control-sm" value="<?= $data['season']->timeStarts ?>">
                        
                    </div>
                    <div class="col">
                        <label for="season.timeEnds" class="d-block">Time Ends</label>
                        <input type="time" name="season[timeEnds]" id="season.timeEnds" class="form-control form-control-sm" value="<?= $data['season']->timeEnds ?>">
                    </div>
                </div>
                <input type="hidden" name="season[locked]" value="<?=$data['season']->locked?>">
                <input type="hidden" name="season[lockStamp]" value="<?=$data['season']->lockStamp?>">
                <button type="submit" class="btn btn-primary mt-2">Save</button>
                <!-- <a class="btn btn-info">Copy</button> -->
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="bookings") echo ' show active';?>" id="bookings" role="tabpanel" aria-labelledby="bookings-tab">
            <h3>Bookings | <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#bookingModal">New</button></h3>
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Venue</th>
                        <th>Pitch</th>
                        <th>Start Time</th>
                        <th>Start Date</th>
                        <th>Duration</th>
                        <th>£/hr</th>
                        <th colspan="3">...</th>
                    </tr>
                </thead>
                <tbody><?php
                if (isset($data['bookings']) && $data['bookings']) { 
                    $data['season']->getStartBooking();
                    foreach ($data['bookings'] as $booking) {
                        if ($data['season']->startBooking && $booking->startDate < $data['season']->startBooking->startDate) continue;
                        ?>
                    <tr>
                        <td><?php echo $booking->getVenue();?></td>
                        <td><?php echo $booking->getPitchCourt();?></td>
                        <td><?php echo $booking->getStartTime();?></td>
                        <td><?php echo $booking->getStartDate('D jS F Y');?></td>
                        <td><?php echo $booking->getDuration();?></td>
                        <td><?php echo $booking->getHourlyRate();?></td>
                        <td><button class="btn btn-info btn-sm" type="button"
                            data-bookingid="<?php echo $booking->id;?>" 
                            data-leagueid="<?php echo $booking->getLeagueID();?>" 
                            data-venueid="<?php echo $booking->getVenueID();?>" 
                            data-pitchcourt="<?php echo $booking->getPitchcourt();?>" 
                            data-starttime="<?php echo $booking->getStartTime();?>" 
                            data-startdate="<?php echo $booking->getStartDate();?>" 
                            data-weeks="<?php echo $booking->getWeeks();?>" 
                            data-duration="<?php echo $booking->getDuration();?>" 
                            data-hourlyrate="<?php echo $booking->getHourlyRate();?>" 
                            data-creditvalue="<?php echo $booking->creditValue;?>" 
                            onclick="editBooking(event)">Edit</button></td>
                        <td>
                            <form action="./User/League/<?php echo $league->id;?>" method="post">
                                <input type="hidden" name="tab" value="bookings">
                                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                                <input type="hidden" name="bookingAction" value="copy">
                                <input type="hidden" name="bookingID" value="<?php echo $booking->id;?>">
                                <button class="btn btn-warning btn-sm" type="submit">Copy</button>
                            </form>
                        </td>
                        <td>
                            <form action="./User/League/<?php echo $league->id;?>" method="post">
                                <input type="hidden" name="tab" value="bookings">
                                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                                <input type="hidden" name="bookingAction" value="remove">
                                <input type="hidden" name="bookingID" value="<?php echo $booking->id;?>">
                                <button class="btn btn-danger btn-sm" type="submit">Delete</button>
                            </form>
                        </td>
                    </tr><?php
                    }
                } ?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="teams") echo ' show active';?>" id="teams" role="tabpanel" aria-labelledby="teams-tab">
            <h3>Teams | <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#teamModal">New</button></h3>
            <select name="" id="teamListFilter" class="form-control">
                <option value="">All Divisions</option><?php
                if (isset($data['divisions'])) {
                    foreach ($data['divisions'] as $division){ ?>
                <option value="<?= $division->id?>"><?= $division ?></option><?php
                    }
                } ?>
            </select>
            <?php if(isset($data['seasonTeams'])){?>
            <table class="table table-sm">
                <caption><?=count($data['seasonTeams']) ?></caption>
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Division</th>
                        <th>Captain</th>
                        <th>Treasurer</th>
                        <th class="text-center">Wild</th>
                        <th class="text-center">Re-signed?</th>
                        <th class="text-center">Pay Method</th>
                        <!-- <th>Invoice</th> -->
                        <th>v</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody id="teamBodyList"><?php
                if (isset($data['seasonTeams'])) {
                    foreach ($data['seasonTeams'] as $seasonTeam){ 
                        $resubscription = \TeamSeason::Resubscribed($seasonTeam['team'],$data['league']);
                        if (isset($divisionTeamCounts[$seasonTeam['divisionID']])) {
                            $divisionTeamCounts[$seasonTeam['divisionID']]++;
                        } else $divisionTeamCounts[$seasonTeam['divisionID']] = 1;
                        ?>
                    <tr data-teamseasonid="<?= $seasonTeam['seasonID'] ?>" data-divisionid="<?= $seasonTeam['divisionID'] ?>">
                        <td class="clickable teamName" data-id="<?= $seasonTeam['id'] ?>" data-name="<?= $seasonTeam['name'] ?>"><?= $seasonTeam['name'] ?></td>
                        
                        <td class="clickable" data-toggle="modal" data-target="#teamseasonDivisionModal" onclick="changeDivision(event)" data-teamseasonid="<?= $seasonTeam['teamSeasonID'] ?>"><?= $seasonTeam['divisionName'] ?></td>

                        <td class="clickable" data-toggle="modal" data-target="#teamSeasonCaptainModal" data-captainid="<?= $seasonTeam['captain']['id'] ?>" data-treasurerid="<?= $seasonTeam['treasurer']['id'] ?>" data-teamname="<?= $seasonTeam['name']?>" data-teamseasonid="<?= $seasonTeam['teamSeasonID'] ?>" onclick="changePeople(event)"><?= ($seasonTeam['captain']['name']) ? $seasonTeam['captain']['name'] : null ?></td>

                        <td class="clickable" data-toggle="modal" data-target="#teamSeasonCaptainModal" data-captainid="<?= $seasonTeam['captain']['id'] ?>" data-treasurerid="<?= $seasonTeam['treasurer']['id'] ?>" data-teamname="<?= $seasonTeam['team']?>" data-teamseasonid="<?= $seasonTeam['teamSeasonID'] ?>" onclick="changePeople(event)"><?= ($seasonTeam['treasurer']['name']) ? $seasonTeam['treasurer']['name'] : null ?></td>

                        <td class="text-center"><?php 
                            if ($resubscription === true) {
                                echo '<span class="bg-success p-1 text-light">&check;</span>';
                             } elseif ($resubscription === false) {
                                 echo <<<EOT
                                    <form method="post" action="/User/League">
                                        <input type="hidden" name="resubscribe[seasonID]" value="{$data['season']->id}">
                                        <input type="hidden" name="resubscribe[teamID]" value="{$seasonTeam['id']}">
                                        <button class="btn btn-sm" name="action" value="resubscribe">Resubscribe</button>
                                    </form>
                                    EOT;
                             } else echo "<span title=\"$resubscription\">&minus;</span>";?>
                        </td>

                        <td class="text-center"><?php echo (true === $seasonTeam['team']->paymentMethodStatus()) ? '<span class="bg-success p-1 text-light">&check;</span>' : '<span class="bg-danger p-1 text-light">&cross;</span>';?></td>

                        <td><?php
                        if (!$data['season']->isLocked()) {?>
                            <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                                <button class="btn btn-danger btn-sm" type="submit" name="teamSeasonRemove" value="<?= $seasonTeam['teamSeasonID'] ?>">Delete</button>
                            </form><?php

                        } ?>
                        </td>
                    </tr><?php
                    }
                } ?>
                </tbody>
            </table>
            <?php } ?>
        </div>

        <?php
        if (isset($data['season']) && $data['season']) {
            
            $data['season']->getStatus(); ?>

        <div class="tab-pane fade<?php if ($data['tab']=="teams2") echo ' show active';?>" id="teams2" role="tabpanel" aria-labelledby="teams2-tab">
            <h3>Teams</h3><?php
            if (isset($data['seasonTeams']) && $data['seasonTeams']) { ?>
            <table class="table table-sm" style="font-size: .9em;">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Division</th>
                        <th>Captain</th>
                        <th>Treasurer</th>
                        <th class="text-center">Wildcard</th>
                        <th>Card</th>
                        <th>Pay Option</th>
                        <th>Next?</th><?php
                        if ($data['season']->status && $data['season']->status->isNextSeason()) { ?>
                            <th class="text-center">...</th><?php
                        } ?>
                    </tr>
                </thead>
                <tbody><?php
                foreach ($data['seasonTeams'] as $s) { 
                    $billAmount = $data['season']->getFixtureCharge();
                    if ($s['payInFull']) $billAmount = number_format($s['team']->FullBalancePaymentValue() ?? 0,2);
                    ?>
                    <tr title="TeamSeason ID <?= $s['teamSeasonID'] ?>">
                        <td class="clickable teamName" data-id="<?= $s['id'] ?>" data-name="<?= $s['name'] ?>"><?= $s['name'] ?></td>
                        <td class="clickable" data-toggle="modal" data-target="#teamseasonDivisionModal" onclick="changeDivision(event)" data-teamseasonid="<?= $s['teamSeasonID'] ?>">
                            <?= $s['divisionName'] ?>
                        </td>

                        <td title="<?= ($s['captain']) ? $s['captain']['id'] : null ?>">
                            <?php if (\User::isManager()) {?><i class="fas fa-user-cog" data-toggle="modal" data-target="#teamSeasonCaptainModal" data-captainid="<?= @$s['captain']['id'] ?>" data-treasurerid="<?= @$s['treasurer']['id'] ?>" data-teamname="<?= $s['name']?>" data-teamseasonid="<?= $s['teamSeasonID'] ?>" onclick="changePeople(event)"></i><?php } ?>
                            <a href="<?= (@$s['captain']['email']) ? 'mailto:'.@$s['captain']['email'] : '#' ?>">
                            <?= ($s['captain']) ? $s['captain']['name'] : null ?></a>
                        </td>

                        <td title="<?= ($s['treasurer']) ? $s['treasurer']['id'] : null ?>">
                            <a href="<?= ($s['treasurer']) ? 'mailto:'.$s['treasurer']['email'] : '#' ?>">
                                <?= ($s['treasurer']) ? $s['treasurer']['name'] : null ?>
                            </a>
                        </td>

                        <td class="text-center">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input wildSwitch" data-teamseasonid="<?= $s['teamSeasonID'] ?>" id="wild_<?= $s['id'] ?>"<?= ($s['wildcard']==1) ? ' checked':null?>>
                                <label class="custom-control-label" for="wild_<?= $s['id'] ?>"></label>
                            </div>
                        </td>

                        <td><?= ($s['paymentSource']) ? $s['paymentSource']['name'] : null ?></td>

                        <td><?= ($s['payInFull']) ? "Full ($billAmount)" : "Fixture ($billAmount)" ?></td>

                        <td><?php 
                            if ($s['team']->haveReentered()) {
                                echo "&check;";
                            } elseif (Auth::isAuthor(User::AuthUser ())) {
                                echo <<<HTML
                                    <form action="/User/League" method="post">
                                        <input type="hidden" name="resubscribe[seasonID]" value="{$data['season']->id}">
                                        <button class="btn btn-sm btn-danger" name="resubscribe[teamID]" value="{$s['id']}">Resub</button>
                                    </form>
                                HTML;
                            } else echo "&nbsp;"; ?>
                        </td><?php
                        if ($data['season']->status && $data['season']->status->isNextSeason()) { ?>
                            <td class="text-center">
                                <form action="" method="post">
                                    <button type="submit" class="btn btn-sm btn-danger" name="deleteTeamSeason" value="<?= $s['teamSeasonID'] ?>">&times;</button>
                                </form>
                            </td><?php 
                        } ?>
                        <td></td>
                    </tr><?php
                } ?>
                </tbody>
            </table><?php
            } else echo "<p>No teams to show</p>"; ?>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="divisions") echo ' show active';?>" id="divisions" role="tabpanel" aria-labelledby="divisions-tab">
            <h3>Divisions<?php if ($data['season']->isEditable()) {?> | <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#divisionModal">New</button><?php }?></h3>
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Rounds</th>
                        <th>Venue</th>
                        <th>Teams</th>
                        <th colspan="2">...</th>
                    </tr>
                </thead>
                <tbody><?php
                if (isset($data['divisions'])) {
                    foreach ($data['divisions'] as $division){ ?>
                    <tr>
                        <td><?php echo $division->getName();?></td>
                        <td><?php
                        if (!$data['season']->isLocked()) {?>
                            <form action="" method="post" class="form-inline">
                                <input type="number" name="division[rounds]" class="form-control form-control-sm text-center mr-2" value="<?= $division->rounds ?>">
                                <button type="submit" class="btn btn-sm btn-info" name="division[id]" value="<?= $division->id ?>">Save</button>
                            </form><?php
                        } else echo $division->rounds; ?>
                        </td> 
                        <td><?php
                        $data['season']->getStatus();
                        if ($data['season']->status->active && !$data['season']->status->live) { ?>
                            <form action="" method="post" class="form-inline">
                                <select name="division[venueID]" class="form-control form-control-sm mr-2">
                                    <option value=""></option><?php
                                    foreach ($data['availableFixtureVenues'] as $venueOption) { ?>
                                        <option value="<?= $venueOption->id ?>" <?= ($division->venueID ==$venueOption->id) ? ' selected' : null ?>><?= $venueOption ?></option><?php
                                    } ?>
                                </select>
                                <button type="submit" class="btn btn-sm btn-info" name="division[id]" value="<?= $division->id ?>">Save</button>
                            </form><?php
                        } elseif ($division->venueID) {
                            echo new Venue($division->venueID);
                        } ?>
                        </td>
                        <td><?= (isset($divisionTeamCounts[$division->id])) ? $divisionTeamCounts[$division->id] : null ?></td>
                        <td><?php
                        if (!$data['season']->isLocked()) {?>
                            <form action="./User/League/<?php echo $league->id;?>" method="post">
                                <input type="hidden" name="tab" value="divisions">
                                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                                <input type="hidden" name="divisionAction" value="remove">
                                <input type="hidden" name="divisionID" value="<?php echo $division->id;?>">
                                <button class="btn btn-danger btn-sm" type="submit">Delete</button>
                            </form><?php
                        } ?>
                        </td>
                        <td><?php
                        if (!$data['season']->isLocked()) {?>
                            <button class="btn btn-info btn-sm" type="button"
                                data-divisionid="<?php echo $division->id;?>" 
                                data-name="<?php echo $division->getName();?>" 
                                onclick="editDivision(event)">Edit
                            </button><?php
                        } ?>
                        </td>
                    </tr><?php
                    }
                } ?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="fixtures") echo ' show active';?>" id="fixtures" role="tabpanel" aria-labelledby="fixtures-tab">
            <h3>Fixtures<?php if ($data['isFixturesLocked']) {?> | <form  onsubmit="return showConfirmation()" style="display: inline-block;" action="/User/League/<?php echo $data['league']->id;?>" method="post">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <input type="hidden" name="action" value="autofixture">
            <button type="submit" class="btn btn-sm btn-success">AutoFixture</button></form><?php }?></h3>
            <form action="" method="post">
                <input type="hidden" name="tab" value="fixtures">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <select class="form-control form-control-sm" name="filterDivision" id="filterDivisionForFixtures" onchange="form.submit()">    
                    <option value="">All Divisions</option><?php
                if (isset($data['divisions'])) {
                    foreach ($data['divisions'] as $division){ ?>
                    <option value="<?php echo $division->id;?>"<?php if (isset($_POST['filterDivision']) && $_POST['filterDivision']==$division->id) echo ' selected';?>><?php echo $division->getName();?></option><?php
                    }
                } ?>
                </select>
            </form>
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Week</th>
                        <th>Division</th>
                        <th class="text-center">Home</th>
                        <th class="text-center">Home Inv.</th>
                        <th class="text-center">Away</th>
                        <th class="text-center">Away Inv.</th>
                    </tr>
                </thead>
                <tbody><?php
                if (isset($data['fixtures']) && $data['fixtures']) {
                    foreach ($data['fixtures'] as $fixture) {
                        if (isset($_POST['filterDivision']) && $_POST['filterDivision'] && $_POST['filterDivision']!=$fixture->getDivision()) continue; ?>
                    <tr title="Fixture ID <?= $fixture->id ?>" data-fixtureid="<?= $fixture->id ?>">
                        <td><?php echo $fixture->getWeek() . "." . $fixture->getWeekPos();?></td>
                        <td><?= new Division($fixture->getDivision()) ?></td>
                        <td class="text-center"><?= new Team($fixture->getHomeTeam()) ?></td>
                        <td class="text-center"><a href="/Finance/Statement/<?= $fixture->getHomeTeam() ?>"><?= $fixture->homeTrans ?></a></td>
                        <td class="text-center"><?= new Team($fixture->getAwayTeam()) ?></td>
                        <td class="text-center"><a href="/Finance/Statement/<?= $fixture->getAwayTeam() ?>"><?= $fixture->awayTrans ?></a></td>
                    </tr><?php
                    } 
                }?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="schedule") echo ' show active';?>" id="schedule" role="tabpanel" aria-labelledby="schedule-tab"><?php
            $unscheduled = Fixture::Unscheduled($data['season']); ?>
            <h3>Schedule<?php if ($unscheduled && is_numeric($unscheduled)) {?>  | <form style="display: inline-block;" action="/User/League/<?php echo $data['league']->id;?>" method="post">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <input type="hidden" name="action" value="autoschedule">
                <button type="submit" class="btn btn-sm btn-success">AutoSchedule <?= $unscheduled ?> fixtures</button></form><?php } ?>
                <form style="display: inline-block;" action="/User/League/<?php echo $data['league']->id;?>" method="post">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <input type="hidden" name="action" value="clearschedule">
                <button type="submit" class="btn btn-sm btn-warning">Clear Unbilled</button></form>
                <?php
                if (\User::isAuthor()) { ?>
                <form style="display: inline-block;" action="/User/League/<?php echo $data['league']->id;?>" method="post">
                    <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                    <input type="hidden" name="action" value="fullclearschedule">
                    <button type="submit" class="btn btn-sm btn-danger">Clear &amp; Credit ALL</button>
                </form><?php
                } ?>
            </h3>
            <form action="" method="post" class="form-inline">
                <input type="hidden" name="tab" value="schedule">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <select class="form-control form-control-sm" name="filterDivision" id="filterDivision" onchange="form.submit()">    
                    <option value="">All Divisions</option><?php
                if (isset($data['divisions'])) {
                    foreach ($data['divisions'] as $division){ ?>
                    <option value="<?php echo $division->id;?>"<?php if (isset($_POST['filterDivision']) && $_POST['filterDivision']==$division->id) echo ' selected';?>><?php echo $division->getName();?></option><?php
                    }
                } ?>
                </select><?php
                /*
                <label for="startDate" class="mx-1">from</label>
                <input type="date" name="startDate" id="startDate" class="form-control form-control-sm" value="<?= $_POST['startDate'] ?>" onchange="form.submit()">
                */ ?>
            </form>            
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Date</th>
                        <th>Pitch</th>
                        <th>Venue</th>
                        <th>Fixture</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody><?php
            if (isset($data['schedule']) && is_array($data['schedule'])) {
                foreach ($data['schedule'] as $schedule) { 
                    if (isset($_POST['filterDivision']) && $_POST['filterDivision'] && $_POST['filterDivision']!=$schedule->fixture->getDivision()) continue; 
                    // echo "Is " . $schedule->booking->getStartDate() . " less than " . $_POST['startDate'] . "<br>"; 
                    // if (isset($_POST['startDate']) && $_POST['startDate'] && $schedule->booking->getStartDate() < $_POST['startDate']) continue; 
                    ?>
                    <tr data-scheduleid="<?php echo $schedule->id;?>">
                        <td><?php echo $schedule->getStartTime();?></td>
                        <td><?php echo $schedule->booking->getStartDate('d/m/Y');?></td>
                        <td><?php echo $schedule->booking->getPitchCourt();?></td>
                        <td><?php echo $schedule->venue->getName();?></td>
                        <td><?php echo $schedule->fixture->__toString();?></td>
                        <td>
                            <button class="btn btn-info btn-sm" type="button"
                                data-scheduleid="<?php echo $schedule->id;?>" 
                                data-fixtureid="<?= $schedule->fixture->id;?>" 
                                data-bookingid="<?= $schedule->booking->id;?>" 
                                data-seasonid="<?php echo $data['season']->id;?>"
                                data-fixturename="<?php echo $schedule->fixture->__toString();?>"
                                data-fixturetiming="<?php echo $schedule->getStartTime() . " " . $schedule->booking->getStartDate('d/m/Y') . " " . $schedule->booking->getPitchCourt() . " " . $schedule->venue->getName();?>"
                                onclick="editSchedule(event)">Edit
                            </button>
                            <button class="btn btn-warning btn-sm" type="button"
                                data-scheduleid="<?php echo $schedule->id;?>" 
                                onclick="switchSchedule(event)">Switch
                            </button>
                        </td>
                    </tr><?php   
                } 
            }?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="results") echo ' show active';?>" id="results" role="tabpanel" aria-labelledby="results-tab">
            <form action="" method="post">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <input type="hidden" name="tab" value="results">
                <h3>Results | <button type="submit" class="btn btn-primary btn-sm">Save</button></h3>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th class="text-left">Home</th>
                            <th class="text-center">Home PoM</th>
                            <th class="text-center">Home Score</th>
                            <th class="text-center">&nbsp;</th>
                            <th class="text-center">Away Score</th>
                            <th class="text-center">Away PoM</th>
                            <th class="text-right">Away</th>
                        </tr>
                    </thead>
                    <tbody><?php
                $resultCounter = 0;
                if (isset($data['schedule']) && $data['schedule']) {
                    foreach ($data['schedule'] as $schedule) { 
                        // if (($fixture->getHomeScore() && $fixture->getAwayScore()) && !$_POST['showAllResults']) continue;
                        $fixture = $schedule->fixture;
                        $resultCounter++;
                        // Tools::Dump();
                        ?>
                        <tr data-fixtureid="<?php echo $fixture->id;?>">
                            <td class="text-left"><?php echo new Team($fixture->getHomeTeam());?></td>
                            
                            <td class="text-center"><input type="text" class="form-control form-control-sm text-center" name="leagueResults[<?php echo $fixture->id;?>][homePom]" value="<?php echo $fixture->getHomePom();?>"></td>
                            
                            <td class="text-center"><input type="text" class="form-control form-control-sm text-center" name="leagueResults[<?php echo $fixture->id;?>][homeScore]" value="<?php echo $fixture->getHomeScore();?>"></td>

                            <td class="text-center"><?= date('d/m/Y', strtotime($schedule->booking->startDate)) ?></td>

                            <td class="text-center"><input type="text" class="form-control form-control-sm text-center" name="leagueResults[<?php echo $fixture->id;?>][awayScore]" value="<?php echo $fixture->getAwayScore();?>"></td>
                            
                            <td class="text-center"><input type="text" class="form-control form-control-sm text-center" name="leagueResults[<?php echo $fixture->id;?>][awayPom]" value="<?php echo $fixture->getAwayPom();?>"></td>
                            
                            <td class="text-right"><?php echo new Team($fixture->getAwayTeam());?></td>

                        </tr><?php
                    }
                } ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="7">Total <?= $resultCounter ?></th>
                    </tr>
                </tfoot>
                </table>
                <button type="submit" class="btn btn-primary">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="tables") echo ' show active';?>" id="tables" role="tabpanel" aria-labelledby="tables-tab">
            <h3>Tables</h3>
            <form action="/User/League" method="post">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <input type="hidden" name="tab" value="tables">
                <select name="divisionID" class="form-control" onchange="form.submit()">
                    <option value="">...</option><?php
                    foreach ($data['divisions'] as $division) { ?>
                        <option value="<?php echo $division->id;?>"<?php if (isset($data['division']) && $data['division']->id==$division->id) echo ' selected';?>><?php echo $division->__toString();?></option><?php
                    } ?>
                </select>
            </form><?php
            if (isset($data['division']->table)) {?>
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th class="text-center" title="Played">P</th>
                        <th class="text-center" title="Won">W</th>
                        <th class="text-center" title="Drawn">D</th>
                        <th class="text-center" title="Lost">L</th>
                        <th class="text-center" title="Goals For">F</th>
                        <th class="text-center" title="Goals Against">A</th>
                        <th class="text-center" title="Goal Difference">GD</th>
                        <th class="text-center" title="Win Points">P</th>
                        <th class="text-center" title="Bonus Points">B</th>
                        <th class="text-center" title="Total Points">Tot</th>
                    </tr>
                </thead>
                <tbody><?php
                foreach ($data['division']->table as $standing) {?>
                    <tr>
                        <td><?php echo new Team($standing['teamID']);?></td>
                        <td class="text-center"><?php echo $standing['played'];?></td>
                        <td class="text-center"><?php echo $standing['won'];?></td>
                        <td class="text-center"><?php echo $standing['drawn'];?></td>
                        <td class="text-center"><?php echo $standing['lost'];?></td>
                        <td class="text-center"><?php echo $standing['for'];?></td>
                        <td class="text-center"><?php echo $standing['against'];?></td>
                        <td class="text-center"><?php echo $standing['gd'];?></td>
                        <td class="text-center"><?php echo $standing['points'];?></td>
                        <td class="text-center"><?php echo $standing['bp'];?></td>
                        <td class="text-center"><?php echo $standing['total'];?></td>
                    </tr><?php
                } ?>
                </tbody>
            </table><?php
            } ?>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="statements") echo ' show active';?>" id="statements" role="tabpanel" aria-labelledby="statements-tab">
            <h3>Statements</h3>
            <form action="" method="post">
                <input type="hidden" name="tab" value="statements">
                <input type="hidden" name="seasonID" value="<?php echo $data['season']->id;?>">
                <select name="teamID" id="statementTeamID" class="form-control" onchange="form.submit();">
                    <option value="">Select</option><?php
                    foreach ($data['teams'] as $team) { ?>
                        <option value="<?php echo $team->id;?>"<?php if (isset($_POST['teamID']) && $_POST['teamID']==$team->id) echo ' selected';?>><?php echo $team->__toString();?></option><?php
                    } ?>
                </select>
            </form><?php
            if (isset($_POST['teamID']) && isset($data['statements'][$_POST['teamID']])) {?>
            <table class="table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Descrip</th>
                        <th class="text-right">Value</th>
                    </tr>
                </thead>
                <tbody><?php
                $total = 0;
                foreach ($data['statements'][$_POST['teamID']] as $statementLine) { 
                    $total += $statementLine->getTotal();
                    ?>
                    <tr>
                        <td><?php echo $statementLine->getTaxDate('d/m/Y');?></td>
                        <td><?php echo $statementLine->getCategoryName();?></td>
                        <td><?php echo $statementLine->getDescription();?></td>
                        <td class="text-right"><?php echo $statementLine->getTotal();?></td>
                    </tr><?php
                } ?>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3">Account Balance</th>
                        <th class="text-right <?php if ($total<0) echo ' text-danger';?>"><?php echo number_format($total,2);?></th>   
                    </tr>
                </tfoot>
            </table><?php
            } ?>
        </div>

        <div class="tab-pane fade<?php if ($data['tab']=="waitinglist") echo ' show active';?>" id="waitinglist" role="tabpanel" aria-labelledby="waitinglist-tab">
            <h3>Waiting List</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Captain</th>
                        <th>...</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody><?php
                foreach ($data['waitingList'] as $waitingList) { ?>
                    <tr>
                        <td><?= $waitingList->getTeam() ?></td>
                        <td><?= $waitingList->getCaptain() ?></td>
                        <td>
                            <form action="/User/League" method="post">
                                <input type="hidden" name="tab" value="teams">
                                <input type="hidden" name="seasonID" value="<?= $data['season']->id ?>">
                                <input type="hidden" name="waitingList[id]" value="<?= $waitingList->id ?>">
                                <button type="submit" class="btn btn-sm btn-primary" name="waitingList[action]" value="add" title="Add to Season">Add</button>
                            </form>
                        </td>
                        <td>
                            <form action="" method="post">
                                <input type="hidden" name="waitingList[id]" value="<?= $waitingList->id ?>">
                                <input type="hidden" name="seasonID" value="<?= $data['season']->id ?>">
                                <input type="hidden" name="tab" value="wiaitingList">
                                <button type="submit" class="btn btn-sm btn-danger" name="waitingList[action]" value="delete">Delete</button>
                            </form>
                        </td>
                    </tr><?php
                }?>
                </tbody>
            </table>
        </div>
        
        <div class="tab-pane fade<?php if ($data['tab']=="leaderboard") echo ' show active';?>" id="leaderboard" role="tabpanel" aria-labelledby="leaderboard-tab"><?php
            if ($data['leaderboard']) {
            echo <<<HTML
            <h4>Player Nominations</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Player</th>
                        <th>Team</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
            HTML;
            $teamNoms = [];
            foreach ($data['leaderboard'] as $divisionID => $leaders) {
                $division = new Division($divisionID);
            echo <<<HTML
                    <tr class="bg-dark text-light">
                        <th colspan="3">$division</th>
                    </tr>
            HTML;
                foreach ($leaders as $k => $v) {
                list($teamID, $name) = explode(":",$k);
                $team = new Team($teamID);
                if (!isset($teamNoms[$team->name])) $teamNoms[$team->name] = 0;
                $teamNoms[$team->name] += $v;
                echo <<<HTML
                    <tr>
                        <td>$name</td>
                        <td>$team</td>
                        <td>$v</td>
                    </tr>
                HTML;
                }
            }
            echo <<<HTML
                </tbody>
            </table>
            <h4>Team Nominations</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
            HTML;
            arsort($teamNoms);
            foreach ($teamNoms as $k => $v) {
            echo <<<HTML
                <tr>
                    <td>$k</td>
                    <td>$v</td>
                </tr>
            HTML;
            }
            echo <<<HTML
                </tbody>
            </table>
            HTML;
        } ?>
        </div>
        
        <?php

        } ?>

    </div>
</div>
<script>
    function editBooking(e) {
        document.getElementById("booking.id").value = e.target.dataset.bookingid
        document.getElementById("booking.leagueid").value = e.target.dataset.leagueid
        document.getElementById("booking.venueid").value = e.target.dataset.venueid
        document.getElementById("booking.pitchcourt").value = e.target.dataset.pitchcourt
        document.getElementById("booking.starttime").value = e.target.dataset.starttime
        document.getElementById("booking.startdate").value = e.target.dataset.startdate
        document.getElementById("booking.weeks").value = e.target.dataset.weeks
        document.getElementById("booking.duration").value = e.target.dataset.duration
        document.getElementById("booking.hourlyrate").value = e.target.dataset.hourlyrate
        document.getElementById("booking.creditvalue").value = e.target.dataset.creditvalue
        $('#bookingModal').modal('show')
    }
    function switchSchedule(e) {
        // return;
        document.getElementById("schedule.old").value = e.target.dataset.scheduleid
        SwitchOptions (e.target.dataset.scheduleid);
        document.getElementById("scheduleWording").innerHTML = "";
        $('#switchModal').modal('show');
    }
    function editTeam(e) {
        document.getElementById("team.id").value = e.target.dataset.id;
        document.getElementById("team.name").value = e.target.dataset.name;
        // document.getElementById("team.divisionid").value = e.target.dataset.divisionid;
        // document.getElementById("team.leagueid").value = e.target.dataset.leagueid;
        // document.getElementById("team.captainID").value = e.target.dataset.captainid;
        // document.getElementById("team.treasurerID").value = e.target.dataset.treasurerid;
        // document.getElementById("team.wildcard").checked = (e.target.dataset.wildcard == true) ? true : false;
        // if (e.target.dataset.locked == "true") {
        //     document.getElementById("team.leagueid").disabled = true;
        //     document.getElementById("team.divisionid").disabled = true;
        // }
        $('#teamModal').modal('show')
    }
    function editDivision(e) {
        document.getElementById("division.id").value = e.target.dataset.divisionid;
        document.getElementById("division.name").value = e.target.dataset.name;
        $('#divisionModal').modal('show')
    }
    function editSchedule(e) {
        // return;
        document.getElementById("scheduling.id").value = e.target.dataset.scheduleid;
        document.getElementById("scheduling.bookingID").dataset.fixtureid = e.target.dataset.fixtureid;
        document.getElementById("fixtureName").innerHTML = e.target.dataset.fixturename;
        document.getElementById("fixtureTiming").innerHTML = e.target.dataset.fixturetiming;
        Bookings(e.target.dataset);
        $('#scheduleModal').modal('show')
    }
    function clearTeamData() {
        document.getElementById("team.id").value = "";
        document.getElementById("team.name").value = "";
        document.getElementById("team.divisionid").value = "";
        document.getElementById("team.leagueid").value = "";
        document.getElementById("team.captainID").value = "";
        document.getElementById("team.treasurerID").value = "";
    }
    // Bookings
    function Bookings(data) {
        // let url = "api.php";
        let url = "https://api.leagues4you.co.uk/seasonBookings/"+data.seasonid;
        let dropdown = document.getElementById("scheduling.bookingID");
        dropdown.options.length = 0;
        // let postData = {
        //     "action": "seasonBookings",
        //     "seasonID": data.seasonid
        // };
        fetch(url, {
            method: "POST",
            mode: "cors",
            credentials: "include",
            // body: JSON.stringify(postData)
        })
        .then(response => response.json())
        .then(responseData => {
            if (!responseData) return;
            // console.log(responseData);
            let opt = document.createElement("option");
            opt.value = "";
            opt.text = "...";
            dropdown.appendChild(opt);
            for (let r in responseData) {
                if (!responseData[r].usage.available) continue;
                // console.log("Create Element");
                let opt = document.createElement("option");
                opt.value = responseData[r].id;
                opt.text = responseData[r].name;
                dropdown.appendChild(opt);
                // consol.elog(opt);
            }
        })
    }
    // BookingSlots
    function BookingSlots(e) {
        let url = "https://api.leagues4you.co.uk/bookingSlots/"+e.target.value+"/"+e.target.dataset.fixtureid;
        fetch(url, {
            mode: "cors",
            credentials: "include"
        })
        .then(response => {
            if (response.status == 200) return response.json()
        })
        .then(responseData => {
            console.log("Dropdown Options",responseData);
            let dropdown = document.getElementById("scheduling.offset");
            dropdown.options.length = 0;
            let opt = document.createElement("option");
            opt.value = "";
            opt.text = "...";
            dropdown.appendChild(opt);
            for (let r in responseData) {
                let opt = document.createElement("option");
                opt.value = responseData[r].offset;
                opt.text = responseData[r].startTime;
                dropdown.appendChild(opt);
            }
        })
    }
    // Switch Options
    function SwitchOptions (scheduleID) {
        // let url = "api.php";
        let url = "https://api.leagues4you.co.uk/switchOptions/"+scheduleID;
        // let postData = {
        //     "action": "switchOptions",
        //     "scheduleID": scheduleID
        // };
        fetch(url, {
            // method: "POST",
            mode: "cors",
            credentials: "include",
            // body: JSON.stringify(postData)
        })
        .then(response => {
            if (response.status == 200) return response.json()
        })
        .then(responseData => {
            console.log(responseData);
            let dropdown = document.getElementById("schedule.new");
            dropdown.options.length = 0;
            let opt = document.createElement("option");
            opt.value = "";
            opt.text = "...";
            dropdown.appendChild(opt);
            for (let r in responseData) {
                let opt = document.createElement("option");
                opt.value = responseData[r].id;
                opt.text = responseData[r].name;
                dropdown.appendChild(opt);
            }
        })
    }
    function changeDivision (e) {
        document.getElementById("changeTeamDivision.id").value = e.target.dataset.teamseasonid;
    }
    function clearTeamSeasonDivisionData () {
        document.getElementById("changeTeamDivision.id").value = null;
    }
    function changePeople (e) {
        document.getElementById("teamSeasonPeople.teamSeasonID").value = e.target.dataset.teamseasonid;
        document.getElementById("teamSeasonPeople.captainID").value = e.target.dataset.captainid;
        document.getElementById("teamSeasonPeople.treasurerID").value = e.target.dataset.treasurerid;
        document.getElementById("teamSeasonPeople.teamname").innerHTML = e.target.dataset.teamname;
    }
    function clearPeople () {
        document.getElementById("teamSeasonPeople.teamSeasonID").value = null;
        document.getElementById("teamSeasonPeople.captainID").value = null;
        document.getElementById("teamSeasonPeople.treasurerID").value = null;
        document.getElementById("teamSeasonPeople.teamname").innerHTML = null;
    }
    function filterTeamsByDivision (e) {
        let tableBody = document.getElementById("teamBodyList");
        let tableRows = tableBody.getElementsByTagName("tr");
        for (let t in tableRows) {
            if (!tableRows[t].dataset) continue;
            // console.log("Val",e.target.value,"Row",tableRows[t].dataset.divisionid);
            if (!e.target.value || e.target.value == tableRows[t].dataset.divisionid) {
                tableRows[t].classList.remove("d-none");
            } else tableRows[t].classList.add("d-none");
        }
    }
    document.getElementById("teamListFilter").addEventListener("change",filterTeamsByDivision);
    function toggleWildcard(e) {
        console.log("Switch Wild",e.target.dataset.teamseasonid);
        if (e.target.dataset.teamseasonid) {
            const url = "<?=getDomainPrefix()?>" + "api.leagues4you.co.uk/toggleWild/" + e.target.dataset.teamseasonid;
            
            fetch(url,{
            // method: "POST",
            mode: "cors",
            credentials: "include",
            // body: JSON.stringify(postData)
        })
            .then(response => response.json())
            .then(data => console.log(data))
            // console.log(e.target.dataset.teamseasonid);
        } 
    }
    let wildSwitches = document.querySelectorAll(".wildSwitch");
    // console.log(wildSwitches);
    for (let w in wildSwitches) {
        if (wildSwitches[w].addEventListener) wildSwitches[w].addEventListener("change",toggleWildcard);
    }
    let teamNames = document.getElementsByClassName("teamName");
    for (let t in teamNames) {
        if (teamNames[t].addEventListener) teamNames[t].addEventListener("click",editTeam);
    }

function showConfirmation() {
  const confirmed = confirm("Are you sure you want to AutoFixture?");
  return confirmed;
}
document.addEventListener('DOMContentLoaded', function() {
    var searchInput = document.getElementById('teamSeasonPeople-captainID-search');
    var captainIDSelect = document.getElementById('teamSeasonPeople.captainID');
    var currentPage = 1;
    var totalPages = 0;
    var isLoading = false;

    function loadUsers(page, query = '') {
      if (isLoading) return;
      isLoading = true;
      fetch('<?=getDomainPrefix(); ?>admin.v2.api.leagues4you.co.uk/users&filterText=' + encodeURIComponent(query), {
                        headers: new Headers({
                            'authorization': 'Bearer <?=getJwt()?>',
                            'Content-Type': 'application/json'
                        }),
                    })
        .then(response => response.json())
        .then(data => {
          totalPages = data.totalPages;
          captainIDSelect.innerHTML = '';

          data.users.forEach(user => {
            var option = document.createElement('option');
            option.value = user.id;
            option.text = user.name + ' (' + user.email + ')';
            captainIDSelect.add(option);
          });

          // Automatically select the first user if the search input is not empty
          if (captainIDSelect.options.length > 0 && searchInput.value.trim() !== '') {
            captainIDSelect.selectedIndex = 0;
          }

          isLoading = false;
        })
        .catch(error => {
          console.error('Error loading users:', error);
          isLoading = false;
        });
    }

    searchInput.addEventListener('input', function() {
      currentPage = 1;
      loadUsers(currentPage, this.value);
    });

    captainIDSelect.addEventListener('scroll', function() {
      if (this.scrollTop + this.offsetHeight >= this.scrollHeight && currentPage <= totalPages) {
        currentPage++;
        loadUsers(currentPage, searchInput.value);
      }
    });

    loadUsers(currentPage);
  });
</script>