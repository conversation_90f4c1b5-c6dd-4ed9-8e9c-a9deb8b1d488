<div class="container-fluid">
    <h1>Taster Sessions</h1>
    <form class="form-inline mb-3" action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
        <label for="from">Show from</label>
        <input class="form-control" type="date" name="from" id="from" value="<?= $_POST['from'] ?>">

        <label for="sessionType">Session Type</label>
        <select class="form-control" name="sessionType" id="sessionType">
            <option value="">All</option>
            <option value="wildcard" <?= ($data['sessionType'] === 'wildcard') ? 'selected' : '' ?>>Wildcard</option>
            <option value="couch2court" <?= ($data['sessionType'] === 'couch2court') ? 'selected' : '' ?>>Couch2Court</option>
            <option value="tournament" <?= ($data['sessionType'] === 'tournament') ? 'selected' : '' ?>>Tournament</option>
        </select>

        <label for="coordinatorID">Coordinator</label>
        <select class="form-control" name="coordinatorID" id="coordinatorID">
            <option value="">All</option>
            <?php foreach ($data['coordinators'] as $coordinator): ?>
                <option value="<?= $coordinator->id ?>" <?= ($data['coordinatorID'] == $coordinator->id) ? 'selected' : '' ?>>
                    <?= htmlspecialchars($coordinator->firstname . ' ' . $coordinator->lastname) ?>
                </option>
            <?php endforeach; ?>
        </select>

        <button type="submit" class="btn btn-sm btn-primary ml-3">Show</button>
    </form>

    <table class="table">
        <thead>
            <tr>
                <th>Session</th>
                <th>Venue</th>
                <th>Location</th>
                <th>Sport</th>
                <th>Date</th>
                <th>Time</th>
                <th>Coordinator</th>
                <th class="text-center">&check;</th>
                <th class="text-center">?</th>
            </tr>
        </thead>
        <tbody><?php
                $total = 0;
                foreach ($data['tasterSessions'] as $tasterSession) {
                    // $total += $tasterSession->attendeeCount();
                    $venue = $tasterSession->getVenue();
                    $tasterTotals = C2C_Booking::TasterNumbers($tasterSession);

                    if ($tasterSession->refunded) {
                        $class = "danger";
                    } elseif ($tasterSession->cancelled) {
                        $class = "warning";
                    } else if ($tasterSession->date < date('Y-m-d')) {
                        $class = null;
                    } else $class = "success";
                ?>
                <tr class="<?= ($class) ? " text-$class" : $class ?>">
                    <td><?= $tasterSession->getSessionType(); ?></a></td>
                    <td><a href="/User/TasterSession/<?= $tasterSession->id ?>"><?= $venue ?></a></td>
                    <td><?= "{$venue->town}, {$venue->postcode}" ?></td>
                    <td><?= $tasterSession->getSport() ?></td>
                    <td><?= $tasterSession->formatDate() ?></td>
                    <td><?= $tasterSession->formatTime() ?></td>
                    <td><?= $tasterSession->getCoordinator() ?></td>
                    <td class="text-center"><?= $tasterTotals['confirmed'] ?></td>
                    <td class="text-center"><?= $tasterTotals['unconfirmed'] ?></td>
                </tr>
            <?php
                } ?>
        </tbody>
        <tfoot>
            <tr>
                <th colspan="6">Total</th>
                <th class="text-right"><?= $total ?></th>
            </tr>
        </tfoot>
    </table>
</div>