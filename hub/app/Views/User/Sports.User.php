<div class="container-fluid">
    <h1>Sports | <a href="/user" class="btn btn-sm btn-warning">Dashboard</a></h1>
    <form action="" method="post">
        <label for="sportID">Select Sport</label>
        <select class="form-control" name="sportID" id="sportID" onchange="form.submit();">
            <option value="">...</option><?php
            foreach($data['sports'] as $sport) { ?>
                <option value="<?php echo $sport->id;?>"<?php if (isset($data['sport']) && $data['sport']->id == $sport->id) echo ' selected';?>><?php echo $sport->__toString();?></option><?php
            } ?>
        </select>
    </form>
    <hr>
    <form action="" method="post">
        <input type="hidden" name="sport[id]" value="<?php if (isset($data['sport'])) echo $data['sport']->id;?>">
        
        <label for="sport.name">Sport Name</label>
        <input type="text" name="sport[name]" id="sport.name" class="form-control" value="<?= $data['sport']->name ?>">
        
        <label for="sport.duration">Sport Duration (in Mins)</label>
        <input type="number" name="sport[duration]" id="sport.duration" class="form-control" value="<?= $data['sport']->duration ?>">
        
        <label for="sport.webImage">URL of Image to Use on Web</label>
        <input type="text" name="sport[webImage]" id="sport.webImage" class="form-control" value="<?= $data['sport']->webImage?>">

        <!-- Uploaded to Wordpress -->
        <label for="sport.teamSignupImage">Team Sign-up Image URL</label>
        <input type="text" name="sport[teamSignupImage]" id="sport.teamSignupImage" class="form-control" value="<?= $data['sport']->teamSignupImage?>" >
        
        <label for="sport.playerSignupImage">Player Sign-up Image URL</label>
        <input type="text" name="sport[playerSignupImage]" id="sport.playerSignupImage" class="form-control" value="<?= $data['sport']->playerSignupImage ?>" >

        <label for="sport.videoExplainer">Video Explainer URL</label>
        <input type="text" name="sport[videoExplainer]" id="sport.videoExplainer" class="form-control" value="<?= $data['sport']->videoExplainer ?>" >
        
        <label for="sport.termsConditions">Terms and Conditions</label>
        <input type="text" name="sport[termsConditions]" id="sport.termsConditions" class="form-control" value="<?= $data['sport']->termsConditions;?>">
        
        <label for="sport.captainsPack">Captains Pack URL</label>
        <input type="text" name="sport[captainsPack]" id="sport.captainsPack" class="form-control" value="<?= $data['sport']->captainsPack;?>">
        
        <label for="sport.tasterSessionName">Taster Session Name</label>
        <input type="text" name="sport[tasterSessionName]" id="sport.tasterSessionName" class="form-control" value="<?= $data['sport']->tasterSessionName ?>">

        <button type="submit" class="btn btn-primary mt-2">Save</button>
    </form>

</div>