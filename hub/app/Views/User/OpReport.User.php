<div class="container-fluid">
    <h1>Op Report</h1>
    <form action="" method="post">
        <select name="coordinatorID" id="" class="form-control" onchange="form.submit()">
            <option value="">All Coordinators</option><?php
            foreach ($data['coordinators'] as $coordinator) { ?>
                <option value="<?= $coordinator->id ?>"<?= (isset($_POST['coordinatorID']) && $_POST['coordinatorID'] && $_POST['coordinatorID']==$coordinator->id) ? ' selected' : null ?>><?= $coordinator ?></option><?php
            } ?>
        </select>
    </form>
    <?php
    /*
    <form class="form-inline" action="" method="post">
        <select name="regionID" id="regionID" class="form-control">
            <option value="">All Regions</option><?php
            foreach ($data['regions'] as $region) { ?>
                <option value="<?= $region->id ?>"<?php if (isset($_POST['regionID']) && $_POST['regionID']==$region->id) echo ' selected';?>><?= $region ?></option><?php
            } ?>
        </select>
        <input type="date" name="startDate" id="startDate" class="form-control" value="<?= $_POST['startDate'] ?>">
        <input type="date" name="endDate" id="endDate" class="form-control" value="<?= $_POST['endDate'] ?>">
        <button type="submit" class="btn btn-sm btn-primary">Filter</button>
    </form>
    */
    ?>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>League</th>
                <th class="text-center">Live</th>
                <th class="text-center">Pending</th>
                <th class="text-center">Next</th>
                <th class="text-center">Launch Date</th>
                <th class="text-center">OFR</th>
                
            </tr>
        </thead>
        <tbody><?php
        $st = ["total" => 0, "pub" => 0, "live" => 0, "next" => 0, "roll" => 0,"ofr" => 0, "waitlist" => 0,"teamsLive" => 0,"teamsPending" => 0, "teamsNext" => 0];
        foreach ($data['opReport'] as $league) {
            if (isset($_POST['coordinatorID']) && $_POST['coordinatorID'] && $_POST['coordinatorID'] != $league->coordinator) continue;
            if (!$league->isActive()) continue;
            $st['total']++;
            $activeSeason = Season::Active($league);
            $nextSeason = Season::Next($league);
            $regSeason = Season::Registration($league);
            $waitlist = WaitingList::LeagueTotal($league);
            $teamsLive = ($activeSeason && $activeSeason->statusID == 1) ? TeamSeason::SeasonCount($activeSeason) : 0;
            $teamsPending = ($activeSeason && $activeSeason->statusID != 1) ? TeamSeason::SeasonCount($activeSeason) : 0;
            $teamsNext = ($nextSeason) ? TeamSeason::SeasonCount($nextSeason) : 0;
            if ($league->visible) $st['pub'] ++;
            if ($activeSeason) $st['live'] ++;
            if ($nextSeason) $st['next'] ++;
            if ($teamsLive) $st['teamsLive'] += $teamsLive;
            if ($teamsPending) $st['teamsPending'] += $teamsPending;
            if ($teamsNext) $st['teamsNext'] += $teamsNext;
            if ($activeSeason && $activeSeason->autoRollForward == 1) $st['roll'] ++;
            if ($waitlist) $st['waitlist'] += $waitlist;
            if ($regSeason) $st['ofr'] ++;?>
            <tr>
                <td><a href="/User/League/<?= $league->id ?>"><?= $league->name ?></a></td>
                <td class="text-center"><?= $teamsLive ?> </td>
                <td class="text-center"><?= $teamsPending ?></td>
                <td class="text-center"><?= $teamsNext ?></td>
                <td class="text-center"><?= ($league->launchDate) ? date('d/m/Y',strtotime($league->launchDate)) : null ?></td>
                <td class="text-center"><?= ($regSeason) ? $regSeason : "&cross;" ?></td>
                <td></td>
            </tr>
<?php
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th><?= $st['total'] ?></th>
                <th class="text-center"><?= $st['teamsLive'] ?></th>
                <th class="text-center"><?= $st['teamsPending'] ?></th>
                <th class="text-center"><?= $st['teamsNext']  ?></th>
                <th class="text-center">&nbsp;</th>
                <th class="text-center"><?= $st['ofr'] ?></th>
            </tr>
        </tfoot>
    </table>
</div>