<div class="container-fluid">
    <h2>Unused Bookings</h2>

    <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post" class="form-inline">
        <label for="coordinator">Coordinator</label>
        <select name="coordinator" id="coordinator" class="form-control">
            <option value="">All Coordinators</option><?php
            foreach ($data['coordinators'] as $coordinator) { ?>
                <option value="<?= $coordinator->id ?>"<?= (isset($_POST['coordinator']) && $coordinator->id == $_POST['coordinator']) ? " selected" : null ?>><?= $coordinator ?></option><?php
            } ?>
        </select>

        <label for="startDate" class="mx-1">From</label>
        <input type="date" name="startDate" id="startDate" class="form-control" value="<?= (isset($_POST['startDate'])) ? ($_POST['startDate']) : null ?>">

        <label for="endDate" class="mx-1">To</label>
        <input type="date" name="endDate" id="endDate" class="form-control" value="<?= (isset($_POST['endDate'])) ? ($_POST['endDate']) : null ?>">

        <button type="submit" class="btn btn-sm btn-primary">Filter</button>
    </form>

    <table class="table">

        <thead>
            <tr>
                <th>Time</th>
                <th>Date</th>
                <th>Pitch</th>
                <th>League</th>
                <th>Venue</th>
                <th>Duration</th>
                <th>&pound;/Hr</th>
                <th class="text-right">Total &pound;</th>
            </tr>
        </thead>

        <tbody><?php
        foreach ($data['unusedBookings'] as $booking) {
            if (!isset($leagues[$booking->leagueID])) $leagues[$booking->leagueID] = $booking->getLeague();
            if (isset($_POST['coordinator']) && $_POST['coordinator'] && $_POST['coordinator'] != $leagues[$booking->leagueID]->coordinator) continue;
            if (isset($_POST['startDate']) && $_POST['startDate'] && $_POST['startDate'] > $booking->startDate) continue;
            if (isset($_POST['endDate']) && $_POST['endDate'] && $_POST['endDate'] < $booking->startDate) continue;
            ?>
            <tr title="<?= "ID" . $booking->id ?>">
                <td><?= substr($booking->startTime,0,5) ?></td>
                <td><?= date('d/m/Y',strtotime($booking->startDate)) ?></td>
                <td><?= $booking->pitchCourt ?></td>
                <td><a href="/User/League/<?= $booking->leagueID ?>"><?= $leagues[$booking->leagueID] ?></a></td>
                <td><a href="/User/Venues?venueID=<?= $booking->venueID ?>"><?= $booking->getVenue() ?></a></td>
                <td><?= $booking->duration ?></td>
                <td><?= $booking->hourlyRate ?></td>
                <td class="text-right"><?= $booking->getCost() ?></td>
            </tr><?php
        } ?>
        </tbody>

    </table>
</div>