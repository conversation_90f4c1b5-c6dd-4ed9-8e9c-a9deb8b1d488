<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.5/xlsx.full.min.js"></script>
<script src="/js/export.js"></script>
<script src="/js/openregiexport.js"></script>
<link rel='stylesheet' type='text/css' href='/css/hub.leagues4you.css'>
<script src="/js/accordion.js"></script>



<div class=" container-fluid">
    <div style=" text-align: center; color: brown; ">
        <h1>Open Registration Report </h1>
    </div>
    <div>
        <form method="GET">
            <label for="coordinator">Coordinator:</label>
            <select name="coordinator" id="coordinator">
                <option value="">All</option>
                <?php foreach ($coordinators as $coordinatorId => $coordinator) : ?>
                    <?php $selected = ($coordinator->id == $selectedCoordinator) ? 'selected' : ''; ?>
                    <option value="<?php echo $coordinator->id; ?>" <?php echo $selected; ?>><?php echo $coordinator; ?></option>
                <?php endforeach; ?>
            </select>
            <button type="submit">Apply</button>
            <button id="openExportExcel">Export to Excel</button>


        </form>
        <table id="tableForOpen" class="table">
            <thead>
                <tr>
                    <th>League</th>
                    <th>Season Name</th>
                    <th>Season Launch Date</th>
                    <th>Teams Registered For Next Season</th>
                    <th>Teams Registered previous Season</th>
                    <th>Payment method load</th>
                    <th>Last Booking Date</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($leagues as $league_id => $league) : ?>
                    <?php if (!empty($league['seasons'])) : ?>
                        <tr>
                            <td rowspan="<?php echo count($league['seasons']); ?>">
                                <?php echo htmlspecialchars($league['name']); ?>
                            </td>
                            <?php $firstSeason = true; ?>
                            <?php foreach ($league['seasons'] as $season_id => $season) : ?>
                                <?php if (!$firstSeason) : ?>
                        <tr>
                        <?php endif; ?>
                            <td><?= $season['season_name']?></td>
                            <td><?php echo isset($season['launch_date']) ? date('d/m/Y', strtotime($season['launch_date'])) : 'N/A'; ?></td>
                            <td><?php echo isset($season['teams_registered_next_season']) ? htmlspecialchars($season['teams_registered_next_season']) : 'N/A'; ?></td>
                            <td><?php echo isset($season['teams_registered']) ? htmlspecialchars($season['teams_registered']) : 'N/A'; ?></td>
                            <td><?php echo isset($season['payment_method_loaded']) ? ($season['payment_method_loaded'] ? 'Yes' : 'No') : 'N/A'; ?></td>
                            <td><?php echo $season['final_fixture_date'] ? date('d/m/Y', strtotime($season['final_fixture_date'])) : 'N/A'; ?></td>
                        <?php if (!$firstSeason) : ?>
                        </tr>
                        <?php endif; ?>
                        <?php $firstSeason = false; ?>
                    <?php endforeach; ?>
                    </tr>
                <?php endif; ?>
                <?php endforeach; ?>
                <?php if (empty($leagues)) : ?>
                    <tr>
                        <td colspan="8">No data available</td>
                    </tr>
                <?php endif; ?>
            </tbody>
            <tfoot>
            <tr>
                <th>Total</th>
                <th></th>
                <th></th>
                <th><?php echo htmlspecialchars($teamsRegisteredNextSeason); ?></th>
                <th><?php echo htmlspecialchars($totalRegisteredTeams); ?></th>
                <th><?php echo htmlspecialchars($totalWildcardTeams); ?></th>
                <th></th>
            </tr>
        </tfoot>
        </table>
    </div>
</div>
