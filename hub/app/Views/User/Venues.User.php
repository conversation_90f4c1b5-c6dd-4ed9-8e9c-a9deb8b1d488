<link rel="stylesheet" href="/css/hub.riskassessment.css">
<link rel="stylesheet" type="text/css" href="/css/hub.liveTeamReport.css">
<?php
if (isset($data['venue']) && $data['venue']->id) { ?>
    <!-- Purchase Modal -->
    <div class="modal" id="purchasingModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Purchasing</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form action="" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="tab" value="purchasing">
                        <input type="hidden" name="purchasing[venueID]" id="purchasing[venueID]" value="<?= $data['venue']->id ?>">
                        <label for="purchasing.typeID">Type</label>
                        <select class="form-control" name="purchasing[typeID]" id="purchasing.typeID" required>
                            <option value="">Invoice or Credit?</option>
                            <option value="3">Invoice</option>
                            <option value="4">Credit</option>
                            <select>
                                <label for="purchasing.reference">Reference</label>
                                <input type="text" name="purchasing[reference]" id="purchasing.reference" class="form-control" placeholder="eg Invoice Number" required>
                                <label for="purchasing.taxDate">Date</label>
                                <input type="date" name="purchasing[taxDate]" id="purchasing.taxDate" class="form-control" required>
                                <label for="purchasing.total">Total</label>
                                <input type="text" name="purchasing[total]" id="purchasing.total" class="form-control" placeholder="eg 106.99" required>
                                <label for="transactionFile" class="form-label">Upload File</label>
                                <input class="form-control" type="file" name="transactionFile" id="transactionFile">
                                <button type="submit" class="btn btn-success mt-2">Save</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Taster Modal -->
    <div class="modal" id="tasterModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Taster Session</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="tab" value="taster">
                        <input type="hidden" name="taster[venueID]" id="taster[venueID]" value="<?= $data['venue']->id ?>">
                        <label for="taster.sportID">Sport</label>
                        <select class="form-control" name="taster[sportID]" id="taster.sportID" required>
                            <option value="">Sport</option>
                            <?php foreach ($data['sports'] as $sport) { ?>
                                <option value="<?= $sport->id ?>"><?= $sport ?></option>
                            <?php } ?>
                        </select>
                        <label for="taster.date">Date</label>
                        <input type="date" name="taster[date]" id="taster.date" class="form-control" required>
                        <label for="taster.time">Time</label>
                        <input type="time" name="taster[time]" id="taster.time" class="form-control" required>
                        <label for="taster.charge">Charge</label>
                        <input type="text" name="taster[charge]" id="taster.charge" class="form-control" placeholder="eg 5.00" required>
                        <label for="taster.coordinators">Coordinators</label>
                        <select name="taster[coordinatorID]" id="taster.coordinators" class="form-control">
                            <option value="">Select Coordinator</option>
                            <?php foreach ($data['coordinators'] as $coordinator): ?>
                                <option class="Coordinator" data-email="<?= $coordinator->email ?>" data-tel="<?= $coordinator->mobile ?>" value="<?php echo $coordinator->id; ?>">
                                    <?php echo $coordinator->firstname . " " . $coordinator->lastname; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <label for="game_location">Game Location:</label>
                        <select name="taster[location]" id="game_location" class="form-control">
                            <option value="indoor">Indoor</option>
                            <option value="outdoor">Outdoor</option>
                        </select>
                        <label for="taster.coordinatorName">Coordinator Name</label>
                        <input type="text" readonly name="taster[coordinatorName]" id="taster.coordinatorName" class="form-control" placeholder="Name of Coordinator">
                        <label for="taster.coordinatorEmail">Coordinator Email</label>
                        <input type="email" readonly name="taster[coordinatorEmail]" id="taster.coordinatorEmail" class="form-control">
                        <label for="taster.CoordinatorTel">Coordinator Tel</label>
                        <input type="text" readonly name="taster[CoordinatorTel]" id="taster.CoordinatorTel" class="form-control">
                        <?php if (!str_contains($data['venue']->name, 'WILDCARD')) { ?>
                            <label for="taster.fbPageLink">Facebook Page Link</label>
                            <input type="text" name="taster[fbPageLink]" id="taster.fbPageLink" class="form-control" placeholder="paste link with https://">
                        <?php } ?>
                        <div class="d-flex pt-2 gap-4">
                            <div class="d-flex align-items-center">
                                <span class="slider-button-title me-2">WildCard: </span>
                                <label class="switch">
                                    <input type="checkbox" value="1" name="taster[is_wildcard]" id="is_wildcard">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="slider-button-title me-2">Tournament: </span>
                                <label class="switch">
                                    <input type="checkbox" value="1" name="taster[is_tournament]" id="is_tournament">
                                    <span class="slider round"></span>
                                </label>
                            </div>
                        </div>

                        <div id="tournamentFields">
                            <label for="taster.tournamentName">Tournament Name</label>
                            <input type="text" name="taster[tournamentName]" id="taster.tournamentName" class="form-control">

                            <label for="taster.numberOfWeeks" class="d-none">Number of Weeks</label>
                            <input type="number" name="taster[numberOfWeeks]" id="taster.numberOfWeeks" class="form-control d-none" min="1" max="10" value="3">
                        </div>

                        <br>
                        <button type="submit" class="btn btn-success mt-2">Save</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php
} ?>
<div class="container-fluid">
    <h2>Venue Management</h2>
    <form action="" method="post">
        <select class="form-control" name="venueID" id="venueID" onchange="form.submit()">
            <option value="">...</option><?php
                                            if ($data['venues']) {
                                                foreach ($data['venues'] as $venue) { ?>
                    <option value="<?php echo $venue->id; ?>" <?php if ($data['venueID'] == $venue->id) echo ' selected'; ?>>
                        <?php echo $venue->__toString(); ?></option>
            <?php
                                                }
                                            } ?>
        </select>
    </form>

    <ul class="nav nav-tabs mt-4" id="myTab" role="tablist">
        <li class="nav-item">
            <a class="nav-link<?php if ($data['tab'] == "main") echo ' active'; ?>" id="main-tab" data-toggle="tab" href="#main" role="tab" aria-controls="main" aria-selected="<?php echo ($data['tab'] == "main") ? 'true' : 'false'; ?>">Main</a>
        </li><?php
                if (isset($data['venue']) && $data['venue']->id) { ?>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "notes") echo ' active'; ?>" id="notes-tab" data-toggle="tab" href="#notes" role="tab" aria-controls="notes" aria-selected="<?= ($data['tab'] == "notes") ? 'true' : 'false' ?>">Notes</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "leagues") echo ' active'; ?>" id="leagues-tab" data-toggle="tab" href="#leagues" role="tab" aria-controls="leagues" aria-selected="<?php echo ($data['tab'] == "leagues") ? 'true' : 'false'; ?>">Leagues</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "facilities") echo ' active'; ?>" id="facilities-tab" data-toggle="tab" href="#facilities" role="tab" aria-controls="facilities" aria-selected="<?php echo ($data['tab'] == "facilities") ? 'true' : 'false'; ?>">Facilities</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "map") echo ' active'; ?>" id="map-tab" data-toggle="tab" href="#map" role="tab" aria-controls="map" aria-selected="<?php echo ($data['tab'] == "map") ? 'true' : 'false'; ?>">Map</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "nearby") echo ' active'; ?>" id="nearby-tab" data-toggle="tab" href="#nearby" role="tab" aria-controls="nearby" aria-selected="<?php echo ($data['tab'] == "nearby") ? 'true' : 'false'; ?>">Nearby</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "weather") echo ' active'; ?>" id="weather-tab" data-toggle="tab" href="#weather" role="tab" aria-controls="weather" aria-selected="<?php echo ($data['tab'] == "weather") ? 'true' : 'false'; ?>">Weather</a>
            </li>

            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "taster") echo ' active'; ?>" id="taster-tab" data-toggle="tab" href="#taster" role="tab" aria-controls="taster" aria-selected="<?php echo ($data['tab'] == "taster") ? 'true' : 'false'; ?>">Tasters</a>
            </li>

            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "finances") echo ' active'; ?>" id="finances-tab" data-toggle="tab" href="#finances" role="tab" aria-controls="finances" aria-selected="<?php echo ($data['tab'] == "finances") ? 'true' : 'false'; ?>">Finance</a>
            </li>

            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "risks") echo ' active'; ?>" id="risks-tab" data-toggle="tab" href="#risks" role="tab" aria-controls="risks" aria-selected="<?php echo ($data['tab'] == "risks") ? 'true' : 'false'; ?>">Risk Assessment</a>
            </li>
            <li class="nav-item">
                <a class="nav-link<?php if ($data['tab'] == "incidents") echo ' active'; ?>" id="incidents-tab" data-toggle="tab" href="#incidents" role="tab" aria-controls="incidents" aria-selected="<?php echo ($data['tab'] == "incidents") ? 'true' : 'false'; ?>">Incident Report</a>
            </li>
        <?php
                } ?>
    </ul>

    <div class="tab-content mt-4" id="myTabContent">

        <div class="tab-pane fade<?php if ($data['tab'] == "main") echo ' show active'; ?>" id="main" role="tabpanel" aria-labelledby="main">
            <h3>Main</h3>
            <form action="/User/Venues" method="post">
                <input type="hidden" name="venue[id]" value="<?php echo $data['venue']->id; ?>">
                <label for="venue.name">Name</label>
                <input type="text" name="venue[name]" id="venue.name" class="form-control" value="<?php echo $data['venue']->getName(); ?>">
                <label for="venue.address1">Address 1</label>
                <input type="text" name="venue[address1]" id="venue.address1" class="form-control" value="<?php echo $data['venue']->getAddress1(); ?>" required>
                <label for="venue.address1">Address 2</label>
                <input type="text" name="venue[address2]" id="venue.address2" class="form-control" value="<?php echo $data['venue']->getAddress2(); ?>">
                <label for="venue.town">Town (* If entered, this will be used as location for Couch2Court
                    sessions)</label>
                <input type="text" name="venue[town]" id="venue.town" class="form-control" value="<?php echo $data['venue']->getTown(); ?>" required>
                <label for="venue.postcode">Post Code</label>
                <input type="text" name="venue[postcode]" id="venue.postcode" class="form-control" value="<?php echo $data['venue']->getPostcode(); ?>" required>
                <div class="mb-3">
                    <label for="venue_status" class="form-label">Status</label>
                    <select name="venue[status]" id="venue_status" class="form-control">
                        <option value="1" <?php if ($data['venue']->getStatus() == 1) echo 'selected'; ?>>Active</option>
                        <option value="0" <?php if ($data['venue']->getStatus() == 0) echo 'selected'; ?>>Inactive</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary mt-4">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?= ($data['tab'] == "notes") ? ' show active' : null ?>" id="notes" role="tabpanel" aria-labelledby="notes">
            <h3>Notes</h3>
            <p>Venue contact details, gate codes and any other useful info</p>
            <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                <label for="notes.notes">Notes</label>
                <textarea name="notes[notes]" id="notes.notes" cols="30" rows="10" class="form-control"><?= $data['venue']->notes ?></textarea>
                <button type="submit" class="btn btn-primary" name="notes[id]" value="<?= $data['venue']->id ?>">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "leagues") echo ' show active'; ?>" id="leagues" role="tabpanel" aria-labelledby="leagues">
            <h3>Leagues</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>League</th>
                    </tr>
                </thead>
                <tbody><?php
                        if ($data['leagues'])
                            foreach ($data['leagues'] as $league) { ?>
                        <tr>
                            <td><?= $league ?></td>
                        </tr><?php
                            }
                                ?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "facilities") echo ' show active'; ?>" id="facilities" role="tabpanel" aria-labelledby="facilities">
            <h3>Facilities</h3>
            <form action="/User/Venues" method="post">
                <input type="hidden" name="facilities[id]" value="<?php echo $data['venue']->id; ?>">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="facilities[facility_freeParking]" id="facilities.facility_freeParking" value="1" <?php if ($data['venue']->facilityAvailable('facility_freeParking')) echo ' checked'; ?>>
                    <label class="form-check-label" for="facilities.facility_freeParking">
                        Free Parking
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="facilities[facility_paidParking]" id="facilities.facility_paidParking" value="1" <?php if ($data['venue']->facilityAvailable('facility_paidParking')) echo ' checked'; ?>>
                    <label class="form-check-label" for="facilities.facility_paidParking">
                        Paid Parking
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="facilities[facility_changingRooms]" id="facilities.facility_changingRooms" value="1" <?php if ($data['venue']->facilityAvailable('facility_changingRooms')) echo ' checked'; ?>>
                    <label class="form-check-label" for="facilities.facility_changingRooms">
                        Changing Rooms
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="facilities[facility_showers]" id="facilities.facility_showers" value="1" <?php if ($data['venue']->facilityAvailable('facility_showers')) echo ' checked'; ?>>
                    <label class="form-check-label" for="facilities.facility_showers">
                        Showers
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="facilities[facility_cafe]" id="facilities.facility_cafe" value="1" <?php if ($data['venue']->facilityAvailable('facility_cafe')) echo ' checked'; ?>>
                    <label class="form-check-label" for="facilities.facility_cafe">
                        Cafe
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="facilities[facility_bar]" id="facility.facility_bar" value="1" <?php if ($data['venue']->facilityAvailable('facility_bar')) echo ' checked'; ?>>
                    <label class="form-check-label" for="facility.facility_bar">
                        Bar
                    </label>
                </div>
                <button type="submit" class="btn btn-primary mt-4">Save</button>
            </form>
        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "map") echo ' show active'; ?>" id="map" role="tabpanel" aria-labelledby="map">
            <h3>Map</h3>
            <form action="/User/Venues" method="post">
                <input type="hidden" name="map[id]" value="<?php echo $data['venue']->id; ?>">
                <label for="map.lat">Latitude</label>
                <input type="text" name="map[lat]" id="map.lat" class="form-control" value="<?php echo $data['venue']->getLat(); ?>">
                <label for="map.lng">Longitude</label>
                <input type="text" name="map[lng]" id="map.lng" class="form-control" value="<?php echo $data['venue']->getLng(); ?>">
                <button type="submit" class="btn btn-primary mt-4">Save</button>
            </form>

        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "nearby") echo ' show active'; ?>" id="nearby" role="tabpanel" aria-labelledby="nearby">
            <h3>Venues Nearby</h3><?php
                                    $data['venue']->fetchNearby(); ?>
            <table class="table-sm">
                <thead>
                    <tr>
                        <th>Venue</th>
                        <th class="text-right">Distance</th>
                    </tr>
                </thead>
                <tbody><?php
                        if ($data['venue']->nearby) {
                            foreach ($data['venue']->nearby as $venueID => $distance) {
                                $v = new \Venue($venueID); ?>
                            <tr>
                                <td><?php echo $v->getName() . ", " . $v->getPostcode(); ?></td>
                                <td class="text-right"><?php echo $distance; ?> km (<?php echo (int)($distance * 0.621371); ?>
                                    miles)</td>
                            </tr><?php
                                }
                            } ?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "weather") echo ' show active'; ?>" id="weather" role="tabpanel" aria-labelledby="weather">
            <h3>Weather</h3><?php
                            $weather = json_decode($data['weather']->json ?? ''); ?>
            <?php if ($weather) { ?>
                <p>Current Weather: <?php echo (int)$weather->current->temp; ?>&deg;
                    <?php echo $weather->current->weather[0]->main; ?></p>
            <?php } ?>
            <h4>Forecast Ahead</h4>
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Day</th>
                        <th>Forecast</th>
                        <th>Temp</th>
                        <th>Weather</th>
                    </tr>
                </thead>
                <tbody><?php
                        if ($weather)
                            foreach ($weather->daily as $d) {
                                if (date('Y-m-d', $d->dt) == date('Y-m-d')) continue; ?>
                        <tr>
                            <td><?php echo date('jS F', $d->dt); ?></td>
                            <td><img src="https://openweathermap.org/img/wn/<?php echo $d->weather[0]->icon; ?>.png" alt="">
                            </td>
                            <td><?php echo (int)$d->temp->day; ?>&deg;</td>
                            <td><?php echo $d->weather[0]->main; ?> (<?php echo ucwords($d->weather[0]->description); ?>)
                            </td>
                        </tr><?php
                            }
                                ?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?php if ($data['tab'] == "purchasing") echo ' show active'; ?>" id="purchasing" role="tabpanel" aria-labelledby="purchasing">
            <h3>Invoices &amp; Credits | <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#purchasingModal">&plus;</button></h3>
            <?php
            if (isset($data['transactions'])) {
                $total = 0; ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Ref</th>
                            <th>Date</th>
                            <th>File</th>
                            <th class="text-right">Total</th>
                        </tr>
                    </thead>
                    <tbody><?php
                            foreach ($data['transactions'] as $transaction) {
                                $total += $transaction->total;
                            ?>
                            <tr>
                                <td><?= $transaction->getType() ?></td>
                                <td><?= $transaction->reference ?></td>
                                <td><?= date('d/m/Y', strtotime($transaction->taxDate)) ?></td>
                                <td><?= $transaction->getFile() ?></td>
                                <td class="text-right"><?= $transaction->total ?></td>
                            </tr>
                    <?php
                            }
                        } ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4">Total</th>
                            <th class="text-right"><?= number_format($total, 2) ?></th>
                        </tr>
                    </tfoot>
                </table>
        </div>

        <!-- Riskassesmentwork start  -->

        <div class="tab-pane fade<?php if ($data['tab'] == "taster") echo ' show active'; ?>" id="taster" role="tabpanel" aria-labelledby="taster">
            <h3>Taster Sessions | <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#tasterModal">&plus;</button></h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Sport</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Charge</th>
                        <th>Attendees</th>
                        <th>...</th>
                    </tr>
                </thead>
                <tbody><?php
                        foreach ($data['tasterSessions'] as $tasterSession) { ?>
                        <tr>
                            <td><?= new Sport($tasterSession->sportID) ?></td>
                            <td><?= date('d/m/y', strtotime($tasterSession->date)) ?></td>
                            <td><?= substr($tasterSession->time, 0, 5) ?></td>
                            <td><?= $tasterSession->charge ?></td>
                            <td><?= $tasterSession->attendeeCount() ?></td>
                            <td><a href="/User/TasterSession/<?= $tasterSession->id ?>" class="btn btn-sm">More</a></td>
                        </tr><?php
                            } ?>
                </tbody>
            </table>
        </div>

        <div class="tab-pane fade<?= ($data['tab'] == "finances") ? ' show active' : null ?>" id="finances" role="tabpanel" aria-labelledby="finances">
            <h3>Finances</h3>
            <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                <input type="hidden" name="tab" value="finances">
                <label for="venueFinancials.sageAccount">Sage Account Ref</label>
                <input class="form-control" type="text" name="venueFinancials[sageAccount]" id="venueFinancials.sageAccount" value="<?= $data['venue']->sageAccount ?>">
                <button type="submit" class="btn btn-primary mt-2" name="venueFinancials[id]" value="<?= $data['venue']->id ?>">Update</button>
            </form>
        </div>

        <?php if ($data['venueID']) { ?>
            <div class="tab-pane fade<?= ($data['tab'] == "risks") ? ' show active' : null ?>" id="risks" role="tabpanel" aria-labelledby="risks">
                <h3>Risk Assessment <button id="toggle-form">+</button></h3>
                <table class="table" id="history-form" class=" col-md-12">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col">Date of Assessment</th>
                            <th scope="col">Review Date</th>
                            <th scope="col">Assessor</th>
                            <!-- <th scope="col">Venue</th> -->
                            <th scope="col">Status</th>
                            <th scope="col">Action</th>
                        </tr>
                    </thead>
                    <tbody>

                        <?php foreach ($data['allRisksAssesment'] as $row) { ?>
                            <tr>
                                <td><?= $row->getDateOfAssessment() ?></td>
                                <td><?= $row->reviewDate() ?></td>
                                <td><?= $row->firstname . ' ' . @$row->lastname ?></td>
                                <td><?= $row->getStatus() ?></td>
                                <td>
                                    <a href="/User/RiskAssessment/<?= $row->id ?>" class='btn edit-button'><i class='fa fa-edit'></i></a>/
                                    <a href='/User/RiskAssessment/<?= $row->id ?>?export=true' class='btn edit-button'><i class='fa fa-download'></i></a>/
                                    <a href="/User/RiskAssessment/<?= $row->id ?>/duplicate" class='btn' onclick='return confirm(`Are you sure you want to duplicate?`)'><i class='fa fa-copy'></i></a>/
                                    <a href="/User/RiskAssessment/<?= $row->id ?>/delete" class='btn' onclick='return confirm(`Are you sure you want to delete?`)'><i class='fa fa-trash'></i></a>
                                </td>
                            </tr>
                        <?php } ?>
                    </tbody>
                </table>
                <form action="<?= $_SERVER['REQUEST_URI'] ?>" onsubmit="return checkRiskAssessment() && validateForm();" method="post" enctype="multipart/form-data">
                    <!-- <input type="hidden" name="id" value="<?php echo isset($data['editData']) ? $data['editData']['id'] : ''; ?>"> -->
                    <input type="hidden" name="venue_id" value="<?php echo isset($data['venues']) ? $data['venue']->getId() : ''; ?>">

                    <div id="risk-assessment-form" class="container col-md-12" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mt-5">
                                <label class="text-center">Date of Assessment : </label><input style="width: 16%;margin-top: -5%;margin-left: 24%;" type="date" name="date_of_assessment" value="<?= isset($data['editData']['date_of_assessment']) ? date('d-m-Y', strtotime($data['editData']['date_of_assessment'])) : ''; ?>" id="Assessment.date" class="form-control" required>
                                <label class="text-center">Review Date (Plus 12 months from date of assessment)</label>
                                <!-- <input style="width: 16%;margin-top: -4%;margin-left: 44%;" type="date" name="date_of_coordinator" value="<?php echo isset($data['date_of_coordinator']) ? $data['date_of_coordinator'] : ''; ?>" id="reviewdate.date" class="form-control" required> -->
                            </div>
                            <div class="col-md-6 mb-2">
                                <label class="text-center" for="additional_info">Additional Information : </label>
                                <textarea name="form[additional_info]" id="additional_info" class="form-control" required rows="4"><?php echo isset($risk['additional_info']) ? $risk['additional_info'] : '' ?></textarea>
                            </div>
                        </div>

                        <?php require_once 'Forms/RiskAssessment.php'; ?>
                    </div>
                </form>
            </div>
        <?php } ?>
        <div class="tab-pane fade<?php if ($data['tab'] == "incidents") echo ' show active'; ?>" id="incidents" role="tabpanel" aria-labelledby="incidents">
            <h3>Incident Report</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th scope="col" style="width: 3%;">#</th>
                        <th scope="col" style="width: 15%;" class="align-middle text-center">Venue Name</th>
                        <th scope="col" class="align-middle text-center">Coordinator</th>
                        <th scope="col" style="width: 10%;" class="align-middle text-center">Date of Incident</th>
                        <th scope="col" class="align-middle text-center">Injured person name</th>
                        <th scope="col" class="align-middle text-center">Injured person contact</th>
                        <th scope="col" class="align-middle text-center">Status</th>
                        <th scope="col" class="align-middle text-center">Important</th>
                        <th scope="col" class="align-middle text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data["incident_report"] as $key =>  $incident) { ?>
                        <tr>
                            <th scope="row">
                                <?= $key + 1 ?>
                            </th>
                            <td>
                                <?= $incident['venue'] ?>
                            </td>
                            <td><?= $incident['coordinator'] ?></td>
                            <td><?= isset($incident['date_of_incident']) ? ukDateFormat($incident['date_of_incident']) : "" ?></td>
                            <td><?= $incident['name'] ?></td>
                            <td><?= $incident['contact_number'] ?></td>
                            <td style="text-transform: capitalize;text-align: center;">
                                <?= $incident['status'] ?>
                            </td>
                            <td style="text-align: center;">
                                <?php if (isset($incident['is_important']) && $incident['is_important'] == '1') { ?>
                                    <div class="wrapper">
                                        <span href="#" class="important-badge">Yes</span>
                                    </div>
                                <?php } else { ?>
                                    <span>No</span>
                                <?php } ?>
                            </td>
                            <td class="td-button text-center">
                                <input type="hidden" class="incident_report_id" name="" value="<?= $incident['id'] ?>">
                                <a class="btn btn-primary" href="/Coordinator/incidentReportDetails/<?= $incident['id'] . '?incidentReportStatus=' . str_replace(" ", "_", $incident['status']) ?>" type="button">Show</a>
                                <?php if ($incident['status'] != 'closed') { ?>
                                    <a class="btn btn-warning" href="/Coordinator/incidentReportUpdate/<?= $incident['id'] . '?incidentReportStatus=' . str_replace(" ", "_", $incident['status']) ?>" type="button" style="margin-left: 5px;">Edit</a>
                                <?php } ?>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get references to select and input elements
            var selectCoordinator = document.getElementById('taster.coordinators');
            var inputCoordinatorName = document.getElementById('taster.coordinatorName');
            var inputCoordinatorEmail = document.getElementById('taster.coordinatorEmail');
            var inputCoordinatorTel = document.getElementById('taster.CoordinatorTel');

            // Check if the elements are found
            if (!selectCoordinator || !inputCoordinatorName || !inputCoordinatorEmail || !inputCoordinatorTel) {
                console.error('One or more elements not found');
                return;
            }

            // Add change event listener to the select element
            selectCoordinator.addEventListener('change', function() {
                // Get the selected option
                var selectedOption = selectCoordinator.options[selectCoordinator.selectedIndex];

                // Check if selectedOption is null
                if (!selectedOption) {
                    console.error('Selected option is null');
                    return;
                }

                // Get data attributes from the selected option
                var email = selectedOption.getAttribute('data-email');
                var tel = selectedOption.getAttribute('data-tel');
                var coordinatorName = selectedOption.textContent;

                // Set values of input fields
                inputCoordinatorName.value = coordinatorName.trim();
                inputCoordinatorEmail.value = email;
                inputCoordinatorTel.value = tel;
            });
        });

        // Add tournament switch toggle handler
        document.addEventListener('DOMContentLoaded', function() {
            var tournamentSwitch = document.getElementById('is_tournament');
            var tournamentFields = document.getElementById('tournamentFields');

            if (tournamentSwitch && tournamentFields) {
                // Initial state
                tournamentFields.style.display = tournamentSwitch.checked ? 'block' : 'none';

                // Add change event listener
                tournamentSwitch.addEventListener('change', function() {
                    tournamentFields.style.display = this.checked ? 'block' : 'none';
                });
            }

            const form = document.querySelector('#tasterModal form');
            const button = form?.querySelector('button[type="submit"]');

            form?.addEventListener('submit', e => {
                if (button?.disabled) {
                    e.preventDefault();
                    return;
                }

                button.disabled = true;
            });
        });
    </script>

</div>