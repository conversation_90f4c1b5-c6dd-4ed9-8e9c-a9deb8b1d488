<div class="container-fluid">
    <h1>Venue Bookings</h1>
    <form action="" method="post" class="form-inline">
        <label for="bookingFilter.month">Month</label>
        <select name="bookingFilter[month]" id="bookingFilter.month" class="form-control mx-1">
            <?php
            for ($i = 1; $i<=12; $i++) { ?>
                <option value="<?= $i ?>" <?= ($_POST['bookingFilter']['month']==$i) ? " selected":null?>><?= ($i < 10) ? "0$i" : $i ?></option><?php
            } ?>
        </select>
        <label for="bookingFilter.year">Year</label>
        <select name="bookingFilter[year]" id="bookingFilter.year" class="form-control mx-1">
            <?php
            for ($i = 2020; $i<=date('Y'); $i++) { ?>
                <option value="<?= $i ?>" <?= ($_POST['bookingFilter']['year']==$i) ? " selected":null?>><?= $i ?></option><?php
            } ?>
        </select>
        <button type="submit" class="btn btn-sm ml-1 btn-primary">Go</button>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>Venue</th>
                <th class="text-right">Order</th>
                <th class="text-right">Credit</th>
                <th class="text-right">Rcvd</th>
                <th class="text-right">Due</th>
            </tr>
        </thead>
        <tbody><?php
        $ordered = $received = $creditValue = 0;
        foreach ($data['totalBookings'] as $totalBooking) { 
            $venue = new Venue($totalBooking['venueID']);
            $ordered += $totalBooking['total'];
            $creditValue += $totalBooking['creditValue'];
            // $transactionTotal = PurchaseTransaction::VenueTotal($venue,$_POST['startDate'],$_POST['endDate']);
            $transactionTotal = PurchaseTransactionItem::TotalMonthYear($venue,$_POST['bookingFilter']['month'],$_POST['bookingFilter']['year']);

            $received += $transactionTotal;
            ?>
            <tr>
                <td><a href="/User/Venues&venueID=<?= $venue->id ?>"><?= $venue ?></a></td>
                <td class="text-right"><?= number_format($totalBooking['total'],2) ?></td>
                <td class="text-right"><?= number_format($totalBooking['creditValue'],2) ?></td>
                <td class="text-right"><?= number_format($transactionTotal,2) ?></td>
                <td class="text-right"><?= number_format(($totalBooking['total']-$totalBooking['creditValue']-$transactionTotal),2) ?></td>
            </tr><?php
        } ?>
        </tbody>
        <tfoot>
            <tr>
                <th>Total</th>
                <th class="text-right"><?= number_format($ordered,2) ?></th>
                <th class="text-right"><?= number_format($creditValue,2) ?></th>
                <th class="text-right"><?= number_format($received,2) ?></th>
                <th class="text-right"><?= number_format(($ordered - $received),2) ?></th>
            </tr>
        </tfoot>
    </table>
</div>