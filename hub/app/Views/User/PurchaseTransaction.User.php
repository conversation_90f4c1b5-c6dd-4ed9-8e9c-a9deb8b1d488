<div class="container-fluid p-4">
    <h1>Purchase Transaction</h1>
    <form action="" method="post" enctype="multipart/form-data">
        <input  type="hidden" name="purchaseTransaction[id]" id="purchaseTransaction[id]" value="<?= $data['purchaseTransaction']->id ?>">

        <div class="row">
            <div class="col-xs-12 col-sm-6">
                <label for="purchaseTransaction.venueID">Venue</label>
                <select name="purchaseTransaction[venueID]" id="purchaseTransaction.venueID" class="form-control" required>
                    <option value="">Venue</option><?php
                    foreach ($data['venues'] as $venue) { ?>
                        <option value="<?= $venue->id ?>"<?= ($venue->id == $data['purchaseTransaction']->venueID) ? ' selected' : null ?>><?= $venue ?></option><?php
                    } ?>
                </select>
            </div>
            <div class="col-xs-12 col-sm-6">
                <label for="purchaseTransaction.typeID">Type</label>
                <select class="form-control" name="purchaseTransaction[typeID]" id="purchaseTransaction.typeID" required>
                    <option value="">Invoice or Credit?</option>
                    <option value="3"<?= (3 == $data['purchaseTransaction']->typeID) ? ' selected' : null ?>>Invoice</option>
                    <option value="4"<?= (4 == $data['purchaseTransaction']->typeID) ? ' selected' : null ?>>Credit</option>
                <select>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 col-sm-6">
                <label for="purchaseTransaction.reference">Reference</label>
                <input type="text" name="purchaseTransaction[reference]" id="purchaseTransaction.reference" class="form-control" placeholder="eg Invoice Number" value="<?= $data['purchaseTransaction']->reference ?>" required>
            </div>
            <div class="col-xs-12 col-sm-6">
                <label for="purchaseTransaction.taxDate">Date</label>
                <input type="date" name="purchaseTransaction[taxDate]" id="purchaseTransaction.taxDate" class="form-control" value="<?= $data['purchaseTransaction']->taxDate ?>" required>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 col-sm-6">
                <label for="purchaseTransaction.total">Total</label>
                <input type="text" name="purchaseTransaction[total]" id="purchaseTransaction.total" class="form-control" placeholder="eg 106.99" value="<?= $data['purchaseTransaction']->total ?>" required>
            </div>
            <div class="col-xs-12 col-sm-6">
                <?php
            if ($data['isClosed'] !== true) { ?>
                <label for="transactionFile" class="form-label">Upload File</label>
                <input class="form-control" type="file" name="transactionFile" id="transactionFile"><?php
            } ?>
            </div>
        </div>

        <button type="submit" class="btn btn-success mt-2"<?= ($data['isClosed']===true)?" disabled":null?>>Save</button>

    </form>
    <h3>Items</h3>
    <table class="table">
        <thead>
            <tr>
                <th>Calendar Month</th>
                <th>VAT Code</th>
                <th colspan="2">Total</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['purchaseTransactionItems'] as $purchaseTransactionItem) {?>
            <tr>
                <td><?= $purchaseTransactionItem->month ?> / <?= $purchaseTransactionItem->year ?></td>
                <td><?= $purchaseTransactionItem->vatCode ?></td>
                <td><?= $purchaseTransactionItem->total ?></td>
                <td>
                    <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                        <button type="submit" class="btn btn-sm btn-danger" name="removeitem" value="<?= $purchaseTransactionItem->id ?>">&times;</button>
                    </form>
                </td>
            </tr><?php
        } ?>
        </tbody>
    </table>
    <?php
    if (!$data['purchaseTransaction']->balanced) {?>
    <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
        <table class="table">
            <input type="hidden" name="newitem[mainID]" value="<?= $data['purchaseTransaction']->id ?>">
            <tbody>
                <tr>
                    <th>
                        <select class="form-control" name="newitem[month]" id="newitem.month" class="form-contol" style="display: inline-block; width: auto;" required>
                            <option value="">Month</option>
                            <option value="1">Jan</option>
                            <option value="2">Feb</option>
                            <option value="3">Mar</option>
                            <option value="4">Apr</option>
                            <option value="5">May</option>
                            <option value="6">Jun</option>
                            <option value="7">Jul</option>
                            <option value="8">Aug</option>
                            <option value="9">Sep</option>
                            <option value="10">Oct</option>
                            <option value="11">Nov</option>
                            <option value="12">Dec</option>
                        </select>
                        <select class="form-control" name="newitem[year]" id="newitem.year" style="display: inline-block; width: auto" required>
                            <option value="">Year</option>
                            <option value="2020">2020</option>
                            <option value="2021">2021</option>
                            <option value="2022">2022</option>
                        </select>
                    </th>
                    <th>
                        <select class="form-control" name="newitem[vatCode]" id="newitem.vatCode" style="display: inline-block; width: auto" required>
                            <option value="">VAT Code</option>
                            <option value="T0">T0 (Zero VAT)</option>
                            <option value="T2">T2 (VAT Incl)</option>
                        </select>
                    </th>
                    <th>
                        <input type="text" class="form-control" name="newitem[total]" id="newitem.total" style="display: inline-block; width: auto" placeholder="0.00" required>
                    </th>
                    <th>
                        <button class="btn btn-sm btn-primary" type="submit" name="tab" value="items">Add</button>
                    </th>
                </tr>
            </tbody>
        </table>
    </form><?php
    } ?>
</div>