<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1" role="dialog" aria-labelledby="userModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="userModalLabel">User</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form action="" method="post">
        <div class="modal-body">
            <input type="hidden" name="user[id]" id="user.id">
            <label for="user.firstname">First Name</label>
            <input type="text" name="user[firstname]" id="user.firstname" class="form-control" value="">
            <label for="user.lastname">Last Name</label>
            <input type="text" name="user[lastname]" id="user.lastname" class="form-control">
            <label for="user.email">Email</label>
            <input type="email" name="user[email]" id="user.email" class="form-control">
            <label for="user.mobile">Mobile</label>
            <input type="tel" name="user[mobile]" id="user.mobile" class="form-control">
            <div class="custom-control custom-switch mt-2">
                <input type="checkbox" class="custom-control-input" id="user.isAdmin" name="user[isAdmin]" value="1">
                <label class="custom-control-label" for="user.isAdmin">Coordinator?</label>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary">Save</button>
        </div>
    </form>
    </div>
  </div>
</div>
<!-- Password Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" role="dialog" aria-labelledby="passwordModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="passwordModalLabel">New Password</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form action="" method="post">
        <div class="modal-body">
            <p>Enter a new password for <span id="newpassword.username"></span></p>
            <input type="hidden" name="newPassword[userID]" id="newpassword.userID">
            <label for="newpassword.password1">Enter Password</label>
            <input type="text" name="newPassword[password1]" id="newpassword.password1" class="form-control">
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="submit" class="btn btn-primary">Save</button>
        </div>
    </form>
    </div>
  </div>
</div>
<div class="container-fluid">
    <h2><!--<i class="fa fa-search toggle-search" id="toggleSearch" style="font-size: 1.6rem; color: #999; cursor: pointer;"></i>-->User Management | <button type="button" class="btn btn-sm btn-info" data-toggle="modal" data-target="#userModal">New</button></h2>
    <input type="search" name="search" id="search" class="form-control mb-2" placeholder="Start typing...">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Email</th>
                <th>Mobile</th>
                <th>...</th>
                <th>...</th>
                <th>...</th>
            </tr>
        </thead>
        <tbody id="userTableBody"><?php
        foreach ($data['users'] as $user) { ?>
            <tr>
                <td class="userinfo firstname"><?php echo $user->getFirstname();?></td>
                <td class="userinfo lastname"><?php echo $user->getLastname();?></td>
                <td class="userinfo email"><?php echo $user->getEmail();?></td>
                <td class="userinfo mobile"><?php echo $user->getMobile();?></td>
                <td>
                    <button class="btn btn-sm btn-warning edit-user" 
                    data-userid="<?php echo $user->id;?>"
                    data-firstname="<?php echo $user->getFirstname();?>"
                    data-lastname="<?php echo $user->getLastname();?>"
                    data-email="<?php echo $user->getEmail();?>"
                    data-mobile="<?php echo $user->getMobile();?>"
                    data-isadmin="<?= $user->isAdmin ?>"
                    >edit</button></td>
                <td><?php
                if (!$user->activated()) {?>
                    <form action="" method="post" onsubmit="return confirm('Confirm you wish to activate this user');">
                        <input type="hidden" name="activateUserID" value="<?php echo $user->id;?>">
                        <button type="submit" class="btn btn-sm btn-info">activate</button>
                    </form><?php
                } else { ?>
                    <button type="button" class="btn btn-sm btn-success passwordChange" data-userid="<?php echo $user->id;?>" data-username="<?php echo $user->__toString();?>">password</button><?php
                } ?>
                </td>
                <td>
                    <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
                        <button type="submit" class="btn-sm <?= ($user->deleted) ? "btn-danger":"btn-outline-danger" ?>" value="<?= $user->id ?>" name="<?= ($user->deleted) ? "unarchiveUser":"archiveUser" ?>" value="<?= $user->id ?>"><?= ($user->deleted) ? "reinstate":"remove" ?></button>
                    </form>
                </td>
            </tr><?php
        } ?>
        </tbody>
    </table>    
</div>

<script>
    function filterSearch() {
        var input, filter, ul, li, a, i, txtValue;
        input = document.getElementById('search');
        
        filter = input.value.toUpperCase();
        tbody = document.getElementById("userTableBody");
        tr = tbody.getElementsByTagName('tr');

        // Loop through all list items, and hide those who don't match the search query
        for (i = 0; i < tr.length; i++) {
            tr[i].classList.add("d-none");
            a = tr[i].getElementsByClassName("firstname")[0];
            txtValue = a.textContent || a.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                tr[i].classList.remove("d-none");
                continue;
            }
            a = tr[i].getElementsByClassName("lastname")[0];
            txtValue = a.textContent || a.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                tr[i].classList.remove("d-none");
                continue;
            }
            a = tr[i].getElementsByClassName("email")[0];
            txtValue = a.textContent || a.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                tr[i].classList.remove("d-none");
                continue;
            }
            a = tr[i].getElementsByClassName("mobile")[0];
            txtValue = a.textContent || a.innerText;
            if (txtValue.toUpperCase().indexOf(filter) > -1) {
                tr[i].classList.remove("d-none");
                continue;
            }

        }
    }
    document.getElementById("search").addEventListener("keyup",filterSearch);

    function toggleSearch() {
        document.getElementById('search').classList.toggle('d-none');
    }
    // document.getElementById("toggleSearch").addEventListener("click",toggleSearch);
    function editUser(e) {
        document.getElementById("user.id").value = e.target.dataset.userid;
        document.getElementById("user.firstname").value = e.target.dataset.firstname;
        document.getElementById("user.lastname").value = e.target.dataset.lastname;
        document.getElementById("user.email").value = e.target.dataset.email;
        document.getElementById("user.mobile").value = e.target.dataset.mobile;
        document.getElementById("user.isAdmin").checked = (e.target.dataset.isadmin == 1) ? true : false;
        $('#userModal').modal('show');
    }
    let editUserBtns = document.querySelectorAll(".edit-user");
    editUserBtns.forEach(editUserBtn => {
        if (editUserBtn.addEventListener) editUserBtn.addEventListener("click",editUser);
    })

    function changePassword(e) {
        document.getElementById("newpassword.userID").value = e.target.dataset.userid;
        document.getElementById("newpassword.username").innerHTML = e.target.dataset.username;
        $('#passwordModal').modal('show');
    }
    let passwordChangeBtns = document.getElementsByClassName("passwordChange");
    for (let p in passwordChangeBtns) {
        if (passwordChangeBtns[p].addEventListener) passwordChangeBtns[p].addEventListener("click",changePassword)
    }

</script>