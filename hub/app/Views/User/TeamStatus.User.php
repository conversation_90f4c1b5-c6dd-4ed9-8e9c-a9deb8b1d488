<div class="container-fluid">
    <h1>Team Status</h1>
    <form action="" method="post" class="d-flex flex-column flex-md-row">
        <select name="leagueID" id="" class="form-control mr-sm-2" onchange="form.submit();">
            <option value="">League</option><?php
            foreach ($data['leagues'] as $league) { ?>
              <option value="<?php echo $league->id;?>"<?php if (isset($data['leagueID']) && $data['leagueID']==$league->id) echo ' selected';?>><?php echo $league->__toString();?></option><?php
            } ?>
        </select>
        <select name="captainID" id="" class="form-control mr-sm-2" onchange="form.submit();">
            <option value="">Captain</option><?php
            foreach ($data['captains'] as $user) { ?>
              <option value="<?php echo $user->id;?>"<?php if (isset($data['captainID']) && $data['captainID']==$user->id) echo ' selected';?>><?php echo $user->__toString();?></option><?php
            } ?>
        </select>
        
    </form>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Team</th>
                <th>League</th>
                <th>Season</th>
                <th>Division</th>
                <th>Captain</th>
                <th>Treasurer</th>
                <th>Coordinator</th>
                <th class="text-center">Pay Card?</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['teams'] as $team) {
            if (isset($data['leagueID']) && $data['leagueID'] != $team['leagueID']) continue;
            if (isset($data['captainID']) && $data['captainID'] != $team['captainID']) continue;
            // if (isset($data['leagueID']) && $data['leagueID'] != $team['leagueID']) continue;
            $captain = new \User($team['captainID']);
            $treasurer = new \User($team['treasurerID']);
            $coordinator = new \User($team['coordinator']);
            ?>
            <tr <?php if ($team['treasurerStripePaymentMethodID']) echo ' class="bg-success text-light"';?>>
                <td><?php echo $team['name'];?></td>
                <td><?php echo $team['leagueName'];?></td>
                <td><?php echo $team['seasonName'];?></td>
                <td><?php echo $team['divisionName'];?></td>
                <td><?php echo $captain->__toString();?></td>
                <td><?php echo $treasurer->__toString();?></td>
                <td><?php echo $coordinator->__toString();?></td>
                <td class="text-center"><?php echo ($team['treasurerStripePaymentMethodID']) ? "&check;" : "&cross;";?></td>
            </tr><?php
        } ?>
        </tbody>
    </table>
</div>