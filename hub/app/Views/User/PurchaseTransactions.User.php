<style>
    .cursor-clickable {
        cursor: pointer;
    }
</style>
<div class="container-fluid p-4">
    <h1>Purchase Transactions | <a href="/User/PurchaseTransaction" class="btn btn-sm btn-primary">New</a></h1>
    <table class="table">
        <thead>
            <tr>
                <th>Reference</th>
                <th>Venue</th>
                <th>Stamp</th>
                <th>Total</th>
                <th colspan="2">&nbsp;</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['purchaseTransactions'] as $purchaseTransaction) {
            if ($purchaseTransaction->total && $purchaseTransaction->totalAllocated == $purchaseTransaction->total) {
                $color = "success";
            } elseif (is_numeric($purchaseTransaction->totalAllocated) && $purchaseTransaction->totalAllocated > 0) {
                $color = "warning";
            } else $color = "danger";
             ?>
            <tr class="text-<?= $color ?>">
                <td><?= $purchaseTransaction->reference ?></td>
                <td><?= new Venue($purchaseTransaction->venueID) ?></td>
                <td><?= $purchaseTransaction->taxDate ?></td>
                <td><?= $purchaseTransaction->total ?></td>
                <td><?php
                if ($purchaseTransaction->downloadFilename() && file_exists($purchaseTransaction->downloadFilename())) { ?>
                    <form action="/download.php" method="post">
                        <button title="Download" type="submit" name="download" value="<?= $purchaseTransaction->downloadFilename() ?>" style="border:0; background: none;" class="text-info"><i class="fas fa-download"></i></button>
                    </form><?php
                } ?>
                </td>
                <td>
                    <a href="/User/PurchaseTransaction/<?= $purchaseTransaction->id ?>" class="text-success"><i class="fas fa-eye" title="View"></i></a></td>
                <td>
                    <span title="Delete" class="cursor-clickable"><i class="deletePurchaseTransaction far fa-trash-alt text-danger"  data-transactionid="<?= $purchaseTransaction->id ?>"></i></span></td>
            </tr><?php
        } ?>
        </tbody>
    </table>
</div>
<script>
    function readCookie(cname) {
        let name = cname + "=";
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for(let i = 0; i <ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                var rlt = c.substring(name.length, c.length);
                return rlt;
            }
        }
    }
    function deleteTransaction(e) {
        if (confirm("Really delete?") == true) {
            let url = "https://admin.v2.api.leagues4you.co.uk/purchase-transaction/"+e.target.dataset.transactionid;
            fetch(url,{
                headers: new Headers({
                    'Authorization': 'Bearer ' + readCookie('jwt') 
                }),
                method: 'DELETE'
            })
            // .then((response) => response.json())
            // .then(data => console.log(data))
            .then( () => location.reload());
        }
    }

    let deleteTransactionElements = document.querySelectorAll(".deletePurchaseTransaction");
    for (let d in deleteTransactionElements) {
        if (deleteTransactionElements[d].addEventListener) deleteTransactionElements[d].addEventListener('click',deleteTransaction);
    }
</script>