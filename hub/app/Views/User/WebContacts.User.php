<style>
    .webContactActions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .contactMessage {
        cursor: pointer;
    }
    .contactMessage:hover {
        background-color: rgba(0,0,0,.1);
    }
</style>
<!-- Modal -->
<div class="modal fade" id="webContactModal" tabindex="-1" role="dialog" aria-labelledby="webContactModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="webContactLabel">Contact Message</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="webContactMessage"></div>
    </div>
  </div>
</div>
<div class="container-fluid">
    <h1>Web Contacts | <a href="/user" class="btn btn-sm btn-warning">Dashboard</a></h1>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Mobile</th>
                <th>Message</th>
                <th>Time Date</th>
                <th>...</th>
            </tr>
        </thead>
        <tbody><?php
        if(isset($data['webContacts']))
            foreach ($data['webContacts'] as $webContact) {?>
                <tr>
                    <td><?= substr($webContact->getName(),0,30);?></td>
                    <td><?php echo $webContact->getEmail();?></td>
                    <td><?php echo $webContact->getMobile();?></td>
                    <td class="contactMessage" data-webcontactid="<?php echo $webContact->id;?>" title="<?php echo $webContact->getMessage();?>"><?php echo substr($webContact->getMessage(),0,30);?></td>
                    <td><?php echo $webContact->received();?></td>
                    <td>
                        <form class="webContactActions" action="/User/WebContacts" method="post">
                            <button type="submit" name="spam" value="<?php echo $webContact->id;?>" class="btn btn-sm btn-danger">Spam</button>
                            <button type="submit" name="complete" value="<?php echo $webContact->id;?>" class="btn btn-sm btn-success m-1">Complete</button>
                        </form>
                    </td>
                </tr><?php
            } 
        ?>
        </tbody>
    </table>
</div>
<script>
    var webContactModal = document.getElementById("webContactModal");
    var webContactMessages = document.querySelectorAll(".contactMessage");
    function showWebContactMessage(e) {
        webContactMessage.innerHTML = e.target.title;
        $('#webContactModal').modal('show');
    }
    webContactMessages.forEach(webContactMessage => {
        if (webContactMessage.addEventListener) webContactMessage.addEventListener("click",showWebContactMessage);
    })
</script>