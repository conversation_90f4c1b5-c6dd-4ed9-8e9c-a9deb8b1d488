<div class="container">
    <h1>Venue Cost Report</h1>
    <form action="/Finance/VenueCostReport" method="post" class="d-flex flex-column flex-sm-row align-items-center">
        <label for="fromDate">From</label>
        <input type="date" name="fromDate" id="fromDate" class="form-control" value="<?php echo $data['fromDate'];?>">
        <label for="toDate">To</label>
        <input type="date" name="toDate" id="toDate" class="form-control" value="<?php echo $data['toDate'];?>">
        <button type="submit" class="btn btn-primary">Show</button>
    </form>
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Venue</th>
                <th class="text-right">&pound; Total</th>
            </tr>
        </thead>
        <tbody><?php
        $total = 0;
        if ($data['venueCostReport']) {
        foreach ($data['venueCostReport'] as $venueCostReport) { ?>
            <tr>
                <td><?php echo new \Venue($venueCostReport['venueID']);?></td>
                <td class="text-right"><?php echo number_format($venueCostReport['total'],2);?></td>
            </tr><?php
            $total += $venueCostReport['total'];
        } 
        }?>
        </tbody>
        <tfoot>
            <tr>
                <th>Total</th>
                <th class="text-right"><?php echo number_format($total,2);?></th>
            </tr>
        </tfoot>
    </table>
</div>