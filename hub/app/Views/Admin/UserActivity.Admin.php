<div class="container-fluid">
    <div class="header d-flex">
        <h3>User Activity |&nbsp;</h3><form action="" method="post" class="d-flex align-items-center">
            <label for="date">Date</label>&nbsp;
            <input type="date" name="date" id="date" class="form-control" value="<?= $_POST['date'] ?>" onchange="form.submit()">
        </form>
    </div>
    <table class="table table-sm" style="font-size: .85em;">
        <thead>
            <tr>
                <th>When</th>
                <th>User</th>
                <th>Team</th>
                <th>League</th>
                <th>Note</th>
                <th class="text-center">isCaptain</th>
                <th class="text-center">isTreasurer</th>
                <th>Payment Card</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['userActivity'] as $a) { 
            $user = new User($a['userID']);
            $team = new Team($a['teamID']);
            $league = $team->getLeague();
            ?>
            <tr <?= ($a['deleted']) ? 'class="text-danger"' : null ?>>
                <td><?= date('H:i',strtotime($a['created'])) ?></td>
                <td title="<?= $user->id ?>"><?= $user ?></td>
                <td title="<?= $team->id ?>"><?= $team ?></td>
                <td title="<?= $league->id ?>"><?= $league ?></td>
                <td><?= ($a['notes']) ? $a['notes'] : null ?></td>
                <td class="text-center"><?= ($a['isCaptain']) ? "&check;" : null ?></td>
                <td class="text-center"><?= ($a['isTreasurer']) ? "&check;" : null ?></td>
                <td><?= ($a['stripePaymentMethodID']) ? $a['stripePaymentMethodID'] : null ?></td>
            </tr><?php
        } ?>
        </tbody>
    </table>
    <?php
    /*
    id, created, deleted, userID, teamID, isCaptain, isTreasurer, treasurerInviter, treasurerAccepted, stripePaymentMethodID, notes, payInFull
    */ 
    ?>
</div>