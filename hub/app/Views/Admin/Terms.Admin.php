<div id="app" class="container-fluid">
    <h1>Terms</h1>
    <form action="" method="post">
        <div id="quill-toolbar"></div>
        <div id="quill-editor"></div>
        <button class="btn btn-sm btn-primary mt-2" @click.prevent="saveTerms">Save</button>
    </form>
</div>
<script>
    Vue.createApp({
        data() {
            return {
                jwt: null,
                terms: null,
                quill: null,
                quillToolbarOptions: [
                    ['bold', 'italic', 'underline', 'strike'], // toggled buttons
                    // ['blockquote', 'code-block'],
                    // [{ 'header': 1 }, { 'header': 2 }], // custom button values
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'script': 'sub'}, { 'script': 'super' }], // superscript/subscript
                    [{ 'indent': '-1'}, { 'indent': '+1' }], // outdent/indent
                    // [{ 'direction': 'rtl' }], // text direction
                    [{ 'size': ['small', false, 'large', 'huge'] }], // custom dropdown
                    [{ 'header': [1, 2, 3, 4, false] }],
                    // [{ 'color': [] }, { 'background': [] }], // dropdown with defaults from theme
                    // [{ 'font': [] }],
                    [{ 'align': [] }],
                    // ['clean'] // remove formatting button
                ]
            }
        },
        methods: {
            readCookie(cname) {
                let name = cname + "=";
                let decodedCookie = decodeURIComponent(document.cookie);
                let ca = decodedCookie.split(';');
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) == ' ') {
                        c = c.substring(1);
                    }
                    if (c.indexOf(name) == 0) {
                        var rlt = c.substring(name.length, c.length);
                        return rlt;
                    }
                }
            },
            fetchTerms() {
                return;
                let url = "https://admin.v2.api.leagues4you.co.uk/terms";
                fetch(url, {
                    headers: new Headers({
                        'method': 'get',
                        'authorization': 'Bearer ' + this.jwt,
                    }),
                })
                .then(response => response.json())
                .then( (data) => this.terms = data.success)
            },
            saveTerms() {
                console.log(document.getElementById("editor").innerHTML);
            }
        },
        watch: {
        },
        computed: {},
        mounted() {
            this.jwt = this.readCookie("jwt");
            this.quill = new Quill('#quill-editor', {
                debug: 'info',
                modules: {
                    // toolbar: ['bold', 'italic', 'underline', 'strike']
                    toolbar: this.quillToolbarOptions,
                },
                theme: 'snow'
            });
            this.fetchTerms();
        }
    }).mount('#app')
</script>