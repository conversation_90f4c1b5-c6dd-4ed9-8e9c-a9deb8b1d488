<div class="container-fluid">
    <h3>Birthdays</h3>
    <form action="" method="post" class="d-flex align-items-center">
        <label for="month">in</label>
        <select name="month" id="month" class="mx-2 form-control" onchange="form.submit()">
            <option value="1"<?= ($_POST['month']==1) ? ' selected' : null ?>>January</option>
            <option value="2"<?= ($_POST['month']==2) ? ' selected' : null ?>>February</option>
            <option value="3"<?= ($_POST['month']==3) ? ' selected' : null ?>>March</option>
            <option value="4"<?= ($_POST['month']==4) ? ' selected' : null ?>>April</option>
            <option value="5"<?= ($_POST['month']==5) ? ' selected' : null ?>>May</option>
            <option value="6"<?= ($_POST['month']==6) ? ' selected' : null ?>>June</option>
            <option value="7"<?= ($_POST['month']==7) ? ' selected' : null ?>>July</option>
            <option value="8"<?= ($_POST['month']==8) ? ' selected' : null ?>>August</option>
            <option value="9"<?= ($_POST['month']==9) ? ' selected' : null ?>>September</option>
            <option value="10"<?= ($_POST['month']==10) ? ' selected' : null ?>>October</option>
            <option value="11"<?= ($_POST['month']==11) ? ' selected' : null ?>>November</option>
            <option value="12"<?= ($_POST['month']==12) ? ' selected' : null ?>>December</option>
        </select>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>User</th>
                <th>DoB</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['birthdayUsers'] as $user) { ?>
            <tr>
                <td><?= $user ?></td>
                <td><?= date('l jS F Y',strtotime($user->dob)) ?></td>
            </tr><?php
        } ?>
        </tbody>
    </table>
</div>