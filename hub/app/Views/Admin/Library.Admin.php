<div class="container-fluid"> 
    <h1>Library</h1>
    <form action="/Admin/Library" method="post" enctype="multipart/form-data">
        <label for="uploadfile.typeID">Type</label>
        <select name="uploadfile[typeID]" id="uploadfile.typeID" class="form-control">
            <option value="">Select</option><?php
        foreach ($data['docTypes'] as $docType) { ?>
            <option value="<?= $docType->id ?>"><?= $docType ?></option><?php
        } ?>
        </select>
        <label for="uploadfile">File Upload</label>
        <input type="file" id="myFile" name="uploadfile">
        <button type="submit" class="btn btn-sm btn-success mt-2">Upload</button>
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>File</th>
                <th>URL</th>
            </tr>
        </thead>
        <tbody><?php
        foreach ($data['docVersions'] as $docVersion) {
            $docVersion->getType();
            ?>
            <tr>
                <td><?= $docVersion ?></td>
                <td><?= $docVersion->type->slug ?></td>
            </tr><?php
        } ?>
        </tbody>
    </table>
</div>
