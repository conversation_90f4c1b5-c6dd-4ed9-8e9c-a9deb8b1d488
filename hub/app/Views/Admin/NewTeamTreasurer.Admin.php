<div class="container-fluid"> 
    <h1>New Team Treasurer</h1>
    <p>Select the Team in question and enter the Treasurer's email address</p>
    <p><small>If the email address is new to us, a User account will be created also.</small></p>
    <form action="" method="post">
        <label for="NewTeamTreasurer.teamID">Team</label>
        <select name="NewTeamTreasurer[teamID]" id="NewTeamTreasurer.teamID" class="form-control" required>
            <option value="">Select</option><?php
            foreach ($data['teams'] as $team) { ?>
                <option value="<?= $team->id ?>"><?= $team . " | " . $team->getLeague() ?></option><?php
            } ?>
        </select>
        <input type="email" name="NewTeamTreasurer[email]" id="NewTeamTreasurer.email" class="form-control" required placeholder="New Treasurer's Email Address">
        <button class="btn btn-success">Submit</button>
    </form>
    
</div>