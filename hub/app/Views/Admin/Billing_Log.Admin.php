<div id="app" class="container-fluid">
    <div class="heading d-flex align-items-center">
        <h1>Billing Log</h1>
        <div v-if="processing" class="mx-4 spinner-border" role="status"><span class="visually-hidden"></span></div>
    </div>
    <input type="date" id="filter_date" class="form-control" v-model="filter_date">
    <input type="search" id="filter_text" class="form-control" v-model="filter_text" placeholder="Search for...">
    <table class="table table-sm">
        <thead>
            <tr>
                <th>Date</th>
                <th>Team</th>
                <th>League</th>
                <th>Aged Days</th>
                <th>Treasurer</th>
                <th>Balance</th>
                <th>Stripe PI</th>
                <th>Note</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="d in filteredLogData" :key="d.id" :class="{ 'text-danger': !d.statusID}" :title="'ID ' + d.id">
                <td>{{ d.date_formatted }}</td>
                <td>{{ d.team }}</td>
                <td><a :href="'/User/League/' + d.leagueID">{{ d.league }}</a></td>
                <td>{{ d.agedDays }}</td>
                <td>{{ d.treasurer }}</td>
                <td>{{ d.balance }}</td>
                <td>{{ d.stripePaymentIntentID }}</td>
                <td>{{ d.note }}</td>
            </tr>
        </tbody>
    </table>
</div>

<script>
        Vue.createApp({
            data() {
                return {
                    processing: false,
                    jwt: null,
                    filter_date: null,
                    filter_text: null,
                    logData: []
                }
            },
            methods: {
                readCookie(cname) {
                    let name = cname + "=";
                    let decodedCookie = decodeURIComponent(document.cookie);
                    let ca = decodedCookie.split(';');
                    for (let i = 0; i < ca.length; i++) {
                        let c = ca[i];
                        while (c.charAt(0) == ' ') {
                            c = c.substring(1);
                        }
                        if (c.indexOf(name) == 0) {
                            var rlt = c.substring(name.length, c.length);
                            return rlt;
                        }
                    }
                },
                fetchLogData() {
                    this.processing = true;
                    let url = "<?=getDomainPrefix();?>admin.v2.api.leagues4you.co.uk/billing-log-data";
                    if (this.filter_date) url += "/" + this.filter_date;
                    fetch(url, {
                        headers: new Headers({
                            'method': 'get',
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                    .then(response => response.json())
                    .then( (data) => {
                        console.log(data);
                        this.logData = data.success;
                    })
                    .then( () => this.processing = false)
                },
            },
            watch: {
                filter_date() {
                    this.fetchLogData();
                }
            },
            computed: {
                filteredLogData() {
                    if (!this.filter_text) return this.logData;
                    let returnVal = [];
                    this.logData.forEach(item => {
                        if (item.team.toLowerCase().includes(this.filter_text.toLowerCase()) || item.note.toLowerCase().includes(this.filter_text.toLowerCase())) returnVal.push(item);
                    })
                    return returnVal;
                }
            },
            mounted() {
                this.jwt = this.readCookie("jwt");
                this.fetchLogData();
            }
        }).mount('#app')
    </script>
