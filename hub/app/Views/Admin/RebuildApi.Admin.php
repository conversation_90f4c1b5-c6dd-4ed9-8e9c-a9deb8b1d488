<div id="app" class="container-fluid">
    <div class="heading d-flex align-items-center">
        <h1>Rebuild API</h1>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">API Rebuild Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Execution Time:</strong>
                        <span class="badge bg-info"><?= number_format($data['duration'], 4) ?> seconds</span>
                    </div>
                    <div class="mb-3">
                        <strong>Start Time:</strong>
                        <span><?= date('Y-m-d H:i:s') ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        <span class="badge bg-success">Completed</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">API Response</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($data['json']) && $data['json']): ?>
                        <div class="alert alert-success">
                            <strong>API Response:</strong> Successfully retrieved data from liveLeagues endpoint
                        </div>
                        <div class="mb-2">
                            <strong>Response Size:</strong>
                            <span class="badge bg-secondary"><?= strlen(json_encode($data['json'])) ?> bytes</span>
                        </div>

                    <?php else: ?>
                        <div class="alert alert-warning">
                            <strong>API Response:</strong> No data received or error occurred
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>