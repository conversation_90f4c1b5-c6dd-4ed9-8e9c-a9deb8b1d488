<div class="container-fluid"> 
    <h1>New Team Captain</h1>
    <p>Select the Team in question and enter the Captain's email address</p>
    <p><small>If the email address is new to us, a User account will be created also.</small></p>
    <form action="" method="post">
        <label for="NewTeamCaptain.teamID">Team</label>
        <select name="NewTeamCaptain[teamID]" id="NewTeamCaptain.teamID" class="form-control" required>
            <option value="">Select</option><?php
            foreach ($data['teams'] as $team) { ?>
                <option value="<?= $team->id ?>"><?= $team . " | " . $team->getLeague() ?></option><?php
            } ?>
        </select>
        <label for="NewTeamCaptain[email]">New Captain's Email Address</label>
        <input type="email" name="NewTeamCaptain[email]" id="NewTeamCaptain.email" class="form-control" required placeholder="<EMAIL>">
        <div class="alerting d-flex flex-column align-items-start">
            <label for="NewTeamCaptain[supress]">Supress Email Notification</label>
            <small>This process <b>should</b> notify both the outgoing and the new captain - particularly because Terms and Conditions are attached to the New Captain Email. To circumvent this, select the option below.</small>
            <input type="checkbox" name="NewTeamCaptain[supress]" id="NewTeamCaptain.supress" class="ms-2" value="1">
        </div>
        <button class="btn btn-success mt-4">Submit</button>
    </form>
    
</div>