<style>
    /* Your styles here */
    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked+.slider {
        background-color: #2196F3;
    }

    input:focus+.slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked+.slider:before {
        -webkit-transform: translateX(26px);
        -ms-transform: translateX(26px);
        transform: translateX(26px);
    }

    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .slider-button-title {
        padding: 0px 10px;
    }
</style>
<div id="app" class="container mt-4">
    <!-- Modal -->
    <div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalLabel">User Management</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <input type="hidden" id="user.id">
                        <label for="user.firstname">Firstname</label>
                        <input type="text" id="user.firstname" class="form-control">
                        <label for="user.lastname">Lastname</label>
                        <input type="text" id="user.lastname" class="form-control">
                        <label for="user.email">Email</label>
                        <input type="email" id="user.email" class="form-control">
                        <label for="user.mobile">Mobile</label>
                        <input type="tel" id="user.mobile" class="form-control">
                        <label for="user.newpassword">New Password</label>
                        <input type="text" id="user.newpassword" class="form-control outline-danger">
                        <label for="user.profilePictureUrl">Profile Pic</label>
                        <input type="url" id="user.profilePictureUrl" class="form-control">
                        <label for="user.bio">User Bio</label>
                        <textarea type="text" id="user.bio" class="form-control"></textarea>
                        <div class="d-flex justify-content-between mt-2">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="user.isActivated" value="1">
                                <label class="form-check-label" for="user.isActivated">Activated</label>
                            </div>
                            <?php if (\User::isManager()) { ?>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="user.isAdmin" value="1">
                                    <label class="form-check-label" for="user.isAdmin">Coordinator</label>
                                </div>
                            <?php } ?>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" v-on:click="saveUser" class="btn btn-primary">Save changes</button>
                </div>
            </div>
        </div>
    </div>
    <!-- List Modal -->
    <div class="modal fade" id="listModal" tabindex="-1" aria-labelledby="listModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="listModalLabel">{{ modalTitle }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul v-if="modalType === 'Teams'">
                        <li v-for="(item, idx) in modalList" :key="idx">{{ item }}</li>
                    </ul>
                    <ul v-else-if="modalType === 'Leagues'">
                        <li v-for="(item, idx) in modalList" :key="idx">{{ item.name }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-6">
                <h2>
                    {{ title }} |
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
                        New
                    </button>
                </h2>
            </div>
            <div class="col-6 d-flex align-items-center">
                <span class="slider-button-title">Coordinator: </span>
                <label class="switch">
                    <input type="checkbox" v-model="coordinatorFilter" @change="fetchUserData">
                    <span class="slider round"></span>
                </label>
            </div>
        </div>
    </div>

    <form class="my-2">
        <input type="search" class="form-control" v-model="filterText" placeholder="Search for ...">
    </form>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th scope="col" style="width: 3%;" class="align-middle text-center">#</th>
                <th scope="col" style="width: 15%;" class="align-middle text-center">Firstname</th>
                <th scope="col" style="width: 15%;" class="align-middle text-center">Lastname</th>
                <th scope="col" class="align-middle text-center">Email</th>
                <th scope="col" style="width: 15%;" class="align-middle text-center">Mobile</th>
                <th scope="col" style="width: 8%;" class="align-middle text-center">Coordinator</th>
                <th scope="col" style="width: 15%;" class="align-middle text-center">Role</th>
                <th scope="col" style="width: 15%;" class="align-middle text-center">Team Name</th>
                <th scope="col" style="width: 30%;" class="align-middle text-center">League Name</th>
                <th scope="col" class="align-middle text-center">Last Logged In</th> <!-- Added column -->
                <th scope="col" class="align-middle text-center">Status</th>
                <th scope="col" class="align-middle text-center">Action</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="(user, index) in users" :key="user.id">
                <th scope="row" class="align-middle text-center">{{ index + 1 }}</th>
                <td class="align-middle">{{ user.firstname }}</td>
                <td class="align-middle">{{ user.lastname }}</td>
                <td class="align-middle">{{ user.email }}</td>
                <td class="align-middle">{{ user.mobile }}</td>
                <td class="align-middle">{{ IsCoordinator(user.isAdmin) }}</td>
                <td class="align-middle">{{ role(user.is_captain, user.is_treasurer) }}</td>
                <td class="align-middle">
                    <span v-if="Array.isArray(user.teams) && user.teams.length === 1">
                        {{ user.teams[0] }}
                    </span>
                    <span v-else-if="Array.isArray(user.teams) && user.teams.length > 1">
                        <a href="#" @click.prevent="showListModal(user.teams, 'Teams')">View teams</a>
                    </span>
                    <span v-else>
                        {{ user.team_name }}
                    </span>
                </td>
                <td class="align-middle">
                    <span v-if="Array.isArray(user.leagues) && user.leagues.length === 1">
                        {{ user.leagues[0].name }}
                    </span>
                    <span v-else-if="Array.isArray(user.leagues) && user.leagues.length > 1">
                        <a href="#" @click.prevent="showListModal(user.leagues, 'Leagues')">View leagues</a>
                    </span>
                    <span v-else>
                        {{ user.league_name }}
                    </span>
                </td>
                <td class="align-middle text-center">{{ user.lastLoggedIn }}</td> <!-- Added value -->
                <td class="align-middle text-center">
                    <i v-if="user.isActivated == 1" class="fas fa-user-shield text-success"></i>
                    <i v-else class="fas fa-user-shield text-danger"></i>
                </td>
                <td class="align-middle text-center">
                    <i v-on:click="openModal" :data-userid="user.id" class="fas fa-edit mx-1"></i>
                </td>
            </tr>
        </tbody>
    </table>
    <span class="d-flex justify-content-center align-content-center p-4">Showing {{ this.maxResults }} results per page</span>
    <nav aria-label="Page navigation" id="paginationLinks">
        <ul class="pagination flex-wrap justify-content-center">
            <li class="page-item" :key="1" v-if="pageOffset > 3">
                <a href="#" v-on:click="setPage" :data-page="1" class="page-link">First</a>
            </li>
            <li class="page-item" v-bind:class="{ active: p == pageOffset }" v-for="p in visiblePages" :key="p">
                <a href="#" v-on:click="setPage" :data-page="p" class="page-link">{{ p }}</a>
            </li>
            <li class="page-item">
                <a href="#paginationLinks" class="page-link">...</a>
            </li>
            <li class="page-item">
                <a href="#paginationLinks" v-on:click="setPage" :data-page="pages" class="page-link">{{ pages }}</a>
            </li>
        </ul>
    </nav>
</div>
<script>
    var userModal;
    var app = new Vue({
        el: '#app',
        data: {
            title: 'Users',
            users: [],
            pageOffset: 1,
            filterText: null,
            maxResults: 50,
            totalCount: null,
            jwt: null,
            userModel: null,
            loading: false,
            coordinatorFilter: false,
            modalList: [],
            modalTitle: '',
            modalType: '',
        },
        computed: {
            pages: function() {
                return Math.ceil(this.totalCount / this.maxResults);
            },
            visiblePages: function() {

                let start = 1;
                let end = 5;
                const pageOffset = parseInt(this.pageOffset);

                if (this.pages <= 5) {
                    return Array.from({
                        length: this.pages
                    }, (v, k) => k + 1);
                }

                if (pageOffset <= 3) {
                    start = 1;
                    end = 5;
                } else if (pageOffset >= this.pages - 2) {
                    start = this.pages - 4;
                    end = this.pages;
                } else {
                    start = pageOffset - 2;
                    end = pageOffset + 2;
                }

                return Array.from({
                    length: end - start + 1
                }, (v, k) => start + k);
            }
        },
        created: function() {
            this.jwt = this.readCookie('jwt');
        },
        watch: {
            filterText: function(val) {
                this.fetchUserData();
            },
            pageOffset: function(val) {
                this.fetchUserData();
            },
            jwt: function(val) {
                this.fetchUserData();
            },
            coordinatorFilter: function(val) {
                this.fetchUserData();
            }
        },
        methods: {
            setPage(e) {
                e.preventDefault();
                if (this.loading) return; // Prevent clicking during loading
                this.pageOffset = parseInt(e.target.dataset.page); // Update the pageOffset
            },
            readCookie(cname) {
                let name = cname + "=";
                let decodedCookie = decodeURIComponent(document.cookie);
                let ca = decodedCookie.split(';');
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) == ' ') {
                        c = c.substring(1);
                    }
                    if (c.indexOf(name) == 0) {
                        var rlt = c.substring(name.length, c.length);
                        return rlt;
                    }
                }
            },
            fetchUserData() {
                if (this.loading) return; // Prevent multiple fetch calls
                this.loading = true; // Set loading to true when fetching starts

                var url = "<?= getDomainPrefix(); ?>admin.v2.api.leagues4you.co.uk/user-table-users?maxResults=" + this.maxResults;
                if (this.pageOffset) url += "&pageOffset=" + this.pageOffset;
                if (this.filterText) url += "&filterText=" + this.filterText;
                if (this.coordinatorFilter) url += "&is_coordinator=true"; // Add the coordinator filter

                fetch(url, {
                        headers: new Headers({
                            'method': 'get',
                            'authorization': 'Bearer ' + this.jwt,
                        }),
                    })
                    .then(response => response.json())
                    .then(json => {
                        this.users = json.users;
                        if (this.pageOffset != json.page) {
                            this.pageOffset = json.page;
                        }
                        this.totalCount = json.totalCount;
                        this.loading = false; // Set loading to false when fetch is complete
                    })
                    .catch(() => {
                        this.loading = false; // Ensure loading is false if there's an error
                    });
            },
            saveUser(e) {
                var bearer = this.readCookie('jwt');
                // console.log("Bearer", bearer);
                var url = "<?= getDomainPrefix(); ?>admin.v2.api.leagues4you.co.uk/user";
                var jsonBody = {
                    id: document.getElementById("user.id").value,
                    firstname: document.getElementById("user.firstname").value,
                    lastname: document.getElementById("user.lastname").value,
                    email: document.getElementById("user.email").value,
                    mobile: document.getElementById("user.mobile").value,
                    <?php if (\User::isManager()) { ?>
                        isAdmin: document.getElementById("user.isAdmin").checked,
                    <?php } ?>
                    isActivated: document.getElementById("user.isActivated").checked,
                    newpassword: document.getElementById("user.newpassword").value,
                    profilePictureUrl: document.getElementById("user.profilePictureUrl").value,
                    bio: document.getElementById("user.bio").value
                };

                fetch(url, {
                        method: 'post',
                        headers: new Headers({
                            'authorization': 'Bearer ' + bearer,
                        }),
                        body: JSON.stringify(jsonBody)
                    })
                    .then(response => response.json())
                    .then(json => console.log(json))
                    .then(() => this.fetchUserData())
                    .then(() => {
                        userModal.hide();
                    })
            },
            openModal(e) {
                // console.log("Open the Modal!")
                // console.log("Looking for ", e.target.dataset.userid);
                for (let u in this.users) {
                    // console.log("Checking ", this.users[u].id);
                    if (this.users[u].id == e.target.dataset.userid) {

                        document.getElementById("user.profilePictureUrl").value = this.users[u].profilePictureUrl;
                        document.getElementById("user.firstname").value = this.users[u].firstname;
                        document.getElementById("user.lastname").value = this.users[u].lastname;
                        document.getElementById("user.email").value = this.users[u].email;
                        document.getElementById("user.mobile").value = this.users[u].mobile;
                        document.getElementById("user.id").value = this.users[u].id;
                        document.getElementById("user.bio").value = this.users[u].bio;
                        document.getElementById("user.isActivated").checked = (this.users[u].isActivated == 1) ? true : false;
                        <?php if (\User::isManager()) { ?>
                            document.getElementById("user.isAdmin").checked = (this.users[u].isAdmin == 1) ? true : false;
                        <?php } ?>
                        break;
                    }
                }

                userModal = new bootstrap.Modal(document.getElementById('userModal'));

                userModal.show();
            },
            showListModal(list, type) {
                this.modalList = list;
                this.modalType = type;
                this.modalTitle = type === 'Teams' ? 'Teams' : 'Leagues';
                const modal = new bootstrap.Modal(document.getElementById('listModal'));
                modal.show();
            },
            role(is_captain = false, is_treasurer = false, ) {
                console.log(`Treasurer`, is_treasurer)
                console.log(`Captain`, is_captain)
                let role = "";
                if (is_captain && is_treasurer) {
                    role += "Treasurer & Captain";
                } else if (is_captain) {
                    role += "Captain";
                } else if (is_treasurer) {
                    role += "Treasurer";
                }
                return role;
            },
            IsCoordinator(coordinator = null) {
                let is_coordinator = "No";
                if (coordinator) {
                    is_coordinator = "Yes";
                }
                return is_coordinator;
            }
        }
    })
</script>