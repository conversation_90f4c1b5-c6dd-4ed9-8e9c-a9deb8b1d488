<?php
$incident = $data['incident'];
$isManager = $data['isManager'] ?? false;
?>

<div class="container-fluid px-4 py-3">
    <link rel="stylesheet" href="css/hub.incident_report.css">

    <!-- Header Section -->
    <div class="page-header bg-white rounded-3 shadow-sm p-3 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0">Incident Report Details</h1>
                <p class="text-muted mb-0">Report ID: <?= $incident->id ?></p>
            </div>
            <div class="d-flex gap-2">
                <a class="btn btn-outline-secondary" href="/Coordinator/incidentReports">
                    <i class="fa-solid fa-arrow-left me-2"></i>Back to Reports
                </a>
                <?php if ($incident->status != 'closed'): ?>
                    <a href="/Coordinator/incidentReportUpdate/<?= $incident->id ?>?incidentReportStatus=<?= urlencode($incident->status) ?>" class="btn btn-warning">
                        <i class="fa-solid fa-pen-to-square me-2"></i>Edit Report
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if (isset($incident)) { ?>
        <div class="row g-4">
            <!-- Basic Information Card -->
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fa-solid fa-user me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="text-muted" style="width: 40%;">Injured Person Name</th>
                                    <td class="text-capitalize"><?= $incident->name ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Date of Birth</th>
                                    <td><?= ukDateFormat($incident->dob) ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Contact Number</th>
                                    <td><?= $incident->contact_number ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Injured Person Type</th>
                                    <td><?= $incident->injured_person_type ?? 'Not specified' ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Relationship to Injured Person</th>
                                    <td><?= ($incident->relationship_to_player) ? htmlspecialchars($incident->relationship_to_player) : 'Not specified' ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Consent</th>
                                    <td><?= ($incident->no_consent == 1) ? '<span class="text-success">Consent given</span>' : '<span class="text-danger">Consent not given</span>' ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Under 18</th>
                                    <td><?= ($incident->under_18 == 1) ? 'Yes' : 'No' ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Date of Incident</th>
                                    <td><?= ukDateFormat($incident->date_of_incident) ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Time of Incident</th>
                                    <td><?= $incident->time_of_incident ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Created</th>
                                    <td><?= $incident->created_at ? ukDateFormat($incident->created_at) . ' ' . date('H:i', strtotime($incident->created_at)) : 'Not available' ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location & Coordinator Card -->
            <div class="col-md-6">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fa-solid fa-location-dot me-2"></i>Location & Coordinator
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="text-muted" style="width: 40%;">Venue</th>
                                    <td class="text-capitalize"><?= $incident->getVenueName() ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Location</th>
                                    <td><?= $incident->location ?? 'Not specified' ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Coordinator</th>
                                    <td class="text-capitalize"><?= $incident->getCoordinatorName() ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Status</th>
                                    <td>
                                        <span class="badge bg-<?= $incident->status == 'closed' ? 'success' : ($incident->status == 'pending' ? 'warning' : 'primary') ?>">
                                            <?= ucfirst($incident->getStatus()) ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php if ($isManager): ?>
                                    <tr>
                                        <th class="text-muted">Severity</th>
                                        <td>
                                            <?php
                                            $severityClass = '';
                                            switch (strtolower($incident->severity)) {
                                                case 'red':
                                                    $severityClass = 'bg-danger';
                                                    break;
                                                case 'amber':
                                                    $severityClass = 'bg-warning text-dark';
                                                    break;
                                                case 'green':
                                                    $severityClass = 'bg-success';
                                                    break;
                                                default:
                                                    $severityClass = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?= $severityClass ?>">
                                                <?= $incident->severity ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Incident Details Card -->
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fa-solid fa-triangle-exclamation me-2"></i>Incident Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th class="text-muted" style="width: 40%;">Description</th>
                                            <td><?= nl2br($incident->description ?? '') ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Location Details</th>
                                            <td><?= nl2br($incident->details_location ?? '') ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Root Cause</th>
                                            <td><?= nl2br($incident->root_cause ?? '') ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Nature of Injury</th>
                                            <td><?= nl2br($incident->nature_of_injury ?? '') ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-borderless">
                                        <tr>
                                            <th class="text-muted" style="width: 40%;">Treatment</th>
                                            <td><?= nl2br($incident->treatment ?? '') ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Match Official</th>
                                            <td class="text-capitalize"><?= $incident->match_official_name ?? 'Not specified' ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Additional Info</th>
                                            <td><?= nl2br($incident->additional_info ?? '') ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Outstanding Info</th>
                                            <td><?= nl2br($incident->outstanding_info ?? '') ?></td>
                                        </tr>
                                        <tr>
                                            <th class="text-muted">Mitigating Circumstances</th>
                                            <td><?= nl2br($incident->mitigating_circumstances ?? '') ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Injury Information Card -->
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">
                            <i class="fa-solid fa-bandage me-2"></i>Injury Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="text-muted">Injury Details</th>
                                    <td><?= nl2br($incident->injury_details ?? 'Not specified') ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Taken to Hospital</th>
                                    <td>
                                        <span class="badge bg-<?= isset($incident->taken_to_hospital) && $incident->taken_to_hospital == '1' ? 'danger' : 'success' ?>">
                                            <?= isset($incident->taken_to_hospital) && $incident->taken_to_hospital == '1' ? 'Yes' : 'No' ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Witness Information Card -->
            <div class="col-md-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fa-solid fa-user-group me-2"></i>Witness Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tr>
                                    <th class="text-muted" style="width: 40%;">Witness Present</th>
                                    <td>
                                        <span class="badge bg-<?= $incident->witness == '1' ? 'success' : 'secondary' ?>">
                                            <?= $incident->witness == '1' ? 'Yes' : 'No' ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php if ($incident->witness == '1'): ?>
                                    <tr>
                                        <th class="text-muted">Witness Name</th>
                                        <td class="text-capitalize"><?= $incident->witness_name ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Files Section -->
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fa-solid fa-file me-2"></i>Attached Files
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Official Statement -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">Official Statement</h6>
                                        <?php if ($incident->official_statement_file): ?>
                                            <div class="d-flex align-items-center gap-2">
                                                <i class="fa-solid fa-file-pdf text-danger"></i>
                                                <span class="text-truncate"><?= basename($incident->official_statement_file) ?></span>
                                                <a class="btn btn-sm btn-primary ms-auto" href="<?= $incident->official_statement_file ?>" target="_blank">
                                                    <i class="fa-solid fa-eye me-1"></i>View
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <p class="text-muted mb-0">No file uploaded</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Witness Statements -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title">Witness Statements</h6>
                                        <?php if ($incident->witness_statement_files): ?>
                                            <?php foreach (unserialize($incident->witness_statement_files) as $statement): ?>
                                                <div class="d-flex align-items-center gap-2 mb-2">
                                                    <i class="fa-solid fa-file-pdf text-danger"></i>
                                                    <span class="text-truncate"><?= basename($statement) ?></span>
                                                    <a class="btn btn-sm btn-primary ms-auto" href="<?= $statement ?>" target="_blank">
                                                        <i class="fa-solid fa-eye me-1"></i>View
                                                    </a>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <p class="text-muted mb-0">No files uploaded</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Files -->
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Additional Files</h6>
                                        <?php
                                        $additionalFiles = \Upload::getIncidentFiles($incident->id);
                                        if ($additionalFiles): ?>
                                            <div class="row g-2">
                                                <?php foreach ($additionalFiles as $filePath):
                                                    $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
                                                    $fileName = basename($filePath);
                                                    $iconClass = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif']) ? 'fa-file-image text-primary' : ($fileExtension === 'pdf' ? 'fa-file-pdf text-danger' : 'fa-file text-secondary');
                                                ?>
                                                    <div class="col-md-4">
                                                        <div class="d-flex align-items-center gap-2 p-2 border rounded">
                                                            <i class="fa-solid <?= $iconClass ?>"></i>
                                                            <span class="text-truncate"><?= $fileName ?></span>
                                                            <a class="btn btn-sm btn-primary ms-auto" href="<?= $filePath ?>" target="_blank">
                                                                <i class="fa-solid fa-eye me-1"></i>View
                                                            </a>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php else: ?>
                                            <p class="text-muted mb-0">No additional files uploaded</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Actions -->
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2 mt-3">
                    <div class="me-auto">
                        <strong>Welfare Check Completed:</strong>
                        <?= $incident->welfare_check_completed ? ukDateFormat($incident->welfare_check_completed) : '<span class="text-danger">Not completed</span>' ?>
                    </div>
                    <a href="/Coordinator/downloadIncidentReport/<?= $incident->id ?>" class="btn btn-info">
                        <i class="fa-solid fa-download me-2"></i>Download Report
                    </a>
                </div>
            </div>
        </div>
    <?php } else { ?>
        <div class="alert alert-warning">
            <i class="fa-solid fa-triangle-exclamation me-2"></i>Incident not found
        </div>
    <?php } ?>
</div>

<style>
    .card {
        border: none;
        transition: transform 0.2s;
    }

    .card:hover {
        transform: translateY(-2px);
    }

    .card-header {
        border-bottom: none;
    }

    .table th {
        font-weight: 500;
    }

    .badge {
        font-weight: 500;
        padding: 0.5em 0.8em;
    }

    .text-truncate {
        max-width: 200px;
    }

    @media (max-width: 768px) {
        .text-truncate {
            max-width: 150px;
        }
    }
</style>

<!-- Keep existing modals and scripts -->
<?php if ($isManager): ?>
    <!-- Status Update Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusModalLabel">Update Incident Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="updateStatusForm" method="POST" action="">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="statusSelect" class="form-label">Select New Status:</label>
                            <select class="form-select" id="statusSelect" name="status">
                                <option value="open">Coordinator Review</option>
                                <option value="pending">Pending Review</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>
                        <input type="hidden" name="redirect" value="/Coordinator/incidentReportDetails/<?= $incident->id ?>">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Close Incident Modal -->
    <div class="modal fade" id="closeIncidentModal" tabindex="-1" aria-labelledby="closeIncidentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="closeIncidentModalLabel">Close Incident Report</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to close this incident report?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger closeIncident">Close Incident</button>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>