<?php
$incidents = !empty($data['incidents']) ? $data['incidents'] : [];
$total = !empty($data['total']) ? $data['total'] : 0;
$pendingReviewCount = !empty($data['pendingReviewCount']) ? $data['pendingReviewCount'] : 0;
$isManager = !empty($data['isManager']) ? $data['isManager'] : false;
?>
<link rel="stylesheet" href="/css/hub.incident_report.css">

<div class="container incident-container">
    <!-- Filter Section - Only visible to admins -->
    <?php if ($isManager): ?>
        <div class="filter-section">
            <div class="filter-title">
                <i class="bi bi-funnel"></i> Filter Reports
            </div>
            <form action="/Coordinator/incidentReports" method="GET" id="filterForm">
                <div class="row">
                    <div class="col-md-2">
                        <label for="start-date" class="form-label">Start Date</label>
                        <input type="date" name="start-date" id="start-date" class="form-control"
                            autocomplete="off" value="<?= $_GET['start-date'] ?? '' ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="end-date" class="form-label">End Date</label>
                        <input type="date" name="end-date" id="end-date" class="form-control"
                            autocomplete="off" value="<?= $_GET['end-date'] ?? '' ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="venues" class="form-label">Venue</label>
                        <select name="venues" id="venues" class="form-select">
                            <option value="">All Venues</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="open" <?= (isset($_GET['status']) && $_GET['status'] == 'open') ? 'selected' : '' ?>>Coordinator Review</option>
                            <option value="pending" <?= (isset($_GET['status']) && $_GET['status'] == 'pending') ? 'selected' : '' ?>>Final Review Required</option>
                            <option value="closed" <?= (isset($_GET['status']) && $_GET['status'] == 'closed') ? 'selected' : '' ?>>Closed</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="severity" class="form-label">Severity</label>
                        <select name="severity[]" id="severity" class="form-select" multiple>
                            <option value="Green" <?= (isset($_GET['severity']) && in_array('Green', $_GET['severity'])) ? 'selected' : '' ?>>Green</option>
                            <option value="Amber" <?= (isset($_GET['severity']) && in_array('Amber', $_GET['severity'])) ? 'selected' : '' ?>>Amber</option>
                            <option value="Red" <?= (isset($_GET['severity']) && in_array('Red', $_GET['severity'])) ? 'selected' : '' ?>>Red</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Apply
                            </button>
                            <a href="/Coordinator/incidentReports" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    <?php endif; ?>

    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0"><i class="bi bi-clipboard-data"></i> Venue Incident Reports</h2>
        <div class="summary-info">
            Showing <?= count($incidents) ?> of <?= $total ?> Entries
            <?php if ($pendingReviewCount > 0): ?>
                <span class="badge bg-warning ms-2"><?= $pendingReviewCount ?> Pending Review</span>
            <?php endif; ?>
        </div>
    </div>

    <!-- Table Section -->
    <div class="incident-table" style="min-height: 400px;">
        <table class="table table-hover mb-0">
            <thead>
                <tr>
                    <th scope="col" class="text-center">#</th>
                    <th scope="col">Venue</th>
                    <th scope="col">Incident Date</th>
                    <th scope="col">Name</th>
                    <th scope="col">Contact</th>
                    <th scope="col">Coordinator</th>
                    <th scope="col" class="text-center">Status</th>
                    <?php if ($isManager): ?>
                        <th scope="col" class="text-center">Severity</th>
                    <?php endif; ?>
                    <th scope="col">Created</th>
                    <th scope="col" class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($incidents)) : ?>
                    <?php foreach ($incidents as $incident) : ?>
                        <tr>
                            <td class="text-center"><?= $incident->id ?></td>
                            <td><?= $incident->getVenueName() ?></td>
                            <td><?= date('d/m/Y', strtotime($incident->date_of_incident)) ?></td>
                            <td><?= $incident->name ?></td>
                            <td><?= $incident->contact_number ?></td>
                            <td><?= $incident->getCoordinatorName() ?></td>
                            <td class="text-center">
                                <?php
                                $statusClass = '';
                                switch ($incident->status) {
                                    case 'open': // Keep for backward compatibility
                                        $statusClass = 'status-open';
                                        break;
                                    case 'pending':
                                        $statusClass = 'status-pending';
                                        break;
                                    case 'closed':
                                        $statusClass = 'status-closed';
                                        break;
                                }
                                ?>
                                <span class="status-badge <?= $statusClass ?>">
                                    <?= ucfirst($incident->getStatus()); ?>
                                </span>
                            </td>
                            <?php if ($isManager): ?>
                                <td class="text-center">
                                    <?php
                                    $severity = $incident->severity ?? 'Green';
                                    $severityClass = 'severity-' . strtolower($severity);
                                    ?>
                                    <span class="severity-badge <?= $severityClass ?>">
                                        <?= $severity ?>
                                    </span>
                                </td>
                            <?php endif; ?>
                            <td><?= $incident->created_at ? date('d/m/Y H:i', strtotime($incident->created_at)) : '-' ?></td>
                            <td class="text-center">
                                <div class="dropdown action-dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="actionDropdown<?= $incident->id ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown<?= $incident->id ?>">
                                        <!-- View option - available to all -->
                                        <li>
                                            <a class="dropdown-item" href="/Coordinator/incidentReportDetails/<?= $incident->id ?>">
                                                <i class="bi bi-eye"></i> View Details
                                            </a>
                                        </li>

                                        <?php if ($incident->status != 'closed') : ?>
                                            <!-- Edit option - available to all for non-closed incidents -->
                                            <li>
                                                <a class="dropdown-item" href="/Coordinator/incidentReportUpdate/<?= $incident->id ?>">
                                                    <i class="bi bi-pencil"></i> Edit Report
                                                </a>
                                            </li>


                                            <?php if ($isManager): ?>
                                                <!-- Status update option - admin only -->
                                                <li>
                                                    <button class="dropdown-item statusUpdateButton" type="button" data-bs-toggle="modal" data-bs-target="#statusModal" data-incident-id="<?= $incident->id ?>" data-current-status="<?= $incident->status ?>">
                                                        <i class="bi bi-arrow-repeat"></i> Change Status
                                                    </button>
                                                </li>

                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>

                                                <!-- Close option - admin only -->
                                                <li>
                                                    <button class="dropdown-item text-danger incidentCloseButton" type="button" data-bs-toggle="modal" data-bs-target="#statusModal" data-incident-id="<?= $incident->id ?>" data-current-status="<?= $incident->status ?>">
                                                        <i class="bi bi-x-circle"></i> Close Report
                                                    </button>
                                                </li>
                                            <?php endif; ?>
                                        <?php elseif ($isManager) : ?>
                                            <!-- Reopen option - only for closed incidents, admin only -->
                                            <li>
                                                <button class="dropdown-item statusUpdateButton" type="button" data-bs-toggle="modal" data-bs-target="#statusModal" data-incident-id="<?= $incident->id ?>" data-current-status="<?= $incident->status ?>">
                                                    <i class="bi bi-arrow-counterclockwise"></i> Reopen Report
                                                </button>
                                            </li>
                                        <?php endif; ?>
                                        <li>
                                            <a class="dropdown-item" href="/Coordinator/downloadIncidentReport/<?= $incident->id ?>">
                                                <i class="bi bi-pencil"></i> Download
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="<?= $isManager ? 8 : 7 ?>" class="text-center py-4">
                            <div class="text-muted">
                                <i class="bi bi-inbox fs-4 d-block mb-2"></i>
                                No incidents found
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination Section -->
    <?php if ($total > 25): ?>
        <?php
        $currentPage = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $itemsPerPage = 25;
        $totalPages = ceil($total / $itemsPerPage);

        // Build query string for pagination links
        $queryParams = $_GET;
        unset($queryParams['page']); // Remove page parameter to rebuild it
        $queryString = !empty($queryParams) ? '&' . http_build_query($queryParams) : '';
        ?>
        <div class="pagination-container">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <!-- Previous button -->
                    <li class="page-item <?= $currentPage <= 1 ? 'disabled' : '' ?>">
                        <a class="page-link" href="<?= $currentPage > 1 ? '/Coordinator/incidentReports?page=' . ($currentPage - 1) . $queryString : '#' ?>"
                            <?= $currentPage <= 1 ? 'tabindex="-1" aria-disabled="true"' : '' ?>>
                            <i class="bi bi-chevron-left"></i> Previous
                        </a>
                    </li>

                    <?php
                    // Calculate page range to show
                    $startPage = max(1, $currentPage - 2);
                    $endPage = min($totalPages, $currentPage + 2);

                    // Show first page if not in range
                    if ($startPage > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="/Coordinator/incidentReports?page=1<?= $queryString ?>">1</a>
                        </li>
                        <?php if ($startPage > 2): ?>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Page numbers -->
                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                        <li class="page-item <?= $i == $currentPage ? 'active' : '' ?>">
                            <a class="page-link" href="/Coordinator/incidentReports?page=<?= $i ?><?= $queryString ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php
                    // Show last page if not in range
                    if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="/Coordinator/incidentReports?page=<?= $totalPages ?><?= $queryString ?>"><?= $totalPages ?></a>
                        </li>
                    <?php endif; ?>

                    <!-- Next button -->
                    <li class="page-item <?= $currentPage >= $totalPages ? 'disabled' : '' ?>">
                        <a class="page-link" href="<?= $currentPage < $totalPages ? '/Coordinator/incidentReports?page=' . ($currentPage + 1) . $queryString : '#' ?>"
                            <?= $currentPage >= $totalPages ? 'tabindex="-1" aria-disabled="true"' : '' ?>>
                            Next <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Page info -->
            <div class="text-center mt-2">
                <small class="text-muted">
                    Page <?= $currentPage ?> of <?= $totalPages ?>
                    (<?= number_format($total) ?> total records)
                </small>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Venue dropdown population
        const selectedVenueId = getQueryParam('venues');
        const venuesDropdownUrl = '<?= getDomainPrefix() ?>public.v2.api.leagues4you.co.uk/venues-dropdown';

        fetch(venuesDropdownUrl)
            .then(response => response.json())
            .then(data => {
                const venuesSelect = document.getElementById('venues');
                if (venuesSelect) { // Check if element exists (might be hidden for non-admins)
                    data.forEach(function(venue) {
                        const option = document.createElement('option');
                        option.value = venue.id;
                        option.textContent = venue.name;
                        venuesSelect.appendChild(option);
                    });

                    if (selectedVenueId) {
                        venuesSelect.value = selectedVenueId;
                    }
                }
            })
            .catch(function(error) {
                console.error('Error:', error);
            });

        // Update incident status functionality
        const closeButtons = document.querySelectorAll('.incidentCloseButton');
        const statusButtons = document.querySelectorAll('.statusUpdateButton');
        const updateStatusForm = document.getElementById('updateStatusForm');
        const statusSelect = document.getElementById('statusSelect');
        const statusModalLabel = document.getElementById('statusModalLabel');

        // Close button handler
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const incidentId = this.getAttribute('data-incident-id');

                // Set the form action
                updateStatusForm.action = '/Coordinator/updateIncidentStatus/' + incidentId;

                // Pre-select "closed" status for close buttons
                statusSelect.value = 'closed';

                // Update modal title
                statusModalLabel.textContent = 'Close Incident Report';
            });
        });

        // Status update button handler
        statusButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const incidentId = this.getAttribute('data-incident-id');
                const currentStatus = this.getAttribute('data-current-status');

                // Set the form action
                updateStatusForm.action = '/Coordinator/updateIncidentStatus/' + incidentId;

                // Pre-select current status
                if (currentStatus === 'closed') {
                    // If currently closed, pre-select "Coordinator Review" for reopen button
                    statusSelect.value = 'open';
                    statusModalLabel.textContent = 'Reopen Incident Report';
                } else {
                    // Otherwise, pre-select current status
                    statusSelect.value = currentStatus === 'open' ? 'open' : currentStatus;
                    statusModalLabel.textContent = 'Update Incident Status';
                }
            });
        });
    });

    function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }
</script>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">Update Incident Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="updateStatusForm" method="POST" action="">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="statusSelect" class="form-label">Select New Status:</label>
                        <select class="form-select" id="statusSelect" name="status">
                            <option value="open">Coordinator Review</option>
                            <option value="pending">Final Review Required</option>
                            <option value="closed" selected>Closed</option>
                        </select>
                    </div>
                    <input type="hidden" name="redirect" value="/Coordinator/incidentReports">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>