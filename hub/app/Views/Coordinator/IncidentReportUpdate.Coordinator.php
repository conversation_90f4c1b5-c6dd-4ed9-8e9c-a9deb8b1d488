<link rel="stylesheet" href="css/hub.incident_report.css">
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.3/themes/base/jquery-ui.min.css" integrity="sha512-8PjjnSP8Bw/WNPxF6wkklW6qlQJdWJc/3w/ZQPvZ/1bjVDkrrSqLe9mfPYrMxtnzsXFPc434+u4FHLnLjXTSsg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

<style>
    .invalid {
        border: 2px solid red !important;
        outline: 4px solid #ff000042 !important;
    }

    .form-section {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-section-title {
        color: #2c3e50;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #e9ecef;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-control {
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 0.75rem;
        transition: all 0.2s;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s;
    }

    .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
    }

    .file-upload-section {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .file-list {
        margin-top: 0.5rem;
    }

    .file-item {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        background: #fff;
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }

    .file-icon {
        margin-right: 0.5rem;
        color: #6c757d;
    }

    .error-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .select2-container--default .select2-selection--single,
    .select2-container--default .select2-selection--multiple {
        border-radius: 6px;
        border: 1px solid #ced4da;
        min-height: 45px;
    }

    .ui-datepicker {
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .ui-timepicker-wrapper {
        width: 150px;
        padding: 0.5rem;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .drop-zone {
        border: 2px dashed #ccc;
        border-radius: 4px;
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .drop-zone.dragover {
        background: #e9ecef;
        border-color: #0d6efd;
    }

    .drop-zone .upload-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 10px;
    }

    .drop-zone .upload-text {
        color: #6c757d;
        margin-bottom: 5px;
    }

    .drop-zone .upload-hint {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .file-preview {
        margin-top: 15px;
    }

    .file-preview .file-item {
        display: flex;
        align-items: center;
        padding: 8px;
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        margin-bottom: 8px;
    }

    .file-preview .file-item .file-name {
        flex-grow: 1;
        margin: 0 10px;
    }

    .file-preview .file-item .file-remove {
        color: #dc3545;
        cursor: pointer;
        padding: 4px;
    }

    .file-preview .file-item .file-remove:hover {
        color: #bb2d3b;
    }
</style>
<form method="post" id="incidentForm" action="" enctype="multipart/form-data" class="row g-4">

    <?php
    $venue_dropdown = getDomainPrefix() . "api.leagues4you.co.uk/venues-dropdown";
    $adminIncidents = getDomainPrefix() . "hub.leagues4you.co.uk/Coordinator/incidentReports";
    $incident = $data['incident'];
    $isManager = $data['isManager'] ?? false;
    ?>
    <input type="hidden" name="" class="venue_dropdown" id="venue_dropdown" value="<?= $venue_dropdown ?>">
    <input type="hidden" name="" class="admin_incidents" value="<?= $adminIncidents ?>">
    <input type="hidden" class="venue" value="<?= $incident->getVenueName() ?>" />

    <?php if ($incident->status != 'closed') { ?>
        <div class="container-fluid px-4 py-3">
            <!-- Consent Tick Box -->
            <div class="form-section mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="no_consent" id="no_consent" value="1" <?= ($incident->no_consent == 1) ? 'checked' : '' ?>>
                    <label class="form-check-label" for="no_consent">
                        The individual does give consent for this form to be completed
                    </label>
                </div>
            </div>
            <div class="page-header bg-white rounded-3 shadow-sm p-3 mb-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Update Incident Report</h1>
                        <p class="text-muted mb-0">Report ID: <?= $incident->id ?></p>
                        <p class="text-muted mb-0 mt-2" style="font-size: 0.85rem; font-weight: 500; color: #495057 !important;">Please complete the incident report with as much detail as possible. You can click save and come back to it later if required.</p>
                    </div>
                    <a class="btn btn-outline-secondary" href="/Coordinator/incidentReports">
                        <i class="fa-solid fa-arrow-left me-2"></i>Back to Reports
                    </a>
                </div>
            </div>

            <?php if (isset($incident)) { ?>
                <!-- Basic Information Section -->
                <div class="col-12">
                    <div class="form-section">
                        <h2 class="form-section-title">
                            <i class="fa-solid fa-user me-2"></i>Basic Information
                        </h2>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="full_name" class="form-label">Injured Person's Full Name</label>
                                <input type="text" name="name" class="form-control" id="full_name" value="<?= $incident->name ?>" />
                            </div>
                            <div class="col-md-6">
                                <label for="dob" class="form-label">Date of Birth</label>
                                <input type="text" name="dob" class="form-control" id="dob" value="<?= ukDateFormat($incident->dob) ?>" />
                            </div>
                            <div class="col-md-6">
                                <label for="contact_number" class="form-label">Contact Number</label>
                                <input type="number" name="contact_number" class="form-control" id="contact_number" value="<?= $incident->contact_number ?>" />
                            </div>
                            <div class="col-md-6">
                                <label for="injured_person_type" class="form-label">Injured Person Type <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <select name="injured_person_type" class="form-control" id="injured_person_type">
                                    <option value="">-- Select Type --</option>
                                    <option value="Employee" <?= ($incident->injured_person_type == 'Employee') ? 'selected' : '' ?>>Employee</option>
                                    <option value="Public" <?= ($incident->injured_person_type == 'Public') ? 'selected' : '' ?>>Public</option>
                                </select>
                            </div>
                            <div class="col-md-12">
                                <label for="relationship_to_player" class="form-label">Relationship to Injured Person</label>
                                <input type="text" name="relationship_to_player" class="form-control" id="relationship_to_player" value="<?= $incident->relationship_to_player ?? '' ?>" placeholder="e.g. Parent, Coach, Guardian, Self">
                            </div>
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="under_18" id="under_18" value="1" <?= ($incident->under_18 == 1) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="under_18">
                                        Injured person is under 18 years old
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Incident Details Section -->
                <div class="col-12">
                    <div class="form-section">
                        <h2 class="form-section-title">
                            <i class="fa-solid fa-calendar me-2"></i>Incident Details
                        </h2>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="doi" class="form-label">Date of Incident <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <input type="text" name="date_of_incident" class="form-control" id="doi" value="<?= ukDateFormat($incident->date_of_incident) ?>" />
                            </div>
                            <div class="col-md-6">
                                <label for="time_of_incident" class="form-label">Time of Incident <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <input type="time" name="time_of_incident" class="form-control" id="time_of_incident" value="<?= $incident->time_of_incident ?>" />
                            </div>
                            <div class="col-md-12">
                                <label for="venueId" class="form-label">Venue</label>
                                <input name="venueId" class="form-control" id="venueId" value="<?= $incident->getVenueName() ?>" disabled>
                                <input type="hidden" name="venueId" value="<?= $incident->venueId ?>">
                            </div>
                            <div class="col-md-12">
                                <label for="location" class="form-label">Location <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <input type="text" name="location" class="form-control" id="location" value="<?= $incident->location ?? '' ?>" placeholder="Specific location within the venue">
                            </div>
                            <div class="col-md-12">
                                <label for="details_location" class="form-label">Location Details <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="details_location" class="form-control" id="details_location" rows="4" placeholder="Examples: On court, On the court surround, Foyer of the venue"><?= $incident->details_location ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Injury Information Section -->
                <div class="col-12">
                    <div class="form-section">
                        <h2 class="form-section-title">
                            <i class="fa-solid fa-bandage me-2"></i>Injury Information
                        </h2>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="injury_details" class="form-label">Injury Details <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="injury_details" class="form-control" id="injury_details" rows="4"><?= $incident->injury_details ?? '' ?></textarea>
                            </div>
                            <div class="col-md-12">
                                <label class="form-label">Was the injured person taken to hospital?</label>
                                <div class="d-flex gap-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="taken_to_hospital" id="taken_to_hospital_yes" value="Yes" <?= ($incident->taken_to_hospital == 'Yes') ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="taken_to_hospital_yes">Yes</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="taken_to_hospital" id="taken_to_hospital_no" value="No" <?= ($incident->taken_to_hospital == 'No' || $incident->taken_to_hospital === null) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="taken_to_hospital_no">No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Incident Description Section -->
                <div class="col-12">
                    <div class="form-section">
                        <h2 class="form-section-title">
                            <i class="fa-solid fa-file-lines me-2"></i>Incident Description
                        </h2>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="description" class="form-label">What happened and the root cause of the incident <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="description" class="form-control" id="description" rows="4"><?= $incident->description ?></textarea>
                            </div>
                            <div class="col-md-12">
                                <label for="root_cause" class="form-label">Root cause following investigation <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="root_cause" class="form-control" id="root_cause" rows="4" placeholder="Examples: Player ran to catch the ball and collided with another player..."><?= $incident->root_cause ?></textarea>
                            </div>
                            <div class="col-md-12">
                                <label for="nature_of_injury" class="form-label">Nature and extent of the injury(s) <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="nature_of_injury" class="form-control" id="nature_of_injury" rows="4" placeholder="Examples: Pulled hamstring during sprint..."><?= $incident->nature_of_injury ?></textarea>
                            </div>
                            <div class="col-md-12">
                                <label for="treatment" class="form-label">Treatment given <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="treatment" class="form-control" id="treatment" rows="4" placeholder="Examples: Ice applied and ankle elevated..."><?= $incident->treatment ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Files Section -->
                <div class="col-12">
                    <div class="form-section">
                        <h2 class="form-section-title">
                            <i class="fa-solid fa-file me-2"></i>Attached Files
                        </h2>

                        <?php
                        // Get existing files
                        $existingFiles = [
                            'official' => $incident->official_statement_file,
                            'witness' => $incident->witness_statement_files ? unserialize($incident->witness_statement_files) : [],
                            'additional' => \Upload::getIncidentFiles($incident->id)
                        ];
                        $hasFiles = $existingFiles['official'] || !empty($existingFiles['witness']) || !empty($existingFiles['additional']);
                        ?>

                        <?php if ($hasFiles): ?>
                            <!-- Existing Files Display -->
                            <div class="mb-4">
                                <h5 class="text-primary mb-3">
                                    <i class="fa-solid fa-folder-open me-2"></i>Current Files
                                </h5>

                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="40"><i class="fa-solid fa-file"></i></th>
                                                <th>File Name</th>
                                                <th>Type</th>
                                                <th width="100" class="text-center">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($existingFiles['official']): ?>
                                                <tr>
                                                    <td><i class="fa-solid fa-file-pdf text-danger"></i></td>
                                                    <td><?= basename($existingFiles['official']) ?></td>
                                                    <td><span class="badge bg-info">Official Statement</span></td>
                                                    <td class="text-center">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="<?= $existingFiles['official'] ?>" target="_blank" class="btn btn-outline-primary btn-sm" title="View">
                                                                <i class="fa-solid fa-eye"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-danger btn-sm delete-file"
                                                                data-file-type="official_statement"
                                                                data-incident-id="<?= $incident->id ?>" title="Delete">
                                                                <i class="fa-solid fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php foreach ($existingFiles['witness'] as $index => $file): ?>
                                                <tr>
                                                    <td><i class="fa-solid fa-file-pdf text-danger"></i></td>
                                                    <td><?= basename($file) ?></td>
                                                    <td><span class="badge bg-warning">Witness Statement</span></td>
                                                    <td class="text-center">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="<?= $file ?>" target="_blank" class="btn btn-outline-primary btn-sm" title="View">
                                                                <i class="fa-solid fa-eye"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-danger btn-sm delete-file"
                                                                data-file-type="witness_statement"
                                                                data-file-index="<?= $index ?>"
                                                                data-incident-id="<?= $incident->id ?>" title="Delete">
                                                                <i class="fa-solid fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>

                                            <?php foreach ($existingFiles['additional'] as $file): ?>
                                                <tr>
                                                    <td><i class="fa-solid <?= pathinfo($file, PATHINFO_EXTENSION) === 'pdf' ? 'fa-file-pdf text-danger' : 'fa-file-image text-primary' ?>"></i></td>
                                                    <td><?= basename($file) ?></td>
                                                    <td><span class="badge bg-secondary">Additional File</span></td>
                                                    <td class="text-center">
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="<?= $file ?>" target="_blank" class="btn btn-outline-primary btn-sm" title="View">
                                                                <i class="fa-solid fa-eye"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-danger btn-sm delete-file"
                                                                data-file-id="<?= \Upload::getFileIdByPath($file) ?>"
                                                                data-incident-id="<?= $incident->id ?>" title="Delete">
                                                                <i class="fa-solid fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Upload New Files -->
                        <h5 class="text-secondary mb-3">
                            <i class="fa-solid fa-plus me-2"></i>Upload New Files
                        </h5>
                        <div class="row g-3">
                            <!-- Official Statement -->
                            <div class="col-md-6">
                                <div class="file-upload-section">
                                    <label for="official_statement_file" class="form-label">Official Statement</label>
                                    <div class="drop-zone" id="officialStatementDropZone">
                                        <div class="upload-icon">
                                            <i class="fa-solid fa-cloud-arrow-up"></i>
                                        </div>
                                        <div class="upload-text">Drag & Drop your file here</div>
                                        <div class="upload-hint">or click to browse</div>
                                        <input type="file" name="official_statement_file" class="form-control official_statement_file" id="official_statement_file" style="display: none;" />
                                    </div>
                                    <div class="file-preview" id="officialStatementPreview"></div>
                                    <small class="form-text">Allowed formats: PDF, JPG, PNG, JPEG (max 5MB)</small>
                                    <span class="error-message official_statement_file_error_message"></span>
                                </div>
                            </div>

                            <!-- Witness Statements -->
                            <?php if ($incident->witness == 1): ?>
                                <div class="col-md-6">
                                    <div class="file-upload-section">
                                        <label for="witness_statement" class="form-label">Witness Statements</label>
                                        <div class="drop-zone" id="witnessStatementDropZone">
                                            <div class="upload-icon">
                                                <i class="fa-solid fa-cloud-arrow-up"></i>
                                            </div>
                                            <div class="upload-text">Drag & Drop your files here</div>
                                            <div class="upload-hint">or click to browse</div>
                                            <input type="file" name="witness_statement[]" class="form-control witness_statement" id="witness_statement" multiple style="display: none;" />
                                        </div>
                                        <div class="file-preview" id="witnessStatementPreview"></div>
                                        <small class="form-text">Allowed formats: PDF, JPG, PNG, JPEG (max 5MB)</small>
                                        <span class="error-message witness_statement_error_message"></span>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Additional Files -->
                            <div class="col-12">
                                <div class="file-upload-section">
                                    <label for="incident_files" class="form-label">Additional Files</label>
                                    <div class="drop-zone" id="additionalFilesDropZone">
                                        <div class="upload-icon">
                                            <i class="fa-solid fa-cloud-arrow-up"></i>
                                        </div>
                                        <div class="upload-text">Drag & Drop your files here</div>
                                        <div class="upload-hint">or click to browse</div>
                                        <input type="file" name="incident_files[]" class="form-control incident_files" id="incident_files" multiple style="display: none;" />
                                    </div>
                                    <div class="file-preview" id="additionalFilesPreview"></div>
                                    <small class="form-text">Allowed formats: PDF, JPG, PNG, JPEG (max 5MB)</small>
                                    <span class="error-message incident_files_error_message"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="col-12">
                    <div class="form-section">
                        <h2 class="form-section-title">
                            <i class="fa-solid fa-circle-info me-2"></i>Additional Information
                        </h2>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="match_official_name" class="form-label">Match Official Name <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <input type="text" name="match_official_name" class="form-control" id="match_official_name" value="<?= $incident->match_official_name ?>" />
                            </div>
                            <?php if ($incident->witness == 1): ?>
                                <div class="col-md-12">
                                    <label for="witness_name" class="form-label">Witness Name <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                    <input type="text" name="witness_name" class="form-control" id="witness_name" value="<?= $incident->witness_name ?>" />
                                </div>
                            <?php endif; ?>
                            <div class="col-md-12">
                                <label for="mitigating_circumstances" class="form-label">Mitigating Circumstances <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="mitigating_circumstances" class="form-control" id="mitigating_circumstances" rows="4" placeholder="Examples: Player was wearing unsupportive footwear..."><?= $incident->mitigating_circumstances ?></textarea>
                            </div>
                            <div class="col-md-12">
                                <label for="additional_info" class="form-label">Additional Information <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <textarea name="additional_info" class="form-control" id="additional_info" rows="4"><?= $incident->additional_info ?></textarea>
                            </div>
                            <div class="col-md-12">
                                <label for="your_name" class="form-label">Your Name <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                                <input type="text" name="your_name" class="form-control" id="your_name" value="<?= $incident->your_name ?>" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Admin Section -->
                <?php if ($isManager): ?>
                    <div class="col-12">
                        <div class="form-section">
                            <h2 class="form-section-title">
                                <i class="fa-solid fa-user-shield me-2"></i>Admin Settings
                            </h2>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="injury_severity" class="form-label">Severity of Injury</label>
                                    <select name="severity" class="form-control" id="injury_severity">
                                        <option value="Green" <?= ($incident->severity == 'Green') ? 'selected' : '' ?>>Green</option>
                                        <option value="Amber" <?= ($incident->severity == 'Amber') ? 'selected' : '' ?>>Amber</option>
                                        <option value="Red" <?= ($incident->severity == 'Red') ? 'selected' : '' ?>>Red</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Status</label>
                                    <select name="status" class="form-control status" id="status">
                                        <option value="">-- Select Status --</option>
                                        <option value="open" <?= ($incident->status == 'open') ? 'selected' : '' ?>>Coordinator Review</option>
                                        <option value="pending" <?= ($incident->status == 'pending') ? 'selected' : '' ?>>Pending Review</option>
                                        <option value="closed" <?= ($incident->status == 'closed') ? 'selected' : '' ?>>Closed</option>
                                    </select>
                                    <span class="error-message status_error_message"></span>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label for="outstanding_info" class="form-label">Outstanding Information</label>
                                <textarea name="outstanding_info" class="form-control" id="outstanding_info" rows="4"><?= $incident->outstanding_info ?></textarea>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <input type="hidden" name="severity" value="<?= $incident->severity ?>">
                    <input type="hidden" name="status" value="<?= $incident->status ?>">
                <?php endif; ?>

                <!-- Submit Button -->
                <div class="col-12">
                    <div class="form-section">
                        <label for="welfare_check_completed" class="form-label">Welfare Check Completed <span style="color: #dc3545; font-size: 0.75rem;">[This is mandatory*]</span></label>
                        <input type="date" name="welfare_check_completed" class="form-control" id="welfare_check_completed" value="<?= $incident->welfare_check_completed ?? '' ?>" />
                        <span class="error-message welfare_check_completed_error_message"></span>
                    </div>
                    <input type="hidden" name="submitter" value="main-submit">
                    <div class="d-flex justify-content-end mt-3 gap-2">
                        <button type="submit" class="btn btn-primary" name="main-submit">
                            <i class="fa-solid fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </div>
</form>
<?php } else { ?>
    <div class="alert alert-warning">
        <i class="fa-solid fa-triangle-exclamation me-2"></i>Incident not found
    </div>
<?php } ?>
</div>
<?php } else { ?>
    <div class="container py-5">
        <div class="text-center">
            <div class="alert alert-warning mb-4">
                <i class="fa-solid fa-triangle-exclamation me-2"></i>Incident Report status is closed, no changes can be made
            </div>
            <a class="btn btn-secondary" href="" onclick="history.back()">
                <i class="fa-solid fa-arrow-left me-2"></i>Return
            </a>
        </div>
    </div>
<?php } ?>

<!-- Keep existing scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script defer src="//cdnjs.cloudflare.com/ajax/libs/timepicker/1.3.5/jquery.timepicker.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle file deletion
        document.querySelectorAll('.delete-file').forEach(button => {
            button.addEventListener('click', function() {
                if (confirm('Are you sure you want to delete this file?')) {
                    const incidentId = this.dataset.incidentId;

                    // Build request body based on data attributes
                    const params = new URLSearchParams({
                        incident_id: incidentId
                    });

                    if (this.dataset.fileId) {
                        params.append('file_id', this.dataset.fileId);
                    }
                    if (this.dataset.fileType) {
                        params.append('file_type', this.dataset.fileType);
                    }
                    if (this.dataset.fileIndex) {
                        params.append('file_index', this.dataset.fileIndex);
                    }

                    fetch('/Coordinator/deleteFile', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded'
                            },
                            body: params.toString()
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                this.closest('tr').remove();
                                showToast('File deleted successfully', 'success');
                            } else {
                                alert(data.message || 'Failed to delete file');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('An error occurred while deleting the file');
                        });
                }
            });
        });

        // Toast notification helper
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `${message} <button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }

        // Drag and Drop functionality
        const dropZones = {
            officialStatement: {
                zone: document.getElementById('officialStatementDropZone'),
                input: document.getElementById('official_statement_file'),
                preview: document.getElementById('officialStatementPreview'),
                multiple: false
            },
            witnessStatement: {
                zone: document.getElementById('witnessStatementDropZone'),
                input: document.getElementById('witness_statement'),
                preview: document.getElementById('witnessStatementPreview'),
                multiple: true
            },
            additionalFiles: {
                zone: document.getElementById('additionalFilesDropZone'),
                input: document.getElementById('incident_files'),
                preview: document.getElementById('additionalFilesPreview'),
                multiple: true
            }
        };

        // Initialize drag and drop for each zone
        Object.values(dropZones).forEach(zone => {
            if (!zone.zone) return;

            // Click to upload
            zone.zone.addEventListener('click', () => zone.input.click());

            // Handle file selection
            zone.input.addEventListener('change', (e) => {
                handleFiles(e.target.files, zone);
            });

            // Drag and drop events
            zone.zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.zone.classList.add('dragover');
            });

            zone.zone.addEventListener('dragleave', () => {
                zone.zone.classList.remove('dragover');
            });

            zone.zone.addEventListener('drop', (e) => {
                e.preventDefault();
                zone.zone.classList.remove('dragover');
                handleFiles(e.dataTransfer.files, zone);
            });
        });

        // Handle files
        function handleFiles(files, zone) {
            const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
            const maxSize = 5 * 1024 * 1024; // 5MB

            // Clear preview if not multiple
            if (!zone.multiple) {
                zone.preview.innerHTML = '';
            }

            Array.from(files).forEach(file => {
                // Validate file type and size
                if (!allowedTypes.includes(file.type)) {
                    alert(`File ${file.name} is not a valid file type. Allowed types: PDF, JPG, PNG, JPEG`);
                    return;
                }

                if (file.size > maxSize) {
                    alert(`File ${file.name} is too large. Maximum size is 5MB`);
                    return;
                }

                // Create preview
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <i class="fa-solid ${file.type === 'application/pdf' ? 'fa-file-pdf text-danger' : 'fa-file-image text-primary'}"></i>
                    <span class="file-name">${file.name}</span>
                    <i class="fa-solid fa-times file-remove"></i>
                `;

                // Add remove functionality
                fileItem.querySelector('.file-remove').addEventListener('click', () => {
                    fileItem.remove();
                    // Clear input if not multiple
                    if (!zone.multiple) {
                        zone.input.value = '';
                    }
                });

                zone.preview.appendChild(fileItem);
            });

            // Update input files
            if (zone.multiple) {
                const dataTransfer = new DataTransfer();
                Array.from(zone.input.files).forEach(file => dataTransfer.items.add(file));
                Array.from(files).forEach(file => dataTransfer.items.add(file));
                zone.input.files = dataTransfer.files;
            } else {
                zone.input.files = files;
            }
        }

        // Add client-side validation for Welfare Check Completed
        document.getElementById('incidentForm').addEventListener('submit', function(e) {
            var welfareDate = document.getElementById('welfare_check_completed');
            var errorSpan = document.querySelector('.welfare_check_completed_error_message');
            if (!welfareDate.value) {
                e.preventDefault();
                welfareDate.classList.add('invalid');
                errorSpan.textContent = 'Welfare Check Completed date is required.';
                welfareDate.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            } else {
                e.target.submitter.value = e.submitter.name
                welfareDate.classList.remove('invalid');
                errorSpan.textContent = '';
            }
        });
    });
</script>