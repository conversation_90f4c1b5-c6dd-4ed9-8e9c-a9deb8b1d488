<style>
    :root {
        --primary-bg: #f0f2f5;
        --card-bg: rgba(255, 255, 255, 0.9);
        --card-border: rgba(255, 255, 255, 0.3);
        --card-shadow: rgba(0, 0, 0, 0.1);
        --text-color: #333;
        --accent-color: #000;
        --hover-effect: rgba(255, 255, 255, 0.2);
    }

    body {
        background-color: var(--primary-bg);
        color: var(--text-color);
        font-family: 'Inter', sans-serif;
        margin: 0;
        padding: 0;
    }

    .dashboard-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .dashboard-card {
        background: var(--card-bg);
        border: 1px solid var(--card-border);
        border-radius: 1.5rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px var(--card-shadow);
        color: var(--text-color);
        padding: 1.5rem;
        text-decoration: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px var(--card-shadow);
        background: var(--hover-effect);
    }

    .dashboard-card-icon {
        font-size: 2rem;
        color: var(--accent-color);
        margin-right: 1rem;
    }

    .dashboard-card-content {
        flex: 1;
    }

    .dashboard-card-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .dashboard-card-text {
        font-size: 2rem;
        font-weight: 700;
        color: var(--accent-color);
    }

    .dashboard-open-incident {
        background: linear-gradient(135deg, #ff416c, #ff4b2b);
        color: white;
    }

    .dashboard-open-incident .dashboard-card-text {
        color: white;
    }

    .quick-access-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .quick-access-card {
        background: var(--card-bg);
        border: 1px solid var(--card-border);
        border-radius: 1.5rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px var(--card-shadow);
        color: var(--text-color);
        padding: 1.5rem;
        text-decoration: none;
        transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quick-access-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px var(--card-shadow);
        background: var(--hover-effect);
    }

    .quick-access-card-title {
        font-size: 1.25rem;
        font-weight: 600;
    }
</style>

<div id="app" class="container mt-4">
    <h2>{{ title }}</h2>
    <div class="dashboard-cards">
        <a href="/User/League" class="dashboard-card">
            <span class="dashboard-card-icon">🏆</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Leagues<br>Total</h3>
                <span class="dashboard-card-text">{{ leagueCount }}</span>
            </div>
        </a>
        <a href="/User/League" class="dashboard-card">
            <span class="dashboard-card-icon">🔥</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Leagues<br>Live</h3>
                <span class="dashboard-card-text">{{ liveCount }}</span>
            </div>
        </a>
        <a href="/User/League" class="dashboard-card">
            <span class="dashboard-card-icon">🚀</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Leagues<br>Open</h3>
                <span class="dashboard-card-text">{{ nextCount }}</span>
            </div>
        </a>
        <a href="/User/TasterSessions" class="dashboard-card">
            <span class="dashboard-card-icon">🎯</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Tasters<br>Planned</h3>
                <span class="dashboard-card-text">{{ tasterCount }}</span>
            </div>
        </a>
        <a href="/User/Fixtures" class="dashboard-card">
            <span class="dashboard-card-icon">📅</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Fixtures<br>Today</h3>
                <span class="dashboard-card-text">{{ fixtureCount }}</span>
            </div>
        </a>
        <a href="/Coordinator/Users" class="dashboard-card">
            <span class="dashboard-card-icon">👥</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Customers<br>Active</h3>
                <span class="dashboard-card-text">{{ customerCount }}</span>
            </div>
        </a>
        <a href="/Coordinator/Users" class="dashboard-card">
            <span class="dashboard-card-icon">🤝</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Teams<br>Active</h3>
                <span class="dashboard-card-text">{{ teamCount }}</span>
            </div>
        </a>
        <a href="/Coordinator/incidentReports" class="dashboard-card" :class="{ 'dashboard-open-incident': openIncidentCount > 0 }">
            <span class="dashboard-card-icon">⚠️</span>
            <div class="dashboard-card-content">
                <h3 class="dashboard-card-title">Incident<br>Reports</h3>
                <span class="dashboard-card-text">
                    {{ openIncidentCount }}
                </span>
            </div>
        </a>
    </div>

    <div id="quick-access" class="mt-4">
        <h2>Quick Links</h2>
        <div class="quick-access-cards">
            <a href="/Tool/VenueMarginCalculator" class="quick-access-card">
                <h3 class="quick-access-card-title">Venue Margin Calculator</h3>
            </a>
        </div>
    </div>
</div>

<script>
    var app = new Vue({
        el: '#app',
        data: {
            title: 'Coordinator Dashboard',
            leagueCount: 0,
            liveCount: 0,
            nextCount: 0,
            tasterCount: 0,
            fixtureCount: 0,
            customerCount: 0,
            teamCount: 0,
            incidentCount: 0,
            openIncidentCount: 0,
            pendingReviewCount: 0,
            jwt: null,
            notes: [],
            loading: true
        },
        created() {
            this.jwt = this.readCookie("jwt");
            this.notes.push("JWT set as " + this.jwt);
            this.fetchCoordinatorData();
        },
        methods: {
            readCookie(cname) {
                let name = cname + "=";
                let decodedCookie = decodeURIComponent(document.cookie);
                let ca = decodedCookie.split(';');
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) == ' ') {
                        c = c.substring(1);
                    }
                    if (c.indexOf(name) == 0) {
                        return c.substring(name.length, c.length);
                    }
                }
            },
            fetchCoordinatorData() {
                var url = "<?= getDomainPrefix(); ?>admin.v2.api.leagues4you.co.uk/coordinator";
                this.notes.push("URL set as " + url);
                fetch(url, {
                        headers: new Headers({
                            'authorization': 'Bearer ' + this.jwt,
                            'Content-Type': 'application/json'
                        }),
                    })
                    .then(response => response.json())
                    .then(json => {
                        this.leagueCount = json.leagueCount || 0;
                        this.liveCount = json.liveCount || 0;
                        this.nextCount = json.nextCount || 0;
                        this.tasterCount = json.tasterCount || 0;
                        this.customerCount = json.customerCount || 0;
                        this.teamCount = json.teamCount || 0;
                        this.incidentCount = json.incidentCount || 0;
                        this.openIncidentCount = json.openIncidentCount || 0;
                        this.pendingReviewCount = json.pendingReviewCount || 0;
                        if (json.fixtureCount) this.fixtureCount = json.fixtureCount;
                        this.loading = false;
                    })
                    .catch(err => {
                        this.notes.push("Error " + err);
                        this.loading = false;
                    });
            }
        }
    });
</script>