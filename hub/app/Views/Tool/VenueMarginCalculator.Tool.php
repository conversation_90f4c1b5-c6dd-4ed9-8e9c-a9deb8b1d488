<?php
$title = "Venue Margin Calculator";
?>

<div class="container-fluid mt-4">
    <h1 class="mb-4"><?php echo $title; ?></h1>

    <!-- Venue Type Selection -->
    <div class="mb-4">
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="venueType" id="indoor" value="indoor" checked>
            <label class="form-check-label" for="indoor">Indoor (Target 59%)</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="venueType" id="outdoor" value="outdoor">
            <label class="form-check-label" for="outdoor">Outdoor (Target 67%)</label>
        </div>
    </div>

    <!-- Calculator Cards -->
    <div class="row g-4">
        <?php for ($i = 1; $i <= 6; $i++): ?>
            <div class="col-md-4">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-3">
                        <form class="margin-calculator" id="calculator<?php echo $i; ?>">
                            <!-- Venue Cost -->
                            <div class="row mb-3">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Venue Cost</label>
                                </div>
                                <div class="col-7">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">£</span>
                                        <input type="number" class="form-control form-control-sm venue-cost" step="0.01" value="0">
                                    </div>
                                </div>
                            </div>
                            <!-- Minutes Hired -->
                            <div class="row mb-3">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Minutes Hired</label>
                                </div>
                                <div class="col-7">
                                    <input type="number" class="form-control form-control-sm minutes-hired" value="0">
                                </div>
                            </div>
                            <!-- Match Duration -->
                            <div class="row mb-3">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Match Duration</label>
                                </div>
                                <div class="col-7">
                                    <input type="number" class="form-control form-control-sm match-duration" value="0">
                                </div>
                            </div>
                            <!-- Match Fee -->
                            <div class="row mb-3">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Match Fee</label>
                                </div>
                                <div class="col-7">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">£</span>
                                        <input type="number" class="form-control form-control-sm match-fee" step="0.01" value="0">
                                    </div>
                                </div>
                            </div>
                            <!-- Match Minutes (Calculated) -->
                            <div class="row mb-3 d-none">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Match Minutes</label>
                                </div>
                                <div class="col-7">
                                    <div class="form-control form-control-sm match-minutes bg-light">0</div>
                                </div>
                            </div>
                            <!-- Target Teams (Calculated) -->
                            <div class="row mb-3">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Target Teams</label>
                                </div>
                                <div class="col-7">
                                    <div class="form-control form-control-sm target-teams bg-light">0</div>
                                </div>
                            </div>
                            <!-- Cash Margin -->
                            <div class="row mb-3">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Cash Margin</label>
                                </div>
                                <div class="col-7">
                                    <div class="form-control form-control-sm cash-margin bg-light">£0.00</div>
                                </div>
                            </div>
                            <!-- Margin Percentage -->
                            <div class="row">
                                <div class="col-5">
                                    <label class="form-label fw-bold">Margin</label>
                                </div>
                                <div class="col-7">
                                    <div class="form-control form-control-sm margin">0.0%</div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        <?php endfor; ?>
    </div>

    <!-- Reset Button -->
    <div class="mt-4 text-center">
        <button class="btn btn-danger btn-lg" id="resetButton">Reset All Calculators</button>
    </div>
</div>

<style>
    .card {
        border-radius: 12px;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }

    .form-control-sm {
        height: 36px;
        padding: 4px 12px;
        border-radius: 8px;
        border: 1px solid #ddd;
    }

    .input-group-text {
        border-radius: 8px 0 0 8px;
        border: 1px solid #ddd;
        background-color: #f8f9fa;
    }

    .input-group .form-control {
        border-radius: 0 8px 8px 0;
    }

    .form-control-sm:focus {
        border-color: #6a11cb;
        box-shadow: 0 0 0 2px rgba(106, 17, 203, 0.2);
    }

    .form-label {
        margin-bottom: 0;
        line-height: 36px;
        color: #333;
    }

    .cash-margin,
    .margin,
    .match-minutes,
    .target-teams {
        font-weight: bold;
        text-align: center;
    }

    .margin {
        color: #fff;
        background-color: black;
        border-radius: 8px;
    }

    .btn-danger {
        background-color: #ff416c;
        border: none;
        border-radius: 8px;
        padding: 10px 24px;
        font-weight: bold;
    }

    .btn-danger:hover {
        background-color: #ff4b2b;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const calculators = document.querySelectorAll('.margin-calculator');
        const venueTypeRadios = document.querySelectorAll('input[name="venueType"]');
        const resetButton = document.getElementById('resetButton');

        // Load venue type from localStorage
        const savedVenueType = localStorage.getItem('venueType') || 'indoor';
        document.querySelector(`input[value="${savedVenueType}"]`).checked = true;

        // Save venue type when changed
        venueTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                localStorage.setItem('venueType', e.target.value);
                // Recalculate all calculators when venue type changes
                calculators.forEach(calc => {
                    calc.querySelector('input').dispatchEvent(new Event('input'));
                });
            });
        });

        calculators.forEach((calculator, index) => {
            const inputs = calculator.querySelectorAll('input');
            const matchMinutesDisplay = calculator.querySelector('.match-minutes');
            const targetTeamsDisplay = calculator.querySelector('.target-teams');
            const cashMarginDisplay = calculator.querySelector('.cash-margin');
            const marginDisplay = calculator.querySelector('.margin');

            // Load saved values from localStorage
            function loadSavedValues() {
                const savedValues = JSON.parse(localStorage.getItem(`calculator${index + 1}`) || '{}');

                if (Object.keys(savedValues).length > 0) {
                    calculator.querySelector('.venue-cost').value = savedValues.venueCost || 0;
                    calculator.querySelector('.minutes-hired').value = savedValues.minutesHired || 0;
                    calculator.querySelector('.match-duration').value = savedValues.matchDuration || 0;
                    calculator.querySelector('.match-fee').value = savedValues.matchFee || 0;

                    // Trigger calculation to update displays
                    calculateMargin();
                }
            }

            // Save current values to localStorage
            function saveValues() {
                const values = {
                    venueCost: calculator.querySelector('.venue-cost').value,
                    minutesHired: calculator.querySelector('.minutes-hired').value,
                    matchDuration: calculator.querySelector('.match-duration').value,
                    matchFee: calculator.querySelector('.match-fee').value
                };
                localStorage.setItem(`calculator${index + 1}`, JSON.stringify(values));
            }

            function calculateMargin() {
                const venueCost = parseFloat(calculator.querySelector('.venue-cost').value) || 0;
                const minutesHired = parseFloat(calculator.querySelector('.minutes-hired').value) || 0;
                const matchDuration = parseFloat(calculator.querySelector('.match-duration').value) || 0;
                const matchFee = parseFloat(calculator.querySelector('.match-fee').value) || 0;

                // New calculations
                const actualMinutesHired = minutesHired / 60;
                const matchMinutes = (minutesHired / matchDuration) * 2;
                const actualVenueCost = venueCost * actualMinutesHired;

                // Round match minutes down to nearest even number for target teams
                const roundedTargetTeams = Math.floor(matchMinutes / 2) * 2;
                const actualRevenue = matchFee * roundedTargetTeams;
                const cashMargin = actualRevenue - actualVenueCost;
                const marginPercentage = actualRevenue > 0 ? (cashMargin / actualRevenue) * 100 : 0;

                // Update results
                matchMinutesDisplay.textContent = matchMinutes.toFixed(1);
                targetTeamsDisplay.textContent = roundedTargetTeams;
                cashMarginDisplay.textContent = '£' + cashMargin.toFixed(2);
                marginDisplay.textContent = marginPercentage.toFixed(1) + '%';

                // Color coding for margin based on venue type
                const isIndoor = document.getElementById('indoor').checked;
                const targetMargin = isIndoor ? 59 : 67;

                if (marginPercentage >= targetMargin) {
                    marginDisplay.style.backgroundColor = '#28a745'; // Green
                } else {
                    marginDisplay.style.backgroundColor = '#dc3545'; // Red
                }

                // Save values after calculation
                saveValues();
            }

            // Add input event listeners
            inputs.forEach(input => {
                input.addEventListener('input', calculateMargin);
            });

            // Load saved values when page loads
            loadSavedValues();
        });

        // Reset all calculators
        resetButton.addEventListener('click', function(e) {
            e.preventDefault();
            if (confirm('Are you sure you want to reset all calculators?')) {
                // Clear all calculator data from localStorage
                for (let i = 1; i <= 6; i++) {
                    localStorage.removeItem(`calculator${i}`);
                }
                localStorage.removeItem('venueType');
                // Reload the page to reset all inputs
                location.reload();
            }
        });
    });
</script>