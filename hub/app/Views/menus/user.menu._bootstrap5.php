<nav class="navbar navbar-expand-lg navbar-light bg-light">
  <div class="container-fluid">
    <a class="navbar-brand" href="/Coordinator">
      <img src="/homeImg/logo.svg" style="
    height: 26px;
">
    </a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarSupportedContent">
      <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
        <li class="nav-item">
          <a class="nav-link active" aria-current="page" href="/Coordinator">Home</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/User/League">League</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/User/Venues">Venues</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/Coordinator/Users">Users</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/User/Sports">Sports</a>
        </li>

        <?php if (\User::isAdmin()) { ?>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              Reports
            </a>
            <ul class="dropdown-menu" aria-labelledby="reportsDropdown">
              <!-- <li><a class="dropdown-item" href="/User/WebSearches">Web Searches</a></li> -->
              <li><a class="dropdown-item" href="/User/WebContacts">Web Contacts</a></li>
              <li><a class="dropdown-item" href="/User/TeamStatus">Team Status</a></li>
              <li><a class="dropdown-item" href="/User/OpReport">Op Report</a></li>
              <li><a class="dropdown-item" href="/User/Fixtures">Fixtures</a></li>
              <li><a class="dropdown-item" href="/User/Bookings">Bookings</a></li>
              <li><a class="dropdown-item" href="/User/UnusedBookings">Unused Bookings</a></li>
              <li><a class="dropdown-item" href="/User/TasterSessions">Taster Sessions</a></li>
              <li><a class="dropdown-item" href="/User/PurchaseTransactions">Purchasing</a></li>
              <li><a class="dropdown-item" href="/User/ScheduleReport">Schedule</a></li>
              <li><a class="dropdown-item" href="/User/LiveTeamReport">Live Team Report</a></li>
              <li><a class="dropdown-item" href="/User/OpenRegistrationReport">Open Registration Report</a></li>
              <li><a class="dropdown-item" href="/User/VenueRiskAssesmentReport">Venue Risk Assesment Report</a></li>
              <li><a class="dropdown-item" href="/Coordinator/incidentReports">Incident Report</a></li>
            </ul>
          </li>
        <?php
        }
        if (\User::isFinance()) { ?>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Finance</a>
            <ul class="dropdown-menu" aria-labelledby="financeDropdown">
              <li><a class="dropdown-item" href="/Finance/Transactions">Transactions</a></li>
              <li><a class="dropdown-item" href="/Finance/Payments">Payments</a></li>
              <li><a class="dropdown-item" href="/Finance/Credits">Credits</a></li>
              <li><a class="dropdown-item" href="/Finance/Statement">Statement</a></li>
              <li><a class="dropdown-item" href="/Finance/Balances">Customer Balances</a></li>
              <li><a class="dropdown-item" href="/Finance/PendingPayments">Pending Payments</a></li>
              <li><a class="dropdown-item" href="/Finance/AgedDebtors">Aged Debtors</a></li>
              <li><a class="dropdown-item" href="/Finance/PandL">P&L</a></li>
              <li><a class="dropdown-item" href="/Finance/PaymentHistory">Payment History</a></li>
              <li><a class="dropdown-item" href="/Finance/Intentions">Payment Intentions</a></li>
              <li><a class="dropdown-item" href="/Finance/Daily">Daily</a></li>
              <li><a class="dropdown-item" href="/Finance/MarginReport">Margin Report</a></li>
              <li><a class="dropdown-item" href="/Finance/FixtureReport">Fixture Report</a></li>
              <li><a class="dropdown-item" href="/Tool/VenueMarginCalculator">Venue Margin Calculator</a></li>
            </ul>
          </li>
        <?php
        }
        if (\User::isManager()) { ?>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Admin</a>
            <ul class="dropdown-menu" aria-labelledby="adminDropdown">
              <li><a class="dropdown-item" href="/Admin/Log">Log</a></li>
              <!-- <a class="dropdown-item" href="/Admin/BillingRun">Bill Run (30s)</a> -->
              <a class="dropdown-item" href="/Admin/Billing_Log">Billing Log</a>
              <a class="dropdown-item" href="/User/WebContacts">Web Contacts</a>
              <!-- <a class="dropdown-item" href="/Admin/Library">Doc Library</a> -->
              <a class="dropdown-item" href="/Admin/UserActivity">User Activity</a>
              <a class="dropdown-item" href="/Admin/SeasonStatus">Season Status</a>
              <!-- <a class="dropdown-item" href="/Admin/Birthdays">Birthdays</a> -->
              <a class="dropdown-item" href="/Admin/RebuildApi">Rebuild Leagues</a>
            </ul>
          </li><?php
              }
              if (\User::isAuthor()) { ?>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="authorDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Author</a>
            <ul class="dropdown-menu" aria-labelledby="authorDropdown">
              <li><a class="dropdown-item" href="/Author/Log">Log</a></li>
              <li><a class="dropdown-item" href="/Author/RebuildApi">Rebuild API</a></li>
              <a class="dropdown-item" href="/Admin/NewTeamCaptain">New Captain</a>
              <a class="dropdown-item" href="/Admin/NewTeamTreasurer">New Treasurer</a>
            </ul>
          </li><?php
              } ?>
        <li class="nav-item">
          <a class="nav-link" href="/User/PasswordChange">Change Password</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/User/Logout">Logout</a>
        </li>
    </div>
  </div>
</nav>