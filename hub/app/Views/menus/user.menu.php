<nav class="navbar navbar-expand-lg navbar-light bg-light">
  <a class="navbar-brand" href="/User"><img src="/homeImg/logo.svg" style="
    height: 26px;
"></a>
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
  </button>

  <div class="collapse navbar-collapse" id="navbarSupportedContent">
    <ul class="navbar-nav ml-auto">
      <li class="nav-item active">
        <a class="nav-link" href="/User">Home</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/User/League">League</a>
      </li>
      <!-- <li class="nav-item">
        <a class="nav-link" href="/User/Sweep">Billing</a>
      </li> -->
      <li class="nav-item">
        <a class="nav-link" href="/User/Venues">Venues</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/Coordinator/Users">Users</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/User/Sports">Sports</a>
      </li><?php
            if (\User::isAdmin()) { ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            Reports
          </a>
          <div class="dropdown-menu" aria-labelledby="reportsDropdown">
            <!-- <a class="dropdown-item" href="/User/WebSearches">Web Searches</a> -->
            <a class="dropdown-item" href="/User/WebContacts">Web Contacts</a>
            <a class="dropdown-item" href="/User/TeamStatus">Team Status</a>
            <!-- <a class="dropdown-item" href="/User/LeagueReport">League Report</a> -->
            <a class="dropdown-item" href="/User/OpReport">Op Report</a>
            <a class="dropdown-item" href="/User/Fixtures">Fixtures</a>
            <a class="dropdown-item" href="/User/Bookings">Bookings</a>
            <a class="dropdown-item" href="/User/UnusedBookings">Unused Bookings</a>
            <a class="dropdown-item" href="/User/TasterSessions">Taster Sessions</a>
            <!-- <a class="dropdown-item" href="/User/PurchaseTransactions">Purchasing</a> -->
            <a class="dropdown-item" href="/User/ScheduleReport">Schedule</a>
            <!-- <div class="dropdown-divider"></div>-->
            <a class="dropdown-item" href="/User/LiveTeamReport">Live Team Report</a>
            <a class="dropdown-item" href="/User/OpenRegistrationReport">Open Registration Report</a>
            <a class="dropdown-item" href="/User/VenueRiskAssesmentReport">Venue Risk Assesment Report</a>
          </div>
        </li><?php
            } ?>
      <?php
      if (\User::isFinance()) { ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="reportsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            Finance
          </a>
          <div class="dropdown-menu" aria-labelledby="reportsDropdown">
            <a class="dropdown-item" href="/Finance/Transactions">Transactions</a>
            <a class="dropdown-item" href="/Finance/Payments">Payments</a>
            <a class="dropdown-item" href="/Finance/Credits">Credits</a>
            <a class="dropdown-item" href="/Finance/Statement">Statement</a>
            <!-- <a class="dropdown-item" href="/Finance/VenueCostReport">Venue Costs</a> -->
            <a class="dropdown-item" href="/Finance/Balances">Customer Balances</a>
            <a class="dropdown-item" href="/Finance/PendingPayments">Pending Payments</a>
            <a class="dropdown-item" href="/Finance/AgedDebtors">Aged Debtors</a>
            <a class="dropdown-item" href="/Finance/PandL">P&L</a>
            <a class="dropdown-item" href="/Finance/PaymentHistory">Payment History</a>
            <a class="dropdown-item" href="/Finance/Intentions">Payment Intentions</a>
            <a class="dropdown-item" href="/Finance/Daily">Daily</a>
            <a class="dropdown-item" href="/Finance/MarginReport">Margin Report</a>
            <a class="dropdown-item" href="/Finance/FixtureReport">Fixture Report</a>
            <a class="dropdown-item" href="/Tool/VenueMarginCalculator">Venue Margin Calculator</a>

          </div>
        </li><?php
            }
            if (\User::isManager()) { ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            Admin
          </a>
          <div class="dropdown-menu" aria-labelledby="adminDropdown">
            <a class="dropdown-item" href="/Admin/Log">Log</a>
            <!-- <a class="dropdown-item" href="/Admin/BillingRun">Bill Run (30s)</a> -->
            <a class="dropdown-item" href="/Admin/Billing_Log">Billing Log</a>
            <a class="dropdown-item" href="/User/WebContacts">Web Contacts</a>
            <!-- <a class="dropdown-item" href="/Admin/Library">Doc Library</a> -->
            <a class="dropdown-item" href="/Admin/UserActivity">User Activity</a>
            <a class="dropdown-item" href="/Admin/SeasonStatus">Season Status</a>
            <!-- <a class="dropdown-item" href="/Admin/Birthdays">Birthdays</a> -->
            <a class="dropdown-item" href="/Admin/RebuildApi">Rebuild Leagues</a>
          <?php
            }
            if (\User::isAuthor()) { ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="authorDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">Author</a>
          <ul class="dropdown-menu" aria-labelledby="authorDropdown">
            <li><a class="dropdown-item" href="/Author/Log">Log</a></li>

            <a class="dropdown-item" href="/Admin/NewTeamCaptain">New Captain</a>
            <a class="dropdown-item" href="/Admin/NewTeamTreasurer">New Treasurer</a>
          </ul>
        </li>
      <?php
            } ?>

      <li class="nav-item">
        <a class="nav-link" href="/User/PasswordChange">Change Password</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" href="/User/Logout" title="<?php echo \User::AuthUserEmail(); ?>">Logout</a>
      </li>
  </div>
</nav>