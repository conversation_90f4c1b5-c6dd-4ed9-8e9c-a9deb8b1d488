<!-- <style>
    .spinner {
        display: block;
        position: fixed;
        z-index: 1031; /* High z-index so it is on top of the page */
        top: 50%;
        right: 50%; /* or: left: 50%; */
    }
</style>
<div id="spinner" class="spinner d-none">
    <div class="spinner-border"></div>
</div> -->
<div class="container-fluid"> 
    <h1>Log</h1>
    <form action="<?= $_SERVER['REQUEST_URI'] ?>" method="post">
        <label for="startDate">Start Date</label>
        <input type="date" name="startDate" id="startDate" class="form-control" value="<?= $data['startDate'] ?>" onFocusOut="form.submit()">
        <input type="search" name="search" id="search" class="form-control mb-2" placeholder="Search for..." value="<?= (isset($_POST['search'])) ? $_POST['search'] : null ?>">
    </form>
    <table class="table">
        <thead>
            <tr>
                <th>Stamp</th>
                <th>Message</th>
            </tr>
        </thead>
        <tbody id="userTableBody"><?php
foreach ($data['logMessages'] as $logMessage) { ?>
            <tr>
                <td class="created"><?= $logMessage->getCreated('H:ia d/m/Y') ?></td>
                <td class="message"><?= $logMessage->message ?></td>
            </tr><?php
} ?>
        </tbody>
    </table>
</div>

<script>
input = document.getElementById('search');
// spinner = document.getElementById('spinner');
function filterSearch() {
    // spinner.classList.remove("d-none");
    var filter, ul, li, a, i, txtValue;
    filter = input.value.toUpperCase();
    tbody = document.getElementById("userTableBody");
    tr = tbody.getElementsByTagName('tr');

    // Loop through all list items, and hide those who don't match the search query
    for (i = 0; i < tr.length; i++) {
        tr[i].classList.add("d-none");
        a = tr[i].getElementsByClassName("created")[0];
        txtValue = a.textContent || a.innerText;
        if (txtValue.toUpperCase().indexOf(filter) > -1) {
            tr[i].classList.remove("d-none");
            continue;
        }
        a = tr[i].getElementsByClassName("message")[0];
        txtValue = a.textContent || a.innerText;
        if (txtValue.toUpperCase().indexOf(filter) > -1) {
            tr[i].classList.remove("d-none");
            continue;
        }
    }
    // spinner.classList.add("d-none");
}
document.getElementById("search").addEventListener("keyup",filterSearch);
document.addEventListener("DOMContentLoaded", function(){
    if (input.value) filterSearch();
    // spinner.classList.add("d-none");
});
// spinner.classList.remove("d-none");
</script>