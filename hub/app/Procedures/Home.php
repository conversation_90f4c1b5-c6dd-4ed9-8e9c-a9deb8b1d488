<?php

namespace Home;

function Password (Array $data = []) {
    global $post;
    if (isset($post['reminder']['email'])) {
        $user = \User::EmailLookup($post['reminder']['email']);
        if ($user) $user->PasswordReset();
    }
    return $data;
}
function PasswordReset(Array $data = []) {
    if (isset($data['variables'])) {
        $user = \User::EmailLookup($data['variables'][0]);        
        if ($user) $user->ResetPassword($data['variables'][1]);
        if (\User::AuthUser()) {
            \Messaging\Add("We have automatically logged you in. Please change your password","success");
            header("Location: {$config['system']['url']}/User/PasswordChange"); exit(0);
        }
    }
    \Messaging\Add("Apologies - we could not process the password reset. You can try again or contact support","warning");
    header("Location: {$Config['Url']}/Home"); exit(0);
}
function Authentication (Array $data = []) {
    global $post;
    /* Receive User object on success - null on failure */
    $user = \User::EmailLookup($post['auth']['username']);

    $sql = "INSERT INTO `logins` SET `username` = :username, `ip` = :ip, `agent` = :agent, `result` = :result"; 
    $sqlData['username'] = $post['auth']['username'];
    $sqlData['ip'] = $_SERVER['REMOTE_ADDR'];
    $sqlData['agent'] = $_SERVER['HTTP_USER_AGENT'];
    $sqlData['result'] = 1;
    if (!$user) {
        \Messaging\Add("Login Failed. Error " . __LINE__ ,"danger");
    } elseif ($user->Authenticate($post['auth']['password']) !== true) {
        \Messaging\Add("Login Failed. Error " . __LINE__ ,"danger");
    } elseif (\User::Authenticated()) {
        new \Db($sql, $sqlData);
        \Messaging\Add("Login Successful","success");
        $location = (isset($_SESSION['redirectAfterLogin']) && $_SESSION['redirectAfterLogin']) ? $_SESSION['redirectAfterLogin'] : "/User";
        if (isset($_SESSION['redirectAfterLogin'])) unset($_SESSION['redirectAfterLogin']);
        if (($jwt=\Jwt::Create($user))) \Jwt::CookieStore($jwt);
        header("Location: $location");
        /* If user->id == 2, set a Jwt */
        exit(0);
    }
    $sqlData['result'] = 0;new \Db($sql, $sqlData);
    header("Location: /Home/Login");
    exit(0);
}