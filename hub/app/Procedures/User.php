<?php

namespace User;


function League (Array $data = []) {
    $logging = false;
    if ($logging === true) $log[__LINE__.":".microtime(true)] = "Starting " . __FUNCTION__;
    global $post;
    /* Roll Forward */
    // if (isset($_GET['seasonRollForward']) && $_GET['seasonRollForward']) {
    //     \SeasonRollForward::Run($_GET['seasonRollForward']);
    //     $post['tab'] = "seasons";
    // } 
    $rebuildApi = false;
    // if (isset($_GET['RollTeamsForward']) && $_GET['RollTeamsForward']) {
    //     $data['season'] = new \Season($_GET['RollTeamsForward']);
        // echo "<pre>"; print_r($data['season']); echo "</pre>"; 
    //     $data['season']->RollTeamsForward();
    //     $data['league'] = new \League($data['season']->leagueID);
    //     $data['tab'] = "seasons";
    //     $rebuildApi = true;
    // } 
    if (isset($_POST['migrateTeamSeasonID'])) {
        $teamSeason = new \TeamSeason($_POST['migrateTeamSeasonID']);
        \TeamFollower::Migratev2($teamSeason);
        $post['tab'] = "teams";
        $data['season'] = new \Season($teamSeason->seasonID);
        $data['league'] = new \League($data['season']->leagueID);
    } 
    if (isset($_POST["resubscribe"])) {
        $data['season'] = new \Season($_POST["resubscribe"]['seasonID']);
        $data['league'] = new \League($data['season']->leagueID);
        $data['team'] = new \Team($_POST["resubscribe"]['teamID']);
        // $rlt = "Resubscribe {$data['team']}";
        $rlt = \TeamSeason::Resubscribe($data['team']);
        \Messaging\Add($rlt);
        $post['tab'] = "teams2";
    }
    if (isset($_POST['changeTeamDivision'])) {
        $teamSeason = new \TeamSeason($_POST['changeTeamDivision']['id']);
        $data['season'] = new \Season($teamSeason->seasonID);
        $data['league'] = new \League($data['season']->getLeagueID());
        if ($teamSeason->divisionID != $_POST['changeTeamDivision']['divisionID']) {
            $teamSeason->divisionID = $_POST['changeTeamDivision']['divisionID'];
            $teamSeason->Save();
            $team = new \Team($teamSeason->teamID);
            $division = new \Division($teamSeason->divisionID);
            // $data['season'] = new \Season($teamSeason->seasonID);
            // $league = new \League($data['season']->leagueID);
            $msg = "$team moved to $division in {$data['league']} {$data['season']}";
            \Messaging\Add($msg);
            \Logging::Add($msg . " by " . \User::AuthUser());
        }
    }

    if (isset($_POST['teamSeasonPeople'])) {
        if (!\User::isManager()){
            \Messaging\Add("Only Managers can invite captains","warning"); 
            return;
        }
        $treasurerId = $_POST['teamSeasonPeople']['teamSeasonID'];
        $captainId = $_POST['teamSeasonPeople']['captainID'];

        $teamSeason = new \TeamSeason($_POST['teamSeasonPeople']['teamSeasonID']);
        $team = new \Team($teamSeason->teamID);
        $user = new \User($captainId);
        // $treasurer = new \User($treasurerId);

        $msg = "$team amended by " . \User::AuthUser()."."; 
        \Logging::Add($msg);
        try{
            $rlt = \TeamFollower::InviteCaptain2($team, $user);
        }catch(\Exception $e){
            $msg = "Already invited as captain for this team.";
        }
        \Messaging\Add($msg);
    }
    // if (isset($_GET['resubscribe'])) {
        // $team = new \Team($_GET['resubscribe']);
        // $post['leagueID'] = $team->leagueID;
        // \Team::Resubscribe($_GET['resubscribe']);
        // \Messaging\Add("{$team} resubscribed","success");
    // }
    /* Schedule here = Switch */
    if (isset($post['schedule'])) {
        \Schedule::Swap($post['schedule']['old'],$post['schedule']['new']);
        $rebuildApi = true;
    } 
    if (isset($post['website'])) {
        $league = new \League($post['website']['id']);
        $league->Load($post['website']);
        $league->Save();
        $post['leagueID'] = $post['website']['id'];
        $rebuildApi = true;
    }
    if (isset($post['leagueID']) && $post['leagueID']) {
        $data['league'] = ($post['leagueID']) ? new \League($post['leagueID']) :new \League();
    } elseif (isset($data['variables'][0]) && $data['variables'][0]) {
        $data['league'] = new \League($data['variables'][0]);
    } 

    if (isset($post['seasonID']) && $post['seasonID']) {
        $data['season'] = new \Season($post['seasonID']);
        $data['league'] = new \League($data['season']->getLeagueID());
        if (isset($post['action']) && $post['action']=="autofixture") {
            $post['tab'] = "fixtures";
            // echo "Trying to Autofixture<br>";
            \Season::AutoFixture($data['season']);
            $rebuildApi = true;
        }
        if (isset($post['action']) && $post['action']=="autoschedule") {
            $post['tab'] = "schedule";
            $result = $data['season']->runSchedule(true);
            if (is_string($result)) {
                \Messaging\Add($result,"warning");
            } else {
                $rebuildApi = true;
            }
        }
        if (isset($post['action']) && $post['action']=="clearschedule") {
            $post['tab'] = "schedule";
            # \Messaging\Add("Clear Unbilled temporarily suspended whilst we troubleshoot","warning");
            $result = $data['season']->clearUnbilled();
            (is_string($result)) ? \Messaging\Add($result,"danger") : \Messaging\Add("Deleted $result fixtures froms schedule","success");
            $rebuildApi = true;
        }
        if (isset($post['action']) && $post['action']=="fullclearschedule") {
            $post['tab'] = "schedule";
            # \Messaging\Add("Clear Unbilled temporarily suspended whilst we troubleshoot","warning");
            $result = $data['season']->clearUnbilled(true);
            (is_string($result)) ? \Messaging\Add($result,"danger") : \Messaging\Add("Deleted $result fixtures froms schedule","success");
            $rebuildApi = true;
        }
    } elseif (isset($data['league']) && $data['league'] && (!isset($data['season']) || !$data['season'])) {
        $data['season'] = \Season::Active($data['league']);
        // $data['season'] = new \Season(\Season::DefaultLeague($data['league']->id));
        // echo "<pre>"; print_r($data);exit("</pre>");
    }

    if (isset($post['league'])) {
        $data['league'] = (isset($post['league']['id'])&&$post['league']['id']) ? new \League($post['league']['id']) : new \League();
        $data['league']->Load($post['league']);
        $data['league']->Save();
        $data['league']->CheckForDefaultSeason();
        $rebuildApi = true;
    }

    if (isset($post['website'])) {
        if (!isset($post['website']['visible'])) $post['website']['visible'] = null;
        // \Tools\Dump($post['website']); exit(0);
        $data['league'] = new \League($post['website']['id']);
        $data['league']->Load($post['website']);
        $data['league']->Save();
        $rebuildApi = true;
        // if (\User::isAuthor()) \Tools\Dump($data['league']);
    }

    if (isset($post['season'])) {
        // $data['season'] = (isset($post['season']['id']) && $post['season']['id']) ? new \Season($post['season']['id']) : new \Season();
        $data['season'] = new \Season();
        $data['season']->Load($post['season']);
        $data['season']->Save();
        // echo "<pre>"; print_r($data['season']); exit ("</pre>");
        $data['league'] = new \League($data['season']->getLeagueID());
        $rebuildApi = true;
    }

    if (isset($_POST['waitingList'])) {
        if ($_POST['waitingList']['action'] == "delete") \WaitingList::Archive($_POST['waitingList']['id']);
        if ($_POST['waitingList']['action'] == "add") \WaitingList::AddToSeason($_POST['waitingList']['id'],$_POST['seasonID']);
    }

    if (isset($post['booking'])) {
        $data['booking'] = (isset($post['booking']['id']) && $post['booking']['id']) ? new \Booking($post['booking']['id']) : new \Booking();
        $data['booking']->Load($post['booking']);
        $rlt = $data['booking']->Save();
        (is_string($rlt)) ? \Messaging\Add($rlt,"warning") : \Messaging\Add("Booking Updated","success");
        $data['league'] = new \League($data['booking']->getLeagueID());
        $data['season'] = \Season::Current($data['league']);
        $rebuildApi = true;        
    } 

    if (isset($post['amendteam'])) {
        if ($post['amendteam']['id']) {
            $team = new \Team($post['amendteam']['id']);
            $team->name = $post['amendteam']['name'];
            $rlt = $team->Save();
            if (!is_numeric($rlt)) \Messaging\Add($rlt);
            $data['season'] = new \Season($post['seasonID']);
        } else {
            $data['season'] = new \Season($post['seasonID']);
            $team = new \Team();
            $team->name = $post['amendteam']['name'];
            $team->leagueID = $data['season']->leagueID;
            $team->Save();
            $teamSeason = new \TeamSeason();
            $teamSeason->teamID = $team->id;
            $teamSeason->seasonID = $data['season']->id;
            $teamSeason->divisionID = \Division::Default($data['season']);
            $teamSeason->Save();
        }
        $data['league'] = new \League($data['season']->leagueID);
    }

    if (isset($_POST['leagueNotes'])) {
        $data['league'] = new \League($_POST['leagueNotes']['id']);
        $data['league']->Load($_POST['leagueNotes']);
        $data['league']->Save();
        $post['tab'] = "notes";
    }
    
    // if (isset($post['team'])) {
    //     $data['season'] = new \Season($post['team']['seasonID']);
    //     $team = new \Team();
    //     $team->name = $post['team']['name'];
    //     $team->leagueID = $data['season']->leagueID;
    //     $team->Save();
    //     $teamSeason = new \TeamSeason();
    //     $teamSeason->teamID = $team->id;
    //     $teamSeason->seasonID = $data['season']->id;
    //     $teamSeason->divisionID = \Division::Default($data['season']);
    //     $teamSeason->Save();
    //     $data['league'] = new \League($data['season']->leagueID);

    // }

    // if (isset($post['team'])) {
        /* If no Team ID - create Team in League */
        // if (!$post['team']['id']) {
            // if (isset($post['team']['id']) && $post['team']['id']) {
            //     $team = new \Team($post['team']['id']);
            //     if ($team->getTreasurerID() != $post['team']['treasurerID']) {
            //         $post['team']['treasurerStripePaymentMethodID'] = null;
            //         \Messaging\Add("Treasurer changed. Please ask new Treasurer to add a payemnt card","warning");
            //     }
            // }
            // $team = (isset($post['team']['id']) && $post['team']['id']) ? new \Team($post['team']['id']) : new \Team();
            // $team->Load($post['team']);
            // $team->Save();
            // $data['season'] = new \Season($team->getSeasonID());        
            // $data['league'] = new \League($data['season']->getLeagueID());
        // } else {
            /* Amend Team Season */

    //     }

    //     $rebuildApi = true;
    // }

    if (isset($post['division'])) {
        // \Tools::Dump($post['division']);
        $division = (isset($post['division']['id']) && $post['division']['id']) ? new \Division($post['division']['id']) : new \Division();
        $division->Load($post['division']);
        $division->Save();
        $rebuildApi = true;
        $data['season'] = new \Season($division->getSeasonID());        
        $data['league'] = new \League($data['season']->getLeagueID());
        $post['tab'] = "divisions";
    }

    if (isset($post['scheduling'])) {
        $rlt = \Schedule::Amend($post['scheduling']);
        // exit(\Tools::Dump($rlt));
        $data['tab'] = "schedule";
        $rebuildApi = true;
    }

    if (isset($_POST['bookingAction'])) {
        if ($_POST['bookingAction']=="remove") {
            $result = \Booking::Archive($_POST['bookingID']);
            ($result === true) ? \Messaging\Add("Booking {$_POST['bookingID']} successfully removed","success") : \Messaging\Add($result,"danger");
        } elseif ($_POST['bookingAction']=="copy") {
            \Booking::Duplicate($_POST['bookingID']);
        }
        $booking = new \Booking($_POST['bookingID']);
        $rebuildApi = true;
    }

    // if (isset($_POST['teamSeasonRemove'])) {
    //     $teamSeason = new \TeamSeason($_POST['teamSeasonRemove']);
    //     $data['season'] = new \Season($teamSeason->seasonID);
    //     $data['team'] = new \Team($teamSeason->teamID);
    //     $data['league'] = new \League($data['season']->leagueID);
    //     $teamSeason->Delete();
    //     $post['tab'] = "teams2";
    //     \Messaging\Add("Removed Team ID {$_POST['teamSeasonRemove']} {$data['team']} from {$data['season']}");
    // }

    if (isset($_POST['divisionAction'])) {
        if ($_POST['divisionAction']=="remove") {
           (($result=\Division::Remove($_POST['divisionID'])) !== true) ? \Messaging\Add($result,"warning") : \Messaging\Add("Division Successfully Deleted","success");
        } 
        $rebuildApi = true;
    }

    if (isset($_POST['leagueResults'])) {
        if (\Auth::isAuthor(\User::AuthUser())) {
            // echo count($_POST['leagueResults']) . " results<br>";
            // foreach ($_POST['leagueResults'] as $k => $v) echo "FixtureID $k<br>";
        } 
        \Fixture::Results($_POST['leagueResults']);
        $rebuildApi = true;
        $data['season'] = new \Season($post['seasonID']);
    }

    if (isset($_POST['deleteTeamSeason'])) {
        $teamSeason = new \TeamSeason($_POST['deleteTeamSeason']);
        $data['team'] = $teamSeason->getTeam();
        $data['season'] = $teamSeason->getSeason();
        $data['league'] = $teamSeason->season->getLeague();
        $teamSeason->Delete();
        \Messaging\Add("Removed {$data['team']} from {$data['season']}","success");
        $rebuildApi = true;
        $post['tab'] = "teams2";
    }

    if (!isset($_POST['startDate']) || !$_POST['startDate']) $_POST['startDate'] = date('Y-m-d');
    $data['regions'] = \Region::Fetch();
    $data['sports'] = \Sport::Fetch();
    $data['leagues'] = \League::Fetch();
    $data['venues'] = \Venue::Fetch();
    // $data['users'] = \User::Live();
    $data['coordinators'] = \User::Coordinators();
    $data['statuses'] = \League::Statuses();
    $data['seasonStatuses'] = \SeasonStatus::Query();

    if (isset($data['league']) && $data['league']) {
        if ($logging === true) $log[__LINE__.":".microtime(true)] = "League " . $data['league']->__toString();
        $data['seasons'] = \Season::forLeague($data['league']);
        if ($logging === true) $log[__LINE__.":".microtime(true)] = count($data['seasons']) . " Seasons";
        $data['statements'] = \League::Statements($data['league']);
        if ($logging === true && $data['statements']) $log[__LINE__.":".microtime(true)] = count($data['statements']) . " Statements";
        $data['waitingList'] = \WaitingList::League($data['league']);
        if ($logging === true && $data['waitingList']) $log[__LINE__.":".microtime(true)] = count($data['waitingList']) . " waitingLists";
        if ($data['seasons'] && count($data['seasons'])==1) {
            if ($logging === true) $log[__LINE__.":".microtime(true)] = "Single Season";
            if (!isset( $data['season']) || !$data['season']) {
                $data['season'] = (($season = \Season::Current($data['league']))) ? $season : current($data['seasons']);
                \LeagueNote::Add($data['league'], \Booking::Status($data['season']));
            }
        }
        if (isset($data['season']) && $data['season']->id) {
            $data['bookings'] = \Booking::forSeason ($data['season']);
            if ($logging === true && $data['bookings']) $log[__LINE__.":".microtime(true)] = count($data['bookings']) . " bookings";
            $data['availableFixtureVenues'] = \Venue::SeasonOptions($data['season']);
            if ($logging === true && $data['availableFixtureVenues']) $log[__LINE__.":".microtime(true)] = count($data['availableFixtureVenues']) . " availableFixtureVenues";
            // $data['bookings'] = ($data['season']->startBookingID) ? \Booking::forLeague($data['league']) : \Booking::forLeague($data['league'],date('Y-m-d'));
        }
        $data['notes'] = \LeagueNote::League($data['league']);
        if ($logging === true && $data['notes']) $log[__LINE__.":".microtime(true)] = count($data['notes']) . " notes";
    }

    if (isset($data['season']) && $data['season']) {
        if ($logging === true) $log[__LINE__.":".microtime(true)] = "Season " . $data['season']->__toString();
        if ($data['season']->schedulingNotes) \Messaging\Add($data['season']->schedulingNotes,"info");
        $data['unbilled'] = \Fixture::Unbilled($data['season']);
        if ($data['unbilled'] && $logging === true) $log[__LINE__.":".microtime(true)] = count($data['unbilled']) . " unbilled";
        $data['divisions'] = \Division::forSeason($data['season']);
        if ($logging === true) $log[__LINE__.":".microtime(true)] = count($data['divisions']) . " unbilled";
        $data['leaderboard'] = \Fixture::Leaderboard($data['season']);
        if ($logging === true) $log[__LINE__.":".microtime(true)] = count($data['leaderboard']) . " leaderboard";
        $overrunAlert = $data['season']->NextStartDateValid();
        if ($overrunAlert) \Messaging\Add($overrunAlert,"warning");
        if ($data['divisions']) {
            if (count($data['divisions'])==1) {
                $data['division'] = current($data['divisions']);
            } elseif (isset($post['divisionID']) && $post['divisionID']) {
                $data['division'] = new \Division($post['divisionID']);
            }
            if (isset($data['division']) && $data['division']) {
                $data['divisionStandings'] = $data['division']->Standings();
                if ($logging === true && $data['divisionStandings']) $log[__LINE__.":".microtime(true)] = count($data['divisionStandings']) . " divisionStandings";
            } 
        }
        // $data['teams'] = \Team::forSeason($data['season']);
        $data['teams'] = \TeamSeason::Season($data['season']);
        if ($logging === true) $log[__LINE__.":".microtime(true)] = count($data['teams']) . " teams";
        // $data['seasonTeams'] = \TeamSeason::SeasonListing($data['season']);
        // if ($logging === true) $log[__LINE__.":".microtime(true)] = count($data['seasonTeams']) . " seasonTeams";
        $data['fixtures'] = \Fixture::forSeason($data['season']);
        $data['isFixturesLocked'] = $data['season']->isLocked() ? false : \Fixture::isLocked($data['fixtures']);
        if ($logging === true && $data['fixtures']) $log[__LINE__.":".microtime(true)] = count($data['fixtures']) . " fixtures";
        if ($data['fixtures']) $data['rescheduleReasons'] = \Fixture::RescheduleReasons();
        $data['schedule'] = \Schedule::forSeason($data['season']);
        if ($logging === true && $data['schedule']) $log[__LINE__.":".microtime(true)] = count($data['schedule']) . " schedule";
        // $data['users'] = \User::League($data['league']);
        // $log[__LINE__.":".microtime(true)] = count($data['users']) . " users";
        // $data['seasonsTeams'] = \TeamSeason::SeasonsTeams($data['season']);
        $data['seasonTeams'] = \Team::SeasonTeams($data['season']);
        if ($logging === true) $log[__LINE__.":".microtime(true)] = count($data['seasonTeams']) . " seasonTeams";
        // $log[__LINE__.":".microtime(true)] = count($data['seasonsTeams']) . " seasonsTeams";
    }

    if (!isset($_POST['showAllResults'])) $_POST['showAllResults']= null;
    $data['tab'] = (isset($post['tab']) && $post['tab']) ? $post['tab'] : "main";
    if ($rebuildApi === true) \Trigger::Set("System::BuildLiveLeaguesApi");
    \Messaging\Show();
    if ($logging === true) {
        $startTime = $_SERVER['REQUEST_TIME'];
        echo "<!--\n$startTime\n";
        foreach ($log as $k => $v) {
            list ($line, $time) = explode(":",$k);
            $elapsed = round($time - $startTime,2);
            echo "$elapsed : $line $time $v\n";
        } 
        echo "-->";
    }
    return $data;
}