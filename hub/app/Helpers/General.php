<?php

use Dompdf\Dompdf;
use Dompdf\Options;

function getDomainPrefix() {
    $match = substr($_SERVER['HTTP_HOST'], 0, strpos($_SERVER['HTTP_HOST'], '.'));
    return match ($match) {
        'local' => 'http://local.',
        'staging' => 'https://staging.',
        default => 'https://',
    };
}

function memoryLimit($limit = '256M') {

    ini_set('memory_limit', $limit);
}

function weeksBetweenDates($start_date, $end_date) {
    // Convert dates to DateTime objects
    $start_date = new DateTime($start_date);
    $end_date = new DateTime($end_date);

    // Calculate the interval
    $interval = $start_date->diff($end_date);

    // Calculate the number of weeks
    $weeks = floor($interval->days / 7);

    return $weeks;
}


function printRiskAssessment($instance, $dateOfAssessment) {
    $user = new \User($instance->user_id);
    $exportUserName = $user->firstname . ' ' . $user->lastname;
    $risk = json_decode($instance->data, true);
    $venue = $instance->getVenue();

    $options = new Options();
    $options->set("isPhpEnabled", true);
    $dompdf = new Dompdf($options);
    ob_start();
    $file = 'app/Views/User/Forms/RiskAssessment.php';
    include_once($file);
    $content = ob_get_clean();
    $contentWithoutButton = preg_replace('/<button id="save-button">.*?<\/button>/s', '', $content);
    $contentWithHiddenDropdownIcons = preg_replace(

        '/<select(.*?)>/',
        '<select$1 style="-webkit-appearance:none !important; -moz-appearance:none !important; appearance:none !important;">',
        $contentWithoutButton
    );
    $contentWithHiddenDropdownIconsBd = preg_replace(
        '/<input(.*?)>/',
        '<input$1 style="width: 90px;">',
        $contentWithHiddenDropdownIcons
    );
    $html = "<html><head><title>$venue->name</title></head>";
    $html .= "<body>";
    $html .= $contentWithHiddenDropdownIconsBd;
    $html .= "</body></html>";
    $dompdf->loadHtml($html);
    // (Optional) Setup the paper size and orientation
    $dompdf->setPaper('A4', 'landscape');
    // Render the HTML as PDF
    $dompdf->render();
    // Output the generated PDF to Browser
    header('Content-Type: application/pdf');
    $dompdf->stream("RiskAssessment-$venue->name-$dateOfAssessment.pdf", array("Attachment" => false));
    exit;
}


function calculateWeekDates($start, $end) {
    $current = strtotime($start);
    $end = strtotime($end);

    $dates = [];

    while ($current <= $end) {
        $weekStart = date('d/m/Y', $current);

        // If the current date is a Sunday, set it as the week's end
        if (date('w', $current) == 0) {
            $weekEnd = $weekStart;
        } else {
            // Otherwise, calculate the end of the week (Sunday) or use the end date if it's earlier
            $weekEnd = date('d/m/Y', min(strtotime('next sunday', $current), $end));
        }

        $dates[] = [
            'start' => $weekStart,
            'end' => $weekEnd
        ];

        $current = strtotime('next monday', $current);
    }

    // The latest array end date should be the same as the end date
    $dates[count($dates) - 1]['end'] = date('d/m/Y', $end);

    return $dates;
}


function filterDataByDateRange($data, $start, $end) {
    $filteredArray = array();

    foreach ($data as $item) {
        $week = $item['week'];
        $weekDate = DateTime::createFromFormat('d/m/Y', $week);
        $startRange = DateTime::createFromFormat('d/m/Y', $start);
        $endRange = DateTime::createFromFormat('d/m/Y', $end);

        if ($weekDate >= $startRange && $weekDate <= $endRange) {
            $filteredArray[] = $item;
        }
    }

    return $filteredArray;
}


function roundTwo($num) {
    return number_format((float)$num, 2, '.', '');
}

function callViaCurl($url, $jwt) {
    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_HTTPHEADER => array(
            'Authorization: Bearer ' . $jwt
        ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);
    return $response;
}

function loginRedirect() {
    if (isset($_COOKIE['jwt']) && !empty($_COOKIE['jwt'])) {
        if (
            Jwt::Validate($_COOKIE['jwt']) == "Invalid Token" ||
            Jwt::Validate($_COOKIE['jwt']) == "Malformed" ||
            Jwt::Validate($_COOKIE['jwt']) == "Deleted" ||
            Jwt::Validate($_COOKIE['jwt']) == "Expired"
        ) {
            $prefix = getDomainPrefix();
            header("location: " . $prefix . "hub.leagues4you.co.uk/Home/Login");
            exit;
        }
    } else {
        $prefix = getDomainPrefix();
        header("location: " . $prefix . "hub.leagues4you.co.uk/Home/Login");
    }
}
function getJwt() {
    global $_COOKIE;
    return $_COOKIE['jwt'];
}

function ukDateFormat($date) {
    return date('d-m-Y', strtotime($date));
}

/**
 * Check if user is authenticated via JWT
 * If not authenticated, redirects to login page
 * 
 * @param string|null $redirectUrl Optional custom redirect URL
 * @return bool True if authenticated, otherwise redirects and exits
 */
function checkJwtAuth($redirectUrl = null) {
    if (
        !isset($_COOKIE['jwt']) ||
        !$_COOKIE['jwt'] ||
        is_string(\Jwt::Validate($_COOKIE['jwt']))
    ) {
        // Store current URL for redirect after login if needed
        if (!isset($_SESSION)) {
            session_start();
        }
        $_SESSION['redirectAfterLogin'] = $_SERVER['REQUEST_URI'];

        // Redirect to login page
        if ($redirectUrl) {
            header("Location: " . $redirectUrl);
        } else {
            $prefix = getDomainPrefix();
            header("Location: " . $prefix . "hub.leagues4you.co.uk/Home/Login");
        }
        exit(0);
    }

    return true;
}
