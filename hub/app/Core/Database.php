<?php
namespace Database;

use PDO;

function Connect() {
    $dbName = "leagues4you";
    $dbHost = "127.0.0.1";
    $dbUsername = "leagues4you";
    $dbPassword = "(S1mpl1f1c4t10n-Pr0gr4m!)";
    try {
        $pdoOptions = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_EMULATE_PREPARES => true
        ];
        $pdo = new \PDO("mysql:dbname=$dbName;host=$dbHost", $dbUsername, $dbPassword,$pdoOptions);
        return $pdo;
    } catch (\PDOException $e) {
        echo 'Connection failed: ' . $e->getMessage();
    }    
}
function Sql (String $sql) {
    $dbh = Connect();
    $stm = $dbh->prepare($sql);
    try {
        $stm->execute();
        return true;
    } catch (\PDOException $e) {
        return $e->getMessage();
    } catch (Exception $e) {
        return $e->getMessage();
    }
}
function Query(String $sql, Array $data = []) {
    $dbh = Connect();
    $stm = $dbh->prepare($sql);
    $return = [];
    try {
        $stm->execute($data);
        while ($row = $stm->fetch(PDO::FETCH_ASSOC)) $return[] = $row;
        return $return;
    } catch (\PDOException $e) {
        return $e->getMessage();
    } catch (Exception $e) {
        return $e->getMessage();
    }
}

function Insert(String $table, Array $data = []) {
    $dbh = Connect();
    $sql = $conn = null;
    $sql = "INSERT INTO `$table` SET ";
    foreach ($data as $field => $value) {
        if (!$value && !is_numeric($value)) $data[$field] = null;
        $sql .= $conn."`$field` = :$field";
        $conn = ",";
    } 
    $stm = $dbh->prepare($sql);
    try {
        $stm->execute($data);
        return (int)$dbh->lastInsertId();
    } catch (\PDOException $e) {
        // \Tools\Dump($stm->debugDumpParams());
        return $e->getMessage();
    } catch (Exception $e) {
        return $e->getMessage();
    }
}

function Update(String $sql, Array $data = []) {
    $dbh = Connect();
    foreach ($data as $field => $value) {
        if (!$value && !is_numeric($value)) $data[$field] = null;
    }
    try {
        $stm = $dbh->prepare($sql);
        $stm->execute($data);
        return (int)$stm->rowCount();
    } catch (\PDOException $e) {
        return $e->getMessage();
    } catch (Exception $e) {
        return $e->getMessage();
    }
}