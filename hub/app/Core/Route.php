<?php

namespace Route;

$GLOBALS['url'] = [
    "controller" => null,
    "action" => null,
    "variables" => null,
];

function Set() {
    if (isset($_GET['url'])) {
        $elements = explode("/",$_GET['url']);
        $GLOBALS['url']['controller'] = ($elements) ? array_shift($elements) : "User";
        if ($elements) $GLOBALS['url']['action'] = array_shift($elements);
        if ($elements) $GLOBALS['url']['variables'] = $elements;
    } else $GLOBALS['url']['controller'] = "User";
}

function Display (Array $url) {
    // exit(json_encode($url));
    // Select Controller
    $data['controller'] = (isset($url['controller']) && $url['controller']) ? ucwords($url['controller']) : "Home";
    // Select the Action
    $data['action'] = (isset($url['action']) && $url['action']) ? $url['action'] : "Index";
    
    // Extract URL variables
    if (isset($url['variables']))$data['variables'] = $url['variables'];
    $controllerClass = $data['controller']."Controller";
    // Class and method exists check
    // if(!class_exists($controllerClass) || !method_exists($controllerClass, $data['action'])) exit("Not Found");
    $controller = new $controllerClass();
    // exit(json_encode($data));
    // exit(json_encode($controllerClass));
    call_user_func_array([$controller,$data['action']], [$data]);
    // } else {
    //     if ($data['controller'] !="Home" && !\User::Authenticated()) {
    //         $_SESSION['redirectAfterLogin'] = "/".$data['controller']."/".$data['action'];
    //         header("Location: /Home/Login");
    //         exit(0);    
    //     }
        
    //     if (function_exists($data['controller']."\\".$data['action'])) $data = call_user_func($data['controller']."\\".$data['action'],$data);
    //     if (isset($data['redirect']) && $data['redirect']) {
    //         header("Location: {$data['redirect']}"); exit(0);
    //     }
    //     // \Messaging\Show();
    //     $view = __DIR__."/../Views/{$data['controller']}/{$data['action']}.{$data['controller']}.php";
    //     if (!file_exists($view)) $view = __DIR__."/../Views/Home/Index.Home.php";
    //     include($view);
    // }
}