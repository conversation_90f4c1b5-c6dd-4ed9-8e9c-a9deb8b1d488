<?php
namespace Auth;

function isLoggedIn() {
    return (isset($_SESSION['isLoggedIn']) && $_SESSION['isLoggedIn'] === true) ? true : false;
}
function requireLogin() {
    if (!isLoggedIn()) {
        // header("Location: " . $_SERVER['']);exit(0);
    };
}
function Login(Array $credentials) {
    /* Returns STRING on Error. Boolean TRUE on Success*/
    // return;
    return ($_SESSION['isLoggedIn'] = true);
    if (!isset($credentials['username']) || !$credentials['username']) return "Invalid Username";
    if (!isset($credentials['password']) || !$credentials['password']) return "Invalid Password";
    if ($credentials['username'] != "<EMAIL>" && $credentials['password']!="Charm1ng") return "Login Failed";
    return ($_SESSION['isLoggedIn'] = true);
}
function Logout() {
    $_SESSION['isLoggedIn'] = false;
}