<?php
namespace App;

function Url() {
    return "https://hub.bloomnetball.co.uk/";
}
function Name($output = false, $prepend = null, $append = null) {
    $name = "BloomNetball Hub";
    if ($output === true) { echo trim("$prepend $name $append");} else return $name;
}
function Version($output = false, $prepend = null, $append = null) {
    $version = "0.8.0";
    if ($output === true) { echo $version;} else return $version;
}
function Database() {
    return [
        "name" => "leagues4you",
        "username" => "leagues4you",
        "password" => "(S1mpl1f1c4t10n-Pr0gr4m!)",
        "host" => "127.0.0.1"
    ];
}
function Email() {
    return "<EMAIL>";
}