<?php

class UserController extends Controller {
    
    protected $folder = "User";

    function __construct() {
        parent::__construct();
    }
    function Index() {
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function Test() {
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function Dashboard() {
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function WebSearches (Array $data = []) {
        // $this->Header();
        global $post;
        $data['searchDate'] = (isset($post['searchDate']) && $post['searchDate']) ? $post['searchDate'] : date('Y-m-d');
        $data['searches'] = \WebSearchLocation::byDate($data['searchDate']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function Users (Array $data = []) {
        // $this->Header();
        global $post;
        if (isset($post['user'])) {
            $user = new \User();
            $user->Load($post['user']);
            $user->Save();
        }
        if (isset($post['activateUserID']) && $post['activateUserID']) {
            $rlt = \User::AdminActivation($post['activateUserID']);
            ($rlt) ? \Messaging\Add("User Activated","success") : \Messaging\Add("Activation Failed","danger");
        } 
        // \Tools\Dump($post);
        if (isset($post['newPassword']) && $post['newPassword']) {
            $user = new \User($post['newPassword']['userID']);
            $rlt = $user->ChangePassword([$post['newPassword']['password1'],$post['newPassword']['password1']]);
            ($rlt === true) ? \Messaging\Add("Password updated","success") : \Messaging\Add("Password could not be updated : $rlt","danger");
            
        }
        $data['users'] = \User::Customers();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function Sports (Array $data = []) {
        global $post;
        if (isset($post['sport'])) {
            $data['sport'] = new \Sport();
            $data['sport']->Load($post['sport']);
            $data['sport']->Save();
        } elseif (isset($post['sportID'])) {
            $data['sport'] = new \Sport($post['sportID']);
        } else $data['sport'] = new \Sport();
        $data['sports'] = \Sport::Listing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function Venues(Array $data = []) {
        global $post;
        $data['tab'] = "main";
        if (isset($_GET['venueID']) && $_GET['venueID']) $post['venueID'] = $_GET['venueID'];
        $data['venueID'] = (isset($post['venueID']) && $post['venueID']) ? $post['venueID'] : null;
        // if (isset($_POST['purchasing'])) {
        //     $PurchaseTransaction = new PurchaseTransaction();
        //     $PurchaseTransaction->Load($_POST['purchasing']);
        //     $PurchaseTransaction->Save();
        //     if (isset($_FILES['transactionFile'])) {
        //         $PurchaseTransaction->filename = $PurchaseTransaction->id.".".pathinfo($_FILES['transactionFile']['name'],PATHINFO_EXTENSION);
        //         $target = $PurchaseTransaction->folder.DIRECTORY_SEPARATOR.$PurchaseTransaction->filename;
        //         move_uploaded_file($_FILES['transactionFile']['tmp_name'], $target);
        //         $PurchaseTransaction->Save();
        //     }
        //     if ($PurchaseTransaction->id) {
        //         $msg = "Purchase Transaction {$PurchaseTransaction->id} added";
        //         \Logging::Add("$msg  by " . \User::AuthUser());
        //         \Messaging\Add("$msg for {$PurchaseTransaction->total}");
        //         $data['venueID'] = $PurchaseTransaction->venueID;
        //         $data['tab'] = "purchasing";
        //     } 
        // }
        if (isset($post['venue'])) {
            $venue = new \Venue();
            $venue->Load($post['venue']);
            $venue->Save();
            $data['venueID'] = $venue->id;
        }
        if (isset($post['facilities'])) {
            $data['venueID'] = $post['facilities']['id'];
            $venue = new \Venue($post['facilities']['id']);
            $facilities = ["facility_freeParking","facility_paidParking","facility_changingRooms","facility_showers","facility_cafe","facility_bar"];
            foreach ($facilities as $facility) {
                if (!isset($post['facilities'][$facility]) || $post['facilities'][$facility] != 1) {
                    $post['facilities'][$facility] = null;
                }
            }
            $venue->Load($post['facilities']);
            $venue->Save();
            $data['tab'] = "facilities";
            $data['venueID'] = $venue->id;
        }
        if (isset($post['map'])) {
            $data['venueID'] = $post['map']['id'];
            $venue = new \Venue($post['map']['id']);
            $venue->Load($post['map']);
            $venue->Save();
            $data['tab'] = "map";
            $data['venueID'] = $venue->id;
        }
        if (isset($post['taster'])) {
            $ts = new TasterSession();
            $ts->Load($post['taster']);
            $ts->Save();
            $data['venueID'] = $ts->venueID;
            $data['tab'] = "taster";
        }
        $data['venues'] = \Venue::Listing();
        $data['sports'] = \Sport::Listing();
        if (isset($data['venueID']) && $data['venueID']) {
            $data['venue'] = new \Venue($data['venueID']);
            $data['leagues'] = League::byVenue($data['venue']);
            $data['transactions'] = \PurchaseTransaction::Venue($data['venue']);
        } else $data['venue'] = new \Venue();
        $data['tasterSessions'] = TasterSession::Venue($data['venue']);
        if ($data['venue']->getLat() && $data['venue']->getLng()) {
            $data['weather'] = new \Weather($data['venue']->getLat(),$data['venue']->getLng());
        } else $data['weather'] = null;

        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function TasterSessions (Array $data = []) {
        global $post;
        $data['tasterSessions'] = \TasterSession::Available();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function TasterSession (Array $data = []) {
        global $post;
        $data['tab'] = "main";
        if (isset($_POST['taster'])) {
            $taster = new \TasterSession();
            $taster->Load($_POST['taster']);
            // Tools::Dump($taster); exit(0);
            $taster->Save();
            $data['variables'][0] = $taster->id;
        }
        if (isset($data['variables'][0]) && $data['variables'][0]) {
            $data['tasterSession'] = new \TasterSession($data['variables'][0]);
            $data['attendees'] = $data['tasterSession']->attendees();
        } else $data['tasterSession'] = new \TasterSession();
        // Tools::Dump($data);
        $data['venues'] = \Venue::Listing();
        $data['sports'] = \Sport::Listing();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function PurchaseTransactions (Array $data = []) {
        $data['purchaseTransactions'] = PurchaseTransaction::Listing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    } 
    function PurchaseTransaction (Array $data = []) {
        $data['tab'] = (isset($_POST['tab']) && $_POST['tab']) ? $_POST['tab'] : "main";
        if (isset($_POST['removeitem'])) {
            $pi_item = new PurchaseTransactionItem($_POST['removeitem']);
            $pi_item->Delete();
        }
        if (isset($_POST['newitem'])) {
            $pi_item = new PurchaseTransactionItem();
            $pi_item->Load($_POST['newitem']);
            $rlt = $pi_item->Save();
            if (is_string($rlt)) \Messaging\Add($rlt,"danger");
        }
        if (isset($_POST['purchaseTransaction'])) {
            $PurchaseTransaction = new PurchaseTransaction();
            $PurchaseTransaction->Load($_POST['purchaseTransaction']);
            $PurchaseTransaction->Save();
            if (isset($_FILES['transactionFile'])) {
                $PurchaseTransaction->filename = $PurchaseTransaction->id.".".pathinfo($_FILES['transactionFile']['name'],PATHINFO_EXTENSION);
                $target = $PurchaseTransaction->folder.DIRECTORY_SEPARATOR.$PurchaseTransaction->filename;
                move_uploaded_file($_FILES['transactionFile']['tmp_name'], $target);
                $PurchaseTransaction->Save();
            }
            if ($PurchaseTransaction->id) {
                $data['variables'][0] = $PurchaseTransaction->id;
                $msg = "Purchase Transaction {$PurchaseTransaction->id} added";
                \Logging::Add("$msg  by " . \User::AuthUser());
                \Messaging\Add("$msg for {$PurchaseTransaction->total}");
                // $data['venueID'] = $PurchaseTransaction->venueID;
                $data['tab'] = "items";
            } 
        }
        $data['venues'] = Venue::Listing();
        if (isset($_POST['purchaseTransaction']['id']) && $_POST['purchaseTransaction']['id']) $data[0] = $_POST['purchaseTransaction']['id'];
        $data['purchaseTransaction'] = (isset($data['variables'][0]) && $data['variables'][0]) ? new PurchaseTransaction($data['variables'][0]) : new PurchaseTransaction();
        $evaluation = $data['purchaseTransaction']->Evaluation();
        if (is_numeric($evaluation)) {
            \Messaging\Add("$evaluation left to allocate");
        } elseif ($evaluation === true) {
            \Messaging\Add("Allocated","success");
        } else {
            \Messaging\Add("Requires Allocation","warning");
        }
        $data['purchaseTransactionItems'] = PurchaseTransactionItem::PurchaseTransaction($data['purchaseTransaction']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function PasswordChange (Array $data = []) {
        global $post;
        if (isset($post['password'])) {
            $user = \User::Authenticated();
            $rlt = $user->ChangePassword($post['password']);
            if ($rlt ===true) {
                \Messaging\Add("Password updated","success");
                header("Location: /User");
                exit(0);
            } else \Messaging\Add($rlt,"warning");
        }
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function Sweep(Array $data = []) {
        $data['due'] = \Fixture:: Billable ();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }
    function WebContacts (Array $data = []) {
        global $post;
        if (isset($post['spam']) && $post['spam']) \WebContact::markSpam($post['spam']);
        if (isset($post['complete']) && $post['complete']) \WebContact::markCompleted($post['complete']);
        $data['webContacts'] = \WebContact::Listing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }

    function Logout() {
        $user = \User::Authenticated();
        $user->Logout();
        \Messaging\Add("You have been logged out","success");
        header("Location: /Home");
        exit(0);
    }

    function TeamStatus (Array $data = []) {
        global $post;
        $data['leagueID'] = (isset($post['leagueID']) && $post['leagueID']) ? $post['leagueID'] : null;
        $data['captainID'] = (isset($post['captainID']) && $post['captainID']) ? $post['captainID'] : null;
        $data['coordinatorID'] = (isset($post['coordinatorID']) && $post['coordinatorID']) ? $post['coordinatorID'] : null;
        $data['teams'] = \Team::Report();
        $data['leagues'] = \League::Listing();
        $data['captains'] = \User::Captains();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }

    function LeagueReport() {
        $data['leagueReport'] = \Team::inLeaguesReport();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }

    function OpReport() {
        $data['opReport'] = \League::OpReport();
        $data['regions'] = \Region::Query();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }

    function Fixtures () {
        global $post;
        $data['startDate'] = date('Y-m-d');
        $data['endDate'] = date('Y-m-d');
        if (isset($post['startDate']) && $post['startDate']) $data['startDate'] = $post['startDate'];
        if (isset($post['endDate']) && $post['endDate']) $data['endDate'] = $post['endDate'];
        $data['report'] = Fixture::Report($data['startDate'],$data['endDate']);
        if (!$data['report']) $data['report'] = [];
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
        $this->Foot();
    }

    function Bookings() {
        if (!isset($_POST['startDate']) || !$_POST['startDate']) $_POST['startDate'] = date('Y-m-01');
        if (!isset($_POST['endDate']) || !$_POST['endDate'] || strtotime($_POST['endDate']) < strtotime($_POST['startDate'])) {
            $_POST['endDate'] = date('Y-m-t',strtotime($_POST['startDate']));
        } 
        $data['totalBookings'] = Booking::Report($_POST['startDate'],$_POST['endDate']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
        $this->Foot();
    }

}