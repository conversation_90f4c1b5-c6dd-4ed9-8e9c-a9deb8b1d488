<?php

class HomeController extends Controller {

    function __construct() {
        parent::__construct();

    }
    function Index() {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        // exit(0);
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
        $this->Foot();
        // \Messaging\Show();
        // include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Login () {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
        $this->Foot();
        // \Messaging\Show();
        // include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Password () {
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }

}