<?php
class UserController extends Controller {

    // protected $folder = "User";

    function __construct() {
        parent::__construct();
        checkJwtAuth();
    }

    function Document(array $data = []) {
        // Tools::Dump($data);
        if (!isset($data['variables'][0]) || !$data['variables'][0] || !is_numeric($data['variables'][0])) exit("No ID");
        $doc = new DocLibraryVersion($data['variables'][0]);
        Tools::Dump($doc);
    }

    function Index() {
        header("Location: /Coordinator");
        exit(0);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function League(array $data) {
        memoryLimit();

        $data = \User\League($data);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Test() {
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Dashboard() {
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function WebSearches(array $data = []) {
        // $this->Header();
        global $post;
        $data['searchDate'] = (isset($post['searchDate']) && $post['searchDate']) ? $post['searchDate'] : date('Y-m-d');
        $data['searches'] = \WebSearchLocation::byDate($data['searchDate']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Users(array $data = []) {
        // $this->Header();
        global $post;
        if (isset($post['user'])) {
            $user = new \User();
            $user->Load($post['user']);
            $user->Save();
        }
        if (isset($post['activateUserID']) && $post['activateUserID']) {
            $rlt = \User::AdminActivation($post['activateUserID']);
            ($rlt) ? \Messaging\Add("User Activated", "success") : \Messaging\Add("Activation Failed", "danger");
        }
        // \Tools\Dump($post);
        if (isset($post['newPassword']) && $post['newPassword']) {
            $user = new \User($post['newPassword']['userID']);
            $rlt = $user->ChangePassword([$post['newPassword']['password1'], $post['newPassword']['password1']]);
            ($rlt === true) ? \Messaging\Add("Password updated", "success") : \Messaging\Add("Password could not be updated : $rlt", "danger");
        }
        if (isset($_POST['archiveUser'])) \User::Archive($_POST['archiveUser']);
        if (isset($_POST['unarchiveUser'])) \User::Unarchive($_POST['unarchiveUser']);
        $data['users'] = \User::Listing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Sports(array $data = []) {
        global $post;
        if (isset($post['sport'])) {
            $data['sport'] = new \Sport();
            $data['sport']->Load($post['sport']);
            $data['sport']->Save();
        } elseif (isset($post['sportID'])) {
            $data['sport'] = new \Sport($post['sportID']);
        } else $data['sport'] = new \Sport();
        $data['sports'] = \Sport::Listing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Venues(array $data = []) {

        global $post;
        $data['tab'] = "main";
        if (@$_GET['tab']) $data['tab'] = $_GET['tab'];
        if (isset($_GET['venueID']) && $_GET['venueID']) $post['venueID'] = $_GET['venueID'];
        $data['venueID'] = (isset($post['venueID']) && $post['venueID']) ? $post['venueID'] : null;
        $data['coordinators'] = \User::Coordinators();
        // if (isset($_POST['purchasing'])) {
        //     $PurchaseTransaction = new PurchaseTransaction();
        //     $PurchaseTransaction->Load($_POST['purchasing']);
        //     $PurchaseTransaction->Save();
        //     if (isset($_FILES['transactionFile'])) {
        //         $PurchaseTransaction->filename = $PurchaseTransaction->id.".".pathinfo($_FILES['transactionFile']['name'],PATHINFO_EXTENSION);
        //         $target = $PurchaseTransaction->folder.DIRECTORY_SEPARATOR.$PurchaseTransaction->filename;
        //         move_uploaded_file($_FILES['transactionFile']['tmp_name'], $target);
        //         $PurchaseTransaction->Save();
        //     }
        //     if ($PurchaseTransaction->id) {
        //         $msg = "Purchase Transaction {$PurchaseTransaction->id} added";
        //         \Logging::Add("$msg  by " . \User::AuthUser());
        //         \Messaging\Add("$msg for {$PurchaseTransaction->total}");
        //         $data['venueID'] = $PurchaseTransaction->venueID;
        //         $data['tab'] = "purchasing";
        //     } 
        // }
        $data['incident_report'] = IncidentReport::getVenueIncidentReport($data['venueID']);
        if (isset($_POST['venueFinancials'])) {
            $venue = new Venue($_POST['venueFinancials']['id']);
            $venue->Load($_POST['venueFinancials']);
            $venue->Save();
            $data['tab'] = "finances";
            $data['venueID'] = $venue->id;
        }
        if (isset($post['venue'])) {
            $venue = new \Venue();
            $venue->Load($post['venue']);
            $venue->status = $post['venue']['status'] ?? 1;
            $venue->Save();
            $data['venueID'] = $venue->id;
        }
        if (isset($post['facilities'])) {
            $data['venueID'] = $post['facilities']['id'];
            $venue = new \Venue($post['facilities']['id']);
            $facilities = ["facility_freeParking", "facility_paidParking", "facility_changingRooms", "facility_showers", "facility_cafe", "facility_bar"];
            foreach ($facilities as $facility) {
                if (!isset($post['facilities'][$facility]) || $post['facilities'][$facility] != 1) {
                    $post['facilities'][$facility] = null;
                }
            }
            $venue->Load($post['facilities']);
            $venue->Save();
            $data['tab'] = "facilities";
            $data['venueID'] = $venue->id;
        }
        if (isset($post['map'])) {
            $data['venueID'] = $post['map']['id'];
            $venue = new \Venue($post['map']['id']);
            $venue->Load($post['map']);
            $venue->Save();
            $data['tab'] = "map";
            $data['venueID'] = $venue->id;
        }
        if (isset($post['taster'])) {
            $ts = new TasterSession();
            $ts->Load($post['taster']);
            if (isset($post['taster']['is_wildcard'])) {
                $ts->is_wildcard = 1;
            } else {
                $ts->is_wildcard = 0;
            }
            if (isset($post['taster']['is_tournament'])) {
                $ts->is_tournament = 1;
            } else {
                $ts->is_tournament = 0;
            }
            $result = $ts->Save();
            $this->throw_unless(is_int($result), "ERROR: tasterSession creation failed. ($result)");

            if ($ts->getVenue() && !str_contains($ts->getVenue()->name, 'WILDCARD')) {
                $ts->slug = $this->slugify($ts->id . '-' . $ts->getVenue()->name);
                $result = $ts->Save();
                $this->throw_unless(is_int($result), "ERROR: tasterSession updating failed. ($result)");
            }

            $data['venueID'] = $ts->venueID;
            $data['tab'] = "taster";
        }
        if (isset($_POST['notes'])) {
            $venue = new \Venue($_POST['notes']['id']);
            $venue->Load($_POST['notes']);
            $venue->Save();
            $data['venueID'] = $venue->id;
            $data['tab'] = "notes";
        }
        if (isset($_POST['date_of_assessment'])) {
            $riskId = $post['riskId'] ?? null;
            $riskModel = new \RiskAssesment($riskId);
            $riskModel->Load([
                'data' => json_encode($_POST['form']),
                'venue_id' => $_POST['venue_id'],
                'user_id' => \User::Authenticated()->id,
                'date_of_assessment' => $_POST['date_of_assessment'],
                'status' => 2, // published default
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            $riskModel->Save();
            $data['venueID'] = $_POST['venue_id'];
            $data['tab'] = "risks";
        }
        $data['venues'] = \Venue::Listing();
        $data['sports'] = \Sport::Listing();
        if (isset($data['venueID']) && $data['venueID']) {
            $data['venue'] = new \Venue($data['venueID']);
            $data['leagues'] = League::byVenue($data['venue']);
            $data['transactions'] = \PurchaseTransaction::Venue($data['venue']);
            $data['allRisksAssesment'] = $data['venue']->getRiskAssesments();
        } else $data['venue'] = new \Venue();
        $data['tasterSessions'] = TasterSession::Venue($data['venue']);
        if ($data['venue']->getLat() && $data['venue']->getLng()) {
            $data['weather'] = new \Weather($data['venue']->getLat(), $data['venue']->getLng());
        } else $data['weather'] = null;

        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function TasterSessions(array $data = []) {
        global $post;

        // Ensure the 'from' date is set; default to today if not provided
        if (!isset($_POST['from']) || !$_POST['from']) {
            $_POST['from'] = date('Y-m-d');
        }

        // Handle session type: default to null if not provided
        $data['sessionType'] = $_POST['sessionType'] ?? null;

        // Handle coordinator ID: cast to integer if set, otherwise null
        $data['coordinatorID'] = isset($_POST['coordinatorID']) && $_POST['coordinatorID'] !== ''
            ? (int)$_POST['coordinatorID']
            : null;


        // Fetch taster sessions based on the filters
        $data['tasterSessions'] = \TasterSession::Available(
            $_POST['from'],
            $data['sessionType'],
            $data['coordinatorID'],
        );

        // Fetch coordinators for the dropdown menu
        $data['coordinators'] = \User::Coordinators();

        // Render the page
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }



    function TasterSession(array $data = []) {
        global $post;
        $data['tab'] = "main";
        if (isset($_POST['refundTaster'])) {
            // $taster = new TasterSession($_POST['refundTaster']);
            // $rlt = $taster->Refund();
        }
        if (isset($_POST['cancelTaster'])) {
            // $taster = new TasterSession($_POST['cancelTaster']);
            // $rlt = $taster->Cancel();
        }
        if (isset($_POST['refundC2c'])) {
            $c2c_booking = new C2C_Booking($_POST['refundC2c']);
            $rlt = $c2c_booking->Refund();
            $data['tab'] = "attendees";
        }
        if (isset($_POST['taster'])) {
            $taster = new \TasterSession();
            $taster->Load($_POST['taster']);
            if (isset($post['taster']['is_wildcard'])) {
                $taster->is_wildcard = 1;
            } else {
                $taster->is_wildcard = 0;
            }
            if (isset($post['taster']['is_tournament'])) {
                $taster->is_tournament = 1;
            } else {
                $taster->is_tournament = 0;
            }
            // Tools::Dump($taster); exit(0);
            $taster->Save();
            $data['variables'][0] = $taster->id;
        }
        if (isset($data['variables'][0]) && $data['variables'][0]) {
            $data['tasterSession'] = new \TasterSession($data['variables'][0]);
            $data['attendees'] = $data['tasterSession']->attendees();
        } else $data['tasterSession'] = new \TasterSession();
        // Tools::Dump($data);
        $data['venues'] = \Venue::Listing();
        $data['sports'] = \Sport::Listing();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function PurchaseTransactions(array $data = []) {
        $data['purchaseTransactions'] = PurchaseTransaction::Undeleted();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function PurchaseTransaction(array $data = []) {
        $data['tab'] = (isset($_POST['tab']) && $_POST['tab']) ? $_POST['tab'] : "main";
        if (isset($_POST['removeitem'])) {
            $pi_item = new PurchaseTransactionItem($_POST['removeitem']);
            $data['variables'][0] = $pi_item->mainID;
            $pi_item->Delete();
        }
        if (isset($_POST['newitem'])) {
            $pi_item = new PurchaseTransactionItem();
            $pi_item->Load($_POST['newitem']);
            $rlt = $pi_item->Save();
            $data['variables'][0] = $pi_item->mainID;
            if (is_string($rlt)) {
                \Messaging\Add($rlt, "danger");
            } else {
                $PurchaseTransaction = new PurchaseTransaction($pi_item->mainID);
                if ($PurchaseTransaction->Evaluation() === true) {
                    header("Location: /User/PurchaseTransactions");
                    exit(0);
                }
            }
        }
        if (isset($_POST['purchaseTransaction'])) {
            $PurchaseTransaction = new PurchaseTransaction();
            $PurchaseTransaction->Load($_POST['purchaseTransaction']);
            $PurchaseTransaction->Save();
            if (isset($_FILES['transactionFile'])) {
                $PurchaseTransaction->filename = $PurchaseTransaction->id . "." . pathinfo($_FILES['transactionFile']['name'], PATHINFO_EXTENSION);
                $target = $PurchaseTransaction->folder . DIRECTORY_SEPARATOR . $PurchaseTransaction->filename;
                move_uploaded_file($_FILES['transactionFile']['tmp_name'], $target);
                $PurchaseTransaction->Save();
            }
            if ($PurchaseTransaction->id) {
                $data['variables'][0] = $PurchaseTransaction->id;
                $msg = "Purchase Transaction {$PurchaseTransaction->id} added";
                \Logging::Add("$msg  by " . \User::AuthUser());
                \Messaging\Add("$msg for {$PurchaseTransaction->total}");
                // $data['venueID'] = $PurchaseTransaction->venueID;
                $data['tab'] = "items";
            }
        }
        $data['venues'] = Venue::Listing();
        if (isset($_POST['purchaseTransaction']['id']) && $_POST['purchaseTransaction']['id']) $data[0] = $_POST['purchaseTransaction']['id'];
        $data['purchaseTransaction'] = (isset($data['variables'][0]) && $data['variables'][0]) ? new PurchaseTransaction($data['variables'][0]) : new PurchaseTransaction();
        $evaluation = $data['purchaseTransaction']->Evaluation();
        $data['isClosed'] = $data['purchaseTransaction']->isClosed();
        if (is_numeric($evaluation)) {
            \Messaging\Add("$evaluation left to allocate");
        } elseif ($evaluation === true) {
            \Messaging\Add("Allocated", "success");
        } else {
            \Messaging\Add("Requires Allocation", "warning");
        }
        $data['purchaseTransactionItems'] = PurchaseTransactionItem::PurchaseTransaction($data['purchaseTransaction']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function PasswordChange(array $data = []) {
        global $post;
        if (isset($post['password'])) {
            $user = \User::Authenticated();
            $rlt = $user->ChangePassword($post['password']);
            if ($rlt === true) {
                \Messaging\Add("Password updated", "success");
                header("Location: /User");
                exit(0);
            } else \Messaging\Add($rlt, "warning");
        }
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Sweep(array $data = []) {
        $data['due'] = \Fixture::Billable();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function WebContacts(array $data = []) {
        global $post;
        if (isset($post['spam']) && $post['spam']) \WebContact::markSpam($post['spam']);
        if (isset($post['complete']) && $post['complete']) \WebContact::markCompleted($post['complete']);
        $data['webContacts'] = \WebContact::Listing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Logout() {
        Jwt::Logout($_SESSION['userID']);
        $user = \User::Authenticated();
        $user->Logout();
        \Messaging\Add("You have been logged out", "success");
        header("Location: /Home");
        exit(0);
    }

    function TeamStatus(array $data = []) {
        global $post;
        $data['leagueID'] = (isset($post['leagueID']) && $post['leagueID']) ? $post['leagueID'] : null;
        $data['captainID'] = (isset($post['captainID']) && $post['captainID']) ? $post['captainID'] : null;
        $data['coordinatorID'] = (isset($post['coordinatorID']) && $post['coordinatorID']) ? $post['coordinatorID'] : null;
        $data['teams'] = \Team::Report();
        $data['leagues'] = \League::Listing();
        $data['captains'] = \User::Captains();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }


    function LeagueReport() {
        $data['leagueReport'] = \Team::inLeaguesReport();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function LiveTeamReport() {
        $params = [];

        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $coordinators = \User::Coordinators();

        $selectedCoordinator = isset($_GET['coordinator']) ? $_GET['coordinator'] : null;
        $wildcard = isset($_GET['wildcard']) ? $_GET['wildcard'] : null;

        $sql = "SELECT 
                    l.id as league_id, 
                    l.name as league_name, 
                    s.id as season_id,
                    CASE 
                        WHEN s.statusID = 1 THEN 
                            s.name 
                        ELSE 
                            NULL 
                        END as season_name,
                    s.launchDate as season_launch_date,
                    (
                        SELECT 
                            s2.name AS season_name_next
                        FROM 
                            seasons s2
                        WHERE 
                            s2.statusID = 2
                            AND s2.leagueID = l.id
                            AND s2.launchDate > s.launchDate
                        ORDER BY 
                            s2.launchDate ASC
                        LIMIT 1
                    ) AS season_name_next,
                    (
                        SELECT COUNT(DISTINCT teams.id)
                            
                        FROM 
                            teams 
                        JOIN 
                            teamSeasons ON teamSeasons.teamID = teams.id 
                        JOIN 
                            seasons ON seasons.id = teamSeasons.seasonID 
                        WHERE 
                            teams.deleted IS NULL 
                            AND teamSeasons.deleted IS NULL 
                            AND seasons.deleted IS NULL 
                            AND seasons.statusID = 1 
                            AND teams.leagueID = l.id
                            AND teamSeasons.seasonID = s.id
                    ) as teams_registered,
                    (
                        SELECT 
                            COUNT(teams.id)
                        FROM 
                            leagues, 
                            seasons, 
                            teams, 
                            teamSeasons
                        WHERE 
                            seasons.leagueID = leagues.id 
                            AND teams.leagueID = leagues.id 
                            AND teams.id = teamSeasons.teamID 
                            AND seasons.id = teamSeasons.seasonID
                            AND leagues.id = l.id
                            AND seasons.statusID = 2
                            AND seasons.id = s.id
                    ) as teams_registered_next_season,
                    (
                        SELECT
                            COUNT(DISTINCT(playing_teams.team_id))
                        FROM 
                            (
                                SELECT
                                    home AS team_id,
                                    id AS fixture_id,
                                    divisionID AS division_id
                                FROM
                                    fixtures
                                WHERE
                                    home IS NOT NULL AND deleted IS NULL
                                UNION ALL
                                SELECT
                                    away AS team_id,
                                    id AS fixture_id,
                                    divisionID AS division_id
                                FROM
                                    fixtures
                                WHERE
                                    away IS NOT NULL AND deleted IS NULL
                            ) AS playing_teams
                            LEFT JOIN 
                                `schedule` ON `schedule`.`fixtureID` = playing_teams.fixture_id
                            LEFT JOIN 
                                `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
                            LEFT JOIN 
                                `divisions` ON playing_teams.division_id = `divisions`.`id`
                            WHERE 
                                divisions.seasonID = s.id
                                AND bookings.startDate  BETWEEN CURRENT_DATE() AND DATE_ADD(CURRENT_DATE(), INTERVAL 10 DAY)
                    ) AS playing_teams,
                    (
                        SELECT 
                            COUNT(t.id)
                        FROM 
                            seasons 
                        LEFT JOIN 
                            teamSeasons t ON t.seasonID = seasons.id
                        WHERE 
                            t.wildcard = 1
                            AND seasons.id = s.id
                    ) AS wildcard_teams,
                    (
                        SELECT 
                            MIN(seasons.launchDate) 
						FROM 
                            seasons 
                        WHERE 
                            seasons.launchDate > s.launchDate
                            AND seasons.leagueID = l.id
                            AND seasons.statusID = 2
                    ) as next_season_start_date,
                    (
                        SELECT
                            b.startDate
                        FROM
                            bookings b
                        WHERE
                            b.leagueID = s.leagueID
                        ORDER BY
                            b.startDate DESC
                        LIMIT 1
                    ) AS final_fixture_date,
                    MAX(t.treasurerStripePaymentMethodID) as payment_method_loaded,
                    (
                        SELECT 
                            b.startDate AS current_season_end_date
                        FROM
                            bookings b
                        JOIN
                            seasons s ON b.leagueID = s.leagueID
                        WHERE
                            s.statusID = 2
                        ORDER BY 
                            b.startDate DESC
                        LIMIT 1
                    ) AS current_season_end_date
                FROM 
                    leagues l
                    LEFT JOIN 
                        seasons s ON l.id = s.leagueID AND s.statusID = 1
                    LEFT JOIN 
                        bookings b ON s.id = b.leagueID AND s.statusID = 1
                    LEFT JOIN 
                        teams t ON s.id = t.seasonID AND s.statusID = 1
                    LEFT JOIN 
                        seasons s2 ON l.id = s2.leagueID AND s.statusID = 1
                WHERE 
                    YEAR(s.launchDate) = YEAR(CURRENT_DATE())";

        if ($selectedCoordinator) {
            $sql .= " AND l.id IN (SELECT leagueID FROM teams WHERE coordinator = :coordinator)";
            $params[':coordinator'] = (int) $selectedCoordinator;
        }

        $sql .= " GROUP BY 
             l.id, s.id
         ORDER BY 
             l.name, s.launchDate";

        $result = new Db($sql, $params);
        $totalRegisteredTeams = 0;
        $totalPlayingTeams = 0;
        $totalWildcardTeams = 0;
        $leagues = [];

        foreach ($result->rows as $row) {
            if ($wildcard == 1 && $row['wildcard_teams'] == 0) continue;
            $leagues[$row['league_id']]['name'] = $row['league_name'];
            $leagues[$row['league_id']]['seasons'][$row['season_id']] = [
                'launch_date' => $row['season_launch_date'],
                'season_name' => $row['season_name'],
                'season_name_next' => $row['season_name_next'],
                'teams_registered' => $row['teams_registered'],
                'teams_registered_next_season' => $row['teams_registered_next_season'],
                'playing_teams' => $row['playing_teams'],
                'wildcard_teams' => $row['wildcard_teams'],
                'payment_method_loaded' => $row['payment_method_loaded'],
                'final_fixture_date' => $row['final_fixture_date'],
                'current_season_end_date' => $row['current_season_end_date'],
                'next_season_start_date' => $row['next_season_start_date']
            ];
            $totalRegisteredTeams += $row['teams_registered'];
            $totalPlayingTeams += isset($row['playing_teams']) ? $row['playing_teams'] : 0;
            $totalWildcardTeams += $row['wildcard_teams'];
        }

        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function OpenRegistrationReport() {
        $params = [];

        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $coordinators = \User::Coordinators();

        $selectedCoordinator = isset($_GET['coordinator']) ? $_GET['coordinator'] : null;

        $sql = "SELECT 
                    l.id as league_id, 
                    l.name as league_name, 
                    s.id as season_id,
                    CASE 
                        WHEN 
                            s.statusID = 2 
                            THEN s.name 
                        ELSE 
                            NULL 
                        END 
                    as season_name,
                    s.launchDate as season_launch_date,
                    (
                        SELECT 
                            s2.name AS season_name_next
                        FROM 
                            seasons s2
                        WHERE s2.statusID = 2
                            AND s2.leagueID = l.id
                            AND s2.launchDate > s.launchDate
                        ORDER BY 
                            s2.launchDate ASC
                        LIMIT 1
                    ) AS season_name_next,
                    (
                        SELECT 
                            COUNT(teams.id)
                        FROM 
                            leagues
                        JOIN 
                            seasons ON seasons.leagueID = leagues.id
                            AND seasons.statusID = 1
                        JOIN 
                            teams ON teams.leagueID = leagues.id
                        JOIN 
                            teamSeasons ON teams.id = teamSeasons.teamID AND seasons.id = teamSeasons.seasonID
                        WHERE 
                            leagues.id = l.id
                        GROUP BY 
                            leagues.id, 
                            seasons.id, 
                            leagues.name, 
                            leagues.status, 
                            seasons.statusID
                        LIMIT 1
                    ) as teams_registered,
                    (
                        SELECT 
                            COUNT(teams.id)
                        FROM 
                            leagues, 
                            seasons, 
                            teams, 
                            teamSeasons
                        WHERE 
                            seasons.leagueID = leagues.id 
                            AND teams.leagueID = leagues.id 
                            AND teams.id = teamSeasons.teamID 
                            AND seasons.id = teamSeasons.seasonID
                            AND leagues.id = l.id
                            AND seasons.statusID = 2
                            AND seasons.id = s.id
                    ) as teams_registered_next_season,
                    (
                        SELECT 
                            COUNT(DISTINCT team_id)
                        FROM 
                        (
                            SELECT 
                                divisionID, 
                                home AS team_id
                            FROM 
                                fixtures
                            WHERE 
                                home IS NOT NULL

                            UNION ALL

                            SELECT 
                                divisionID, 
                                away AS team_id
                            FROM 
                                fixtures
                            WHERE 
                                away IS NOT NULL
                        ) AS playing_teams
                        WHERE 
                            playing_teams.divisionID = s.id
                    ) AS playing_teams,
                    (
                        SELECT 
                            COUNT(t.id)
                        FROM 
                            seasons 
                        LEFT JOIN 
                            teams t ON t.seasonID = seasons.id
                        WHERE 
                            t.wildcard = 1
                            AND seasons.id = s.id
                    ) AS wildcard_teams,
                    (
                        SELECT 
                            b.startDate
                        FROM 
                            bookings b
                        WHERE 
                            b.leagueID = s.leagueID
                        ORDER BY 
                            b.startDate DESC
                        LIMIT 1
                    ) AS final_fixture_date,
                    (
                        SELECT 
                            MIN(seasons.launchDate) 
						FROM 
                            seasons 
                        WHERE 
                            seasons.launchDate > s.launchDate
                            AND seasons.leagueID = l.id
                            AND seasons.statusID = 2
                    ) as next_season_start_date,
                    (
                        SELECT 
                            COUNT(tf.stripePaymentMethodID) 
                        FROM 
                            seasons
                        JOIN 
                            teamSeasons ts ON seasons.id = ts.seasonID 
                        JOIN 
                            teamFollowers tf ON tf.teamID = ts.teamID 
                        JOIN 
                            teams t ON ts.teamID = t.id 
                        WHERE seasons.deleted IS NULL 
                            AND tf.deleted IS NULL 
                            AND ts.deleted IS NULL 
                            AND tf.stripePaymentMethodID IS NOT NULL 
                            AND t.leagueID = l.id 
                            AND seasons.id = s.id
                            AND s.statusID = 2
                    ) AS payment_method_loaded,
				l.launchDate AS current_season_end_date
                FROM 
                    leagues l
				LEFT JOIN 
                    seasons s ON l.id = s.leagueID AND s.statusID = 2
				LEFT JOIN 
                    bookings b ON s.id = b.leagueID 
				LEFT JOIN 
                    teams t ON s.id = t.seasonID 
				LEFT JOIN 
                    seasons s2 ON l.id = s2.leagueID 
                WHERE 
				    s.launchDate > CURRENT_DATE() 
                    AND YEAR(s.launchDate) = YEAR(CURRENT_DATE())";

        if ($selectedCoordinator) {
            $sql .= " AND l.id IN (SELECT leagueID FROM teams WHERE coordinator = :coordinator)";
            $params[':coordinator'] = (int) $selectedCoordinator;
        }

        $sql .= " GROUP BY 
             l.id, s.id
         ORDER BY 
             l.name, s.launchDate";

        $result = new Db($sql, $params);
        $totalRegisteredTeams = 0;
        $teamsRegisteredNextSeason = 0;
        $totalWildcardTeams = 0;
        $leagues = [];

        foreach ($result->rows as $row) {
            $leagues[$row['league_id']]['name'] = $row['league_name'];
            $leagues[$row['league_id']]['seasons'][$row['season_id']] = [
                'launch_date' => $row['season_launch_date'],
                'season_name' => $row['season_name'],
                'season_name_next' => $row['season_name_next'],
                'teams_registered' => $row['teams_registered'],
                'teams_registered_next_season' => $row['teams_registered_next_season'],
                'playing_teams' => $row['playing_teams'],
                'wildcard_teams' => $row['wildcard_teams'],
                'payment_method_loaded' => $row['payment_method_loaded'],
                'final_fixture_date' => $row['final_fixture_date'],
                'current_season_end_date' => $row['current_season_end_date'],
                'next_season_start_date' => $row['next_season_start_date']
            ];
            $totalRegisteredTeams += $row['teams_registered'];
            $teamsRegisteredNextSeason += isset($row['teams_registered_next_season']) ? $row['teams_registered_next_season'] : 0;
            $totalWildcardTeams += $row['wildcard_teams'];
        }

        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function OpReport() {
        $data['opReport'] = \League::OpReport();
        $data['regions'] = \Region::Query();
        $data['coordinators'] = \User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Fixtures() {
        global $post;
        $data['startDate'] = date('Y-m-d');
        $data['endDate'] = date('Y-m-d');
        if (isset($post['startDate']) && $post['startDate']) $data['startDate'] = $post['startDate'];
        if (isset($post['endDate']) && $post['endDate']) $data['endDate'] = $post['endDate'];
        $data['report'] = Fixture::Report($data['startDate'], $data['endDate']);
        if (!$data['report']) $data['report'] = [];
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/User/" . __FUNCTION__ . ".User.php");
        $this->Foot();
    }

    function Bookings() {
        if (!isset($_POST['bookingFilter']['month']) || !$_POST['bookingFilter']['month']) $_POST['bookingFilter']['month'] = date('n');
        if (!isset($_POST['bookingFilter']['year']) || !$_POST['bookingFilter']['year']) $_POST['bookingFilter']['year'] = date('Y');
        $data['totalBookings'] = Booking::Report($_POST['bookingFilter']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function ScheduleReport() {
        if (!isset($_POST['startDate']) || !$_POST['startDate']) $_POST['startDate'] = date('Y-m-d');
        if (!isset($_POST['endDate']) || !$_POST['endDate'] || strtotime($_POST['endDate']) < strtotime($_POST['startDate'])) $_POST['endDate'] = date('Y-m-d', strtotime("+7 days", strtotime($_POST['startDate'])));
        $data['schedule'] = Schedule::BetweenDates($_POST['startDate'], $_POST['endDate']);

        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function UnusedBookings() {
        $data['unusedBookings'] = Booking::Orphans();
        $data['coordinators'] = User::Coordinators();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function RiskAssessment(array $data = []) {

        if (!isset($data['variables'][0]) || !$data['variables'][0] || !is_numeric($data['variables'][0])) exit("No ID");
        $id = $data['variables'][0];
        $isDelete = (isset(
            $data['variables'][1]
        ) && $data['variables'][1] == "delete") ? true : false;

        $isDuplicate = (isset(
            $data['variables'][1]
        ) && $data['variables'][1] == "duplicate") ? true : false;

        $instance = new \RiskAssesment($id);
        if (!$instance->id) exit("No Risk Assessment");
        // Form data
        $dateOfAssessment = $instance->date_of_assessment;

        // Export Form
        if (isset($_GET['export'])) printRiskAssessment($instance, $dateOfAssessment);

        $user = new \User($instance->user_id); // Assuming user_id is the correct property
        $firstName = $user->firstname;
        $lastName = $user->lastname;
        $exportUserName = $firstName . ' ' . $lastName;
        $risk = json_decode($instance->data, true);

        if ($isDelete) {
            $instance->Delete();
            \Messaging\Add("Risk Assessment Deleted", "success");
            header("Location: /User/Venues?venueID={$instance->venue_id}&tab=risks");
            exit(0);
        }

        if ($isDuplicate) {
            $instance->Duplicate($instance->id);
            \Messaging\Add("Risk Assessment Duplicated", "success");
            header("Location: /User/Venues?venueID={$instance->venue_id}&tab=risks");
            exit(0);
        }

        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function VenueRiskAssesmentReport() {
        $risks = \RiskAssesment::fetchAll();
        $data['risks'] = $risks;

        $this->Head();
        include "./app/Views/menus/user.menu.php";
        \Messaging\Show();
        include "{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php";
        $this->Foot();
    }
    private function slugify($text, string $divider = '-') {
        // replace non letter or digits by divider
        $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);
        $text = trim($text, $divider);

        // remove duplicate divider
        $text = preg_replace('~-+~', $divider, $text);

        // lowercase
        $text = strtolower($text);

        if (empty($text)) {
            return 'n-a';
        }

        return $text;
    }

    function throw_unless($condition, $message = "Something went wrong!") {
        if (!$condition) {
            throw new \RuntimeException($message);
        }
    }
}
