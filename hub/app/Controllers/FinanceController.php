<?php

class FinanceController extends Controller {

    // protected $folder = "Finance";

    function __construct() {
        if (!\User::isManager()) {
            \Messaging\Add("You need to be logged in as a Manager", "warning");
            header("Location: /Home/Login");
            exit(0);
        }
        parent::__construct();
    }

    function Index() {
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Transactions() {
        memoryLimit();
        global $post;
        $data['userID'] = (isset($post['userID']) && $post['userID']) ? $post['userID'] : null;
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;

        if (isset($post['transaction']) && isset($post['transaction']['teamIDs']) && is_array($post['transaction']['teamIDs'])) {
            $transactionData = $post['transaction'];
            $teamIDs = $transactionData['teamIDs'];
            $transactionsCreated = 0;

            foreach ($teamIDs as $teamID) {
                // Create a new transaction for each team
                $t = new Finance();
                $transactionData['teamID'] = $teamID; // Set the current team ID
                $t->Load($transactionData);
                $t->Save();
                $transactionsCreated++;
            }

            if ($transactionsCreated > 0) {
                \Messaging\Add($transactionsCreated . " transactions created successfully", "success");
            }
        }

        $data['leagues'] = League::Listing();
        $data['transactions'] = ($data['teamID']) ? Finance::Transactions($data['teamID']) : [];
        $data['transactionTypes'] = TransactionType::Listing();
        $data['teams'] = Team::AlphaListing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Payments() {
        memoryLimit('512M');
        global $post;
        $data['userID'] = (isset($post['userID']) && $post['userID']) ? $post['userID'] : null;
        $data['typeID'] = (isset($post['typeID']) && $post['typeID']) ? $post['typeID'] : null;
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        if (isset($post['payment'])) {
            $t = new Finance();
            $t->Load($post['payment']);
            $t->Save();
            $data['teamID'] = $post['payment']['teamID'];
        }
        $data['transactions'] = Finance::Payments($data['teamID']);
        $data['teams'] = Team::AlphaListing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Credits() {
        global $post;
        $data['userID'] = (isset($post['userID']) && $post['userID']) ? $post['userID'] : null;
        $data['typeID'] = (isset($post['typeID']) && $post['typeID']) ? $post['typeID'] : null;
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        if (isset($post['payment'])) {
            $t = new Finance();
            $t->Load($post['payment']);
            $t->Save();
            $data['teamID'] = $post['payment']['teamID'];
        }
        $data['transactions'] = Finance::Credits($data['teamID']);
        $data['users'] = User::Live();
        $data['teams'] = Team::AlphaListing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Statement(array $data = []) {
        memoryLimit();
        global $post;
        if (isset($data['variables'][0])) $post['teamID'] = $data['variables'][0];
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        $data['teams'] = Team::AlphaListing();
        $data['transactions'] = ($data['teamID']) ? Finance::Statement($data['teamID']) : [];
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function VenueCostReport() {
        global $post;
        $data['fromDate'] = (isset($post['fromDate']) && $post['fromDate']) ? $post['fromDate'] : date('Y-m-d');
        $data['toDate'] = (isset($post['toDate']) && $post['toDate']) ? $post['toDate'] : date('Y-m-d', strtotime("+6 days", strtotime($data['fromDate'])));
        $data['venueCostReport'] = \Booking::CostReport($data['fromDate'], $data['toDate']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Balances() {
        $data['balances'] = Finance::Balances();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function PendingPayments() {
        global $post;
        if (isset($post['pendingPaymentID'])) {
            $sp = new StripePayment($post['pendingPaymentID']);
            $sp->Cancel();
            $sp->Save();
            \Messaging\Add(($sp->getFailText()) ? $sp->getFailText() : "Payment {$post['pendingPaymentID']} Deleted");
        }
        if (isset($post['pendingPaymentAmend'])) {
            $sp = new StripePayment($post['pendingPaymentAmend']['id']);
            $rlt = $sp->newTotal($post['pendingPaymentAmend']['newTotal']);
            ($rlt === true) ? \Messaging\Add("Payment Updated", "success") : \Messaging\Add($rlt, "danger");
        }
        if (!isset($_POST['date'])) $_POST['date'] = date('Y-m-d');
        // $data['pending'] = \StripePayment::Pending($_POST['date']);
        $data['pending'] = \StripePayment::byDate($_POST['date']);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function AgedDebtors() {
        $data['debtors'] = Team::AgedDebtors();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function PandL() {
        $data['opReport'] = \Season::PandLReport();
        $data['regions'] = \Region::Query();
        if (!isset($_POST['startDate']) || !$_POST['startDate']) {
            $_POST['startDate'] = date('Y-m-d', strtotime("-7 days"));
        }
        if (!isset($_POST['endDate']) || !$_POST['endDate']) {
            $_POST['endDate'] = date('Y-m-d', strtotime("+6 days", strtotime($_POST['startDate'])));
        }
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function PaymentHistory(array $data = []) {
        if (isset($data['variables'][0])) $_POST['teamID'] = $data['variables'][0];
        if (isset($_POST['teamID']) && $_POST['teamID']) {
            $startDate = (isset($_POST['startDate']) && $_POST['startDate']) ? $_POST['startDate'] : null;
            $endDate = (isset($_POST['endDate']) && $_POST['endDate']) ? $_POST['endDate'] : null;
            $data['stripePayments'] = StripePayment::PaymentHistory($_POST['teamID'], $startDate, $endDate);
        }
        // $data['teams'] = Team::Listing();
        $data['teams'] = Team::AlphaListing();
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Intentions() {
        $data['stage1'] = StripePayment::BillingStage1(false, true);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Daily() {
        $rlt = new Db("SELECT * FROM `billingTotals` ORDER BY `date` DESC");
        $data['billingTotals'] = $rlt->rows;
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function MarginReport() {
        if (!isset($_POST['startDate']) || !$_POST['startDate']) $_POST['startDate'] = date('Y-m-d', strtotime("-4 weeks"));
        if (!isset($_POST['endDate']) || !$_POST['endDate'] || strtotime($_POST['endDate']) < strtotime($_POST['startDate'])) $_POST['endDate'] = date('Y-m-d');

        $sql = "SELECT `leagueID`, SUM(`duration`) AS `bookingDuration`, SUM(`hourlyRate` * (`duration` / 60)) AS `bookingCost` FROM `bookings` WHERE `startDate` >= '{$_POST['startDate']}' AND `startDate` <= '{$_POST['endDate']}' AND `deleted` IS NULL GROUP BY `leagueID` ORDER BY `cost` DESC";

        $bookings = new Db($sql);
        $leagues = [];
        foreach ($bookings->rows as $b) {
            $leagues[$b['leagueID']] = [
                'bookingDuration' => $b['bookingDuration'],
                'bookingCost' => $b['bookingCost']
            ];
        }

        $sql = "
    SELECT 
        `seasons`.`id` AS `seasonID`, 
        `seasons`.`leagueID` AS `leagueID`, 
        `seasons`.`fixtureCharge` AS `matchFee`, 
        `leagues`.`name`, 
        `leagues`.`coordinator` AS `coordinatorID`, 
        SUM(`seasons`.`duration`) AS `fixtureDuration`, 
        COUNT(fixtures.id) as `totalFixtureCount`,
        SUM(CASE WHEN `home_teams`.`wildcard` IS NOT NULL THEN 1 ELSE 0 END) + 
        SUM(CASE WHEN `away_teams`.`wildcard` IS NOT NULL THEN 1 ELSE 0 END) AS `totalWildcardCount`
    FROM 
        `schedule`
    LEFT JOIN 
        `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
    LEFT JOIN 
        `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
    LEFT JOIN 
        `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
    LEFT JOIN 
        `seasons` ON `divisions`.`seasonID` = `seasons`.`id`
    LEFT JOIN 
        `leagues` ON `seasons`.`leagueID` = `leagues`.`id`
    INNER JOIN 
        `teams` AS `home_teams` ON `fixtures`.`home` = `home_teams`.`id`
    INNER JOIN 
        `teams` AS `away_teams` ON `fixtures`.`away` = `away_teams`.`id`
    WHERE 
        `schedule`.`deleted` IS NULL
        AND `bookings`.`startDate` >= '{$_POST['startDate']}' 
        AND `bookings`.`startDate` <= '{$_POST['endDate']}' 
        AND `bookings`.`deleted` IS NULL
    GROUP BY 
        `seasons`.`leagueID`
";


        $fixtures = new Db($sql);
        foreach ($fixtures->rows as $f) {
            if (!isset($coordinators[$f['coordinatorID']])) {
                $coordinator = new User($f['coordinatorID']);
                $coordinators[$f['coordinatorID']] = $coordinator->__toString();
            }
            $season = new Season($f['seasonID']);
            $totalTeamsWithoutWildcard = $season->teamCount(false);
            $wildcardCount = $f['totalWildcardCount'];
            $fixtureCount = $f['totalFixtureCount'];
            $matchFee = $f['matchFee'];
            $totalCharged = (($fixtureCount * 2) - $wildcardCount) * $matchFee;

            $leagues[$f['leagueID']]['teamCount'] = $season->teamCount();
            $leagues[$f['leagueID']]['wildcardCount'] = $totalTeamsWithoutWildcard;
            $leagues[$f['leagueID']]['name'] = $f['name'];
            $leagues[$f['leagueID']]['fixtureDuration'] = $f['fixtureDuration'];
            $leagues[$f['leagueID']]['fixtureCharge'] = $totalCharged;
            $leagues[$f['leagueID']]['matchFee'] = $matchFee;
            $leagues[$f['leagueID']]['coordinatorID'] = $f['coordinatorID'];
        }

        asort($coordinators);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function FixtureReport() {
        if (!isset($_POST['startDate']) || !$_POST['startDate']) $_POST['startDate'] = date('Y-m-d', strtotime("-4 weeks"));
        if (!isset($_POST['endDate']) || !$_POST['endDate'] || strtotime($_POST['endDate']) < strtotime($_POST['startDate'])) $_POST['endDate'] = date('Y-m-d');
        $startDate = isset($_POST['startDate']) ? $_POST['startDate'] : date('Y-m-d', strtotime("-4 weeks"));
        $endDate = isset($_POST['endDate']) ? $_POST['endDate'] : date('Y-m-d');

        $coordinatorCondition = "";
        if (isset($_POST['coordinatorID']) && $_POST['coordinatorID'] != "") {
            $coordinatorCondition = " AND `leagues`.`coordinator` = " . intval($_POST['coordinatorID']);
        }

        $params = [
            'startDate' => $startDate,
            'endDate' => $endDate,
        ];

        include 'queries/finance.php';

        $bookings = (new Db($bookingSql, $params))->rows;

        $fixtures = new Db($fixtureSql, $params);
        $results = [];
        $other = [];
        $book = [];
        $coordinators = [];
        $selectedCoordinatorID = $_POST['coordinatorID'] ?? '';
        $weeks = calculateWeekDates($_POST['startDate'], $_POST['endDate']);

        foreach ($weeks as $week) {
            $startandEnd = $week['start'] . ' - ' . $week['end'];
            $filteredData = filterDataByDateRange($fixtures->rows, $week['start'], $week['end']);
            $filteredBookingData = filterDataByDateRange($bookings, $week['start'], $week['end']);

            foreach ($filteredData as $d) {
                $league = trim($d['name']);
                if (!isset($coordinators[$d['coordinatorID']])) {
                    $coordinator = new User($d['coordinatorID']);
                    $coordinators[$d['coordinatorID']] = $coordinator->__toString();
                }

                $matchFee = $d['matchFee'];
                $wildcard = $d['homeWildcard'] ?? $d['awayWildcard'] ?? 0;
                $totalCharged = (($wildcard == 0) ? 2 : 1) * $matchFee;

                $results[$league][$startandEnd][] = $d;
                // Other Datas
                @$other[$league]['wildcard'] = @$other[$league]['wildcard'] + $wildcard;
                @$other[$league]['fixture'] = @$other[$league]['fixture'] + 1;
                @$other[$league]['total'] = @$other[$league]['total'] + $totalCharged;
                // Date Range Datas 
                @$other[$league][$startandEnd] = @$other[$league][$startandEnd] + $totalCharged;
            }


            foreach ($filteredBookingData as $d) {
                $league = trim($d['name']);
                @$book[$league][$startandEnd] = roundTwo(@$book[$league][$startandEnd] + $d['bookingCost']);
            }
        }

        asort($coordinators);
        ksort($results);
        $this->Head();
        include("./app/Views/menus/user.menu.php");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }
}
