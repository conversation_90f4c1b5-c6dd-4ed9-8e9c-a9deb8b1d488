<?php

class Coordinator<PERSON>ontroller extends ControllerBootstrap5 {

    public $menu = "user.menu._bootstrap5.php";

    function __construct() {
        parent::__construct();
        checkJwtAuth();
    }

    function Index() {
        $data['leagues'] = \League::Coordinator(\User::AuthUser());
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Users() {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }


    public function IncidentReports($array = []) {
        // Get current user
        $user = \User::AuthUser();
        $isManager = $user->isManager;

        // Get filter parameters - only apply if user is admin
        $filters = [];

        if ($isManager) {
            // Admin can use all filters
            $filters['startDate'] = isset($_GET['start-date']) ? $_GET['start-date'] : '';
            $filters['endDate'] = isset($_GET['end-date']) ? $_GET['end-date'] : '';
            $filters['venueId'] = isset($_GET['venues']) ? $_GET['venues'] : '';
            $filters['status'] = isset($_GET['status']) ? $_GET['status'] : '';
            $filters['severity'] = isset($_GET['severity']) ? $_GET['severity'] : [];
        } else {
            // Non-admin users only see "Coordinator Review Required" status by default
            $filters['status'] = 'open';
        }

        // Add user to filters for access control
        $filters['user'] = $user;
        $filters['page'] = isset($_GET['page']) ? intval($_GET['page']) : 1;

        // Get filtered incidents
        $result = IncidentReport::getFilteredIncidents($filters);

        $data['incidents'] = $result['incidents'];
        $data['total'] = $result['total'];
        $data['pendingReviewCount'] = $result['pendingReviewCount'];
        $data['isManager'] = $isManager;

        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function IncidentReportDetails($array = []) {
        if (empty($array['variables'][0])) {
            \Messaging\Add("Incident report ID not provided", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        $incidentId = $array['variables'][0];
        $status = $_GET['incidentReportStatus'] ?? null;

        // Get current user and check if admin
        $user = \User::AuthUser();
        $isManager = $user->isManager;

        // Get incident report details directly from the database
        $incident = IncidentReport::getIncidentById($incidentId, $status, $user);

        if (!$incident) {
            \Messaging\Add("Incident report not found", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        $data['incident'] = $incident;
        $data['isManager'] = $isManager;

        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }


    function IncidentReportUpdate($array = []) {
        if (empty($array['variables'][0])) {
            \Messaging\Add("Incident report ID not provided", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        $data['seriousInjuries'] = \IncidentReport::seriousInjuriesList();

        $incidentId = $array['variables'][0];
        $status = $_GET['incidentReportStatus'] ?? null;

        // Get current user and check if admin
        $user = \User::AuthUser();
        $isManager = $user->isManager;
        $data['isManager'] = $isManager;

        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get the incident report
            $incident = new IncidentReport($incidentId);

            if (!$incident || !$incident->id) {
                \Messaging\Add("Incident report not found", "warning");
                header("Location: /Coordinator/IncidentReports");
                exit;
            }

            // Store original status before loading form data
            $originalStatus = $incident->status;

            // Load form data into incident object
            $incident->Load($_POST);

            // Handle consent tick box (store as 1 or 0)

            $incident->no_consent = isset($_POST['no_consent']) ? 1 : 0;
            // Relationship to player (string, non-mandatory)
            if (!$incident->welfare_check_completed) {
                \Messaging\Add("Welfare Check Completed date is required.", "error");
            } else {
                // If not admin, set status to "pending review" when coordinator edits
                if (!$isManager) {
                    // Only change status if it's not already closed
                    if ($originalStatus != 'closed' && $_POST['submitter'] != 'save-as') {
                        $incident->status = 'pending';
                    } else {
                        // If incident was closed, keep it closed
                        $incident->status = 'closed';
                    }

                    // Preserve original severity
                    $originalIncident = new IncidentReport($incidentId);
                    $incident->severity = $originalIncident->severity;
                }

                // Define allowed file types
                $allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
                $maxFileSize = 5 * 1024 * 1024; // 5MB
                // Handle official statement file upload
                if (isset($_FILES['official_statement_file']) && $_FILES['official_statement_file']['error'] == UPLOAD_ERR_OK) {
                    $fileType = mime_content_type($_FILES['official_statement_file']['tmp_name']);
                    $fileSize = $_FILES['official_statement_file']['size'];

                    if (!in_array($fileType, $allowedTypes)) {
                        \Messaging\Add("Official statement file must be PDF, JPG, PNG, or JPEG", "error");
                    } elseif ($fileSize > $maxFileSize) {
                        \Messaging\Add("Official statement file exceeds maximum size of 5MB", "error");
                    } else {
                        $uploadDir = getcwd() . '/public/images/';
                        $fileName = date("h:i:sa") . '-' . basename($_FILES['official_statement_file']['name']);
                        $uploadFile = $uploadDir . $fileName;

                        // Ensure upload directory exists
                        if (!is_dir($uploadDir)) {
                            mkdir($uploadDir, 0775, true);
                        }

                        if (move_uploaded_file($_FILES['official_statement_file']['tmp_name'], $uploadFile)) {
                            $incident->official_statement_file = getDomainPrefix() . 'hub.leagues4you.co.uk/public/images/' . $fileName;
                        }
                    }
                }

                // Handle witness statements if present
                if (isset($_FILES['witness_statement']) && !empty($_FILES['witness_statement']['name'][0])) {
                    $witnessStatements = [];
                    $validFiles = true;

                    foreach ($_FILES['witness_statement']['name'] as $key => $name) {
                        if ($_FILES['witness_statement']['error'][$key] == UPLOAD_ERR_OK) {
                            $fileType = mime_content_type($_FILES['witness_statement']['tmp_name'][$key]);
                            $fileSize = $_FILES['witness_statement']['size'][$key];

                            if (!in_array($fileType, $allowedTypes)) {
                                \Messaging\Add("Witness statement file '$name' must be PDF, JPG, PNG, or JPEG", "error");
                                $validFiles = false;
                                break;
                            } elseif ($fileSize > $maxFileSize) {
                                \Messaging\Add("Witness statement file '$name' exceeds maximum size of 5MB", "error");
                                $validFiles = false;
                                break;
                            }
                        }
                    }

                    if ($validFiles) {
                        foreach ($_FILES['witness_statement']['name'] as $key => $name) {
                            if ($_FILES['witness_statement']['error'][$key] == UPLOAD_ERR_OK) {
                                $uploadDir = getcwd() . '/public/images/';
                                $fileName = date("h:i:sa") . '-' . basename($name);
                                $uploadFile = $uploadDir . $fileName;

                                if (move_uploaded_file($_FILES['witness_statement']['tmp_name'][$key], $uploadFile)) {
                                    $witnessStatements[] = getDomainPrefix() . 'hub.leagues4you.co.uk/public/images/' . $fileName;
                                }
                            }
                        }

                        if (!empty($witnessStatements)) {
                            $incident->witness_statement_files = serialize($witnessStatements);
                        }
                    }
                }

                // Handle additional files upload
                if (isset($_FILES['incident_files']) && !empty($_FILES['incident_files']['name'][0])) {
                    $savedFiles = \Upload::saveIncidentFiles($_FILES['incident_files'], $incidentId);
                    if (!empty($savedFiles)) {
                        \Messaging\Add("Additional files uploaded successfully", "success");
                    }
                }
                // Save the incident
                $result = $incident->save();

                if ($result) {
                    \Messaging\Add("Incident report updated successfully", "success");
                    header("Location: /Coordinator/IncidentReportDetails/{$incidentId}");
                    exit;
                } else {
                    \Messaging\Add("Failed to update incident report", "error");
                }
            }
        }

        // Get incident report details directly from the database
        $incident = IncidentReport::getIncidentById($incidentId, $status, \User::AuthUser());

        if (!$incident) {
            \Messaging\Add("Incident report not found", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        $data['incident'] = $incident;

        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }


    /**
     * Updates an incident report status
     * 
     * @param array $array Contains the incident ID in $array['variables'][0]
     * @return void
     */
    public function updateIncidentStatus($array = []) {

        if (empty($array['variables'][0])) {
            \Messaging\Add("Incident report ID not provided", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        $incidentId = $array['variables'][0];

        // Get the new status from POST data
        $newStatus = isset($_POST['status']) ? $_POST['status'] : 'closed';

        // Validate status
        $validStatuses = ['open', 'pending', 'closed'];
        if (!in_array($newStatus, $validStatuses)) {
            \Messaging\Add("Invalid status provided", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        // Get the incident report
        $incident = new IncidentReport($incidentId);

        if (!$incident || !$incident->id) {
            \Messaging\Add("Incident report not found", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        // Update the status
        $incident->status = $newStatus;
        $result = $incident->save();

        if ($result) {
            $statusText = ucfirst($newStatus);
            \Messaging\Add("Incident report status has been updated to '$statusText' successfully", "success");
        } else {
            \Messaging\Add("Failed to update incident report status", "error");
        }

        // Redirect back to the incident reports page or details page
        $redirectUrl = isset($_POST['redirect']) ? $_POST['redirect'] : "/Coordinator/IncidentReports";
        header("Location: $redirectUrl");
        exit;
    }

    public function downloadIncidentReport($array = []) {
        if (empty($array['variables'][0])) {
            \Messaging\Add("Incident report ID not provided", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        $incidentId = $array['variables'][0];
        $incident = new IncidentReport($incidentId);

        if (!$incident || !$incident->id) {
            \Messaging\Add("Incident report not found", "warning");
            header("Location: /Coordinator/IncidentReports");
            exit;
        }

        // Get venue and coordinator information
        $venueName = $incident->getVenueName();
        $coordinatorName = $incident->getCoordinatorName();

        // Format the date and time
        $dateOfIncident = date('d/m/Y', strtotime($incident->date_of_incident));
        $timeOfIncident = date('H:i', strtotime($incident->time_of_incident));

        // Create the HTML content
        $html = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Incident Report #{$incident->id}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 20px; }
                .section { margin-bottom: 15px; }
                .label { font-weight: bold; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                h1 { color: #333; }
                h2 { color: #666; margin-top: 20px; }
                @media print {
                    body { margin: 0; padding: 20px; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Incident Report</h1>
                <p>Report ID: {$incident->id}</p>
                <div class='no-print'>
                    <button onclick='window.print()'>Print Report</button>
                </div>
            </div>

            <div class='section'>
                <h2>Basic Information</h2>
                <table>
                    <tr>
                        <th>Field</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Name</td>
                        <td>{$incident->name}</td>
                    </tr>
                    <tr>
                        <td>Date of Birth</td>
                        <td>{$incident->dob}</td>
                    </tr>
                    <tr>
                        <td>Contact Number</td>
                        <td>{$incident->contact_number}</td>
                    </tr>
                    <tr>
                        <td>Date of Incident</td>
                        <td>{$dateOfIncident}</td>
                    </tr>
                    <tr>
                        <td>Time of Incident</td>
                        <td>{$timeOfIncident}</td>
                    </tr>
                    <tr>
                        <td>Venue</td>
                        <td>{$venueName}</td>
                    </tr>
                    <tr>
                        <td>Coordinator</td>
                        <td>{$coordinatorName}</td>
                    </tr>
                </table>
            </div>

            <div class='section'>
                <h2>Incident Details</h2>
                <table>
                    <tr>
                        <th>Field</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Description</td>
                        <td>{$incident->description}</td>
                    </tr>
                    <tr>
                        <td>Location Details</td>
                        <td>{$incident->details_location}</td>
                    </tr>
                    <tr>
                        <td>Root Cause</td>
                        <td>{$incident->root_cause}</td>
                    </tr>
                    <tr>
                        <td>Nature of Injury</td>
                        <td>{$incident->nature_of_injury}</td>
                    </tr>
                    <tr>
                        <td>Treatment</td>
                        <td>{$incident->treatment}</td>
                    </tr>
                    <tr>
                        <td>Match Official Name</td>
                        <td>{$incident->match_official_name}</td>
                    </tr>
                    <tr>
                        <td>Additional Information</td>
                        <td>{$incident->additional_info}</td>
                    </tr>
                    <tr>
                        <td>Mitigating Circumstances</td>
                        <td>{$incident->mitigating_circumstances}</td>
                    </tr>
                </table>
            </div>";

        // Add witness information if available
        if ($incident->witness == '1') {
            $html .= "
            <div class='section'>
                <h2>Witness Information</h2>
                <table>
                    <tr>
                        <th>Field</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Witness Name</td>
                        <td>{$incident->witness_name}</td>
                    </tr>
                </table>
            </div>";
        }

        $html .= "
            <div class='section'>
                <h2>Status Information</h2>
                <table>
                    <tr>
                        <th>Field</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Status</td>
                        <td>{$incident->status}</td>
                    </tr>
                    <tr>
                        <td>Reported By</td>
                        <td>{$incident->your_name}</td>
                    </tr>
                    <tr>
                        <td>Created At</td>
                        <td>{$incident->created_at}</td>
                    </tr>
                    <tr>
                        <td>Last Updated</td>
                        <td>{$incident->updated_at}</td>
                    </tr>
                </table>
            </div>
        </body>
        </html>";

        // Set headers for HTML download
        header('Content-Type: text/html');
        header('Content-Disposition: attachment; filename="incident_report_' . $incidentId . '.html"');
        echo $html;
        exit;
    }

    /**
     * Delete a file from an incident report
     * Handles both uploads table files and incident report embedded files
     *
     * @param array $array
     * @return void
     */
    public function deleteFile($array = []) {
        if (!isset($_POST['incident_id'])) {
            $this->jsonResponse(false, 'Incident ID not provided');
        }

        $incidentId = $_POST['incident_id'];

        // Handle uploads table files (additional files)
        if (isset($_POST['file_id'])) {
            $this->deleteUploadFile($_POST['file_id']);
            return;
        }

        // Handle incident report embedded files
        if (isset($_POST['file_type'])) {
            $this->deleteIncidentFile($incidentId, $_POST['file_type'], $_POST['file_index'] ?? null);
            return;
        }

        $this->jsonResponse(false, 'Invalid request parameters');
    }

    /**
     * Delete file from uploads table
     */
    private function deleteUploadFile($fileId) {
        $result = \Upload::deleteFile($fileId);
        $this->jsonResponse($result, $result ? 'File deleted successfully' : 'Failed to delete file');
    }

    /**
     * Delete file from incident report fields
     */
    private function deleteIncidentFile($incidentId, $fileType, $fileIndex = null) {
        $incident = new IncidentReport($incidentId);
        if (!$incident->id) {
            $this->jsonResponse(false, 'Incident report not found');
        }

        if ($fileType === 'official_statement' && $incident->official_statement_file) {
            $this->deletePhysicalFile($incident->official_statement_file);
            $incident->official_statement_file = null;
        } elseif ($fileType === 'witness_statement' && $incident->witness_statement_files && $fileIndex !== null) {
            $statements = unserialize($incident->witness_statement_files);
            if (isset($statements[$fileIndex])) {
                $this->deletePhysicalFile($statements[$fileIndex]);
                unset($statements[$fileIndex]);
                $incident->witness_statement_files = !empty($statements) ? serialize(array_values($statements)) : null;
            }
        } else {
            $this->jsonResponse(false, 'Invalid file type or file not found');
        }

        $incident->save();
        $this->jsonResponse(true, 'File deleted successfully');
    }

    /**
     * Delete physical file from server
     */
    private function deletePhysicalFile($filePath) {
        $serverPath = str_replace(
            getDomainPrefix() . 'hub.leagues4you.co.uk/public/images/',
            getcwd() . '/public/images/',
            $filePath
        );

        if (file_exists($serverPath)) {
            unlink($serverPath);
        }
    }

    /**
     * Send JSON response and exit
     */
    private function jsonResponse($success, $message) {
        echo json_encode(['success' => $success, 'message' => $message]);
        exit;
    }
}
