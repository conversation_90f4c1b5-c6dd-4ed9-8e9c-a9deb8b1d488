<?php

class AdminController extends Controller {
    protected $vue = "https://unpkg.com/vue@3.2.31";
    protected $chart = "https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js";
    protected $fontAwesome = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css";
    protected $jQuery = null;
    protected $bootstrapCSS = "https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css";
    protected $bootstrapJS = "https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js";
    public $menu = "user.menu._bootstrap5.php";

    function __construct() {
        // if (!\User::isAdmin()) {
        //     \Messaging\Add("You need to be an Admin","warning");
        //     header("Location: /Home/Login");
        //     exit(0);
        // }
        parent::__construct();
    }

    function Index() {
    }

    function BillingRun() {
        $data['billingRun'] = StripePayment::BillingStage2(false);
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/Admin/" . __FUNCTION__ . ".Admin.php");
        $this->Foot();
    }

    function Billing_Log() {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function UserActivity() {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        $data['sql'] = "SELECT * FROM teamFollowers WHERE updated >= :startDate AND updated <= :endDate ORDER BY updated DESC, deleted ASC";
        if (!isset($_POST['date'])) $_POST['date'] = date('Y-m-d');
        $data['rlt'] = Database::Execute($data['sql'], ["startDate" => "{$_POST['date']} 00:00:00", "endDate" => "{$_POST['date']} 23:59:59"]);
        $data['userActivity'] = ($data['rlt']['success']['rows']) ?? [];
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Terms() {
        if (isset($_POST['terms'])) {
            Tools::Dump($_POST['terms']);
        }
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Library() {
        if ($_POST && $_FILES) {
            DocLibraryVersion::Upload(
                $_POST['uploadfile'],
                $_FILES['uploadfile']
            );
        }
        $data['docTypes'] = DocLibraryType::Listing();
        $data['docVersions'] = DocLibraryVersion::Listing();
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }

    function Log() {
        $data['startDate'] = (isset($_POST['startDate'])) ? $_POST['startDate'] : date("Y-m-d", strtotime("-7 days"));
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        $data['logMessages'] = Logging::Query("SELECT * FROM `logging` WHERE `created` >= '{$data['startDate']} 00:00:00' AND (`isAdmin` IS NULL OR `isAdmin` = 0) ORDER BY `created` DESC");
        include("{$this->viewFolder}/Admin/" . __FUNCTION__ . ".Admin.php");
        $this->Foot();
    }

    function SeasonStatus() {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        $data['results'] = TeamSeason::SeasonStatusReport();
        include("{$this->viewFolder}/Admin/" . __FUNCTION__ . ".Admin.php");
        $this->Foot();
    }

    function Birthdays() {
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        if (!isset($_POST['month'])) $_POST['month'] = date('m');
        $data['birthdayUsers'] = User::Birthdays($_POST['month']);
        include("{$this->viewFolder}/Admin/" . __FUNCTION__ . ".Admin.php");
        $this->Foot();
    }

    function NewTeamCaptain() {
        if (isset($_POST['NewTeamCaptain'])) {
            $team = new Team($_POST['NewTeamCaptain']['teamID']);
            $supress = (isset($_POST['NewTeamCaptain']['supress']) && $_POST['NewTeamCaptain']['supress'] == 1) ? true : false;
            $rlt = TeamFollower::InviteCaptain($team, $_POST['NewTeamCaptain']['email'], $supress);
            if ($rlt['success'] && $rlt['success']['lastInsertID']) {
                \Messaging\Add("{$_POST['NewTeamCaptain']['email']} has been invited to be Captain of $team");
            } elseif ($rlt['error']) \Messaging\Add($rlt['error']['message'], "warning");
        }
        $data['teams'] = Team::Listing();
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/Admin/" . __FUNCTION__ . ".Admin.php");
        $this->Foot();
    }

    function NewTeamTreasurer() {
        if (isset($_POST['NewTeamTreasurer'])) {
            $team = new Team($_POST['NewTeamTreasurer']['teamID']);
            $captain = $team->getTeamManagers()['captain'];
            // echo "Invite {$_POST['NewTeamTreasurer']['email']} to be treasurer of {$team} for Captain {$captain}<br>";
            $rlt = TeamFollower::InviteTreasurer($_POST['NewTeamTreasurer']['email'], $team, $captain);

            if ($rlt['success'] && $rlt['success']['lastInsertID']) {
                \Messaging\Add("{$_POST['NewTeamTreasurer']['email']} has been invited to be a Treasurer of $team");
            } elseif ($rlt['error']) \Messaging\Add($rlt['error']['message'], "warning");
        }
        $data['teams'] = Team::Listing();
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/Admin/" . __FUNCTION__ . ".Admin.php");
        $this->Foot();
    }

    function RebuildApi() {
        $data['start'] = microtime(true);
        System::BuildLiveLeaguesApi();
        $data['duration'] = microtime(true) - $data['start'];
        $data['json'] = json_decode(file_get_contents("https://api.leagues4you.co.uk/liveLeagues"), true);
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/" . __FUNCTION__ . ".{$this->folder}.php");
        $this->Foot();
    }
}
