<?php

$fixtureSql = "SELECT 
                `seasons`.`id` AS `seasonID`, 
                DATE_FORMAT(`bookings`.`startDate`, '%d/%m/%Y') AS `week`, 
                `seasons`.`leagueID` AS `leagueID`, 
                `seasons`.`fixtureCharge` AS `matchFee`, 
                `leagues`.`name`, 
                `leagues`.`coordinator` AS `coordinatorID`, 
                home_teams.wildcard AS `homeWildcard`,
                away_teams.wildcard AS `awayWildcard`,
                SUM(`bookings`.`duration`) AS `bookingDuration`, 
                SUM(`bookings`.`hourlyRate` * (`bookings`.`duration` / 60)) AS `bookingCost`,
                WEEK(`bookings`.`startDate`) AS `weekNumber`
            FROM 
                `schedule`
            LEFT JOIN 
                `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN 
                `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
            LEFT JOIN 
                `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            LEFT JOIN 
                `seasons` ON `divisions`.`seasonID` = `seasons`.`id`
            LEFT JOIN 
                `leagues` ON `seasons`.`leagueID` = `leagues`.`id`
            INNER JOIN 
                `teams` AS `home_teams` ON `fixtures`.`home` = `home_teams`.`id`
            INNER JOIN 
                `teams` AS `away_teams` ON `fixtures`.`away` = `away_teams`.`id`
            WHERE 
                `schedule`.`deleted` IS NULL
                AND `bookings`.`startDate` >= :startDate
                AND `bookings`.`startDate` <= :endDate
                AND `bookings`.`deleted` IS NULL
                {$coordinatorCondition}
            GROUP BY 
                `fixtures`.`id`, `weekNumber`
            ORDER BY 
                `bookingCost` DESC;
                ";

$bookingSql = "SELECT 
               DATE_FORMAT(`bookings`.`startDate`, '%d/%m/%Y') AS `week`,
                `leagues`.`id` AS `leagueID`, 
                `leagues`.`name`, 
                `bookings`.`hourlyRate` * (`bookings`.`duration` / 60) AS `bookingCost`,
                WEEK(`bookings`.`startDate`) AS `weekNumber`
            FROM
    `bookings`
INNER JOIN `leagues` ON `bookings`.`leagueID` = `leagues`.`id`
WHERE
    `bookings`.`startDate` >= :startDate
    AND `bookings`.`startDate` <= :endDate
    AND `bookings`.`deleted` IS NULL
    {$coordinatorCondition}
            GROUP BY 
                `bookings`.`id`
            ORDER BY 
                `bookingCost` DESC;
                ";
