<?php

class UserController extends Controller {

    function __construct() {
        parent::__construct();
    }
    function Index() {
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Dashboard() {
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function WebSearches (Array $data = []) {
        global $post;
        $data['searchDate'] = (isset($post['searchDate']) && $post['searchDate']) ? $post['searchDate'] : date('Y-m-d');
        $data['searches'] = \WebSearchLocation::byDate($data['searchDate']);
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Users (Array $data = []) {
        global $post;
        if (isset($post['user'])) {
            $user = new \User();
            $user->Load($post['user']);
            $user->Save();
        }
        if (isset($post['activateUserID']) && $post['activateUserID']) {
            $rlt = \User::AdminActivation($post['activateUserID']);
            ($rlt) ? \Messaging\Add("User Activated","success") : \Messaging\Add("Activation Failed","danger");
        } 
        // \Tools\Dump($post);
        if (isset($post['newPassword']) && $post['newPassword']) {
            $user = new \User($post['newPassword']['userID']);
            $rlt = $user->ChangePassword([$post['newPassword']['password1'],$post['newPassword']['password1']]);
            ($rlt === true) ? \Messaging\Add("Password updated","success") : \Messaging\Add("Password could not be updated : $rlt","danger");
            
        }
        $data['users'] = \User::Customers();
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Sports (Array $data = []) {
        global $post;
        if (isset($post['sport'])) {
            $data['sport'] = new \Sport();
            $data['sport']->Load($post['sport']);
            $data['sport']->Save();
        } elseif (isset($post['sportID'])) {
            $data['sport'] = new \Sport($post['sportID']);
        }
        $data['sports'] = \Sport::Listing();
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Venues(Array $data = []) {
        global $post;
        $data['tab'] = "main";
        $data['venueID'] = (isset($post['venueID']) && $post['venueID']) ? $post['venueID'] : null;
        if (isset($post['venue'])) {
            $venue = new \Venue();
            $venue->Load($post['venue']);
            $venue->Save();
            $data['venueID'] = $venue->id;
        }
        if (isset($post['facilities'])) {
            $data['venueID'] = $post['facilities']['id'];
            $venue = new \Venue($post['facilities']['id']);
            $facilities = ["facility_freeParking","facility_paidParking","facility_changingRooms","facility_showers","facility_cafe","facility_bar"];
            foreach ($facilities as $facility) {
                if (!isset($post['facilities'][$facility]) || $post['facilities'][$facility] != 1) {
                    $post['facilities'][$facility] = null;
                }
            }
            $venue->Load($post['facilities']);
            $venue->Save();
            $data['tab'] = "facilities";
            $data['venueID'] = $venue->id;
        }
        if (isset($post['map'])) {
            $data['venueID'] = $post['map']['id'];
            $venue = new \Venue($post['map']['id']);
            $venue->Load($post['map']);
            $venue->Save();
            $data['tab'] = "map";
            $data['venueID'] = $venue->id;
        }
        $data['venues'] = \Venue::Listing();
        $data['venue'] = ($data['venueID']) ? new \Venue($data['venueID']) : new \Venue();
        if ($data['venue']->getLat() && $data['venue']->getLng()) {
            $data['weather'] = new \Weather($data['venue']->getLat(),$data['venue']->getLng());
        } else $data['weather'] = null;
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function PasswordChange (Array $data = []) {
        global $post;
        if (isset($post['password'])) {
            $user = \User::Authenticated();
            $rlt = $user->ChangePassword($post['password']);
            if ($rlt ===true) {
                \Messaging\Add("Password updated","success");
                header("Location: /User");
                exit(0);
            } else \Messaging\Add($rlt,"warning");
        }
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Sweep(Array $data = []) {
        $data['due'] = \Fixture:: Billable ();
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function WebContacts (Array $data = []) {
        global $post;
        if (isset($post['spam']) && $post['spam']) \WebContact::markSpam($post['spam']);
        if (isset($post['complete']) && $post['complete']) \WebContact::markCompleted($post['complete']);
        $data['webContacts'] = \WebContact::Listing();
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Logout() {
        $user = \User::Authenticated();
        $user->Logout();
        \Messaging\Add("You have been logged out","success");
        header("Location: /");
        exit(0);
    }
    function TeamStatus (Array $data = []) {
        global $post;
        $data['leagueID'] = (isset($post['leagueID']) && $post['leagueID']) ? $post['leagueID'] : null;
        $data['captainID'] = (isset($post['captainID']) && $post['captainID']) ? $post['captainID'] : null;
        $data['coordinatorID'] = (isset($post['coordinatorID']) && $post['coordinatorID']) ? $post['coordinatorID'] : null;
        $data['teams'] = \Team::Report();
        $data['leagues'] = \League::Listing();
        $data['captains'] = \User::Captains();
        $data['coordinators'] = \User::Coordinators();
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function LeagueReport() {
        $data['leagueReport'] = \Team::inLeaguesReport();
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
    function Fixtures () {
        global $post;
        $data['startDate'] = date('Y-m-d');
        $data['endDate'] = date('Y-m-d');
        if (isset($post['startDate']) && $post['startDate']) $data['startDate'] = $post['startDate'];
        if (isset($post['endDate']) && $post['endDate']) $data['endDate'] = $post['endDate'];
        $data['report'] = Fixture::Report($data['startDate'],$data['endDate']);
        if (!$data['report']) $data['report'] = [];
        \Messaging\Show();
        include("{$this->viewFolder}/User/".__FUNCTION__.".User.php");
    }
}