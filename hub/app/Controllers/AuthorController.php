<?php

class AuthorController extends Controller {

    protected $vue = "https://unpkg.com/vue@3.2.31";
    public $menu = "user.menu._bootstrap5.php";

    function __construct() {
        if (!\User::isAuthor()) {
            \Messaging\Add("You need to be an Author","warning");
            header("Location: /Home/Login");
            exit(0);
        }
        parent::__construct();
    }

    function Index() {
    }

    function Log() {
        $data['startDate'] = (isset($_POST['startDate'])) ? $_POST['startDate'] : date("Y-m-d",strtotime("-7 days"));
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        $data['logMessages'] = Logging::Query("SELECT * FROM `logging` WHERE `created` >= '{$data['startDate']} 00:00:00' AND `isAdmin` IS NOT NULL ORDER BY `created` DESC");
        include("{$this->viewFolder}/Admin/".__FUNCTION__.".Admin.php");       
        $this->Foot();
    }

    function RebuildApi () {
        $data['start'] = microtime(true);
        System::BuildLiveLeaguesApi();
        $data['duration'] = microtime(true) - $data['start'];
        $data['json'] = json_decode(file_get_contents("https://api.leagues4you.co.uk/liveLeagues"),true);
        $this->Head();
        include("./app/Views/menus/{$this->menu}");
        \Messaging\Show();
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
        $this->Foot();
    }

}