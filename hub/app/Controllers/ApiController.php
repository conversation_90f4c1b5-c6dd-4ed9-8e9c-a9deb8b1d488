<?php

class ApiController extends Controller {

    function __construct() {
        parent::__construct();
        checkJwtAuth();
    }

    public function teamsByLeague($data) {
        $leagueID = (int) $data['variables'][0];
        // Validate input
        if (!$leagueID) {
            return $this->jsonResponse(['error' => 'League ID is required'], 400);
        }

        // Get liveOnly parameter from request
        $liveOnly = isset($_GET['liveOnly']) ? filter_var($_GET['liveOnly'], FILTER_VALIDATE_BOOLEAN) : true;

        // Get teams for the league
        $league = new \League($leagueID);
        $teams = \Team::GetTeamsByLeague($league, $liveOnly);

        if (!$teams) {
            return $this->jsonResponse([]);
        }

        $result = [];
        foreach ($teams as $team) {
            $result[] = [
                'id' => $team->id,
                'name' => $team->name
            ];
        }

        return $this->jsonResponse($result);
    }

    private function jsonResponse($data, $statusCode = 200) {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }
}
