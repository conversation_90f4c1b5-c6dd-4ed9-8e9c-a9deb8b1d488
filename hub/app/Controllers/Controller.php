<?php

class Controller {

    protected $viewFolder = "/var/www/html/hub/app/Views";
    protected $folder;
    protected $config;
    public $menu = "guest.menu.php";
    protected $vue = "https://cdn.jsdelivr.net/npm/vue@2";
    protected $chart = "https://cdn.jsdelivr.net/npm/chart.js@3.4.0/dist/chart.min.js";
    protected $fontAwesome = "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css";
    protected $jQuery = 'js/jquery-3.5.1.slim.min.js';
    // protected $jQuery = 'https://code.jquery.com/jquery-3.5.1.slim.min.js';
    // protected $popper = 'https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js';
    protected $bootstrapCSS = "https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css";
    protected $bootstrapJS = "https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.bundle.min.js";
    protected $quilJS = "//cdn.quilljs.com/1.3.6/quill.min.js";
    protected $quilSnowCSS = "//cdn.quilljs.com/0.20.1/quill.snow.css";

    function __construct() {
        // if (!\User::isAdmin()) {
        //     \Messaging\Add("You need to be logged in as an Administrator","warning");
        //     header("Location: /Home/Login");
        //     exit(0);
        // }
        global $config;
        $this->config = $config;
        $this->folder = strstr(get_class($this),"Controller",true);
    }

    function Head() {
        echo <<<EOT
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <!-- Controller -->
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{$this->config['system']['name']} | {$this->config['system']['version']}</title>
            <base href="{$this->config['system']['url']}">
            <link rel="stylesheet" href="{$this->bootstrapCSS}">
            <link rel="stylesheet" href="{$this->fontAwesome}">
            <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
            <script src="{$this->chart}"></script>
            <script src="{$this->vue}"></script>
            <script src="https://cdn.quilljs.com/1.3.6/quill.js"></script>
            <script type="text/javascript">
                (function(c,l,a,r,i,t,y){
                    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                    t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "swmqmomhmg");
            </script>
            <style>
                .clickable {
                    cursor: pointer;
                }
                button:disabled {
                    cursor: no-drop;
                }
            </style>
        </head>
        <body>
        EOT;
    }

    function Foot() {
        $return = null; 
        if ($this->jQuery) $return .= "<script src=\"{$this->jQuery}\"></script>";
        // if ($this->popper) $return .= "<script src=\"{$this->popper}\"></script>";
        $return .= "<script src=\"{$this->bootstrapJS}\"></script>";
        $return .= "</body></html>";
        echo $return;
    }

    function Download (Array $data) {
        if (isset($_POST['download']) && $_POST['download'] && file_exists($_POST['download'])) {
            $ext = substr($_POST['download'],strrpos($_POST['download'],".")+1);
            $inlineExtensions = ["pdf"];
            $disposition = (in_array($ext,$inlineExtensions)) ? "inline" : "attachment";
            // exit($disposition);
            $disposition = "attachment";
            header("Content-Type: application/octetstream");
            // header("Content-Type: application/$ext");
            header("Content-Transfer-Encoding: Binary");
            header("Content-Length:" . filesize($_POST['download']));
            header("Content-Disposition: $disposition; filename=".basename($_POST['download']));
            readfile($_POST['download']);
            exit(0);   
        }
    }

}
