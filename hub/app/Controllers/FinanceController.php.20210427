<?php

class FinanceController extends Controller {

    protected $folder = "Finance";

    function __construct() {
        if (!\User::isManager()) {
            \Messaging\Add("You need to be logged in as a Manager","warning");
            header("Location: /Home/Login");
            exit(0);
        }
        parent::__construct();
    }
    function Index() {
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }
    function Transactions() {
        global $post;
        $data['userID'] = (isset($post['userID']) && $post['userID']) ? $post['userID'] : null;
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        if (isset($post['transaction'])) {
            $t = new Finance();
            $t->Load($post['transaction']);
            $t->Save();
        }
        $data['transactions'] = Finance::Transactions($data['teamID']);
        $data['users'] = User::Live();
        $data['teams'] = Team::Listing(); 
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }
    function Payments() {
        global $post;
        $data['userID'] = (isset($post['userID']) && $post['userID']) ? $post['userID'] : null;
        $data['typeID'] = (isset($post['typeID']) && $post['typeID']) ? $post['typeID'] : null;
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        if (isset($post['payment'])) {
            $t = new Finance();
            $t->Load($post['payment']);
            $t->Save();
            $data['teamID'] = $post['payment']['teamID'];
        }
        $data['transactions'] = Finance::Payments($data['teamID']);
        $data['users'] = User::Live();
        $data['teams'] = Team::Listing(); 
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }
    function Credits() {
        global $post;
        $data['userID'] = (isset($post['userID']) && $post['userID']) ? $post['userID'] : null;
        $data['typeID'] = (isset($post['typeID']) && $post['typeID']) ? $post['typeID'] : null;
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        if (isset($post['payment'])) {
            $t = new Finance();
            $t->Load($post['payment']);
            $t->Save();
            $data['teamID'] = $post['payment']['teamID'];
        }
        $data['transactions'] = Finance::Credits($data['teamID']);
        $data['users'] = User::Live();
        $data['teams'] = Team::Listing(); 
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }
    function Statement(Array $data = []) {
        global $post;
        if (isset($data['variables'][0])) $post['teamID'] = $data['variables'][0];
        $data['teamID'] = (isset($post['teamID']) && $post['teamID']) ? $post['teamID'] : null;
        $data['users'] = User::Live();
        $data['teams'] = Team::Listing(); 
        $data['transactions'] = ($data['teamID']) ? Finance::Statement($data['teamID']) : [];
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }
    function VenueCostReport() {
        global $post;
        $data['fromDate'] = (isset($post['fromDate']) && $post['fromDate']) ? $post['fromDate'] : date('Y-m-d');
        $data['toDate'] = (isset($post['toDate']) && $post['toDate']) ? $post['toDate'] : date('Y-m-d',strtotime("+6 days",strtotime($data['fromDate'])));
        $data['venueCostReport'] = \Booking::CostReport($data['fromDate'],$data['toDate']);
        \Messaging\Show();
        include("{$this->viewFolder}/Finance/".__FUNCTION__.".User.php");
    }
    function Balances () {
        $data['balances'] = Finance::Balances();
        \Messaging\Show();
        include("{$this->viewFolder}/Finance/".__FUNCTION__.".Finance.php");
    }
    function PendingPayments() {
        global $post;
        if (isset($post['pendingPaymentID'])) {
            $sp = new StripePayment($post['pendingPaymentID']);
            $sp->Cancel();
            $sp->Save();
            \Messaging\Add(($sp->getFailText()) ? $sp->getFailText() : "Payment {$post['pendingPaymentID']} Deleted");
        }
        if (isset($post['pendingPaymentAmend'])) {
            $sp = new StripePayment($post['pendingPaymentAmend']['id']);
            $rlt = $sp->newTotal($post['pendingPaymentAmend']['newTotal']);
            ($rlt === true) ? \Messaging\Add("Payment Updated","success") : \Messaging\Add($rlt,"danger");
        }
        $data['pending'] = \StripePayment::Pending();
        \Messaging\Show();
        include("{$this->viewFolder}/Finance/".__FUNCTION__.".Finance.php");
    }
    function AgedDebtors() {
        $data['debtors'] = Team::AgedDebtors();
        \Messaging\Show();
        include("{$this->viewFolder}/Finance/".__FUNCTION__.".Finance.php");
    }
    function PandL() {
        \Messaging\Show();
        include("{$this->viewFolder}/Finance/".__FUNCTION__.".Finance.php");
    }
    function PaymentHistory(Array $data = []) {
        if (isset($data['variables'][0])) $_POST['teamID'] = $data['variables'][0];
        if (isset($_POST['teamID']) && $_POST['teamID']) {
            $startDate = (isset($_POST['startDate']) && $_POST['startDate']) ? $_POST['startDate'] : null;
            $endDate = (isset($_POST['endDate']) && $_POST['endDate']) ? $_POST['endDate'] : null;
            $data['stripePayments'] = StripePayment::PaymentHistory($_POST['teamID'],$startDate,$endDate);
        }
        $data['teams'] = Team::Listing(); 
        include("{$this->viewFolder}/{$this->folder}/".__FUNCTION__.".{$this->folder}.php");
    }
}