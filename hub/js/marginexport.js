document.addEventListener("DOMContentLoaded", function () {
  var exportButton = document.getElementById("exportButton");

  exportButton.addEventListener("click", function () {
    exportTableToExcel();
  });

  function exportTableToExcel() {
    var sheet = XLSX.utils.table_to_sheet(document.querySelector(".table"));
    var workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, sheet, "Sheet1");
    var excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
    saveAs(
      new Blob([excelBuffer], { type: "application/octet-stream" }),
      "Margin_Report_Export.xlsx"
    );
  }

  function saveAs(blob, fileName) {
    var link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();
  }
});
