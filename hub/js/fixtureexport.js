document.addEventListener("DOMContentLoaded", function () {
  var exportButton = document.getElementById("exportButton");

  exportButton.addEventListener("click", function () {
    exportTableToCSV();
  });

  function exportTableToCSV() {
    var csv = [];

    // Add the header row with dates
    var headerRow = [];
    var weekDates = [];
    document.querySelectorAll(".week-th").forEach(function (th) {
      headerRow.push(th.innerText.trim());
      weekDates.push(th.innerText.trim());
    });
    csv.push(headerRow.join(","));

    // Add the data rows
    var rows = document.querySelectorAll(".week-table tr");
    for (var i = 0; i < rows.length; i++) {
      var cols = rows[i].querySelectorAll("td");
      var leagueName = cols[0].innerText.trim();
      const total = document
        .getElementsByClassName("total" + i)[0]
        .innerText.trim();

      var leagueNameRow = [leagueName];
      var bookingCostRow = ["Booking cost"];
      var profitRow = ["Profit"];

      for (var j = 1; j < cols.length; j++) {
        var bookingCostElement = cols[j].querySelector(".booking-cost");
        var profitElement = cols[j].querySelector(".profit");
        var venueElement = cols[j].querySelector(".fixture-cost");

        if (venueElement) {
          leagueNameRow.push(venueElement.innerText.trim());
        } else {
          leagueNameRow.push("");
        }

        // Process cell content
        if (bookingCostElement) {
          bookingCostRow.push(bookingCostElement.innerText.trim());
        } else {
          bookingCostRow.push("");
        }

        if (profitElement) {
          profitRow.push(
            profitElement.innerText.trim()
            //.replace(/[^0-9.]/g, "")
          );
        } else {
          profitRow.push("");
        }
      }

      if (total) {
        // Remove latest array
        leagueNameRow.pop();
        leagueNameRow.push(total);
      }

      csv.push(leagueNameRow.join(","));
      csv.push(bookingCostRow.join(","));
      csv.push(profitRow.join(","));
    }

    var footerRow = [];
    document.querySelectorAll("tfoot th").forEach(function (th) {
      footerRow.push(th.innerText.trim());
    });
    csv.push(footerRow.join(","));

    // console.log(csv.join("\n"));

    downloadCSV(
      "\uFEFF" + csv.join("\n"),
      "fixture_report.csv",
      "text/csv;charset=utf-8"
    );
  }

  // Helper function to trigger the CSV download
  function downloadCSV(csv, filename, mimeType) {
    var blob = new Blob([csv], { type: mimeType });
    if (navigator.msSaveBlob) {
      // IE 10+
      navigator.msSaveBlob(blob, filename);
    } else {
      var link = document.createElement("a");
      if (link.download !== undefined) {
        // feature detection
        var url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  }
});
