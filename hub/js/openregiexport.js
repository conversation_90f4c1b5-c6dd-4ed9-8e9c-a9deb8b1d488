document.addEventListener('DOMContentLoaded', function () {
    // Add event listener to the export button
    document.getElementById('openExportExcel').addEventListener('click', function () {
        // Get the selected coordinator name
        var selectedCoordinatorName = document.getElementById('coordinator').options[document.getElementById('coordinator').selectedIndex].text;

        // Create a new workbook
        var wb = XLSX.utils.book_new();

        // Add a worksheet
        var ws = XLSX.utils.table_to_sheet(document.querySelector('#tableForOpen'));

        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');

        // Save the workbook as an Excel file with coordinator name in the filename
        XLSX.writeFile(wb, 'Open_Registration_Report_' + selectedCoordinatorName + '.xlsx');
    });
});

