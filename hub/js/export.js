document.addEventListener("DOMContentLoaded", function () {

  document.getElementById("exportExcel").addEventListener("click", function (event) {
    event.preventDefault();
    // Get selected coordinator element
    var selectedCoordinatorElement = document.getElementById("coordinator");
    var selectedWildcard = document.getElementById("wildcard")['checked'];

    // Get selected coordinator name
    var selectedCoordinator =
      selectedCoordinatorElement.options[
        selectedCoordinatorElement.selectedIndex
      ].text;

    var selectedWildcardValue = selectedWildcard ? "on" : "off";
    // Get table data
    var table = document.querySelector(".table");
    var sheetData = XLSX.utils.table_to_sheet(table);

    // Create a workbook with one sheet
    var wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, sheetData, "Live Team Report");

    // Add a new sheet for coordinator information
    var filterInfo = XLSX.utils.aoa_to_sheet([
      ["Selected Coordinator:", selectedCoordinator],
      ["Selected Wildcard:", selectedWildcardValue],
    ]);
    XLSX.utils.book_append_sheet(wb, filterInfo, "Filter info");

    // Save the workbook to a file with coordinator name
    var fileName =
      "Live_Team_Report_" + selectedCoordinator.replace(/\s+/g, "_") + ".xlsx";
    XLSX.writeFile(wb, fileName);
  });
});



