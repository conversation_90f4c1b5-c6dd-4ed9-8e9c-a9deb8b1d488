// riskoverallratingtoggle.js

document.addEventListener("DOMContentLoaded", function () {
  const toggleButton = document.getElementById("toggle-form");
  const exportpdf = document.getElementById("export-pdf");
  const riskForm = document.getElementById("risk-assessment-form");
  const historyForm = document.getElementById("history-form");

  if (toggleButton && riskForm && exportpdf && historyForm) {
    toggleButton.addEventListener("click", function () {
      if (riskForm.style.display === "none" || riskForm.style.display === "") {
        exportpdf.style.display = "none";
        riskForm.style.display = "block";
        toggleButton.textContent = "-";
        historyForm.style.display = "none";
      } else {
        exportpdf.style.display = "none";
        riskForm.style.display = "none";
        toggleButton.textContent = "+";
        historyForm.style.display = "table";
      }

      updateRiskRating();
    });
  }
  // Add input event listeners for staff_severity and staff_likelihood
  document
    .getElementById("staff_severity")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("staff_likelihood")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("medical_fitness_severity")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("medical_fitness_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("condition_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("condition_severity")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("obstruction_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("obstruction_severity")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("manual_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("manual_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("fire_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("fire_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("weather_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("weather_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("parking_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("parking_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("electricity_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("electricity_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("asbestos_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("asbestos_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("sports_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("sports_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("jewellery_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("jewellery_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("insurance_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("insurance_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("violent_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("violent_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("crowd_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("crowd_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("medical_treatment_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("medical_treatment_severity")
    .addEventListener("input", updateRiskRating);

  document
    .getElementById("welfare_likelihood")
    .addEventListener("input", updateRiskRating);
  document
    .getElementById("welfare_severity")
    .addEventListener("input", updateRiskRating);

  // Initial calculation of risk rating on page load
  updateRiskRating();
});
function validateInput(inputId) {
  var inputElement = document.getElementById(inputId);
  var inputValue = parseInt(inputElement.value) || 0;
  if (inputValue < 0 || inputValue > 5) {
    inputElement.value = 0; // Resetting value to 0 if out of bounds
    alert("Input must be between 0 and 5.");
    return 0;
  }
  return inputValue;
}

// Extract the calculation logic into a separate function
function updateRiskRating() {
  validateInput("staff_severity");
  validateInput("staff_likelihood");
  validateInput("medical_fitness_likelihood");
  validateInput("medical_fitness_severity");
  validateInput("condition_likelihood");
  validateInput("condition_severity");
  validateInput("obstruction_likelihood");
  validateInput("obstruction_severity");
  validateInput("manual_likelihood");
  validateInput("manual_severity");
  validateInput("fire_likelihood");
  validateInput("fire_severity");
  validateInput("weather_likelihood");
  validateInput("weather_severity");
  validateInput("parking_likelihood");
  validateInput("parking_severity");
  validateInput("electricity_likelihood");
  validateInput("electricity_severity");
  validateInput("asbestos_likelihood");
  validateInput("asbestos_severity");
  validateInput("sports_likelihood");
  validateInput("sports_severity");
  validateInput("jewellery_likelihood");
  validateInput("jewellery_severity");
  validateInput("insurance_likelihood");
  validateInput("insurance_severity");
  validateInput("violent_likelihood");
  validateInput("violent_severity");
  validateInput("crowd_likelihood");
  validateInput("crowd_severity");
  validateInput("medical_treatment_likelihood");
  validateInput("medical_treatment_severity");
  validateInput("welfare_likelihood");
  validateInput("welfare_severity");

  var staff_severity =
    parseInt(document.getElementById("staff_severity").value) || 0;
  var staff_likelihood =
    parseInt(document.getElementById("staff_likelihood").value) || 0;
  var medical_fitness_likelihood =
    parseInt(document.getElementById("medical_fitness_likelihood").value) || 0;
  var medical_fitness_severity =
    parseInt(document.getElementById("medical_fitness_severity").value) || 0;
  var condition_likelihood =
    parseInt(document.getElementById("condition_likelihood").value) || 0;
  var condition_severity =
    parseInt(document.getElementById("condition_severity").value) || 0;
  var obstruction_likelihood =
    parseInt(document.getElementById("obstruction_likelihood").value) || 0;
  var obstruction_severity =
    parseInt(document.getElementById("obstruction_severity").value) || 0;

  var manual_likelihood =
    parseInt(document.getElementById("manual_likelihood").value) || 0;
  var manual_severity =
    parseInt(document.getElementById("manual_severity").value) || 0;
  var fire_likelihood =
    parseInt(document.getElementById("fire_likelihood").value) || 0;
  var fire_severity =
    parseInt(document.getElementById("fire_severity").value) || 0;
  var weather_likelihood =
    parseInt(document.getElementById("weather_likelihood").value) || 0;
  var weather_severity =
    parseInt(document.getElementById("weather_severity").value) || 0;
  var parking_likelihood =
    parseInt(document.getElementById("parking_likelihood").value) || 0;
  var parking_severity =
    parseInt(document.getElementById("parking_severity").value) || 0;
  var electricity_likelihood =
    parseInt(document.getElementById("electricity_likelihood").value) || 0;
  var electricity_severity =
    parseInt(document.getElementById("electricity_severity").value) || 0;
  var asbestos_likelihood =
    parseInt(document.getElementById("asbestos_likelihood").value) || 0;
  var asbestos_severity =
    parseInt(document.getElementById("asbestos_severity").value) || 0;
  var sports_likelihood =
    parseInt(document.getElementById("sports_likelihood").value) || 0;
  var sports_severity =
    parseInt(document.getElementById("sports_severity").value) || 0;
  var jewellery_likelihood =
    parseInt(document.getElementById("jewellery_likelihood").value) || 0;
  var jewellery_severity =
    parseInt(document.getElementById("jewellery_severity").value) || 0;
  var insurance_likelihood =
    parseInt(document.getElementById("insurance_likelihood").value) || 0;
  var insurance_severity =
    parseInt(document.getElementById("insurance_severity").value) || 0;
  var violent_likelihood =
    parseInt(document.getElementById("violent_likelihood").value) || 0;
  var violent_severity =
    parseInt(document.getElementById("violent_severity").value) || 0;
  var crowd_likelihood =
    parseInt(document.getElementById("crowd_likelihood").value) || 0;
  var crowd_severity =
    parseInt(document.getElementById("crowd_severity").value) || 0;
  var medical_treatment_likelihood =
    parseInt(document.getElementById("medical_treatment_likelihood").value) ||
    0;
  var medical_treatment_severity =
    parseInt(document.getElementById("medical_treatment_severity").value) || 0;
  var welfare_likelihood =
    parseInt(document.getElementById("welfare_likelihood").value) || 0;
  var welfare_severity =
    parseInt(document.getElementById("welfare_severity").value) || 0;

  document.getElementById("overall_risk_rating").innerHTML =
    staff_likelihood * staff_severity;

  document.getElementById("medical_fitness_risk_rating").innerHTML =
    medical_fitness_likelihood * medical_fitness_severity;

  document.getElementById("condition_risk_rating").innerHTML =
    condition_likelihood * condition_severity;

  document.getElementById("obstruction_risk_rating").innerHTML =
    obstruction_likelihood * obstruction_severity;
  document.getElementById("manual_risk_rating").innerHTML =
    manual_likelihood * manual_severity;

  document.getElementById("fire_risk_rating").innerHTML =
    fire_likelihood * fire_severity;

  document.getElementById("weather_risk_rating").innerHTML =
    weather_likelihood * weather_severity;

  document.getElementById("parking_risk_rating").innerHTML =
    parking_likelihood * parking_severity;

  document.getElementById("electricity_risk_rating").innerHTML =
    electricity_likelihood * electricity_severity;

  document.getElementById("asbestos_risk_rating").innerHTML =
    asbestos_likelihood * asbestos_severity;

  document.getElementById("sports_risk_rating").innerHTML =
    sports_likelihood * sports_severity;

  document.getElementById("jewellery_risk_rating").innerHTML =
    jewellery_likelihood * jewellery_severity;

  document.getElementById("insurance_risk_rating").innerHTML =
    insurance_likelihood * insurance_severity;

  document.getElementById("violent_risk_rating").innerHTML =
    violent_likelihood * violent_severity;

  document.getElementById("crowd_risk_rating").innerHTML =
    crowd_likelihood * crowd_severity;

  document.getElementById("medical_treatment_risk_rating").innerHTML =
    medical_treatment_likelihood * medical_treatment_severity;

  document.getElementById("welfare_risk_rating").innerHTML =
    welfare_likelihood * welfare_severity;

  const riskRatings = [
    "overall_risk_rating",
    "medical_fitness_risk_rating",
    "condition_risk_rating",
    "obstruction_risk_rating",
    "manual_risk_rating",
    "fire_risk_rating",
    "weather_risk_rating",
    "parking_risk_rating",
    "electricity_risk_rating",
    "asbestos_risk_rating",
    "sports_risk_rating",
    "jewellery_risk_rating",
    "insurance_risk_rating",
    "violent_risk_rating",
    "crowd_risk_rating",
    "medical_treatment_risk_rating",
    "welfare_risk_rating",
  ];

  // Iterate over each risk rating
  riskRatings.forEach(function (riskRatingId) {
    // Get the element and its value
    const element = document.getElementById(riskRatingId);
    const value = parseInt(element.innerHTML, 10);

    // Determine the background color based on the value
    if (value === 0) {
      // If value is 0, set background color to white
      element.style.backgroundColor = "white";
    } else if (value >= 1 && value <= 7) {
      element.style.backgroundColor = "green";
    } else if (value >= 8 && value <= 15) {
      element.style.backgroundColor = "#ffc10787";
    } else if (value >= 16 && value <= 25) {
      element.style.backgroundColor = "red";
    }
  });

}

function checkRiskAssessment() {
  // Check dropdowns in the Fire Evacuation Procedures section
  var fireSelect1 = document.querySelector('[name="form[fire_select1]"]').value;
  var fireSelect2 = document.querySelector('[name="form[fire_select2]"]').value;
  var fireSelect3 = document.querySelector('[name="form[fire_select3]"]').value;
  var fireSelect4 = document.querySelector('[name="form[fire_select4]"]').value;
  var fireSelect5 = document.querySelector('[name="form[fire_select5]"]').value;

  // Check dropdowns in the Electricity and Gas section
  var electricitySelect1 = document.querySelector(
    '[name="form[electricity_select1]"]'
  ).value;
  var electricitySelect2 = document.querySelector(
    '[name="form[electricity_select2]"]'
  ).value;

  // Check if any dropdowns have the 'not' options selected
  if (
    fireSelect1 === "hasnot" ||
    fireSelect2 === "isnot" ||
    fireSelect3 === "isnot" ||
    fireSelect4 === "arenot" ||
    fireSelect5 === "hasnot" ||
    electricitySelect1 === "doesnot" ||
    electricitySelect2 === "doesnot"
  ) {
    // Alert message
    alert(
      "This Risk Assessment has failed because of the answers to the Fire Procedure and Electricity and Gas control measures. Please contact either Richard or Charlotte for further instructions."
    );
    return false; // Prevent form submission
  }
  return true; // Allow form submission
}

function validateForm() {
  // Get all the input elements within the specified tr
  var inputs = document.querySelectorAll('tr input');

  // Loop through each input element
  for (var i = 0; i < inputs.length; i++) {
    // Check if input box is empty
    if (inputs[i].value.trim() === '') {
      alert('Please fill in all input boxes before submitting.');
      return false; // Prevent form submission
    }

    // Check if likelihood or severity input is empty
    if ((inputs[i].name.includes('likelihood') || inputs[i].name.includes('severity')) && inputs[i].value === '') {
      alert('Please fill in both Likelihood and Severity for all rows before submitting.');
      return false; // Prevent form submission
    }
  }

  // Check the total sum of severity and likelihood for each row
  var riskRatings = document.querySelectorAll('[id$="_risk_rating"]');
  for (var i = 0; i < riskRatings.length; i++) {
    var ratingValue = parseInt(riskRatings[i].innerHTML, 10) || 0;
    if (ratingValue > 16) {
      alert('The total risk rating for one or more categories is above 16. Please adjust the values before submitting.');
      return false; // Prevent form submission
    }
  }

  // If no empty input box or likelihood/severity input found, and no row has a total above 16, allow form submission
  return true;
}



document.addEventListener("DOMContentLoaded", function () {
  const publishedFilter = document.getElementById("publishedFilter");
  const controlFilter = document.getElementById("controlFilter");
  const venueFilter = document.getElementById("venueFilter"); // Add this line
  const rows = document.querySelectorAll("#riskTable tbody tr");

  function applyFilters() {
    const publishedValue = publishedFilter.value;
    const controlValue = controlFilter.value;
    const venueValue = venueFilter.value.trim().toLowerCase(); // Trim and convert to lowercase

    rows.forEach((row) => {
      const publishedCell = row.querySelector("td:nth-child(3)").textContent;
      const controlCell = row.querySelector("#red_amber_control").textContent;
      const venueCell = row.querySelector("td:nth-child(1)").textContent.trim().toLowerCase(); // Trim and convert to lowercase

      const showRow =
        (publishedValue === "" || publishedCell === publishedValue) &&
        (controlValue === "" || controlCell === controlValue) &&
        (venueValue === "" || venueCell === venueValue);

      row.style.display = showRow ? "" : "none";
    });
  }


  // Event listeners for dropdown changes
  publishedFilter.addEventListener("change", applyFilters);
  controlFilter.addEventListener("change", applyFilters);
  venueFilter.addEventListener("change", applyFilters); // Add this line

  const exportButton = document.getElementById("exportButton");
  exportButton.addEventListener("click", exportToCSV);

  // Function to Export Filtered Table Data to CSV
  function exportToCSV() {
    const filteredRows = document.querySelectorAll(
      '#riskTable tbody tr:not([style*="none"])'
    );
    const headers = Array.from(
      document.querySelectorAll("#riskTable thead th")
    ).map((header) => header.textContent.trim());
    const rowsData = [];

    filteredRows.forEach((row) => {
      const rowData = Array.from(row.children).map((cell) =>
        cell.textContent.trim()
      );
      rowsData.push(rowData.join(","));
    });

    const csvContent = [headers.join(","), ...rowsData].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });

    // Triggering the download
    const link = document.createElement("a");
    if (navigator.msSaveBlob) {
      // For IE
      navigator.msSaveBlob(blob, "VenuRiskAssesmentReport.csv");
    } else {
      link.href = URL.createObjectURL(blob);
      link.setAttribute("download", "VenuRiskAssesmentReport.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
});
