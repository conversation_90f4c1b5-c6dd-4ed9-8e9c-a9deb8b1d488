document.addEventListener("DOMContentLoaded", function () {
    var accordionBtn1 = document.querySelector("#accordionBtn1");
    var accordionBtn2 = document.querySelector("#accordionBtn2");
  
    // Function to toggle accordion state
    function toggleAccordion(button, accordionId) {
      button.addEventListener("click", function () {
        var panel = this.nextElementSibling;
        var isOpen = panel.style.display === "block";
  
        // Close all accordion sections
        closeAllAccordions();
  
        if (isOpen) {
          panel.style.display = "none";
        } else {
          panel.style.display = "block";
        }
  
        // Save the state in local storage
        localStorage.setItem(accordionId, isOpen ? "closed" : "open");
      });
    }
  
    // Function to close all accordion sections
    function closeAllAccordions() {
      // Close accordion 1
      accordionBtn1.classList.remove("active");
      accordionBtn1.nextElementSibling.style.display = "none";
      // Close accordion 2
      accordionBtn2.classList.remove("active");
      accordionBtn2.nextElementSibling.style.display = "none";
    }
  
    // Toggle accordion state for accordion 1
    toggleAccordion(accordionBtn1, "accordionId1");
    // Toggle accordion state for accordion 2
    toggleAccordion(accordionBtn2, "accordionId2");
  
    // Restore state from local storage on page load
    var accordionId1State = localStorage.getItem("accordionId1");
    var accordionId2State = localStorage.getItem("accordionId2");
  
    if (accordionId1State === "open") {
      accordionBtn1.classList.add("active");
      accordionBtn1.nextElementSibling.style.display = "block";
    }
  
    if (accordionId2State === "open") {
      accordionBtn2.classList.add("active");
      accordionBtn2.nextElementSibling.style.display = "block";
    }
  });
  