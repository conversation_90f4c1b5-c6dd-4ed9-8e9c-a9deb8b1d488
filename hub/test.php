<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

define ("ROOT_FOLDER", strrev(strstr(strrev(dirname(__FILE__)),DIRECTORY_SEPARATOR)));
define ("CORE_FOLDER", ROOT_FOLDER ."core" . DIRECTORY_SEPARATOR);
define ("LOCAL_FOLDER", __DIR__ . DIRECTORY_SEPARATOR);
$init = CORE_FOLDER."init.php";
require_once ($init);

//Database::Backup();
// $teamSeason = new TeamSeason(8795);
// TeamSeason::ToggleWildcard($teamSeason);

// $data['season'] = new Season(561);
// $seasonOptions = Venue::SeasonOptions($data['season']);
// $seasonOptions = Booking::SeasonVenues($data['season']);
// Tools::Dump($seasonOptions);

// StripePayment::AlertSweep();
// exit("Done");
// $seasonID = 661;
// $season = new Season($seasonID);
// $season->getDivisions();
// Tools::Dump($season);
exit("Done");
// $seasonID = 543;
// $season = new Season($seasonID);
// $teams = Team::SeasonTeams($season);
// Tools::Dump($teams);
// exit("Done");

// Tools::Dump(StripePayment::CaptureAll(date('Y-m-d',strtotime("-1 day"))));
// Tools::Dump(StripePayment::RaiseSuccessTransactions());
// Db::Backup();
// StripePayment::AlertSweep();
// exit("Done");

// $alertables = StripePayment::AlertSweep();
// Tools::Dump($alertables);

// StripePayment::BillingStage4();
// StripePayment::BillingStage5();
// exit("Done");



// exec("cd /var/www/html/core && git pull https://leagues4you:<EMAIL>/leagues4you/core.git");
// exec("cd /var/www/html/core && git pull");ß

// exit("Nothing to do");

/* Copy Teams from source Season to destination Season */
// $sourceSeasonID = 651;
// $sourceSeason = new Season($sourceSeasonID);
// $destinationSeasonID = 695;
// $destinationSeason = new Season($destinationSeasonID);
// $sourceTeams = TeamSeason::TeamsInSeason($sourceSeason);
// $destinationTeams = TeamSeason::TeamsInSeason($destinationSeason);
// foreach ($sourceTeams as $sourceTeam) {
//   $rlt = TeamSeason::Enter($sourceTeam,$destinationSeason,$sourceSeason);
//   Tools::Dump($rlt);
// }
// exit("Done");



// Score Testing
// function soccer_scoring(Float $homeScore, Float $awayScore) {
//   $winPoints = 3; $scoreDrawPoints = 2; $noScoreDrawPoints = 1;
//   $return['log'][] = "Calculate Points for Home score {$homeScore} and Away score {$awayScore}";
//   if ($homeScore > $awayScore) {
//     $return['home']['points'] = $winPoints;
//     $return['log'][] = "{$return['home']['points']} points for home win";
//     $return['home']['win'] = 1;
//   } elseif ($awayScore > $homeScore) {
//     $return['away']['points'] = $winPoints;
//     $return['log'][] = "{$return['away']['points']} points for away win";
//     $return['away']['win'] = 1;
//   } elseif ($homeScore > 0) {
//     $return['home']['points'] = $return['away']['points'] = $scoreDrawPoints;
//     $return['log'][] = "{$return['home']['points']} points for a home score draw";
//     $return['log'][] = "{$return['away']['points']} points for a away score draw";
//     $return['home']['draw'] = 1;
//     $return['away']['draw'] = 1;
//   } else {
//     $return['home']['points'] = $return['away']['points'] = $noScoreDrawPoints;
//     $return['log'][] = "{$return['home']['points']} points for a home no score draw";
//     $return['log'][] = "{$return['away']['points']} points for a away no score draw";
//     $return['home']['draw'] = 1;
//     $return['away']['draw'] = 1;
//   }
//   return $return;
//   // Losing Bonus Points
//   if ($this->homeScore != $this->awayScore) {
//       if ($this->homeScore < $this->awayScore) {
//           if ($this->homeScore >= ($this->awayScore - 5)) {
//               $standings[$this->home]['bp'] = 2;
//               $standings[$this->home]['log'][] = "2 BP for within 5";
//           } elseif ($this->homeScore >= ($this->awayScore / 2)) {
//               $standings[$this->home]['bp'] = 1;
//               $standings[$this->home]['log'][] = "1 BP for loss within a half";
//           }
//       } else {
//           if ($this->awayScore >= ($this->homeScore - 5)) {
//               $standings[$this->away]['bp'] = 2;
//               $standings[$this->away]['log'][] = "2 BP for within 5";
//           } elseif ($this->awayScore >= ($this->homeScore / 2)) {
//               $standings[$this->away]['bp'] = 1;
//               $standings[$this->away]['log'][] = "1 BP for within half";
//           }
//       }
//   }
// }

// if (isset($_GET['home']) && is_numeric($_GET['home']) && isset($_GET['away']) && is_numeric($_GET['away'])) {
//   Tools::Dump(soccer_scoring ($_GET['home'],$_GET['away']));
// }

exit("Nothing to do");

// Tools::Dump(Stripe::Synchronise());

// $team = new Team(1685);
// $rlt = StripePayment::BillStage2($team);
// exit(Tools::Dump($rlt));

// Team::AgedDebtorEmails();

// Tools::Dump(Team::AgedDebtorEmails());

// $team = new Team(1656);
// $rlt = StripePayment::BillStage2($team);
// exit(Tools::Dump($rlt));

// $agedDebtors = Team::AgedDebtors();
// foreach ($agedDebtors as $teamID => $agedDebtor) {
//   if (!$agedDebtor['pending']) {
//     $team = new Team($teamID);
//     $return[] = $team->AgedDateEmail();
//   } 
// }


// $team = new Team(1408);
// $active = StripePayment::Active($team);
// Tools::Dump($active);
// exit(Tools::Dump($team->ApiData()));
exit(Tools::Dump($return));

// Tools::Dump(Stripe::Synchronise());

// $rlt1 = StripePayment::BillingStage1(true); Tools::Dump($rlt1);
// $rlt2 = StripePayment::BillingStage2(true); Tools::Dump($rlt2);
// $rlt3 = StripePayment::BillingStage3(true); Tools::Dump($rlt3);
// $rlt4 = Stripe::Synchronise(); Tools::Dump($rlt4);
// StripePayment::CancelAllWithStatus('requires_payment_method');
exit("Done");

// $rlt = Finance::Alerts();
// $bcc = [
//   "<EMAIL>" => "Marc Conlon",
//   "<EMAIL>" => "Richard Dakin",
//   "<EMAIL>" => "Charlotte Waugh"
// ];
// foreach ($rlt as $r) {
  // Email::Issue($r['subject'], $r['message'], ["<EMAIL>" => "Charlotte Waugh"],["<EMAIL>" => "Richard Dakin"], ["<EMAIL>" => "Marc Conlon"]);
  // break;
//   Tools::Dump(Email::Issue($r['subject'], $r['message'], $r['to'],$r['cc'], $bcc));
// }
// exit(Tools::Dump($rlt));

// $user = new User(2);
// exit(Tools::Dump($user->Profile()));

// $teamFollowers = TeamFollower::FullSeasonTeams();
// if ($teamFollowers) {
//   $total = 0;
//   echo "<table><thead><tr><th>Team</th><th>League</th><th>User</th><th>Balance</th></tr></thead><tbody>";
//   foreach ($teamFollowers as $teamFollower) {
//     $total += ($balance = Finance::Balance($teamFollower->getTeam()));
//     echo "<tr>";
//     echo "<td>".$teamFollower->getTeam()."</td>";
//     echo "<td>".$teamFollower->getLeague()."</td>";
//     echo "<td>".$teamFollower->getUser()."</td>";
//     echo "<td>$balance</td>";
//     echo "</tr>";
//     // Tools::Dump($teamFollower->ApiData());
//   }
//   echo "<tr><th colspan=\"3\">Total</th><th>$total</th></tr></tbody></table>";
// }
exit(0);
// $team = new Team(1280);
// $rlt = $team->getTeamManagers();
// $rlt = TeamFollower::Current($team);
// exit(Tools::Dump($rlt));

// $cardPayment = new CardPayment();
// $customers = $cardPayment->Customer_Fetch("<EMAIL>");
// if (isset($customers['error'])) exit($customers['error']);
// foreach ($customers['success'] as $customer) {
//   $payMethods = $cardPayment->PaymentMethods_Fetch($customer->id);
//   if (isset($payMethods['error'])) exit($payMethods['error']);
//   $paymentMethods[$customer->id][] = $payMethods['success'];
// }
// foreach ($paymentMethods as $stripeCustomerID => $customerPayMethods) {
//   echo "<b>$stripeCustomerID</b><br>";
//   foreach ($customerPayMethods as $customerPayMethod) {
//     $card = $customerPayMethod[0]->card;
//     echo "$stripeCustomerID : ".ucwords("$card->brand $card->funding")." xxxx-{$card->last4} Expiring {$card->exp_month}/{$card->exp_year}";
//     echo "<br>";
//   }
// }
// exit(Tools::Dump($paymentMethods));

// $pi_id = "pi_3KZ9ahLOCeRFt5lu1HyrVNtw";
// $cardPayment = new CardPayment();
// $pi = $cardPayment->PaymentIntent_Fetch($pi_id);
// exit(Tools::Dump($pi['success']));
/*
# $pi_id = "pi_3KglbZLOCeRFt5lu1QgGgTgR";
# $pi_id = "pi_3LAqMkLOCeRFt5lu1VlJbX8j";
# $pi_id = "pi_3LAqUNLOCeRFt5lu19OG5rER";
# $pi_id = "pi_3LAqULLOCeRFt5lu0E9froiG";
# $pi_id = "pi_3LAqUFLOCeRFt5lu1Oua865s";
$pi = Stripe::getPaymentIntent($pi_id);
echo "$pi_id created ".date('g:ia d/m/Y',$pi->created) . " is {$pi->charges->data[0]->outcome->network_status}";
if ($pi->charges->data[0]->outcome->reason) echo " : {$pi->charges->data[0]->outcome->reason}";
echo "<br>";
exit(Tools::Dump($pi));
*/

// $user = User::fromEmail("<EMAIL>");
// exit(Tools::Dump($user->getTeams()));

// $team = new Team(1997);
// $rlt = $team->PaymentChecks();
// exit(Tools::Dump($rlt));

# Bill a Team
/*
$team = new Team(1997);
$rlt = StripePayment::BillStage2($team);
exit(Tools::Dump($rlt));
*/

#exit(Tools::Dump(StripePayment::RemovePaymentIntentsNotPreAuthd('2022-06-13')));

# Billing Stages
#$stage1 = StripePayment::BillingStage1(true); #exit(Tools::Dump($stage1));
#$stage2 =  StripePayment::BillingStage2(true); exit(Tools::Dump($stage2));
#$stage3 =  StripePayment::BillingStage3(true); exit(Tools::Dump($stage3));
#exit("Done Billing");

/*
$team = new Team(2025);
exit(Tools::Dump(StripePayment::BillStage2($team)));
*/

# Full Balance Payment
/*
$user = new User(685); $profile = $user->Profile(); if (!$profile['teams']) exit("No Teams");
Tools::Dump($profile);
foreach ($profile['teams'] as $t) {
  $team = new Team($t['id']);
  $canReenter = $team->CanReenter();
  echo "$team CanReenter:<br>";
  Tools::Dump($canReenter);
  $liveSeason = TeamSeason::Live($team);
  Tools::Dump($liveSeason->FullBalanceToPay());
}
exit("Done");
*/

// $docLibraryTypeID = 3;
// $docLibraryType = new DocLibraryType($docLibraryTypeID);
// $docLibraryType->Save();

// $teamID  = 1855;
// $team = new Team($teamID);
// Tools::Dump($team->ApiData());


# Season Invoice
/*
$teamSeasonID = 7023;
$teamSeason = new TeamSeason($teamSeasonID);
try {
  $pdf = new PDF\SeasonInvoice2($teamSeason,"I");
} catch (Exception $e) {
  echo $e->getMessage();
}
exit(0);
*/
// $captain = $team->getTeamCaptain();
// Tools::Dump($captain);
// Tools::Dump($team);
// $teamSeasons = TeamSeason::Current($team);
// $season = $division = null;
// if ($teamSeasons[0]) {
//     $season = new Season($teamSeasons[0]->seasonID);
//     $division = new Division($teamSeasons[0]->divisionID);
// }
// Tools::Dump($season);
// Tools::Dump($division);
// $stripePaymentMethodID = "pm_1KqgN5LOCeRFt5luJmbQeHb8";
// TeamFollower::CancelPaymentMethod($stripePaymentMethodID);

// $userID = 2;
// $user = new User($userID);
// $teamID = 1814;
// $team = new Team($teamID);
// $stripe = new StripeConnection(2);
// $setupIntent = $stripe->FetchSetupIntent("seti_1KqfOKLOCeRFt5lu82zFJboo");

// $rlt = TeamFollower::PaymentMethod($team,$user,$setupIntent->payment_method);

// $setupIntentID = 'seti_1Kqck1LOCeRFt5luAPwCeLme';
// $stripe->Connect();
// $setupIntent = $stripe->fetchSetupIntent($setupIntentID);
// $rlt = $stripe->SaveSetupIntent($setupIntent);

// $setupIntent = StripeSetupIntent::GetFromSetupIntentID($setupIntentID);
// $setupIntent = StripeSetupIntent::FetchFromSetupIntentID($setupIntentID);
// Tools::Dump($rlt);
// Tools::Dump($rlt);
// Tools::Dump($stripe);
exit(0);
// $venues = Venue::Listing();
// foreach ($venues as $venue) {
//   if ($venue->deleted) { echo "{$venue->id} : $venue deleted<br>"; continue;}
//   $leagues = $venue->getLeagues();            
//   if (!$leagues) { echo "{$venue->id} : $venue has no leagues<br>"; continue;}
//   echo $venue."<br>";
// }
// exit(0);


// $venues = Venue::Listing();
// foreach ($venues as $venue) {
//   if (!$venue->getLeagues()) continue;
//   $return[] = $venue->ApiData();
// }
// Tools::Dump($return);
// exit(0);

// $venueID = 208;
// $venue = new Venue($venueID);
// $leagues = $venue->getLeagues();
// if ($leagues) $returnexit("Nope");
// Tools::Dump($venue->ApiData());
// exit(0);


// $userID = 2;
// $user = new User($userID);
// echo $user."<br>";
// $stripeCustomer = StripeCustomer::Get($user,1);
// Tools::Dump($stripeCustomer);
// $setupIntent = StripeSetupIntent::CreateReuse($stripeCustomer,1);
// Tools::Dump($setupIntent);
// Tools::Dump(StripeSetupIntent::Cancel($setupIntent));
// exit(0);
// $start = microtime(true);
// Tools::Dump(StripePaymentMethod::ResolveUnresolved(10,2));
// echo "Completed in " . (microtime(true) - $start) . " seconds";
// exit(0);

// $pm = StripePaymentMethod::GetByStripePaymentMethodID('pm_1Jp8A5LOCeRFt5lumKcSZL4R');
// $pm->Fetch();
// Tools::Dump($pm);
// exit(0);

/* Import Cards from Teams to StripePaymentMethods */
/*
$TeamsWithCardsWhereCaptainIsTreasurer = Team::TeamsWithCardsWhereCaptainIsTreasurer();
$sql = "INSERT INTO `stripePaymentMethods` (`stripePaymentMethodID`) VALUES ";
$conn = null;
$stripeCards = [];
foreach ($TeamsWithCardsWhereCaptainIsTreasurer as $team) {
  if (in_array($team->treasurerStripePaymentMethodID,$stripeCards)) continue;
  $stripeCards[] = $team->treasurerStripePaymentMethodID;
  $sql .= $conn . "('{$team->treasurerStripePaymentMethodID}')";
  if (!$conn) $conn = ",";
}
Tools::Dump(Database::Execute($sql));
*/
exit(0);

// $venueSearch = Venue::liveSeasonLeagueNameVenueSearch("malv");
// Tools::Dump($venueSearch);exit(0);

// $email = "<EMAIL>";

// $stripe = new Stripe_v2(false);
// $pmID = "pm_1KldSaLOCeRFt5luf4NpFKJh";
// $pm = $stripe->fetchPaymentMethod($pmID,true);
// $userID = 2;
// $user = new User($userID);
// $teamsManaged = Team::Managed($user);
// Tools::Dump($pm);

$userID = 2;
$user = new User($userID);
$teams = Team::Following($user);
foreach ($teams as $team) Tools::Dump($team->ApiData());
// Tools::Dump($teams);

exit(0);

// $userID = 2;
// $user = new User($userID);
// $stripe = new Stripe_v2(false);
// $stripeCustomerID = $stripe->getStripeCustomer($user);
// $setupIntent = $stripe->createSetupIntent($stripeCustomerID);
// Tools::Dump($setupIntent);
// exit("Stripe Customer ID $stripeCustomerID");

// Tools::Dump(System::BuildLiveLeaguesApi());exit(0);

// if (isset($_FILES['file'])) Tools::Dump(DocLibraryVersion::Upload($_POST['doc'],$_FILES['file']));

// DocLibraryType::UploadForm(basename(__FILE__));
// $doc = DocLibraryVersion::Latest(1);

// $listing = DocLibraryVersion::Listing();
// foreach ($listing as $l) echo $l."<br>";
// Tools::Dump($listing);

// $leagueID = 90;
// $league = new League($leagueID);

exit(0);

// $userID = 2;
// $user = new User($userID);
// Tools::Dump($user->Profile());
// exit(0);

// $log = StripePayment::RaiseSuccessTransactions();
// Tools::Dump($log);
// exit(0);

$yesterdaysSucceeded = StripePayment::FetchFor('succeeded',date('Y-m-d',strtotime("-1 day")));
// $todaysUncaptured = StripePayment::FetchFor('requires_capture');
echo "<style> tr.highlight { background-color: red; color: white;}</style>";
echo "<table><tbody>";
foreach ($yesterdaysSucceeded as $sp) {
  $teams[] = $sp->teamID;
  echo "<tr><td>" . implode("</td><td>",$sp->Tabular())."</td></tr>";
}
echo "<tbody>
// <tbody>";
// foreach ($todaysUncaptured as $sp) {
//   if (in_array($sp->teamID,$teams)) {
//     $sp->Cancel();
//     $class = "highlight";
//   } else $class = null;
//   echo "<tr class=\"$class\"><td>" . implode("</td><td>",$sp->Tabular())."</td></tr>";
// }
// echo "</tbody>";
echo "</table>";
exit(0);

// Tools::Dump(System::BuildLiveLeaguesApi());exit(0);
// $leagueID = 151;
// $league = new League($leagueID);
// Tools::Dump($league->ApiData());

// exit("Done");

// $leagueID = 159;
// $league = new League($leagueID);
// $season = Season::hasOFR($league);
// Tools::Dump($season);

// $tm_approvals = TeamManagement::CheckApprovals();
// Tools::Dump($tm_approvals);

$userID = 2;
$user = new User($userID);
// Tools::Dump($user->Profile());
$teamID = 372;
$team = new Team($teamID);
$rlt = Team::Follow($team,$user);
exit("Done");
  
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://js.stripe.com/v3/"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
  <title>Document</title>
</head>
<body>
  <div class="container">
  $jwt = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjE1MDUifQ.GaarEalc5FyZkl8hyslh04S5vEXqVAmj9i1d6gkwhUw";
  echo "JWT $jwt<br>";
  $userID = Jwt::Validate($jwt);
  echo "UserID = $userID<br>";
  if (is_string($userID)) exit(json_encode(["error" => $userID]));
  $user = new User($userID);
  if (!$user->id) exit(json_encode(["error" => "Invalid User"]));
  echo "$user<br>";
  $stripe = new Stripe_v2(false);
  $stripeCustomerID = $stripe->getStripeCustomer($user);
  if (!$stripeCustomerID) exit(json_encode(["error" => "Not a Stripe user"]));
  echo "Stripe User ID $stripeCustomerID<br>";
  // $paymentMethods = $stripe->getPaymentMethods($stripeCustomerID);
  // $stripe->removePaymentCard(strtolower("PM_1KDYGSLOCERFT5LUAOSWER4T"));
  $paymentMethods = $stripe->fetchPaymentMethods($stripeCustomerID);
  if (!$paymentMethods || !count($paymentMethods['data'])) exit(json_encode(["error" => "No payment methods"]));
  echo "Retrieved " . count($paymentMethods['data'])." payment methods<br>";
  foreach ($paymentMethods['data'] as $paymentMethod) {
    echo "{$paymentMethod->id} " . strtoupper ("{$paymentMethod->card->brand} xxxx-xxxx-xxxx-{$paymentMethod->card->last4} {$paymentMethod->card->exp_month}/{$paymentMethod->card->exp_year}")."<br>";
    $pm[] = $paymentMethod; 
  }
  // $last = array_pop($pm);
  Tools::Dump($pm);
  // $stripe->removePaymentCard($last->id);
  // $seasons = Season::LiveList();
    // foreach ($seasons as $season) {
    //   $season->getLeague();
    //   if (!$season->venueID && $season->league->defaultVenueID) {
    //     $season->venueID = $season->league->defaultVenueID;
    //     $season->Save();
    //     echo "Update {$season->league} $season to {$season->league->defaultVenueID}<br>";
    //   } 
    // }
  exit(0);
  // $bookingID = 9429;
  // $bookingID = 9149;
  // $booking = new Booking($bookingID);
  // $fixtureID = 35693;
  // $fixture = new Fixture($fixtureID);
  // $options = Schedule::FixtureOptions($fixture,$booking);
  // Tools::Dump($options);
  // $venueID = 190;
  // $venue = new Venue($venueID);
  // $ApiListing = Venue::ApiListing();
  // Tools::Dump($ApiListing);
  // exit(0);

  // Billing_Log::ClearTestRuns (); exit(0);
  // StripePayment::BillingStage2();
  // $seasonID = 299;
  // $season = new Season($seasonID);
  // $venues = Venue::SeasonOptions($season);
  // if (!$venues) exit("Nothing");
  // foreach ($venues as $venue) echo $venue."<br>";
  // exit(0);
/*
  $tasterSessionID = 53;
  $tasterSession = new TasterSession($tasterSessionID);
  $cancelRlt = $tasterSession->Cancel();
  echo ($cancelRlt['success']) ? $cancelRlt['success'] : $cancelRlt['error'];
  $refundRlt = $tasterSession->Refund();
  Tools::Dump($refundRlt);
  exit(0);
*/
// Tools::Dump(Email::Test()); exit(0);
// System::BuildLiveLeaguesApi();exit(0);

// Tools::Dump(Team::AgedDebtors());
// Email::Test();
// exit(0);

// $captured = $refunded = null;
// foreach ($pi->charges->data as $charge) {
//   $captured += $charge->amount_captured;
//   $refunded += $charge->amount_refunded;
// }
// if ($pi->status == "succeeded") {
//   if ($refunded) {
//     $status = "$refunded refunded of $captured";
//   } else $status = "$captured charged";
// } elseif ($pi->status == "requires_capture") {
//   $status = "Pre-authorised $captured";
// } elseif ($pi->status == "requires_payment_method") {
//   $status = "Incomplete";
// } else $status = "Unpaid";
// $taster_booking = new C2C_Booking(84);
// $taster_booking->SendConfirmation();
// $tasterSessionID = 38;
// $tasterSession = new TasterSession($tasterSessionID);
// $attendees = $tasterSession->attendees();
// Tools::Dump($tasterSession);
// Tools::Dump($attendees);
exit(0);

  // StripePayment::BillingStage1(true);
  // StripePayment::BillingStage2(true);
  // StripePayment::BillingStage3(true);
  /* Get all cancelled Seasons */

  /* Foreach check for unbilled fixtures */
  /* $result = $season->clearUnbilled(); */

  // $tasterAttendee = new TasterAttendee(501);
  // $tasterAttendee->SendCancellation();
  // Tools::Dump($tasterAttendee);
  // $stripe = new Stripe_v2(true);
  // $pi = $stripe->fetchPaymentIntent("pi_3JugzPLOCeRFt5lu0qjwbEHe");
  // Tools::Dump($pi);

  // System::BuildLiveLeaguesApi();

  // $start = microtime(true);
  // $cancelledSeasons = Season::Cancelled();
  // foreach ($cancelledSeasons as $cancelledSeason) {
  //   $cancelledSeason->clearUnbilled();
    // echo $cancelledSeason . "<br>";
  // }
  // echo "Took " . (microtime(true) - $start) ." seconds<br>";
  exit(0);

  // $report = Fixture::Report('2021-11-16','2021-11-16');
  // Tools::Dump($report);
  // $unconfirmeds = C2C_Booking::Unconfirmeds();
  // $unconfirmed = array_pop($unconfirmeds);
  // $unconfirmed = new C2C_Booking(75);
  // echo $unconfirmed->SendConfirmation();
  // $stripe = new Stripe_v2(true);
  // $pi = $stripe->fetchPaymentIntent("pi_3JuBjTLOCeRFt5lu0nGbe1ix");
  // Tools::Dump($pi);
  ?>
</body>
</html>
<?php
exit(0);

// Initiaise FB Connection
$fb = new \Facebook\Facebook([
  'app_id' => '231715815526403',
  'app_secret' => 'bc3966551bb38beb36a53cf54a2fd2ce',
  'default_graph_version' => 'v12.0',
  //'default_access_token' => '{access-token}', // optional
]);

// Is there an Access Token?
if (isset($_GET['code'])) {
  # Received Access Token from FB
  $helper = $fb->getRedirectLoginHelper();
  $accessToken = $helper->getAccessToken();
  $oAuth2Client = $fb->getOAuth2Client();
  $tokenMetadata = $oAuth2Client->debugToken($accessToken);
  echo '<h3>Metadata</h3>';

}

if (!$accessToken) {
  exit("Nope");
} else exit("Yes");

echo "<pre>";
print_r($accessToken->getValue());
exit("</pre>");

// $accessToken = "EAADSvorfkAMBAC3bcZACkiUVCbcqX24t1UZCPdl4rqViTiIai0WiHpET2CYp5ZB5ozw7Ff92ZCS2LiTkex8C4EZBLpr3ZBPvyI02yrVP2VJi6x6BN4kds5dIBC0rksgLSh9BodFTTyYef4ZCrbc5bWXzOZAKfjQNJjrpyADO6W7QN7WQJmALC7DQzVdVzqgAB7ycPYcbl0MVTvaoAuq9qZAyXyN9sXkp55caZBH5B3snA5PwZDZD";
// echo "<pre>";
// print_r($_SERVER);
// exit("</pre>");

if (!$accessToken) {
  // Get an Access Token
  $permissions = ['email']; // Optional permissions
  $url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS']) ? "https://" : "http://";
  $url .= $_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
  $loginUrl = $helper->getLoginUrl($url, $permissions);
  exit ('<a href="' . $loginUrl . '">Log in with Facebook!</a>');
} else {
  exit($accessToken);
} 


  try {
    // Get the \Facebook\GraphNodes\GraphUser object for the current user.
    // If you provided a 'default_access_token', the '{access-token}' is optional.
    $response = $fb->get('/me?fields=id,name,email', $access_token);
  } catch(\Facebook\Exceptions\FacebookResponseException $e) {
    // When Graph returns an error
    // echo 'Graph returned an error: ' . $e->getMessage();
    // exit;
  } catch(\Facebook\Exceptions\FacebookSDKException $e) {
    // When validation fails or other local issues
    // echo 'Facebook SDK returned an error: ' . $e->getMessage();
    // exit;
  }
  
  if (isset($response)) {
    $me = $response->getGraphUser();
  
    echo 'Logged in as ' . $me->getName() . ". E: " . $me->getEmail();
  
    Tools::Dump($me);
  } else {
    $helper = $fb->getRedirectLoginHelper();
    $permissions = ['name','email']; // Optional permissions
    $loginUrl = $helper->getLoginUrl($_SERVER['REQUEST_URI'], $permissions);
    echo '<a href="' . $loginUrl . '">Log in with Facebook!</a>';
  }

// $apiListing = \Venue::ApiListing();
// $leagueID = 126;
// $league = new League($leagueID);
// $apiLeague = $league->ApiData();
// Tools::Dump($apiLeague);



// $orphanBookings = Booking::Orphans();
// Tools::Dump($orphanBookings);


// $teamID = 963;
// $seasonID = 273;

// echo "WildCard: ";
// echo (TeamSeason::isWildcard(new Team($teamID), new Season($seasonID)) === true) ? "Yes" : "No";

// exit("Root: " . ROOT_FOLDER . "<br>Core: " . CORE_FOLDER."<br>Local: " . LOCAL_FOLDER);

// Tools::Dump(PurchaseTransactionItem::SagePurchaseImportFiles());
// exit(0);

// $pi_items = PurchaseTransactionItem::SagePurchaseImportData();
// $import_rows = PurchaseTransactionItem::SagePurchaseImportCreate($pi_items);
// Tools::Dump($import_rows);

exit(0);

// $seasonID = 234;
// $data['season'] = new Season($seasonID);
// $data['leaderboard'] = Fixture::LiveLeaderBoard ();

// if ($data['leaderboard']) {
//     echo <<<HTML
//     <table>
//         <thead>
//             <tr>
//                 <th>Player</th>
//                 <th>Team</th>
//                 <th>Total</th>
//             </tr>
//         </thead>
//         <tbody>
//     HTML;
//     foreach ($data['leaderboard'] as $k => $v) {
//         list($teamID, $name) = explode(":",$k);
//         $team = new Team($teamID);
//         echo <<<HTML
//             <tr>
//                 <td>$name</td>
//                 <td>$team</td>
//                 <td>$v</td>
//             </tr>
//         HTML;
//     }
//     echo <<<HTML
//         </tbody>
//     </table>
//     HTML;
// }
// Tools::Dump($data['leaderboard']);

// System::BuildLiveLeaguesApi();

// $piID = "pi_1IyX2ALOCeRFt5luablt9Fuq";
// $pi = Stripe::getPaymentIntent($piID);
// echo "Payment Intent $piID " . date('H:ia d/m/Y',$pi->created) ." status: {$pi->status}<br>";
// Tools::Dump($pi);

// StripePayment::SynchroniseAll();
// Tools::Dump(StripePayment::BillingStage3(true));
// StripePayment::BillingStage2(true);
// StripePayment::BillingStage3(true);
// Tools::Dump(StripePayment::BillingStage3());
// echo StripePayment::BillingStage4();
// echo StripePayment::BillingStage5();

// echo file_get_contents("https://api.leagues4you.co.uk/toggleWild/4879");


// $seasonID = 212;
// $season = new Season($seasonID);
// $bookings = Booking::SeasonBookings($season);
// Tools::Dump($bookings);
// $result = $season->runSchedule();
// foreach ($result['log'] as $l) echo "$l<br>";
exit(0);

// $leagueID = 90;
// $league = new League($leagueID);
// Tools::Dump($league->isOpen());

// Trigger::Set("System::BuildLiveLeaguesApi");

// System::BuildLiveLeaguesApi();

// exit(0);

// $leagueID = 91;
// $league = new League($leagueID);
// $liveSeason = Season::Live($league);

// $nextSeason = Season::Next($league);
// Tools::Dump($league);
// Tools::Dump($liveSeason);
// exit(Tools::Dump($nextSeason));

// $teamID = 302;
// $team = new Team($teamID);
// $currentSeasons = TeamSeason::Current($team);
// echo "$team<br>";
// foreach ($currentSeasons as $currentSeason) {
//     $season = new Season($currentSeason->seasonID);
//     echo "League: " . new League($season->leagueID) ."<br>";
//     echo "Season: $season<br>";
//     #echo "Next: " . new Season($currentSeason->nextID)."<br>";
// }
// exit(Tools::Dump($currentSeason));
// $teams = Team::Listing();
// $counter = 0;
// foreach ($teams as $team) {
//     if ($team->deleted) continue;
//     $teamSeasons = TeamSeason::Team($team);
//     $success = false;
//     if ($teamSeasons) {
//         foreach ($teamSeasons as $teamSeason) {
//             if ($teamSeason->seasonID == $team->seasonID) $success = true;
//         }
//     }
//     if ($success !== true) {
//         $counter ++;
//         $season = ($team->seasonID) ? new Season($team->seasonID) : null;
//         $status = ($season->statusID) ? new SeasonStatus($season->statusID) : "Pending";
//         TeamSeason::Add($team->id,$team->seasonID,$team->divisionID,$team->captainID,$team->treasurerID);
//         if ($season->statusID == 3) continue;
//         echo "<b>{$team->id} $team</b>: League: ({$team->leagueID}) ".new League($team->leagueID)." Season: ({$team->seasonID}) $season (Status $status). Division: {$team->divisionID}<br>";
//     } 
// }
// echo "Total Missing: $counter";
// exit(0);

// $leagues = League::Listing();
// foreach ($leagues as $league) $league->TransferMe();
// exit(0);

// $liveSeasons = Season::Open();
// foreach ($liveSeasons as $liveSeason) {
//     echo "$liveSeason " . $liveSeason->getLeagueName() . "<br>";
//     $teams = Team::forSeason ($liveSeason);
//     if (!$teams) continue;
//     foreach ($teams as $team) {
//         if (!$team->divisionID) $team->divisionID = Division::Default($liveSeason);
//         $wildcard = (!$team->captainID) ? 1 : null; 
//         echo TeamSeason::Add ($team->id, $liveSeason->id, $team->divisionID, $team->captainID, $team->treasurerID,1, $wildcard);
//         echo " : $team<br>";
//     }
// } 
// exit(0);



// $seasonID = 75; $season = new Season($seasonID);

// $teams = Team::forSeason ($season);
// foreach ($teams as $team) {
//     echo TeamSeason::Add ($team->id, $season->id, $team->divisionID, $team->captainID, $team->treasurerID,1);
//     echo " : $team <br>";
// } 

// $teams2 = TeamSeason::Season($season);
// foreach ($teams2 as $t) echo new Team($t['teamID'])."<br>";
// Tools::Dump($teams); 