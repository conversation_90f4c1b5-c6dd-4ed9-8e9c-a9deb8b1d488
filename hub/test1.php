<?php

define ("ROOT_FOLDER", strrev(strstr(strrev(dirname(__FILE__)),DIRECTORY_SEPARATOR)));
define ("CORE_FOLDER", ROOT_FOLDER ."core" . DIRECTORY_SEPARATOR);
define ("LOCAL_FOLDER", __DIR__ . DIRECTORY_SEPARATOR);

include (__DIR__."/app/Init.php");

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// $user = new User(4984);
// Tools::Dump($user->Profile());exit(0);

exit("Nothing to do");

// $team = new Team(2091);
// $rlt = StripePayment::BillStage2($team);
// Tools::Dump($rlt);
// exit(0);

// $session = new TasterSession(139);
// $bookings = C2C_Booking::Bookers($session);
// foreach ($bookings as $booking) {
//   if ($booking->id == 6972) continue;
//   $booking->SendConfirmation(false,true);
// } 
// echo count($bookings)."<br>";

// $tasterBooking = new C2C_Booking(6972);
// $tasterBooking->SendConfirmation(false,true);
// exit(Tools::Dump($tasterBooking));

// $team = new Team(2058);
// $rlt = StripePayment::BillStage2($team);
// exit(Tools::Dump($bookings));

exit("Nothing to do");

# Create a requires_action stripe payment intent
// $cardPayment = new CardPayment(false);
// $stripeCustomerIDs = $cardPayment->Customer_Fetch("<EMAIL>");
// $stripeCustomerID = $stripeCustomerIDs['success'][0];
// $paymentMethod = $cardPayment->PaymentMethod_Fetch("pm_card_authenticationRequired");
// $paymentIntent = $cardPayment->PaymentIntent_Create ($stripeCustomerID->id, $paymentMethod->id, 19.89, "Test Payment");
// $paymentIntent = $cardPayment->PaymentIntent_Confirm($paymentIntent->id);
// exit("We're done here");

# Bill a Team
// $team = new Team(2055);
// $rlt = StripePayment::BillStage2($team,true);
// $rlt = StripePayment::BillingStage3(true);
// exit(Tools::Dump($rlt));


// $team = new Team(904);
// $user = User::fromEmail("<EMAIL>");
// $user->CaptainInvitation($team);
// exit("Done");

# Switch Team Captain
// $team = new Team(904);
// $rlt = TeamFollower::InviteCaptain($team,"<EMAIL>");
// $user = User::fromEmail("<EMAIL>");
// $rlt = $user->CaptainInvitation($team);
// exit(Tools::Dump($rlt));


# Invite Treasurer
/*
$captain = User::EmailLookup("<EMAIL>");#exit(Tools::Dump($user));
$team = new Team(1280);
$treasurerEmail = "<EMAIL>";
$rlt = TeamFollower::InviteTreasurer($treasurerEmail,$team,$captain);
exit(Tools::Dump($rlt));
*/

// $sql = "SELECT venues.* FROM seasons LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id LEFT JOIN venues ON seasons.venueID = venues.id WHERE seasons.deleted IS NULL AND venues.deleted IS NULL AND seasonStatus.active = 1 AND seasonStatus.live IS NULL";
// $leagues = League::SearchRegisterable("sutton");
// Tools::Dump($leagues);
// foreach ($venues as $venue) echo $venue."<br>";

// $divisionID = 622;
// $division = new Division($divisionID);
// $teams = Team::byDivision($division->id,false);
// Tools::Dump($teams);
// $team = new Team(1308);
// $newTreasurerTeamFollowerID = 1329;
// $resignations = TeamFollower::TreasurerResign($team,$newTreasurerTeamFollowerID);
// Tools::Dump($resignations);
// $user = new User(5142);
// Tools::Dump($user->Profile());

// $team = new Team(433);
// $team = new Team(847);
// $user = new User(341);
// Tools::Dump(TeamFollower::Follow($team,$user));
// Tools::Dump(TeamFollower::UnFollow($team,$user));
exit(0);

/*
// $pendingTeamSeasons = TeamSeason::MigrateToV2_Next();
$pendingTeamSeasons = TeamSeason::MigrateToV2();
foreach ($pendingTeamSeasons as $teamSeason) {
  $teamSeason->getTeam();
  $teamSeason->getLeague();
  $teamSeason->getSeason();
  $action = ($teamSeason->team->lockerRoomVersion == 2) ? "Skipping" : "Migrating";
  echo "$action {$teamSeason->team} ({$teamSeason->team->id}) from {$teamSeason->league} {$teamSeason->season}<br>";
  $rlt = TeamFollower::Migratev2($teamSeason);
  Tools::Dump($rlt);
  // break;
}
exit(0);
*/

// $team = new Team(1976);
// $canReenter = $team->CanReenter();

// Tools::Dump($canReenter);
// $teamSeason = new TeamSeason(8855);
// $teamID = 1976; $team = new Team($teamID);
// $seasonID = 529; $season = new Season($seasonID);
// $teamSeason = TeamSeason::TeamSeason($team, $season);
// $email = $teamSeason->ConfirmationEmail();
// Tools::Dump($teamSeason);
// TeamSeason::Add ($teamID, Int $seasonID, Int $divisionID, Int $captainID = null, Int $treasurerID = null, Int $statusID = 1, Int $wildcard = null)

# Fetch all current TeamSeasons where team is not v2
// $teamSeasons = TeamSeason::MigrateToV2();
// echo "Total " . count($teamSeasons)."<br>";
// foreach ($teamSeasons as $teamSeason) {
//   echo $teamSeason->getTeam() . " : " . $teamSeason->getLeague() . " : " . $teamSeason->getSeason() . "<br>";
  // Tools::Dump(TeamFollower::Migratev2($teamSeason));
  // Tools::Dump($teamSeason);
  // break;
// }
// Tools::Dump($teamSeasons);
exit(0);

// if (isset($_GET['teamSeasonID']) && $_GET['teamSeasonID']) exit(Tools::Dump(TeamFollower::Migratev2(new TeamSeason($_GET['teamSeasonID']))));

// $userID = 422;
// $user = new User($userID);
// $teams = Team::Query("SELECT * FROM teams WHERE captainID = {$user->id}");
// foreach ($teams as $team) {
//   if ($team->lockerRoomVersion == 2) continue;
  
//   echo $team . " : {$team->lockerRoomVersion}<br>";
// }
// Tools::Dump($teams);

// $teamID = 469;
// $team = new Team($teamID);
// Tools::Dump($team->getTreasurer());
// exit(0);

// exit(Tools::Dump(StripePayment::BillingStage2_v2(true)));
// $team = new Team(469);
// exit(Tools::Dump(StripePayment::BillStage2($team)));


// $userID = 1;
// $user = new User($userID);
// $apiData = $user->Profile();
// Tools::Dump($apiData);
// exit(0);

// $g = StripePayment::CancelAllWithStatus('requires_action'); Tools::Dump($g); exit(0);
// $g = StripePayment::CancelAllWithStatus('requires_payment_method'); Tools::Dump($g); exit(0);

// $stripe = new Stripe_v2(true);
// $pi = $stripe->fetchPaymentIntent("pi_3L0jXWLOCeRFt5lu0TkGzFb3");
// Tools::Dump($pi);
// exit(0);

// $a = Team::DebtorAlerts();
// $b = StripePayment::BillingStage1(true); Tools::Dump($b); exit(0);
// $c = StripePayment::BillingStage2(true); Tools::Dump($c); exit(0);
// $d = StripePayment::BillingStage3(true); Tools::Dump($d); exit(0);
// $e = Venue::CalculateDistances();
// $e = System::BuildLiveLeaguesApi();
// $e = TasterSession::CheckStatuses();
// $f = Trigger::Clear ();
// $g = StripePayment::CancelAllWithStatus('requires_payment_method'); Tools::Dump($g); exit(0);
// Tools::Dump($a);
// Tools::Dump($b);
// Tools::Dump($c);
// Tools::Dump($d);
// Tools::Dump($e);
// Tools::Dump($f);
// Tools::Dump($g);
exit(0);
// Tools::Dump(TeamFollower::PaymentOption(new Team(847))); exit(0);

// $rlt = StripePayment::ConfirmAll();
// Tools::Dump($rlt);
// exit(0);

// $team = new Team(588);
// $billables = Fixture::TeamBillable($team);
// $balance = Finance::Balance ($team);
// if (!$balance) exit("Nothing to Bill");
// $pending = StripePayment::TeamPendingTotal($team);
// if ((($billAmount = $balance - $pending)) <= 0) exit("A pending Payment already covers Balance");
// $stripe = new Stripe_v2(true);
// $team->getTeamManagers();
// $stripeCustomerID = $stripe->getStripeCustomer($team->treasurer);
// $paymentSource = $team->PaymentSource();
// $paymentIntent = Stripe_v2::BillTeam($team,$billAmount);
// Tools::Dump($billables);
// Tools::Dump($balance);
// Tools::Dump($pending);
// Tools::Dump($team->treasurer);
// Tools::Dump("Stripe Customer ID: $stripeCustomerID");
// Tools::Dump($paymentSource);
// Tools::Dump($paymentIntent);
exit(0);
// StripePayment::CancelAllWithStatus('requires_payment_method');

// $rlt = StripePayment::BillingStage3 (true);
// Tools::Dump($rlt);
// $team = new Team(847);
// $followers = TeamFollower::Current($team);
// Tools::Dump($followers);

// $team = new Team(847);Tools::Dump($team); exit(0);
// $teamSeasons = [8406,8407,8405,8051];
// foreach ($teamSeasons as $teamSeasonID) {
//   $teamSeason = new TeamSeason($teamSeasonID);
//   echo "Team {$teamSeason->teamID}<br>";
//   Tools::Dump(($balances=$teamSeason->FullBalancePayment()));
//   echo "Total: " . array_sum($balances)."<br>";
  // foreach ($remaining as $r) echo $r."<br>";
  // Tools::Dump($remaining);
exit(0);

// $teamID = 456;
// $team = new Team($teamID);
// Tools::Dump(TeamFollower::Current($team));
// $team->getTeamManagers();
// Tools::Dump($team);

// $teamSeason = new TeamSeason(8406);
// $rlt = TeamFollower::Migratev2($teamSeason);
// Tools::Dump($rlt);
// $teamSeason = new TeamSeason(8407);
// $rlt = TeamFollower::Migratev2($teamSeason);
// Tools::Dump($rlt);
// $teamSeason = new TeamSeason(8405);
// $rlt = TeamFollower::Migratev2($teamSeason);
// Tools::Dump($rlt);
// $teamSeason = new TeamSeason(8051);
// $rlt = TeamFollower::Migratev2($teamSeason);
// Tools::Dump($rlt);
// $rlt = $teamSeason->ConfirmationEmail($team);
// $teamSeason = new TeamSeason(7567);
// $rlt = TeamFollower::Migratev2($teamSeason);
// Tools::Dump($rlt);
// exit(0);
exit(0);

// $userID = 2;
// $user = new User($userID);
// $teamID = 1894;
// $team = new Team($teamID);
// $current = TeamFollower::Current($team,$user);
// Tools::Dump($current);


// Tools::Dump($user->Profile());


// $teamID = 469;
// $team = new Team($teamID);
// $statement = Finance::Statement($teamID,true);
// Tools::Dump($statement);

// $leagueID = 252;
// $league = new League($leagueID);
// $next = Season::Next($league);
// Tools::Dump($next);

// $teamID = 1869;
// $team = new Team($teamID);
// $team->getLeague();
// $next = Season::Next ($team->league);
// echo (TeamSeason::IsIn($team,$next)) ? null : (int)$next->id;
// Tools::Dump(TeamSeason::IsIn($team,$next));
// Tools::Dump($team);
// Tools::Dump($next);
// $team->CanReenter();


// $userID = 341;
// $user = new User($userID);
// Tools::Dump($user);
// $invites = TeamFollower::TreasurerInvites($user);
// Tools::Dump($invites);
// foreach ($invites as $i) Tools::Dump($i->TreasurerInvite_ApiData());
exit(0);

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://js.stripe.com/v3/"></script>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
  <title>Document</title>
</head>
<body>
  <div class="container">
    <?php
        $flushedPaymentIntents = StripePayment::FlushExpired ();
        if (!$flushedPaymentIntents) {
            echo "Nothing to do";
        } else {
            foreach ($flushedPaymentIntents as $flushedPaymentIntent) echo $flushedPaymentIntent . "<br>";
        }
    ?>
  </div>
</body>
</html>
