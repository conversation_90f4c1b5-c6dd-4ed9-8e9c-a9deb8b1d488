<?php
# Development Error Setting
error_reporting(E_ALL); 
ini_set('display_errors', 1); 
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1); 
# Production Error Setting
// error_reporting(E_ALL);
// ini_set('display_errors', 0);
// ini_set('log_errors', 1);
// ini_set('display_startup_errors', 0); 

include("app/config.php");
global $config;

include (__DIR__."/app/Init.php");
global $config;
global $url;
if (
    !\User::Authenticated() && 
    isset($_COOKIE['jwt']) &&
    $_COOKIE['jwt']) {
        if (($userID=\Jwt::Validate($_COOKIE['jwt'])) && is_int($userID) &&  ($user=new \User($userID))) {
            $user->Login(false);
            // Only re-direct if no URL defined?
            if (!$url) {
                header("Location: /User");
                exit(0);
            }
        } else {
            unset($_COOKIE['jwt']);
            Jwt::CookieRemove();
        }
}
/*
if (substr($_SERVER['REQUEST_URI'],0,12)=="/User/League" || substr($_SERVER['REQUEST_URI'],0,12)=="/Home/Login") { 

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= "{$config['system']['name']} | {$config['system']['version']}" ?></title>
    <base href="<?= $config['system']['url'] ?>">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.14.0/css/all.min.css">
    <link rel="stylesheet" href="css/hub.leagues4you.css">
</head>
<body><?php
} ?>
<?php



if (substr($_SERVER['REQUEST_URI'],0,12)=="/User/League" || substr($_SERVER['REQUEST_URI'],0,12)=="/Home/Login") {
    
    
    if (\User::Authenticated()) {
        include("./app/Views/menus/user.menu.php");
    } else include("./app/Views/menus/guest.menu.php");
    ?><br><?php
}
*/
    Route\Display($url);
/*
    if (substr($_SERVER['REQUEST_URI'],0,12)=="/User/League" || substr($_SERVER['REQUEST_URI'],0,12)=="/Home/Login") {
?>
    <script src="/js/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js" integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous"></script>
    </body>
    </html><?php
} ?>
*/