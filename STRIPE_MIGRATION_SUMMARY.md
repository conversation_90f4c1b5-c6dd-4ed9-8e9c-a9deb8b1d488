# Stripe Migration: Payment Request Button to Express Checkout Element

## Overview

This document summarizes the migration from Stripe's deprecated Payment Request Button Element to the new Express Checkout Element.

## What Was Changed

### 1. Client-Side Changes

#### Elements Instance Creation

**Before:**

```javascript
var elements = stripe.elements();
```

**After:**

```javascript
const options = {
  mode: "payment",
  amount: parseInt(formatPrice(price * 100)),
  currency: "gbp",
};
const elements = stripe.elements(options);
```

#### Payment Element Replacement

**Before:**

```javascript
var paymentRequest = stripe.paymentRequest({
  country: "GB",
  currency: "gbp",
  total: {
    label: "Total",
    amount: parseInt(formatPrice(price * 100)),
  },
  requestPayerName: true,
  requestPayerEmail: true,
});

const prButton = elements.create("paymentRequestButton", {
  paymentRequest,
});
```

**After:**

```javascript
const expressCheckoutElement = elements.create("expressCheckout", {
  emailRequired: true,
});
```

#### HTML Element ID

**Before:**

```html
<div id="payment-request-button">
  <!-- A Stripe Element will be inserted here. -->
</div>
```

**After:**

```html
<div id="express-checkout-element">
  <!-- A Stripe Element will be inserted here. -->
</div>
```

#### Payment Confirmation

**Before:**

```javascript
paymentRequest.on("paymentmethod", async (ev) => {
  const { paymentIntent, error: confirmError } =
    await stripe.confirmCardPayment(
      clientSecret,
      {
        payment_method: ev.paymentMethod.id,
      },
      {
        handleActions: false,
      }
    );
  // ... complex confirmation logic
});
```

**After:**

```javascript
expressCheckoutElement.on("confirm", async (event) => {
  const { error } = await stripe.confirmPayment({
    elements,
    clientSecret,
    confirmParams: {
      return_url: window.location.href,
    },
    redirect: "if_required",
  });

  if (error) {
    alert("Payment failed: " + error.message);
  } else {
    checkoutSuccess();
  }
});
```

### 2. Server-Side Changes

#### PaymentIntent Creation

**Before:**

```php
$stripeData = [
    'amount' => ($amount*100),
    'currency' => 'gbp',
    'customer' => $stripeCustomerID,
    'description' => $description,
    'capture_method' => 'manual',
    'payment_method' => $paymentMethodID
];
```

**After:**

```php
$stripeData = [
    'amount' => ($amount*100),
    'currency' => 'gbp',
    'customer' => $stripeCustomerID,
    'description' => $description,
    'capture_method' => 'manual',
    'payment_method' => $paymentMethodID,
    // Enable automatic payment methods for Express Checkout Element
    'automatic_payment_methods' => [
        'enabled' => true,
    ],
];
```

## Files Updated

### Primary Files (Current Workspace)

- `bloom/wp-content/themes/salient-child/page-checkout.php`
- `bloom/wp-content/themes/salient-child/page-checkout-tournament.php`
- `core/classes/Stripe.php`

### Other Environment Files (Need Separate Updates)

- `1ftp/prod/bloom/wp-content/themes/salient-child/page-checkout.php`
- `1ftp/bloomnetball/wp-content/themes/salient-child/page-checkout.php`
- `1ftp/prod/html/bloom/wp-content/themes/salient-child/page-checkout.php`
- `1ftp/staging/www/wp-content/themes/leagues4you1/page-checkout.php`
- And others in staging/prod environments

## Benefits of Migration

1. **More Payment Methods**: Express Checkout Element supports PayPal, Apple Pay, Google Pay, and other wallet payments
2. **Better UX**: Cleaner, more modern payment interface
3. **Future-Proof**: Payment Request Button Element is deprecated
4. **Simplified Code**: Cleaner confirmation flow with `stripe.confirmPayment()`
5. **Automatic Payment Method Detection**: Stripe automatically shows relevant payment methods based on currency and customer location

## What to Enable in Stripe Dashboard

1. Go to [Stripe Dashboard > Payment Methods](https://dashboard.stripe.com/settings/payment_methods)
2. Enable the payment methods you want to support:
   - Cards (enabled by default)
   - Apple Pay
   - Google Pay
   - PayPal
   - Other relevant payment methods for your business

## Testing

1. Test the checkout flow with different payment methods
2. Verify that the Express Checkout Element appears correctly
3. Test payment confirmation flow
4. Verify that the `ready` event fires and shows/hides the element appropriately
5. Test with different currencies and amounts

## Notes

- The legacy card payment flow is kept as a fallback
- The `redirect: 'if_required'` option ensures redirects only happen for payment methods that require it
- The `emailRequired: true` option ensures customer email is collected
- The `ready` event can be used for custom animations when payment buttons appear

## Next Steps

1. Test the migration in a staging environment
2. Update other environment files (staging, prod, etc.)
3. Monitor payment success rates after deployment
4. Consider enabling additional payment methods based on customer needs
5. Set up webhook handling for `payment_intent.succeeded` events if not already done







