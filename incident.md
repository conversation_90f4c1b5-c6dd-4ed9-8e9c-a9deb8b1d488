# Incident Report System Documentation

## 📋 Overview

The Incident Report System is a comprehensive web application for managing venue incidents in sports leagues. It provides functionality for creating, updating, viewing, and managing incident reports with file attachments and user role-based access control.

## 🏗️ System Architecture

### Core Components

1. **Models**

   - `IncidentReport` - Main incident data model
   - `Upload` - File management model
   - `User` - User authentication and roles
   - `Venue` - Venue information
   - `League` - League management

2. **Controllers**

   - `CoordinatorController` - Main incident management controller
   - File operations and CRUD operations

3. **Views**
   - `IncidentReports.Coordinator.php` - List view with pagination
   - `IncidentReportUpdate.Coordinator.php` - Edit/Update form
   - `IncidentReportDetails.Coordinator.php` - Detail view

## 🔄 Data Flow

### 1. Incident Creation Flow

```
User Input → Form Validation → IncidentReport::save() → Database → Redirect
```

### 2. File Upload Flow

```
File Upload → Validation → Physical Storage → Database Record → UI Update
```

### 3. Incident Listing Flow

```
Request → Filters → IncidentReport::getFilteredIncidents() → Pagination → View
```

## 🗄️ Database Schema

### incident_report Table

```sql
- id (Primary Key)
- name (Injured person name)
- dob (Date of birth)
- contact_number
- date_of_incident
- time_of_incident
- venueId (Foreign Key)
- leagueId (Foreign Key)
- description
- witness (Boolean)
- details_location
- root_cause
- nature_of_injury
- treatment
- match_official_name
- official_statement_file (File path)
- witness_name
- witness_statement_files (Serialized array)
- additional_info
- mitigating_circumstances
- status (open/pending/closed)
- your_name (Reporter name)
- created_at
- updated_at
- severity (Green/Amber/Red)
- injured_person_type (Employee/Public)
- taken_to_hospital (Yes/No)
- injury_details
- location
- welfare_check_completed
- no_consent (Boolean)
- relationship_to_player
- under_18
- outstanding_info
```

### uploads Table

```sql
- id (Primary Key)
- type ('incident')
- path (File URL)
- content_id (incident_report.id)
```

## 👥 User Roles & Permissions

### Manager/Admin Users

- View all incidents across all venues
- Filter by date, venue, status, severity
- Change incident status (open/pending/closed)
- Edit severity levels
- Access to admin-only fields

### Coordinator Users

- View incidents only from their assigned leagues
- Edit incident reports (limited fields)
- Cannot change severity (preserved from original)
- Status automatically set to 'pending' when edited

## 📄 File Management System

### File Types Supported

1. **Official Statement Files**

   - Stored in: `incident_report.official_statement_file`
   - Location: `/public/images/`
   - Single file per incident

2. **Witness Statement Files**

   - Stored in: `incident_report.witness_statement_files` (serialized array)
   - Location: `/public/images/`
   - Multiple files per incident

3. **Additional Files**
   - Stored in: `uploads` table
   - Location: `/public/uploads/`
   - Multiple files per incident

### File Upload Validation

- **Allowed Types**: PDF, JPG, JPEG, PNG
- **Max Size**: 5MB per file
- **Security**: MIME type validation
- **Naming**: Timestamp prefix to avoid conflicts

## 🔍 Pagination System

### Implementation

```php
// Backend (IncidentReport::getFilteredIncidents)
$itemsPerPage = 25;
$page = isset($filters['page']) ? (int)$filters['page'] : 1;
$offset = ($page - 1) * $itemsPerPage;
$sql .= " ORDER BY id DESC LIMIT $itemsPerPage OFFSET $offset";
```

### Frontend Features

- Smart page range display (current ± 2 pages)
- First/Last page links with ellipsis
- Filter preservation across pages
- Page info display
- Responsive design

## 🗑️ File Deletion System

### Unified Deletion Endpoint

```
POST /Coordinator/deleteFile
```

### Parameters by File Type

| File Type          | Parameters                                                 | Action                       |
| ------------------ | ---------------------------------------------------------- | ---------------------------- |
| Additional Files   | `file_id`, `incident_id`                                   | Delete from uploads table    |
| Official Statement | `file_type=official_statement`, `incident_id`              | Clear incident_report field  |
| Witness Statements | `file_type=witness_statement`, `file_index`, `incident_id` | Remove from serialized array |

### Deletion Process

1. **Parameter Detection** - Identify file type from request
2. **File Validation** - Check file exists and user has permission
3. **Physical Deletion** - Remove file from server filesystem
4. **Database Update** - Clear/update database records
5. **Response** - JSON success/error response
6. **UI Update** - Remove file row from interface

## 🔐 Security Features

### Access Control

- JWT authentication required
- Role-based permissions (Manager vs Coordinator)
- League-based data isolation for coordinators
- Incident ownership validation

### File Security

- MIME type validation
- File size limits
- Secure file paths
- Upload directory permissions

### Input Validation

- Server-side form validation
- XSS prevention
- SQL injection protection via prepared statements

## 📊 Status Management

### Status Flow

```
open (Coordinator Review) → pending (Final Review Required) → closed
```

### Status Rules

- **Coordinators**: Can only set to 'pending' when editing
- **Managers**: Can set any status
- **Closed incidents**: Cannot be edited (except by managers)
- **Status preservation**: Original severity maintained for coordinators

## 🎨 Frontend Features

### Responsive Design

- Bootstrap 5 framework
- Mobile-friendly interface
- Drag & drop file uploads
- Interactive pagination
- Toast notifications

### User Experience

- Real-time file preview
- Confirmation dialogs
- Loading states
- Error handling
- Success feedback

## 🔧 Technical Implementation

### Key Methods

#### IncidentReport Model

```php
getFilteredIncidents($filters) // Pagination + filtering
getIncidentById($id, $status, $user) // Single incident retrieval
save() // Create/update with timestamps
```

#### Upload Model

```php
saveIncidentFiles($files, $incidentId) // Bulk file upload
getIncidentFiles($incidentId) // Retrieve incident files
deleteFile($fileId) // Remove file and database record
```

#### CoordinatorController

```php
IncidentReports() // List with pagination
IncidentReportUpdate() // Edit form with file management
deleteFile() // Unified file deletion
```

### Error Handling

- Try-catch blocks for file operations
- JSON error responses
- User-friendly error messages
- Logging for debugging

## 📈 Performance Considerations

### Database Optimization

- Indexed foreign keys (venueId, leagueId)
- Efficient pagination queries
- Prepared statements for security

### File Management

- Organized directory structure
- Unique filename generation
- Cleanup of orphaned files

### Frontend Optimization

- Lazy loading for large lists
- AJAX for file operations
- Minimal DOM manipulation

## 🚀 Deployment Notes

### File Permissions

- `/public/images/` - 775 permissions
- `/public/uploads/` - 775 permissions
- Web server write access required

### Configuration

- Domain prefix configuration for file URLs
- Upload limits in PHP configuration
- Database connection settings

### Monitoring

- File storage usage
- Database performance
- Error logs for file operations

## 🔄 Future Enhancements

### Potential Improvements

1. **File Versioning** - Track file changes
2. **Bulk Operations** - Mass status updates
3. **Advanced Search** - Full-text search
4. **Email Notifications** - Status change alerts
5. **Audit Trail** - Change history tracking
6. **API Endpoints** - REST API for mobile apps
7. **File Preview** - In-browser file viewing
8. **Export Features** - PDF/Excel export

### Scalability Considerations

- File storage migration to cloud (S3, etc.)
- Database sharding for large datasets
- Caching layer for frequently accessed data
- CDN for file delivery

## 📝 Code Examples

### Creating a New Incident Report

```php
$incident = new IncidentReport();
$incident->Load($_POST);
$incident->venueId = $venueId;
$incident->leagueId = $leagueId;
$incident->status = 'open';
$incident->save();
```

### Filtering Incidents with Pagination

```php
$filters = [
    'user' => $currentUser,
    'startDate' => '2024-01-01',
    'endDate' => '2024-12-31',
    'status' => 'open',
    'page' => 1
];
$result = IncidentReport::getFilteredIncidents($filters);
```

### File Upload Process

```php
if (isset($_FILES['official_statement_file']) && $_FILES['official_statement_file']['error'] == UPLOAD_ERR_OK) {
    $uploadDir = getcwd() . '/public/images/';
    $fileName = date("h:i:sa") . '-' . basename($_FILES['official_statement_file']['name']);
    $uploadFile = $uploadDir . $fileName;

    if (move_uploaded_file($_FILES['official_statement_file']['tmp_name'], $uploadFile)) {
        $incident->official_statement_file = getDomainPrefix() . 'hub.leagues4you.co.uk/public/images/' . $fileName;
    }
}
```

### File Deletion Process

```php
// For uploads table files
if (isset($_POST['file_id'])) {
    $result = \Upload::deleteFile($_POST['file_id']);
}

// For incident report embedded files
if (isset($_POST['file_type'])) {
    $incident = new IncidentReport($_POST['incident_id']);
    if ($_POST['file_type'] === 'official_statement') {
        $incident->official_statement_file = null;
        $incident->save();
    }
}
```

## 🔍 Troubleshooting Guide

### Common Issues

#### File Upload Failures

**Problem**: Files not uploading
**Solutions**:

- Check PHP upload_max_filesize setting
- Verify directory permissions (775)
- Ensure disk space available
- Check file type validation

#### Pagination Not Working

**Problem**: All records showing on one page
**Solutions**:

- Verify $total count is correct
- Check LIMIT/OFFSET SQL syntax
- Ensure page parameter is being passed
- Debug getFilteredIncidents() method

#### File Deletion Errors

**Problem**: Files not being deleted
**Solutions**:

- Check file path conversion logic
- Verify file exists before deletion
- Ensure proper permissions
- Check database transaction completion

#### Access Control Issues

**Problem**: Users seeing wrong incidents
**Solutions**:

- Verify JWT authentication
- Check user role assignment
- Validate league coordinator mapping
- Debug SQL WHERE clauses

### Debug Tips

1. **Enable Error Logging**: Check PHP error logs
2. **Database Queries**: Log SQL queries for debugging
3. **File Paths**: Verify file path generation
4. **User Permissions**: Check user role and league assignments
5. **AJAX Requests**: Use browser dev tools to inspect requests

## 📊 System Metrics

### Performance Benchmarks

- **Page Load Time**: < 2 seconds for incident list
- **File Upload**: < 5 seconds for 5MB files
- **Search/Filter**: < 1 second response time
- **Pagination**: < 500ms page navigation

### Storage Requirements

- **Database**: ~1KB per incident record
- **Files**: Variable (up to 5MB per file)
- **Estimated Growth**: 100 incidents/month = ~500MB/month

### User Capacity

- **Concurrent Users**: 50+ simultaneous users
- **Daily Active Users**: 200+ users
- **Peak Usage**: During match seasons

## 🔧 Maintenance Tasks

### Regular Maintenance

1. **Database Cleanup**: Remove old closed incidents (if policy allows)
2. **File Cleanup**: Remove orphaned files not linked to incidents
3. **Log Rotation**: Manage application and error logs
4. **Backup Verification**: Test backup and restore procedures

### Monthly Tasks

- Review storage usage and cleanup
- Update user permissions and roles
- Performance monitoring and optimization
- Security audit and updates

### Quarterly Tasks

- Database performance tuning
- File storage optimization
- User access review
- System capacity planning

## 📋 API Documentation

### Endpoints

#### GET /Coordinator/IncidentReports

**Purpose**: List incidents with pagination and filtering
**Parameters**:

- `page` (optional): Page number (default: 1)
- `start-date` (optional): Filter start date
- `end-date` (optional): Filter end date
- `venues` (optional): Venue ID filter
- `status` (optional): Status filter
- `severity[]` (optional): Severity filter array

**Response**: HTML page with incident list

#### POST /Coordinator/IncidentReportUpdate/{id}

**Purpose**: Update incident report
**Parameters**: Form data with incident fields
**Files**:

- `official_statement_file`
- `witness_statement[]`
- `incident_files[]`

**Response**: Redirect to incident details or error page

#### POST /Coordinator/deleteFile

**Purpose**: Delete incident files
**Parameters**:

- `incident_id` (required): Incident ID
- `file_id` (optional): For uploads table files
- `file_type` (optional): For incident report files
- `file_index` (optional): For witness statement files

**Response**: JSON success/error response

### Response Formats

#### Success Response

```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

#### Error Response

```json
{
  "success": false,
  "message": "Error description"
}
```

## 🎯 Best Practices

### Development Guidelines

1. **Follow MVC Pattern**: Keep logic in appropriate layers
2. **Use Prepared Statements**: Prevent SQL injection
3. **Validate Input**: Both client and server-side validation
4. **Handle Errors Gracefully**: User-friendly error messages
5. **Log Important Events**: For debugging and auditing

### Security Best Practices

1. **Authentication**: Always verify user authentication
2. **Authorization**: Check user permissions for each action
3. **File Validation**: Validate file types and sizes
4. **SQL Injection**: Use parameterized queries
5. **XSS Prevention**: Escape output data

### Performance Best Practices

1. **Database Indexing**: Index frequently queried columns
2. **Pagination**: Always paginate large datasets
3. **File Optimization**: Compress images when possible
4. **Caching**: Cache frequently accessed data
5. **Lazy Loading**: Load data only when needed

---

_Last Updated: 2024-01-15_
_Version: 1.0_
