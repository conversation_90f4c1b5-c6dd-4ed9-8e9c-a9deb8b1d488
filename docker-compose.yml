services:
  web:
    build: ./docker
    ports:
      - "80:80"
    volumes:
      - ./api:/var/www/html/api
      - ./hub:/var/www/html/hub
      - ./lockerroom:/var/www/html/lockerroom
      # - ./cdn:/var/www/html/cdn
      - ./core:/var/www/html/core
      - ./core:/var/www/core
    depends_on:
      - db

  db:
    image: mariadb
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: leagues4you
      MYSQL_USER: leagues4you
      MYSQL_PASSWORD: (S1mpl1f1c4t10n-Pr0gr4m!)
      MYSQL_ROOT_PASSWORD: leagues4you
    volumes:
      - db_data:/var/lib/mysql
      - ./data:/root

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    restart: unless-stopped
    platform: linux/amd64
    ports:
      - "8080:80"
    environment:
      PMA_HOST: db
      MYSQL_ROOT_PASSWORD: leagues4you
    volumes:
      - ./docker/phpmyadmin.ini:/usr/local/etc/php/conf.d/phpmyadmin-misc.ini

volumes:
  db_data:
