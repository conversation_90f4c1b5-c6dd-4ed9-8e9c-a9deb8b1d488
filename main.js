const client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
const appId =
  "007eJxTYChd5hKd8ubRzu2bPI9YntmTvdzexv79XaX3+VVZq89tY0hVYDC2NDYzMU0xSU5OTTVJsjS2MDNLNDEEslNSLY2TTZMYM/QzGgIZGXrV/ZgYGSAQxGdnSC+tSs3JzGVgAAAsTyFc";
const channel = "guzelim";
let localAudioTrack;

async function joinVoice() {
  const joinBtn = document.getElementById("join-btn");
  const leaveBtn = document.getElementById("leave-btn");
  const status = document.getElementById("voice-status");

  joinBtn.disabled = true;
  status.textContent = "Sohbete bağlanılıyor...";

  try {
    await client.join(appId, channel, null, null);
    localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();
    await client.publish([localAudioTrack]);

    status.textContent = "Sohbete katıldın.";
    leaveBtn.disabled = false;
    console.log("Sesli sohbete katıldın.");

    // 🎧 Karşı tarafın sesini dinle
    client.on("user-published", async (user, mediaType) => {
      await client.subscribe(user, mediaType);
      console.log("Yeni kullanıcı yayınladı:", user.uid);

      if (mediaType === "audio") {
        const remoteAudioTrack = user.audioTrack;
        remoteAudioTrack.play(); // Bu olmadan ses gelmez
        console.log("Karşı tarafın sesi oynatılıyor.");
      }
    });
  } catch (error) {
    console.error("Bağlantı hatası:", error);
    status.textContent = "Bağlantı hatası oluştu.";
    joinBtn.disabled = false;
  }
}

async function leaveVoice() {
  const joinBtn = document.getElementById("join-btn");
  const leaveBtn = document.getElementById("leave-btn");
  const status = document.getElementById("voice-status");

  try {
    if (localAudioTrack) {
      localAudioTrack.stop();
      localAudioTrack.close();
    }
    await client.leave();

    status.textContent = "Sohbetten ayrıldın.";
    joinBtn.disabled = false;
    leaveBtn.disabled = true;
    console.log("Sohbetten ayrıldın.");
  } catch (error) {
    console.error("Ayrılma hatası:", error);
    status.textContent = "Sohbetten ayrılırken bir hata oluştu.";
  }
}
