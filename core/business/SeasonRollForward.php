<?php

class SeasonRollForward {

    static function Run (Int $seasonID, $immediate = false) {
        /* 
            immediate indicates whether the current season continues or
            if current season is suspended and the new season becomes status = 1
        */
        $season = new Season($seasonID);
        $league = new League($season->leagueID);
        
        $messages[] = "Rolling forward Season ID $seasonID for $league";
        $newSeason = clone $season;
        // New Season starts, week after previous Season
        // Get Last BookingID from Old Season schedule
        $lastBooking = Booking::LastScheduledBooking($season);
        $newSeason->launchDate = date('Y-m-d',strtotime("+7 days",strtotime($lastBooking->startDate)));
        
        $newSeason->AutoName();
        $newSeason->status = $newSeason->locked = $newSeason->startBookingID = $newSeason->lockStamp = null;
        return [$newSeason,$new,$messages];
        $newSeason->Save();
        Season::SetNext($season->id, $newSeason->id);
        $messages[] = "New Season ID {$newSeason->id}";
        if ($immediate === true) {
            $messages[] = "Close current season and make new season live";
            $season->status = null;
            $newSeason->status = 1;
            // $season->Save();
            // $newSeason->Save();
        } else $messages[] = "Current season remains live";
        /* Duplicate Divisions */
        $divisions = Division::forSeason($season);
        
        if (!$divisions) {
            $messages[] = "No divisions to roll forward";
            return [$newSeason,$messages];
        } 
        // echo "Working with new Season ID {$newSeason->id}<br>";
        foreach ($divisions as $division) {
            $messages[] = "Rolling forward {$division} ID {$division->id}";
            $newDivision = new Division();
            $newDivision->seasonID = $newSeason->id;
            $newDivision->name = $division->name;
            
            $newDivision = new Division();
            $newDivision->seasonID = $newSeason->id;
            $newDivision->name = $division->name;
            $newDivision->Save();
            $messages[] = "New Division ID {$newDivision->id} created from Division ID {$division->id}";
            $newDivisions[] = $newDivision;
            if ($immediate === true) {
                $teams = Team::byDivision($division->id);
                if ($teams) {
                    $messages[] = "Re-assigning teams to new Season and Division";
                    foreach ($teams as $k => $team) {
                        $team->seasonID = $newSeason->id;
                        $team->divisionID = $newDivision->id;
                        // $team->Save();
                    }
                    $messages[] = "Updated $team to new season and division";
                    // $teams[$k]->seasonID = "new season ID";
                    // $team[$k]->divisionID = "new division ID";
                } else $messages[] = "$division has no teams to roll forward";
            }
        }
        // exit(0);
        return [$newSeason, $newDivisions, $messages];
        /* Duplicate Division Teams */

        /* Set Teams "accepted" to NULL */

        /**
         * Auto name (Current Season + Year) - Spring/Summer/Autumn/Winter - eg Winter 2021
         * Duration (Copied from current or league/sport default or 40
         * Rounds (Default 2?)
         * Fixture Charge (Default = Current or 27?)
         * Official Charge (Default from Current or 5?)
         * Start Booking = Blank
         * Status - Open for Registration
         * Invite teams
         */
        // $newSeason = new \Season();
        // return "Roll Forwrd Season ID {$season->id}";

    }
}