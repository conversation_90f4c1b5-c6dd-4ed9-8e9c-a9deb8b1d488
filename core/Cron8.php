<?php
// $configFile = "/var/www/html/core/config.php";
// include($configFile);
// $autoloadFile = "/var/www/html/core/vendor/autoload.php";
// include($autoloadFile);
// function ClassAutoloader($class) {
//     $file = "/var/www/html/core/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
// }
// spl_autoload_register("ClassAutoloader");
// Team::TreasurerChecks();
include(__DIR__ . "/init.php");
$start_time = microtime(true); // Record the start time

// Cron functions
// Team::DebtorAlerts();

// Stage 3
StripePayment::BillingStage3(true); // charge them

// Log with Sentry
$execution_time  = microtime(true) - $start_time; // Calculate the execution time
\Sentry\captureMessage('Cron8.php ran in ' . $execution_time . ' seconds');
