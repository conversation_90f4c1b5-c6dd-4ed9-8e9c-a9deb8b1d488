<?php

# Development Error Setting
// error_reporting(E_ALL); 
// ini_set('display_errors', 1); 
// ini_set('log_errors', 0);
// ini_set('display_startup_errors', 1); 
# Production Error Setting
// error_reporting(E_ALL);
// ini_set('display_errors', 0);
// ini_set('log_errors', 1);
// ini_set('display_startup_errors', 0); 

if (!defined("CORE_FOLDER")) define("CORE_FOLDER",__DIR__);

include(CORE_FOLDER . DIRECTORY_SEPARATOR . "config.php");

// include(dirname(__FILE__) . DIRECTORY_SEPARATOR . "config.php");
// global $config;
#( int $lifetime [, string $path [, string $domain [, bool $secure = FALSE [, bool $httponly = FALSE ]]]] ) 
/*
$cookieLifetime = 60*60*24; # Session length;
$pathOnDomain = "/"; # All paths
$cookieDomain = '.'.$config['system']['parentDomain'];
$cookieSecure = true; # https only?
$httpOnly = false; # Only send via HTTP (eg not JS) 
session_set_cookie_params($cookieLifetime, $pathOnDomain, $cookieDomain,$cookieSecure,$httpOnly);
if(!isset($_SESSION)) session_start();
*/

// if (file_exists(($vendorAutoload = $config['core']['path']."/vendor/autoload.php"))) include($vendorAutoload);
// define("APP_ROOT",dirname(__FILE__).DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."..".DIRECTORY_SEPARATOR."core");
// $includeFolder = __DIR__ ."/Core/";
// $include_files = scandir($includeFolder);
// foreach ($include_files as $include_file) {
//     if ($include_file != "." && $include_file != ".." && $include_file!= "Cron.php") include_once($includeFolder.$include_file);
// }
// $includeFolder = __DIR__ ."/Procedures/";
// $include_files = scandir($includeFolder);
// foreach ($include_files as $include_file) {
//     if ($include_file != "." && $include_file != "..") include_once($includeFolder.$include_file);
// }

# Vendor Autoloading
$vendorAutoloader = __DIR__ . DIRECTORY_SEPARATOR . "vendor" . DIRECTORY_SEPARATOR . "autoload.php";
if (file_exists($vendorAutoloader)) include($vendorAutoloader);
# Core Autoloading;
function CoreClassAutoloader ($class) {
    // $file = __DIR__ . DIRECTORY_SEPARATOR . "classes" . DIRECTORY_SEPARATOR . "$class.php";
    $class = str_replace(["\\"],[DIRECTORY_SEPARATOR],$class);
    $file = CORE_FOLDER . DIRECTORY_SEPARATOR . "classes" . DIRECTORY_SEPARATOR . "$class.php";
    if (file_exists($file))return (include_once ($file));
    // $file = __DIR__ . DIRECTORY_SEPARATOR . "Controllers" . DIRECTORY_SEPARATOR . "$class.php";
    // if (file_exists($file))return (include_once ($file));
    // exit("$class not found in ".__FILE__." as $file");
}
spl_autoload_register("CoreClassAutoloader");

include("versioning.php");

// function ClassAutoloader($class) {
//     global $config;
//     $class = str_replace(["\\"],[DIRECTORY_SEPARATOR],$class);
//     $file = $config['core']['path']."/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
//     $message[] = "Failed $file";
//     $file = $config['system']['path']."/app/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
//     $message[] = "Failed $file";
//     $file = $config['core']['path'] . DIRECTORY_SEPARATOR . "business" . DIRECTORY_SEPARATOR ."$class.php";
//     if (file_exists($file))return (include_once ($file));
//     $message[] = "Failed $file";
//     $file = $config['system']['path']."/app/Controllers/$class.php";
//     if (file_exists($file))return (include_once ($file));
//     $message[] = "Failed $file";
//     exit(implode("<br>",$message));
// }

new Session();
// $GLOBALS['post'] = $_POST;
// $GLOBALS['get'] = $_GET;
// $GLOBALS['config']['system']['name'] = "L4Y Hub";
// $GLOBALS['config']['dir']['app'] = __DIR__."/";
// $GLOBALS['config']['dir']['base'] = __DIR__."/../";
// Route\Set();