<!DOCTYPE HTML>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!--Fix to get Windows Phone 8 to render CSS3 and Media Queries-->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="stylesheet" href="https://use.typekit.net/nvs8mrr.css">
    <title>Email Template</title>

    <style type="text/css">
        body,
        td,
        p,
        ol,
        li,
        ul,
        table,
        td,
        tr {
            font-family: "neue-kabel";
            font-weight: 400;
            font-size: 16px;
        }


        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: "neue-haas-grotesk-display";
            font-weight: 400;
            font-size: 34px;
        }
    </style>
    <!-- Main styling #1 -->
    <style type="text/css">

        body {
            padding: 0;
            margin: 0;
        }



        .header,
        .footer {
            background-color: #00DF74;
            color: white;
            padding: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 36px;
        }

        .header p {
            font-size: 18px;
            margin-top: -10px;
        }

        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f9f9f9;
            text-align: left;
        }

        .footer-icons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .footer-icons img {
            width: 24px;
            height: 24px;
        }

        .footer p {
            font-size: 12px;
            margin: 5px 0 0;
        }

        .footer-logo {
            width: 7.5rem;
            /* 120px */
        }

        .footer-logo2 {
            width: 7.5rem;
            /* 120px */
        }

        .footer-logo3 {
            width: 15.625rem;
            /* 250px */
        }
    </style>


</head>

<body>
    <div style="background-color:#F5F2EE">

        <!--HEADER START -->
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #00DF74"
            amf:container="preheader" amf:conditional>
            <tr>
                <td align="center">
                    <table cellpadding="0" cellspacing="0" border="0" width="600" class="wrapper" style="width:600px;">
                        <tr class="mob_none">
                            <td style="padding: 8px 0px;">
                                <table width="100%" cellpadding="0" cellspacing="0" border="0" role="presentation">
                                    <tr>
                                        <td align="left" style="width: 65%;">
                                            <img style="width: 90%;" src="https://www.netball.co.uk/wp-content/uploads/2024/12/<EMAIL>" alt="logo">
                                        </td>
                                        <td align="right" style="width: 35%;">
                                            <img style="width: 100%;" src="https://www.bloomnetball.co.uk/wp-content/uploads/2024/12/<EMAIL>" alt="logo">
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
        <!--HEADER END -->
