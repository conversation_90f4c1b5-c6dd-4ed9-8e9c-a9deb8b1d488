<?php
// $configFile = "/var/www/html/core/config.php";
// include($configFile);
// $autoloadFile = "/var/www/html/core/vendor/autoload.php";
// include($autoloadFile);
// function ClassAutoloader($class) {
//     $file = "/var/www/html/core/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
// }
// spl_autoload_register("ClassAutoloader");

include(__DIR__ . "/init.php");
$start_time = microtime(true); // Record the start time

// Cron functions
StripePayment::BillingStage5();
TasterSession::CheckStatuses();
Database::Backup();
if (date('N') == 7 || date('N') == 3) PurchaseTransaction::PeriodicImport();


// Log with Sentry
$execution_time  = microtime(true) - $start_time; // Calculate the execution time
\Sentry\captureMessage('Cron2330.php ran in '.$execution_time.' seconds');
