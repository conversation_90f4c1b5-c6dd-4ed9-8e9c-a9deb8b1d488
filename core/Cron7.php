<?php

include(__DIR__ . "/init.php");
$start_time = microtime(true); // Record the start time

// Cron functions
StripePayment::BillingStage1(true); // unbilled fixtures
StripePayment::BillingStage2(true); // try billed them

// Log with Sentry
$execution_time  = microtime(true) - $start_time; // Calculate the execution time
\Sentry\captureMessage('Cron7.php ran in ' . $execution_time . ' seconds');
