<?php

class TasterBooking extends Base {
    
    protected $dbTable = "tasterBookings";
    protected $dbFields = ["tasterID","userID","stripePaymentIntentID","discountCode"];

    protected $tasterID, $userID, $stripePaymentIntentID, $discountCode;

    protected $taster;
    protected $user;
    protected $stripePaymentIntent, $stripeCustomer;
    protected $coordinator; # Inherited from TasterSession->getCoordinator()
    protected $confirmIssued;

    function Save () {
        $rlt = parent::Save();
        if (!is_numeric($rlt) && strpos($rlt,"Integrity constraint violation: 1062 Duplicate entry")) return "Already registered for that Session";
        return $rlt;
    }

    function getTaster () {
        if (!$this->tasterID) return;
        if (!$this->taster) $this->taster = new TasterSession($this->tasterID);
        return $this->taster;
    }

    function getUser () {
        if (!$this->userID) return;
        if (!$this->user) $this->user = new User($this->userID);
        return $this->user;
    }

    function getCoordinator () {
        $this->getTaster();
        if (!$this->taster) return;
        $this->coordinator = $this->taster->getCoordinator();
    }

    function getStripePaymentIntent () {
        if (!$this->stripePaymentIntentID) return;
        if (!$this->stripePaymentIntent) {
            try {
                $this->stripePaymentIntent = Stripe::getPaymentIntent($this->stripePaymentIntentID);
                return $this->stripePaymentIntent;
            } catch (Exception $e) {
                return $e->getMessage();
            }
        } 

    }

    function getStripeCustomer () {
        if ($this->stripeCustomer) return $this->stripeCustomer;
        $this->getStripePaymentIntent();
        if (!$this->stripePaymentIntent || !$this->stripePaymentIntent->customer) return;
        try {
            $this->stripeCustomer = Stripe::getStripeCustomer($this->stripePaymentIntent->customer);
            return $this->stripeCustomer;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    
    function SendConfirmation ($output = false) {
        $this->getTaster();
        $this->getUser();
        $this->getCoordinator();
        if (!$this->taster) return "No Taster";
        if (!$this->user) return "No User";
        $subject = "Your Couch2Court Registration";
        $message[] = "Hi {$this->user->firstname}";
        $message[] = "Congratulations! You have been registered for the Couch2Court session.";
        $message[] = "When: " . $this->taster->formatTime() . " on " . $this->taster->formatDate();
        $message[] = "Where: " . $this->taster->formatVenue();
        if ($this->getTaster() && $this->getTaster()->notes) $message[] = $this->getTaster()->notes;
        if ($this->taster->coordinator) {
            $message[] = "Your Couch2Court Coordinator: " . $this->taster->coordinator.". " . $this->taster->coordinator->email . " " . $this->taster->coordinator->mobile;
        }
        $getInTouch = "If you have any questions then please do get in touch with ";
        $getInTouch .= ($this->taster->coordinator->firstname) ? $this->taster->coordinator->firstname : "us";
        $message[] = $getInTouch . ".";
        $message[] = "We can't wait to welcome you on the night and get you started on your journey back into netball!";
        $message[] = "Best wishes";
        $message[] = "The Bloom Netball Team";
        $message[] = "W: https://www.bloomnetball.co.uk";
        $to[$this->user->email] = $this->user->email;
        $cc = [
            $this->taster->coordinator->email => "{$this->taster->coordinator->firstname} {$this->taster->coordinator->lastname}"
        ];
        $bcc = [
            "<EMAIL>" => "Charlotte Waugh",
        ];
        if ($output === true) {
            Tools::Dump($this); return;
        }
        new Db("UPDATE `tasterAttendee` SET `confirmIssued` = NOW() WHERE `id` = {$this->id}");
        $rlt = Email::Issue ($subject, $message, $to, $cc, $bcc);
        return [
            "subject" => $subject,
            "message" =>  $message,
            "to" => $to,
            "cc" => $cc,
            "bcc" => $bcc,
        ];
    }

    function SynchroniseCustomerData () {
        if (!$this->getUser()) return;
        if (!$this->getStripeCustomer()) return;
        if (!$this->stripeCustomer->email || $this->stripeCustomer->email != $this->user->email) {
            Stripe::updateStripeCustomer($this->stripeCustomer->id,["email" => $this->user->email, "name" => $this->user->firstname." ".$this->user->lastname, "phone" => $this->user->mobile]);
            return true;
        }
    }

    static function Taster (TasterSession $tasterSession) {
        if (!$tasterSession->id) return [];
        $sql = "SELECT * FROM tasterAttendee WHERE tasterID = {$tasterSession->id} AND deleted IS NULL ORDER BY created";
        return static::Query($sql);
    }

    static function Total (TasterSession $tasterSession) {
        if (!$tasterSession->id) return [];
        $sql = "SELECT COUNT(`id`) AS `total` FROM tasterAttendee WHERE tasterID = {$tasterSession->id} AND deleted IS NULL";
        $db = new Db($sql);
        return (isset($db->rows[0]['total']) && $db->rows[0]['total']) ? $db->rows[0]['total'] : 0;
    }

    static function Create (Array $data = []) {
        if (!isset($data['tasterID'])) return "No Taster session specified";
        if (!isset($data['userID'])) return "No User specified";
        $t = new static();
        $t->Load($data);
        $rlt = $t->Save();
        if (is_numeric($rlt)) {
            $user = new User($data['userID']);
            $tasterSession = new TasterSession($data['tasterID']);
            Logging::Add("C2C Reg: $user $tasterSession");
            $t->SendConfirmation();
            $t->SynchroniseCustomerData();
            return $t;
        } else {
            return $rlt;
        } 
    }

    static function Exists (Int $userID, Int $tasterID) {
        $sql = "SELECT `id` FROM `tasterAttendee` WHERE `userID` = $userID AND `tasterID` = $tasterID";
        $db = new Db($sql);
        return ($db->rows) ? true : false;
    }

}