<?php

class System extends Base {

    static function BuildLiveLeaguesApi() {
        $apiFile = "/var/www/html/api/liveLeagues.json";
        $output = \Venue::ApiListing();
        file_put_contents($apiFile,json_encode($output));
        // $rebuildUrl = "https://leagues4you.co.uk/?codeframe_league_post_rebuild=true";
        // try {
        //     $ch = curl_init();
        //     curl_setopt($ch, CURLOPT_URL, $rebuildUrl);
        //     curl_setopt ($ch, CURLOPT_RETURNTRANSFER, 1);
        //     $content = curl_exec ($ch);
        //     curl_close ($ch);
        // } catch (Exception $e) {
        //     $content = $e->getMessage();
        // }
        return $output;
    }

}