<?php

class Transaction extends Base {

    protected $dbTable = "transactionMain", $dbKey = "id";
    protected $dbFields = ["typeID","date","due","seasonID","teamID","contactID","net","vat","total","issue","issued","instalment","sageImport"];

    protected $typeID = 1;
    protected $date, $due;
    protected $seasonID, $teamID, $contactID;
    protected $net = null, $vat = null, $total = null;
    protected $issue, $issued;
    protected $instalment, $sageImport;

    protected $from = ["Leagues 4 You", "The Malvern Spa Hotel","Grovewood Rd","Malvern","WR14 1GD"];

    function __construct(Int $id = null) {
        parent::__construct($id);
        if ($this->id && !$this->total) $this->Save();
    }

    function __toString() {
        return new TransactionType($this->typeID) . "-{$this->id}";
    }

    function Save() {
        if (!$this->date) $this->date = date('Y-m-d');
        if (!$this->due) $this->due = $this->date;
        $this->calcTotals();
        parent::Save();
    }

    function calcTotals () {
        if (!$this->id)return;
        $this->total = TransactionItem::Totals($this->id);
    }

    function pdf () {
        $transactionType = new TransactionType($this->type);
        $transactionItems = TransactionItem::byTransaction($this->id);
        $folder = \Tools\CheckFolder("/var/www/html/app/Downloads/Transactions/");
        $file = "{$transactionType}-{$this->id}.pdf";
        if ($this->issue) return ["folder" => $folder, "file" => $file];
        $pdf = new FPDF('P','mm','A4');
        $pdf->AddPage();
        $pdf->Image("/var/www/html/www/img/logo.png",131,12);
        $pdf->setXY(10,14);
        $pdf->SetFont('Arial','B',32);
        $pdf->Cell(0,15,$transactionType->name,"B");
        $pdf->SetFont('Arial','',12);
        $pdf->setXY(150,35);
        $pdf->MultiCell(0,5,implode("\n",$this->from),null,"L");
        $pdf->setXY(10,$pdf->GetY()+5);
        $contact = new User($this->user);
        $to[] = $contact->name();
        if ($contact->email) $to[] = $contact->email;
        $pdf->SetFont('Arial','B',12);
        $pdf->Cell(0,5,"To:",null,1);
        $pdf->SetFont('Arial','',12);
        $pdf->MultiCell(0,5,implode("\n",$to),null,"L");
        $pdf->Ln(5);
        $pdf->SetFont('Arial','B',20);
        $pdf->Cell(0,15,"Items","B",1);
        $pdf->Ln(5);
        $pdf->SetFont('Arial','B',12);
        $pdf->Cell(20,7,"Qty");
        $pdf->Cell(100,7,"Description");
        $pdf->Cell(35,7,"Unit",null,null,"R");
        $pdf->Cell(35,7,"SubTotal",null,1,"R");
        $pdf->SetFont('Arial','',12);
        foreach ($transactionItems as $k => $v) {
            $pdf->Cell(20,5,$v->qty);
            $pdf->Cell(100,5,$v->description);
            $pdf->Cell(35,5,$v->unit,null,null,"R");
            $pdf->Cell(35,5,$v->total,null,1,"R");
        }
        $pdf->Line(10,$pdf->GetY()+15,200,$pdf->GetY()+15);
        // $pdf->SetXY(150,250);
        $pdf->Ln(5);
        $pdf->SetFont('Arial','B',12);
        $pdf->Cell(20,5,"Total");
        $pdf->SetFont('Arial','B',12);
        $pdf->Cell(170,5,$this->total,null,null,"R");
        $pdf->Output('F',$folder.$file);
        return ["folder" => $folder,"file" => $file];
    }

    static function issueIssued () {
        $sql = "SELECT `id` FROM `transactionMain` WHERE `issue` = 1 AND `issued` IS NULL";
        $rlt = \Datastore\Query($sql);
        if (!$rlt) return;
        $return = [];
        foreach ($rlt as $r) {
            $return[$r['id']] = new static($r['id']);
        }
        return $return;
    }

    static function Listing (Int $transactionType = null) {
        $sql = "SELECT `id` FROM `transactionMain`";
        if ($transactionType) {$sql .= " WHERE `type` = :type"; $data['type'] = $transactionType;}
        $sql .= " ORDER BY `created` ASC";
        $rlt = \Datastore\Query($sql, $data);
        if (!$rlt) return;
        $return = [];
        foreach ($rlt as $r) {
            $return[$r['id']] = new static($r['id']);
        }
        return $return;
    }

    static function ChargeListing() {
        $sql = "SELECT `id` FROM `divisions` WHERE `closed` IS NOT NULL";
        $rlt = \Datastore\Query($sql);
        if (!$rlt) return;
        $return = [];
        foreach ($rlt as $r) {
            $return[$r['id']] = new static($r['id']);
        }
        return $return;
    }

    static function Raise (Int $userID, Int $seasonID, $typeID = 1) {
        $sql = "SELECT `id` FROM `transactionMain` WHERE `issued` IS NULL AND `contactID` = :contactID AND `seasonID` = :seasonID";
        $sqlData['contactID'] = $userID;
        $sqlData['seasonID'] = $seasonID;
        $rlt = \Datastore\Query($sql, $sqlData);
        if ($rlt && is_array($rlt) && $rlt[0]['id']) return new static($rlt[0]['id']);
        $transaction = new static();
        $transaction->typeID = $typeID; # 1 = SI
        $transaction->contactID = $userID;
        $transaction->seasonID = $seasonID;
        $transaction->Save();
        return $transaction;
    }

    static function ListingPerLeague (Int $leagueID) {
        $sql = "
            SELECT
            `transactionMain`.`id`
            FROM
                `transactionMain`
                LEFT JOIN `divisions` ON `transactionMain`.`division` = `divisions`.`id`
                LEFT JOIN `seasons` ON `divisions`.`season` = `seasons`.`id`
            WHERE `seasons`.`league` = $leagueID AND `transactionMain`.`deleted` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $t = new static();
            $t->Load($r);
            ($return) ? array_push($return,$t) : $return[] = $t;
        } 
        return $return;
    }

}