<?php

class Fixture extends Base {
    protected $dbTable = "fixtures";
    public $home, $away;
    protected $homeScore, $awayScore;
    protected $homePom, $awayPom;
    public $homePoints, $awayPoints;
    public $weekNo, $weekPos;
    public $duration;
    // protected $cycle, $weekNo, $cycleWeek;
    public $pom;
    public $startTime;
    public $seasonID;
    public $bookingID, $bookingOffset;
    public $billingInfo;
    public $feeMatch, $feeOfficial, $includeOfficialFee, $total;
    public $homeTrans, $awayTrans;
    protected $divisionID;
    protected $rescheduleReason;

    protected $homeTeam, $awayTeam;
    protected $division;

    protected $dbFields = ["home", "away","homeScore", "awayScore","homePoints", "homeTrans", "awayPoints","awayTrans","weekNo", "weekPos", "duration","homePom","awayPom","rescheduleReason"];

    function __construct(Int $id = null) { parent::__construct($id);}

    function Save() {
        if (!$this->duration) {
            $division = new Division($this->divisionID);
            $this->duration = $division->duration();
        }
        if (is_numeric($this->homeScore) && is_numeric($this->awayScore)){
            if (!$this->homeScore && $this->awayScore) $this->homeScore = 0;
            if (!$this->awayScore && $this->homeScore) $this->awayScore = 0;
            $this->fixturePoints ();
        } 
        $rlt = parent::Save();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }

    function __toString() {
        $this->FetchHome();$this->FetchAway();
        return trim(preg_replace("/[^a-zA-Z0-9_ ]/", "",$this->homeTeam)) . " v " . trim(preg_replace("/[^a-zA-Z0-9_ ]/", "",$this->awayTeam));
    }

    function FetchHome() {
        return ($this->homeTeam) ?? ($this->homeTeam = new Team($this->home));
    }

    function FetchAway() {
        return ($this->awayTeam) ?? ($this->awayTeam = new Team($this->away));
    }

    function fixturePoints() {
        if (!$this->homeScore && !$this->awayScore) {
            Database::Execute("DELETE FROM standings WHERE fixtureID = :id",["id" => $this->id]);
        } else {
            $sport = $this->getSport();
            if ($sport->id == 9) {
                $points = $this->calculatePoints_soccer();
                return $this->RecordPoints($points);
            } else {
                return $this->calculatePoints ();
            }
        }
    }

    function calculatePoints_soccer() {
        $winPoints = 3; $scoreDrawPoints = 2; $noScoreDrawPoints = 1;
        $return['log'][] = "Calculate Points for Home score {$this->homeScore} and Away score {$this->awayScore}";
        $dataFields = ["points" => 0, "bp" => null, "text" => null,"win" => null, "draw" => null,"text" => null];
        $return = [$this->home => $dataFields, $this->away => $dataFields];
        $return[$this->home]['teamID'] = $this->home;
        $return[$this->away]['teamID'] = $this->away;
        $return[$this->home]['plus'] = $return[$this->away]['minus'] = $this->homeScore;
        $return[$this->home]['minus'] = $return[$this->away]['plus'] = $this->awayScore;
        $return[$this->home]['fixtureID'] = $return[$this->away]['fixtureID'] = $this->id;

        if ($this->homeScore > $this->awayScore) {
            # Home Win
            $return[$this->home]['points'] = $winPoints;
            $return[$this->home]['text'] = "$winPoints points for home win";
            $return[$this->home]['win'] = 1;
        } elseif ($this->awayScore > $this->homeScore) {
            $return[$this->away]['points'] = $winPoints;
            $return[$this->away]['text'] = "$winPoints points for away win";
            $return[$this->away]['win'] = 1;
        } elseif ($this->homeScore > 0) {
            $return[$this->home]['points'] =  $return[$this->away]['points'] = $scoreDrawPoints;
            $return[$this->home]['text'] = $return[$this->away]['text'] = "$scoreDrawPoints points for a score draw";
            $return[$this->home]['draw'] = $return[$this->away]['draw'] = 1;
        } else {
            $return[$this->home]['points'] = $return[$this->away]['points'] = $noScoreDrawPoints;
            $return[$this->home]['text'] = $return[$this->away]['text'] = "$noScoreDrawPoints points for a no-score draw";
            $return[$this->home]['draw'] = $return[$this->away]['draw'] = 1;
        }
        return $return;
    }

    function RecordPoints (Array $teamsPoints) {
        $sql ="INSERT INTO `standings` SET `fixtureID` = :fixtureID, `teamID` = :teamID, `points` = :points, `bp` = :bp, `win`= :win, `draw` = :draw, `plus` = :plus, `minus` = :minus, `text` = :text ON DUPLICATE KEY UPDATE `points` = :points, `bp` = :bp, `win`= :win, `draw` = :draw, `plus` = :plus, `minus` = :minus, `text` = :text";
        foreach ($teamsPoints as $teamPoints) $rlts[] = ['db' => Database::Execute($sql,$teamPoints), 'sql' => $sql, 'data' => $teamPoints];
        return $rlts;
    }

    function calculatePoints () {
        // 5 Points for a win, 3 for a Draw
        // Score within 5 = 2 Points, Score within half = 1 point
        if (!$this->homeScore && !$this->awayScore) {
            $sql ="DELETE FROM `standings` WHERE `fixtureID` = {$this->id}";
            new Db($sql);
        } else {
            $dataFields = ["points" => 0, "bp" => 0, "text" => null, "log" => null, "win" => null, "draw" => null];
            $standings = [$this->home => $dataFields, $this->away => $dataFields];
            // Result Points
            if ($this->homeScore > $this->awayScore) {
                $standings[$this->home]['points'] = 5;
                $standings[$this->home]['log'][] = "5 points for home win";
                $standings[$this->home]['win'] = 1;
            } elseif ($this->awayScore > $this->homeScore) {
                $standings[$this->away]['points'] = 5;
                $standings[$this->away]['log'][] = "5 points for away win";
                $standings[$this->away]['win'] = 1;
            } else {
                $standings[$this->home]['points'] = $standings[$this->away]['points'] = 3;
                $standings[$this->home]['log'][] = "3 points for a draw";
                $standings[$this->away]['log'][] = "3 points for a draw";
                $standings[$this->home]['draw'] = 1;
                $standings[$this->away]['draw'] = 1;
            }
            // Losing Bonus Points
            if ($this->homeScore != $this->awayScore) {
                if ($this->homeScore < $this->awayScore) {
                    if ($this->homeScore >= ($this->awayScore - 5)) {
                        $standings[$this->home]['bp'] = 2;
                        $standings[$this->home]['log'][] = "2 BP for within 5";
                    } elseif ($this->homeScore >= ($this->awayScore / 2)) {
                        $standings[$this->home]['bp'] = 1;
                        $standings[$this->home]['log'][] = "1 BP for loss within a half";
                    }
                } else {
                    if ($this->awayScore >= ($this->homeScore - 5)) {
                        $standings[$this->away]['bp'] = 2;
                        $standings[$this->away]['log'][] = "2 BP for within 5";
                    } elseif ($this->awayScore >= ($this->homeScore / 2)) {
                        $standings[$this->away]['bp'] = 1;
                        $standings[$this->away]['log'][] = "1 BP for within half";
                    }
                }
            }
            $points = ($standings[$this->home]['points']) ? $standings[$this->home]['points'] : "NULL";
            $bp = ($standings[$this->home]['bp']) ? $standings[$this->home]['bp'] : "NULL";
            $win = ($standings[$this->home]['win']) ? $standings[$this->home]['win'] : "NULL";
            $draw = ($standings[$this->home]['draw']) ? $standings[$this->home]['draw'] : "NULL";
            $plus = ($this->homeScore) ? $this->homeScore : "NULL";
            $minus = ($this->awayScore) ? $this->awayScore : "NULL";
            $text = ($standings[$this->home]['log']) ? "'".implode(". ",$standings[$this->home]['log'])."'" : "NULL";
            $sql ="INSERT INTO `standings` SET `fixtureID` = {$this->id}, `teamID` = {$this->home}, `points` = $points,`bp` = $bp, `win`= $win, `draw` = $draw, `plus` =  $plus, `minus` = $minus ON DUPLICATE KEY UPDATE `points` = $points,`bp` = $bp, `win` = $win, `draw` = $draw, `plus` = $plus, `minus` = $minus, `text` = $text";
            new Db($sql);

            $points = ($standings[$this->away]['points']) ? $standings[$this->away]['points'] : "NULL";
            $bp = ($standings[$this->away]['bp']) ? $standings[$this->away]['bp'] : "NULL";
            $win = ($standings[$this->away]['win']) ? $standings[$this->away]['win'] : "NULL";
            $draw = ($standings[$this->away]['draw']) ? $standings[$this->away]['draw'] : "NULL";
            $plus = ($this->awayScore) ? $this->awayScore : "NULL";
            $minus = ($this->homeScore) ? $this->homeScore : "NULL";
            $text = ($standings[$this->away]['log']) ? "'".implode(". ",$standings[$this->away]['log'])."'" : "NULL";
            $sql ="INSERT INTO `standings` SET `fixtureID` = {$this->id}, `teamID` = {$this->away}, `points` = $points,`bp` = $bp, `win`= $win, `draw` = $draw, `plus` =  $plus, `minus` = $minus ON DUPLICATE KEY UPDATE `points` = $points,`bp` = $bp, `win` = $win, `draw` = $draw, `plus` =  $plus, `minus` =  $minus, `text` = $text";
            new Db($sql);
            $this->homePoints = ($standings[$this->home]['points'] + $standings[$this->home]['bp']) ;
            $this->awayPoints = ($standings[$this->away]['points'] + $standings[$this->away]['bp']) ;
        }
    }

    /* Getters */
    function getWeek() { return $this->weekNo;}
    function getWeekPos() { return $this->weekPos;}
    function getDivision() { return $this->divisionID;}
    function getDivisionID() { return $this->divisionID;}
    function getTheDivision() {
        if (!$this->division && $this->divisionID) $this->division = new Division($this->divisionID);
        return $this->division;
    }
    function getSeasonID() {
        $division = new Division($this->divisionID);
        return $division->getSeasonID();
    }
    function getLeagueID() {
        $season = new Season($this->getSeasonID());
        $league = new League($season->getLeagueID());
        return $league->id;
    }

    function getHomeTeam() { return $this->home;}
    function getHomeTeamName() { return new Team($this->home);}
    function getAwayTeam() { return $this->away;}
    function getAwayTeamName() { return new Team($this->away);}

    function getHome() {
        if (!$this->homeTeam) $this->homeTeam = new Team($this->home);
        return $this->homeTeam;
    }

    function getAway() {
        if (!$this->awayTeam) $this->awayTeam = new Team($this->away);
        return $this->awayTeam;
    }

    function getDuration() { 
        $season = new Season($this->seasonID);
        return $season->getDuration();
    }

    function getSport() {
        $division = $this->getTheDivision();
        $season = $division->getSeason();
        $league = $season->getLeague();
        return $league->getSport();
    }

    function getHomeScore() { return ($this->homeScore) ? (float)$this->homeScore:null;}
    function getAwayScore() { return ($this->awayScore) ? (float)$this->awayScore : null;}
    function getHomePom() { return $this->homePom;}
    function getAwayPom() { return $this->awayPom;}
    function getHomeTrans() { return $this->homeTrans;}
    function getAwayTrans() { return $this->awayTrans;}
    /* Setters */
    function setHomeScore(String $homeScore) { $this->homeScore = $homeScore;}
    function setAwayScore(String $awayScore) { $this->awayScore = $awayScore;}
    function setHomePom(String $pom) { $this->homePom = $pom;}
    function setAwayPom(String $pom) { $this->awayPom = $pom;}
    function setHomeTrans(Int $transactionID = null) { $this->homeTrans = $transactionID; $this->Save();}
    function setAwayTrans(Int $transactionID = null) { $this->awayTrans = $transactionID; $this->Save();}
    
    function createInvoices() {
        $schedule = Schedule::Fixture($this);
        $booking = new Booking($schedule->getBookingID());
        $season = new Season($this->getSeasonID());
        // echo "FixtureID {$this->id}<br>";
        $this->feeMatch = $season->getFixtureCharge();
        $home = new Team($this->home);
        $away = new Team($this->away);
        $return = [];
        if (!$this->homeTrans && !TeamSeason::isWildcard($home, $season)) {
            $transaction = Finance::Create($this->home, $this->feeMatch, $this->__toString(), $booking->getStartDate());
            $this->homeTrans = $transaction->id;
            Logging::Add("Transaction ID {$this->homeTrans} created for Fixture ID {$this->id} for $home (vs $away) in " . $season->getLeagueName() . " league");
            $return[] = ["home" => $transaction->id];
        } 
        // elseif (TeamSeason::isWildcard($home, $season)) {
        //     echo "Home: $home is WildCarded<br>";
        // } else echo "Home already billed under {$this->homeTrans}<br>";
        
        if (!$this->awayTrans && !TeamSeason::isWildcard($away, $season)) {
            $transaction = Finance::Create($this->away, $this->feeMatch, $this->__toString(), $booking->getStartDate());
            $this->awayTrans = $transaction->id;
            Logging::Add("Transaction ID {$this->awayTrans} created for Fixture ID {$this->id} for $away (vs $home) in " . $season->getLeagueName() . " league");
            $return[] = ["away" => $transaction->id];
        }
        //  elseif (TeamSeason::isWildcard($away, $season)) {
        //     echo "Away: $away is WildCarded<br>";
        // } else echo "Away already billed under {$this->homeTrans}<br>";
        $this->Save();
        return $return;
    }
    /* Statics */
    static function forSeason (Season $season) {
        $sql = "SELECT `fixtures`.* FROM `fixtures` WHERE `fixtures`.`deleted` IS NULL AND `divisionID` IN (SELECT `id` FROM `divisions` WHERE `seasonID` = " . $season->getID() . ") ORDER BY weekNo, weekPos";
        $db = new Db($sql);if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $f = new static();
            $f->Load($r);
            $f->schedule = Schedule::Fixture($f);
            $return[] = $f;
        } 
        return $return;
    }

    // Check if lock , we will need to find better way to check
    static function isLocked ($fixtures) {
        $autofixtureEnabled = true;
        if($fixtures) {
            foreach ($fixtures as $fixture) {
                if ($fixture->homeTrans && $fixture->awayTrans) {
                    $autofixtureEnabled = false;
                    break;
                }
            }
        }
        return $autofixtureEnabled;
    }

    static function Autofixture (Division $division) {
        /* Fetch Division */
        // $division = new Division($divisionID);
        /* Remove any pre-existing fixtures */
        // $sql = "DELETE FROM `fixtures` WHERE `divisionID` = $divisionID";
        $deletionStamp = date('Y-m-d H:i:s');
        $sql = "UPDATE `fixtures` SET `deleted` = '$deletionStamp' WHERE `divisionID` = {$division->id}";
        new Db($sql);
        // echo $sql;
        // Tools::Dump(new Db($sql));
        // return;
        /* Clear any bookings made for pre-existing Fixtures */
        static::ClearDivisionBookings($division->id);
        /* Fetch Season, League and Teams - if any missing, return */
        $season = new Season($division->getSeasonID());if (!$season) return "Could not find a linked Season for Division ID {$division->id}";
        $league = new League($season->getLeagueID());if (!$league) return "Could not find a linked League for Season " . $season->getLeagueID() . " (Division ID {$division->id})";
        $teams = Team::byDivision($division->id,false);
        if (!$teams || !is_array($teams) || count($teams)<4) return "Fewer than 4 teams cannot be autofixtured";

        $numberOfRounds = ($division->rounds) ? $division->rounds : $season->getRounds();
        $fixtureData = static::CircularFixtures($teams,$numberOfRounds);

        // \Tools\Dump($fixtureData);
        $sql = "INSERT INTO `fixtures` (`divisionID`, `duration`, `feeMatch`, `feeOfficial`, `weekNo`, `weekPos`, `home`, `away`) VALUES ";
        $conn = null;
        $duration = ($season->getDuration()) ? $season->getDuration() : 40;
        $feeMatch = ($season->getFixtureCharge()) ? $season->getFixtureCharge() : 27;
        $feeOfficial = ($season->getOfficialCharge()) ? $season->getOfficialCharge() :5;
        foreach  ($fixtureData as $weekNo => $fixtures) {
            $week = $weekNo + 1;
            $playOrder = 1;
            foreach ($fixtures as $k => $v) {
                if ($v[0]=="B" || $v[1]=="B") continue;
                // echo "Adding {$v[0]} v {$v[1]} in Week $week Pos $playOrder<br>";
                // $playOrder = $k + 1;
                $sql .= $conn . "($division->id, $duration, $feeMatch, $feeOfficial,$week, $playOrder,{$v[0]}, {$v[1]})";
                $conn = ",";
                $playOrder ++;
            }
        }
        $db = new Db($sql);
        // Tools::Dump($db);
        return $fixtureData;
    }

    static function CircularFixtures (Array $teams = [], $cycles = 3) {
        // \Tools\Dump($teams);
        if (count($teams)<4) return "Not enough teams";
        foreach ($teams as $team) {
            if (!is_numeric($team)) return "Teams must be numeric. $team isn't";
        }
        if (count($teams)%2==1)  $teams[] = "B";
        $circle1 = $teams;
        $count = count($teams)/2;
        // echo "<!-- Circle 1\n" . print_r($circle1,true) . " -->\n";
        // echo "<!-- Count " . ($count = count($teams)/2) . " -->\n";
        $circle2 = array_reverse(array_splice($circle1,($count)));
        // echo "<!--\nCircle 1\n" . print_r($circle1,true) . " -->\n";
        // echo "<!--\nCircle 2\n" . print_r($circle2,true) . " -->\n";
        /* For each fixture week */
        for ($i = 0; $i < (count($teams)-1); $i++) {
            // echo "<!-- Fixture Week $i -->\n";
            /* Loop through Circle1 teams matching each against a Circle2 team */
            for ($w = 0; $w < count($circle1); $w++) {
                if ($circle1[$w] && ($circle1[$w] == "B" || $circle2[$w] == "B")) continue;
                // echo "<!-- $i $w {$circle1[$w]} plays {$circle2[$w]}-->\n";
                $fixtureWeeks[$i][$w] = [$circle1[$w],$circle2[$w]];
            }
            // Last element from Circle 1 moved to end of Circle 2
            $circle2[] = array_pop($circle1);
            // (count($teams)<=6) ? array_splice($circle1, 0, 0, array_shift($circle2)) : array_splice($circle1, 1, 0, array_shift($circle2));             
            array_splice($circle1, 1, 0, array_shift($circle2));
            // First element from Circle 2 moved to 2nd element of Circle 1
            // array_splice($circle1, 1, 0, array_shift($circle2));
            // First element from Circle 2 moved to 1st element of Circle 1
            // array_splice($circle1, 0, 0, array_shift($circle2));
        }
        // echo "<!-- Intermediate Result \n" . print_r($fixtureWeeks,true) . "\n -->\n";
        // return $fixtureWeeks;
        /*Adjust fixture order by offsetting fixtures in each week */
        $move = 0;
        foreach ($fixtureWeeks as $fixtureWeek => $fixtures) {
            for ($m = 0; $m<$move;$m++) {
                array_unshift($fixtures,array_pop($fixtures)); # From end of array, prepend to beginning
                array_unshift($fixtures,array_pop($fixtures)); # From end of array, prepend to beginning
            }
            $move ++;
            $fixtureWeeks[$fixtureWeek] = $fixtures;
        }
        if ($cycles) {
            $fixtureBlock = $fixtureWeeks;
            foreach ($fixtureBlock as $fixtureWeek => $fixtures) {
                foreach ($fixtures as $k =>$v) {
                    $fixtures[$k] = [$v[1],$v[0]];
                }
                $reverseFixtureBlock[$fixtureWeek] = array_reverse($fixtures);
            }
            for ($i=2;$i<=$cycles;$i++) {
                $fixtureWeeks = ($i%2==0) ? array_merge($fixtureWeeks,$reverseFixtureBlock) :array_merge($fixtureWeeks,$fixtureBlock);
            }
        }
        // echo "<!-- Completed Result \n" . print_r($fixtureWeeks,true) . "\n -->\n";
        return $fixtureWeeks;
    }

    static function ClearDivisionBookings (Int $divisionID) {
        $sql = "DELETE FROM `schedule` WHERE `fixtureID`IN (SELECT `id` FROM `fixtures` WHERE `divisionID` = $divisionID)";
        // echo $sql;
        new Db($sql);
    }

    static function Unscheduled (Season $season) {
        /* Returns STRING on Error. Numeric on Sucess */
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`seasonID` = :seasonID AND `divisions`.`deleted` IS NULL AND `fixtures`.`id` NOT IN (SELECT DISTINCT `fixtureID` FROM `schedule` WHERE `deleted` IS NULL) ORDER BY `fixtures`.`weekNo` ASC, `fixtures`.`weekPos` ASC";
        $db = new Db($sql, ["seasonID" => $season->id]);
        return ($db->errors) ? implode(". ",$db->errors) : count($db->rows);
    }

    static function UnscheduledList (Season $season) {
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`seasonID` = :seasonID AND `divisions`.`deleted` IS NULL AND `fixtures`.`id` NOT IN (SELECT DISTINCT `fixtureID` FROM `schedule` WHERE `deleted` IS NULL) ORDER BY `fixtures`.`weekNo` ASC, `fixtures`.`weekPos` ASC";
        // echo "Season {$season->id}<br>$sql";
        return static::Query($sql, ["seasonID" => $season->id]);
    }

    static function Season (Season $season, Bool $unscheduledOnly = false) {
        // $sql = "SELECT `id` FROM `fixtures` WHERE `fixtures`.`deleted` IS NULL AND `divisionID` IN (SELECT `id` FROM `divisions` WHERE `seasonID` = :seasonID) AND `deleted` IS NULL";
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`seasonID` = :seasonID AND `divisions`.`deleted` IS NULL";
        if ($unscheduledOnly === true) {
            $sql .= " AND `fixtures`.`id` NOT IN (SELECT DISTINCT `fixtureID` FROM `schedule` WHERE `deleted` IS NULL)";
        }
        // $sql .= "";
        $sql .= " ORDER BY `fixtures`.`weekNo` ASC, `fixtures`.`weekPos` ASC";
        // return $sql;
        $db = new Db($sql, ["seasonID" => $season->id]);if (!$db->rows) return implode(",",$db->errors);
        $return = [];
        foreach ($db->rows as $r) {
            $fixture = new static();
            $fixture->Load($r);
            $adjustedWeekNo = $fixture->weekNo - 1;
            if (!isset($return[$adjustedWeekNo])) $return[$adjustedWeekNo]['required'] = 0;
            $return[$adjustedWeekNo]['required'] ++;
            $return[$adjustedWeekNo]['fixtures'][] = $fixture;
        }
        return $return;
    }

    static function SeasonTotal (Season $season) {
        $sql = "SELECT COUNT(`fixtures`.`id`) AS `total` FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`seasonID` = :seasonID";
        $db = new Db($sql,["seasonID" => $season->id]);
        if (!isset($db->rows[0]['total']) || !$db->rows[0]['total']) return;
        return $db->rows[0]['total'];
    }

    static function Unbilled (Season $season) {
        $sql = "SELECT `fixtures`.`id` AS `fixtureID`, `fixtures`.`home`, `fixtures`.`homeTrans`, `fixtures`.`away`,`fixtures`.`awayTrans`, `bookings`.`startDate` AS `fixtureDate`, `schedule`.`startTime` AS `fixtureTime` 
        FROM `fixtures` 
        LEFT JOIN schedule ON `fixtures`.`id` = `schedule`.`fixtureID` 
        LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` 
        LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
        WHERE `fixtures`.`deleted` IS NULL AND (`fixtures`.`homeTrans` IS NULL OR `fixtures`.`awayTrans` IS NULL ) AND (`bookings`.`startDate` < NOW() OR (`bookings`.`startDate` = NOW() AND `bookings`.`startTime` <= NOW())) AND `divisions`.`seasonID` = {$season->id}";
        $db = new Db($sql);if (!$db->rows) return;
        $return = [];
        return $db->rows;
    }

    static function TeamBillable (Team $team) {
        # Purpose : To retrieve any fixtures (up to and including today) for this Team, not yet billed
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `fixtures`.`deleted` IS NULL AND ((fixtures.home = :teamID AND `fixtures`.`homeTrans` IS NULL) OR (fixtures.away = :teamID AND `fixtures`.`awayTrans` IS NULL)) AND `bookings`.`deleted` IS NULL AND `schedule`.`deleted` IS NULL AND `bookings`.`startDate` <= :date";
        $rlt = Database::Execute($sql,["teamID" => $team->id, "date" => date('Y-m-d')]);
        return $rlt['success']['rows'] ?? null;
    }

    static function Billable ($createInvoices = true, String $date = null, Bool $dateOnly = false) {
        if (!$date) $date = date('Y-m-d');
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `fixtures`.`deleted` IS NULL AND `bookings`.`deleted` IS NULL AND `schedule`.`deleted` IS NULL AND (`fixtures`.`homeTrans` IS NULL OR `fixtures`.`awayTrans` IS NULL ) AND `bookings`.`startDate` ";
        $sql .= ($dateOnly === true) ? " = " : " <= ";
        $sql .= "'$date'";
        $rlt = static::Query($sql);
        if (!$rlt) return;
        $return = [];
        $billedFixtures = 0;
        foreach ($rlt as $f) {
            if ($f->homeTrans) {
                $billedFixtures -= $f->feeMatch;
                $return[$f->id] = [
                    "home" => ["id" => $f->homeTrans, "note" => "Pre-existing"],
                ];
            } 
            if ($f->awayTrans) {
                $billedFixtures -= $f->feeMatch;
                $return[$f->id] = [
                    "away" => ["id" => $f->awayTrans, "note" => "Pre-existing"],
                ];
            } 
            if ($createInvoices === true) {
                $billedFixtures += ($f->feeMatch*2);
                
                $transactionRlt = $f->createInvoices();
                if ($f->homeTrans && !isset($return[$f->id]["home"])) {
                    $return[$f->id] = [
                        "home" => ["id" => $f->homeTrans, "note" => "Created"],
                    ];
                }
                if ($f->awayTrans && !isset($return[$f->id]["away"])) {
                    $return[$f->id] = [
                        "away" => ["id" => $f->awayTrans, "note" => "Created"],
                    ];
                }
            } 
            // $return[$f->id] = [
            //     "home" => $f->homeTrans,
            //     "away" => $f->awayTrans
            // ]; 
        }
        if ($createInvoices === true) {
            Logging::Add("Billed Fixtures for " . date('d/m/Y')." : $billedFixtures");
            StripePayment::setBillingTotals($billedFixtures,'fixtures');
        }
        return $return;
    }

    static function Report (String $startDate, String $endDate) {
        $sql = "SELECT `schedule`.`id`, `schedule`.`bookingID`, `schedule`.`fixtureID` FROM `schedule` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` WHERE `fixtures`.`deleted` IS NULL AND `bookings`.`startDate` >= :startDate AND `bookings`.`startDate` <= :endDate ORDER BY  `bookings`.`startDate` ASC, `schedule`.`startTime` ASC";
        // exit($sql." : $startDate : $endDate");
        $db = new Db($sql,["startDate" => $startDate, "endDate" => $endDate]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $fixture = new Fixture($r['fixtureID']);
            $booking = new Booking($r['bookingID']);
            $season = new Season($fixture->getSeasonID());
            // $season->getFixtureCharge();
            // $homeTransaction = new Finance($fixture->homeTrans);
            // $awayTransaction = new Finance($fixture->awayTrans);
            $return[$r['fixtureID']] = [
                "fixture" => $fixture,
                "booking" => $booking,
                "venue" => new Venue($booking->getVenueID()),
                "league" => new League($fixture->getLeagueID()),
                "schedule" => new Schedule($r['id']),
                "home" => new Team($fixture->home),
                "away" => new Team($fixture->away),
                "charges" => $season->getFixtureCharge() * 2,
                "homeTrans" => $fixture->homeTrans,
                "awayTrans" => $fixture->awayTrans
            ];
        }
        return $return;
    }

    static function Results (Array $results) { 
        $sql = "UPDATE `fixtures` SET `homeScore` = :homeScore, `awayScore` = :awayScore, `homePom` = :homePom, `awayPom` = :awayPom WHERE `id` = :id";
        foreach ($results as $fixtureID => $details) {
            // if (Auth::isAuthor(User::AuthUser())) echo "Fixture $fixtureID<br>";
            $homeScore = ($details['homeScore']) ? $details['homeScore'] : 0;
            $awayScore = ($details['awayScore']) ? $details['awayScore'] : 0;
            if (!$homeScore && !$awayScore) {
                $homeScore = $awayScore = null;
            }
            // if (!$homeScore || !$awayScore) $homeScore = $awayScore = null;
            $db = new \Db($sql,["homeScore" => $homeScore,"awayScore" => $awayScore,"homePom" => $details['homePom'],"awayPom" => $details['awayPom'],"id" => $fixtureID]);
            $fixture = new static($fixtureID);
            $fixture->fixturePoints();                
            /* Ensure Season now Locked */
            Season::Lock($fixture->getSeasonID());
        }
        // if (Auth::isAuthor(User::AuthUser())) Tools::Dump($results);
    }

    static function getResults (Division $division) {
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `homeScore` IS NOT NULL AND `awayScore` IS NOT NULL AND `divisionID` = {$division->id} AND `fixtures`.`deleted` IS NULL AND `schedule`.`deleted` IS NULL ORDER BY `bookings`.`startDate` ASC, `schedule`.`startTime` ASC";
        $db = new Db($sql);
        $return = [];
        if (!$db->rows) return $return;
        foreach ($db->rows as $r) {
            $home = new Team($r['home']);
            $away = new Team($r['away']);
            $fixture = new static();
            $fixture->Load($r);
            $schedule = Schedule::Fixture($fixture);
            if ($schedule) {
                $booking = new Booking($schedule->getBookingID());
                $fixtureDate = $booking->getStartDate("d/m/Y");
                $fixtureTime = $schedule->getStartTime();
            } else $fixtureDate = $fixtureTime = null;
            $return[] = [
                "homeID" => $home->id,
                "homeTeam" => $home->__toString(),
                "homeScore" => $r['homeScore'],
                "homePom" => $r['homePom'],
                "awayID" => $away->id,
                "awayTeam" => $away->__toString(),
                "awayScore" => $r['awayScore'],
                "awayPom" => $r['awayPom'],
                "fixtureDate" => $fixtureDate,
                "fixtureTime" => $fixtureTime
            ];
        }
        return $return;
    }

    static function SeasonCharges (Season $season) {
        $sql = "SELECT SUM(`feeMatch`*2) as `total` FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`seasonID` = {$season->id}";
        $db = new Db($sql); if (!$db->rows) return;
        return $db->rows[0]['total'];
    }

    static function LeagueTotals (String $startDate, String $endDate, League $league) {
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `fixtures`.`deleted` IS NULL AND `bookings`.`leagueID` = :leagueID AND `bookings`.`startDate` >= :startDate AND `bookings`.`startDate` <= :endDate";
        $db = new Db($sql,["leagueID" => $league->id, "startDate" => $startDate, "endDate" => $endDate]);
        if (!$db->rows) return;
        $season = Season::Current($league);
        return [
            "minutes" => count($db->rows) * $season->getDuration(),
            "charge" => count($db->rows) * $season->getFixtureCharge() * 2
        ];
    }

    static function SuspendUnplayed (Season $season, String $reason) {
        $sql = "UPDATE fixtures SET deleted = NOW(), notes = '$reason' WHERE `homeTrans` IS NULL AND `awayTrans` IS NULL AND `deleted` IS NULL AND `divisionID` IN (SELECT id FROM divisions WHERE seasonID = {$season->id})";

    }

    static function TotalByDate (String $date) {
        $sql = "SELECT SUM(`total`) AS `total` FROM `finances` WHERE `created` >= '$date 00:00:00' AND `created` <= '$date 23:59:59' AND `deleted` IS NULL AND `ledger` = 'S' AND `type` = 'T' AND `category` = 'I'";
        $rlt = new Db($sql);
        return (isset($rlt->rows[0]['total'])) ? (int)$rlt->rows[0]['total'] : 0;
    }

    static function RescheduleReasons() {
        return [
            1 => ["id" => 1, "name" => "Weather"],
            2 => ["id" => 2, "name" => "Venue Unavailable"]
        ];
    }

    static function Leaderboard (Season $season) {
        $sql = "SELECT `home`, `homePom`, `away`, `awayPom`, `divisionID` FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `divisions`.`seasonID` = {$season->id} AND `fixtures`.`deleted` IS NULL AND `divisions`.`deleted` IS NULL";
        $rlt = new Db($sql);
        if (!$rlt->rows) return [];
        $return = [];
        foreach ($rlt->rows as $r) {
            if ($r['homePom']) {
                (isset($return[$r['divisionID']][$r['home'].":".$r['homePom']])) ? $return[$r['divisionID']][$r['home'].":".$r['homePom']]++ : $return[$r['divisionID']][$r['home'].":".$r['homePom']] = 1;
            }
            if ($r['awayPom']) {
                (isset($return[$r['divisionID']][$r['away'].":".$r['awayPom']])) ? $return[$r['divisionID']][$r['away'].":".$r['awayPom']]++ : $return[$r['divisionID']][$r['away'].":".$r['awayPom']] = 1;
            }
        }
        if ($return) {
            foreach ($return as $divisionID => $results) {
                arsort($return[$divisionID]);
            }
        }
        arsort($return);
        return $return;
    }

    static function LiveLeaderBoard () {
        $sql = "SELECT `home`, `homePom`, `away`, `awayPom`, `divisionID` FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` LEFT JOIN `seasons` ON `divisions`.`seasonID` = `seasons`.`id` WHERE `seasons`.`statusID` = 1 AND `fixtures`.`deleted` IS NULL AND `divisions`.`deleted` IS NULL";
        $rlt = new Db($sql);
        if (!$rlt->rows) return [];
        $return = [];
        foreach ($rlt->rows as $r) {
            if ($r['homePom']) {
                (isset($return[$r['home'].":".$r['homePom']])) ? $return[$r['home'].":".$r['homePom']]++ : $return[$r['home'].":".$r['homePom']] = 1;
            }
            if ($r['awayPom']) {
                (isset($return[$r['away'].":".$r['awayPom']])) ? $return[$r['away'].":".$r['awayPom']]++ : $return[$r['away'].":".$r['awayPom']] = 1;
            }
        }
        arsort($return);
        return $return;
    }

    static function Coordinator (User $user, String $startDate = null, String $endDate = null) {
        if (!$startDate) $startDate = date('Y-m-d');
        if (!$endDate || strotime($endDate) < strtotime($startDate)) $endDate = $startDate;
        $sql = "SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` LEFT JOIN `seasons` ON `divisions`.`seasonID` = `seasons`.`id` LEFT JOIN `leagues` ON `seasons`.`leagueID` = `leagues`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`deleted` IS NULL AND `seasons`.`deleted` IS NULL AND `bookings`.`startDate` >= '$startDate' AND `bookings`.`startDate` <= '$endDate'";
        if (!$user->isManager) $sql .= " AND `leagues`.`coordinator` = {$user->id}";
        $sql .= " ORDER BY `bookings`.`startDate` ASC";
        return static::Query($sql);
    }

    static function Booking (Booking $booking) {
        return static::Query("SELECT `fixtures`.* FROM `fixtures` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` WHERE `schedule`.`bookingID` = {$booking->id} AND `schedule`.`deleted` IS NULL");
    }

    static function Played (Season $season) {
        $sql = "SELECT fixtures.* FROM fixtures LEFT JOIN divisions ON fixtures.divisionID = divisions.id WHERE fixtures.homeScore IS NOT NULL AND fixtures.awayScore IS NOT NULL AND fixtures.deleted IS NULL AND divisions.seasonID = :seasonID";
        return static::Query($sql,["seasonID" => $season->id]);
    }
}