<?php

class TasterAttendee extends Base {
    
    protected $dbTable = "tasterAttendee";
    protected $dbFields = ["tasterID","userID","stripePaymentIntentID","discountCode","synchronised"];

    protected $tasterID, $userID, $stripePaymentIntentID, $discountCode, $synchronised;

    protected $taster;
    protected $user;
    protected $stripePaymentIntent, $stripeCustomer;
    protected $coordinator; # Inherited from TasterSession->getCoordinator()
    protected $confirmIssued;

    function Save () {
        $rlt = parent::Save();
        if (!is_numeric($rlt) && strpos($rlt,"Integrity constraint violation: 1062 Duplicate entry")) return "Already registered for that Session";
        return $rlt;
    }

    function getTaster () {
        if (!$this->tasterID) return;
        if (!$this->taster) $this->taster = new TasterSession($this->tasterID);
        return $this->taster;
    }

    function getUser () {
        if (!$this->userID) return;
        if (!$this->user) $this->user = new User($this->userID);
        return $this->user;
    }

    function getCoordinator () {
        $this->getTaster();
        if (!$this->taster) return;
        $this->coordinator = $this->taster->getCoordinator();
    }

    function getStripePaymentIntent () {
        if (!$this->stripePaymentIntentID) return;
        if (!$this->stripePaymentIntent) {
            try {
                $this->stripePaymentIntent = Stripe::getPaymentIntent($this->stripePaymentIntentID);
                return $this->stripePaymentIntent;
            } catch (Exception $e) {
                return $e->getMessage();
            }
        } 

    }

    function getStripeCustomer () {
        if ($this->stripeCustomer) return $this->stripeCustomer;
        $this->getStripePaymentIntent();
        if (!$this->stripePaymentIntent || !$this->stripePaymentIntent->customer) return;
        try {
            $this->stripeCustomer = Stripe::getStripeCustomer($this->stripePaymentIntent->customer);
            return $this->stripeCustomer;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    
    function SendConfirmation ($output = false) {
        $this->getTaster();
        $this->getUser();
        $this->getCoordinator();
        if (!$this->taster) return "No Taster";
        if (!$this->user) return "No User";
        $subject = "Couch2Court Registration";
        $message[] = "Hi {$this->user->firstname}";
        $message[] = "Congratulations! You have been registered for the Couch2Court session.";
        $message[] = "When: " . $this->taster->formatTime() . " on " . $this->taster->formatDate();
        $message[] = "Where: " . $this->taster->formatVenue();
        if ($this->taster->coordinator) {
            $message[] = "Your Couch2Court Coordinator: " . $this->taster->coordinator.". " . $this->taster->coordinator->email . " " . $this->taster->coordinator->mobile;
        }
        $getInTouch = "If you have any questions then please do get in touch with ";
        $getInTouch .= ($this->taster->coordinator->firstname) ? " {$this->taster->coordinator->firstname}." : " us.";
        $message[] = $getInTouch;
        $message[] = "Terms and Conditions : https://cdn.leagues4you.co.uk/netball-terms-and-conditions";
        $message[] = "We can't wait to welcome you on the night and get you started on your journey back into netball!";
        $message[] = "Best wishes";
        $message[] = "The Bloom Netball Team";
        $message[] = "W: https://www.bloomnetball.co.uk";
        $to[$this->user->email] = $this->user->email;
        $cc = [
            $this->taster->coordinator->email => "{$this->taster->coordinator->firstname} {$this->taster->coordinator->lastname}"
        ];
        $bcc = [
            "<EMAIL>" => "Charlotte Waugh",
            "<EMAIL>" => "a2ztech",
        ];
        if ($output === true) {
            Tools::Dump($this); return;
        }
        new Db("UPDATE `tasterAttendee` SET `confirmIssued` = NOW() WHERE `id` = {$this->id}");
        return Email::Issue ($subject, $message, $to, $cc, $bcc);
    }

    // function SendCancellation ($output = false) {
    //     $this->getTaster();
    //     $this->getUser();
    //     $this->getCoordinator();
    //     if (!$this->taster) return "No Taster";
    //     if (!$this->user) return "No User";
    //     $subject = "Couch2Court Cancellation";
    //     $message[] = "Hi {$this->user->firstname}";
    //     $message[] = "Unfortunately we have had to cancel our planned session at " . $this->taster->formatVenue() . "  on " . $this->taster->formatDate() ." due to circumstances beyond our control";
    //     $message[] = "We're so sorry and apologise for any disappointment caused.";
    //     $message[] = "Payments made via our website have been refunded and should appear between 5-10 days in your account.";
    //     $message[] = "We really hope to bring netball to your local area in the future but until then please keep an eye out for updates on our website https://leagues4you.co.uk and many thanks again for taking an interest.";
    //     $message[] = "Best wishes";
    //     $message[] = "The Leagues4You Team";
    //     $message[] = "W: https://leagues4you.co.uk";
        
    //     $to[$this->user->email] = $this->user->email;
    //     $cc = [
    //         $this->taster->coordinator->email => "{$this->taster->coordinator->firstname} {$this->taster->coordinator->lastname}"
    //     ];
    //     $bcc = [
    //         "<EMAIL>" => "Charlotte Waugh"
    //     ];
    //     return Email::Issue ($subject, $message, $to, $cc, $bcc);
    // }

    function SynchroniseCustomerData () {
        if (!$this->getUser()) return;
        if (!$this->getStripeCustomer()) return;
        new Db("UPDATE `{$this->dbTable}` SET `synchronised` = NOW() WHERE `id` = {$this->id}");
        if (!$this->stripeCustomer->email || $this->stripeCustomer->email != $this->user->email) {
            Stripe::updateStripeCustomer($this->stripeCustomer->id,["email" => $this->user->email, "name" => $this->user->firstname." ".$this->user->lastname, "phone" => $this->user->mobile]);
            new Db("UPDATE `{$this->dbTable}` SET `synchronised` = NOW() WHERE `id` = {$this->id}");
            return true;
        }
    }

    static function Taster (TasterSession $tasterSession) {
        if (!$tasterSession->id) return [];
        $sql = "SELECT * FROM tasterAttendee WHERE tasterID = {$tasterSession->id} AND deleted IS NULL ORDER BY created";
        return static::Query($sql);
    }

    static function Total (TasterSession $tasterSession) {
        if (!$tasterSession->id) return [];
        $sql = "SELECT COUNT(`id`) AS `total` FROM tasterAttendee WHERE tasterID = {$tasterSession->id} AND deleted IS NULL";
        $db = new Db($sql);
        return (isset($db->rows[0]['total']) && $db->rows[0]['total']) ? $db->rows[0]['total'] : 0;
    }

    static function Create (Array $data = []) {
        if (!isset($data['tasterID'])) return "No Taster session specified";
        if (!isset($data['userID'])) return "No User specified";
        $t = new static();
        $t->Load($data);
        $rlt = $t->Save();
        $discountCode = (isset($data['discountCode'])) ? $data['discountCode'] : null;
        $stripePaymentIntentID = (isset($data['stripePaymentIntentID'])) ? $data['stripePaymentIntentID'] : null;
        if (is_numeric($rlt)) {
            $user = new User($data['userID']);
            $tasterSession = new TasterSession($data['tasterID']);
            Logging::Add("C2C Reg: $user $tasterSession $discountCode $stripePaymentIntentID");
            $t->SendConfirmation();
            $t->SynchroniseCustomerData();
            return $t;
        } else {
            return $rlt;
        } 
    }

    static function Exists (Int $userID, Int $tasterID) {
        $sql = "SELECT `id` FROM `tasterAttendee` WHERE `userID` = $userID AND `tasterID` = $tasterID";
        $db = new Db($sql);
        return ($db->rows) ? true : false;
    }
    
    static function SyncUnsyncd (Int $limit = 20) {
        $sql = "SELECT * FROM tasterAttendee WHERE deleted IS NULL AND synchronised IS NULL AND stripePaymentIntentID IS NOT NULL LIMIT $limit";
        $db = new Db($sql);
        if (!$db->rows) return false;
        foreach ($db->rows as $r) {
            $ta = new static();
            $ta->Load($r);
            $ta->SynchroniseCustomerData ();
            $return[] = $ta->id;
        }
        return $return;
    }
}