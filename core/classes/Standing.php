<?php

class Standing extends Base {

    protected $dbTable = "standings";
    protected $fixtureID;
    protected $teamID;
    protected $points, $bp;
    protected $win, $draw;
    protected $plus, $minus;
    protected $text;
    protected $dbFields = ["fixtureID", "teamID", "points","bp", "win", "draw","plus","minus","text"];

    protected $teamName, $played, $won, $drawn, $lost, $for, $against, $gd, $totalPoints, $totalBP, $total;

    function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() { return "{$this->name}";}
    function getFixtureID() { return $this->fixtureID;}
    function getTeamID() { return $this->teamID;}
    function getPoints () { return $this->points;}
    function getBp () { return $this->bp;}
    function getTotal () { return ($this->points + $this->bp);}
    function getWon () { return $this->won;}
    function getDrawn () { return $this->drawn;}
    function getPlus () { return $this->plus;}
    function getMinus () { return $this->minus;}
    function getText () { return $this->text;}

    function ApiData() {
        return [
            "teamID" => $this->teamID,
            "teamID" => $this->teamName,
            "played" => $this->played,
            "won" => $this->won,
            "drawn" => $this->drawn,
            "lost" => $this->lost,
            "for" => $this->for,
            "against" => $this->against, 
            "gd" => $this->gd,
            "points" => $this->totalPoints,
            "bp" => $this->totalBP,
            "total" => $this->total,
        ];
    }

    static function byDivision (Division $division) {
        $sql = "SELECT teamID, teams.name AS teamName, COUNT(teamID) AS played, COUNT(win) AS won, COUNT(draw) AS drawn, (COUNT(teamID) - COUNT(win) - COUNT(draw)) AS lost, COALESCE(SUM(points),0) AS totalPoints, COALESCE(SUM(bp),0) AS totalBP, (COALESCE(SUM(points),0) + COALESCE(SUM(bp),0)) AS total, SUM(plus) AS `for`, SUM(minus) AS against, (SUM(plus) - SUM(minus)) AS gd FROM standings LEFT JOIN fixtures ON standings.fixtureID = fixtures.id LEFT JOIN teams ON standings.teamID = teams.id WHERE fixtures.divisionID = :divisionID AND fixtures.deleted IS NULL GROUP BY standings.teamID ORDER BY total DESC, gd DESC";
        return static::Query($sql,["divisionID" => $division->id]);
    }

    static function Division (Int $divisionID) {
        $sql = "SELECT `standings`.`teamID`, COUNT(`standings`.`teamID`) AS `played`, COUNT(`standings`.`win`) AS `won`, COUNT(`standings`.`draw`) AS `drawn`, (COUNT(`standings`.`teamID`) - COUNT(`standings`.`win`) - COUNT(`standings`.`draw`)) AS `lost`, COALESCE(SUM(`standings`.`points`),0) AS `points`, COALESCE(SUM(`standings`.`bp`),0) AS `bp`, (COALESCE(SUM(`standings`.`points`),0) + COALESCE(SUM(`standings`.`bp`),0)) AS `total`, SUM(`standings`.`plus`) AS `for`, SUM(`standings`.`minus`) AS `against`, (SUM(`standings`.`plus`) - SUM(`standings`.`minus`)) AS `gd` FROM `standings` LEFT JOIN `fixtures` ON `standings`.`fixtureID` = `fixtures`.`id` WHERE `fixtures`.`divisionID` = $divisionID AND fixtures.deleted IS NULL GROUP BY `standings`.`teamID` ORDER BY `total` DESC, `gd` DESC";
        // $sql = "SELECT `teamID`, COUNT(`teamID`) AS `played`, COUNT(`win`) AS `won`, COUNT(`draw`) AS `drawn`, (COUNT(`teamID`) - COUNT(`win`) - COUNT(`draw`)) AS `lost`, COALESCE(SUM(`points`),0) AS `points`, COALESCE(SUM(`bp`),0) AS `bp`, (COALESCE(SUM(`points`),0) + COALESCE(SUM(`bp`),0)) AS `total`, SUM(`plus`) AS `for`, SUM(`minus`) AS `against`, (SUM(`plus`) - SUM(`minus`)) AS `gd` FROM `standings` WHERE teamID IN (SELECT `id` FROM `teams` WHERE `divisionID` = $divisionID) GROUP BY `teamID` ORDER BY `total` DESC, `gd` DESC";
        $rlt =  new Db($sql);
        if (!$rlt->rows) {
            $teams = Team::byDivision($divisionID);
            if ($teams) {
                foreach ($teams as $team) {
                    $return[] = [
                        "teamID" => $team->id,
                        "played" => 0,
                        "won" => 0,
                        "drawn" => 0,
                        "lost" => 0,
                        "for" => 0,
                        "against" => 0,
                        "gd" => 0,
                        "points" => 0,
                        "bp" => 0,
                        "total" => 0
                    ];
                }
                return $return;
            }
        } else return $rlt->rows;        
        return $rlt->rows;
    }

    static function forAPI (Division $division) {
        $standings = static::byDivision($division);
        $return = [];
        if (!$standings) {
            $sql = "SELECT `teamSeasons`.`teamID` AS teamID, `teams`.`name` AS teamName, '0' AS played,  '0' AS won, '0' AS drawn, '0' AS lost, '0' as totalPoints, '0' AS totalBP, '0' AS total, '0' AS `for`, '0' AS against, '0' AS gd FROM teamSeasons LEFT JOIN teams ON teamSeasons.teamID = teams.id WHERE teams.deleted IS NULL AND teamSeasons.deleted IS NULL AND teamSeasons.divisionID = :divisionID ORDER BY teams.name ASC";
            $standings = static::Query($sql,["divisionID" => $division->id]);
        }
        foreach ($standings as $standing) {
            $return[] = $standing->ApiData();
        }
        return $return;
    }

    static function Recalculate (Season $season) {
        /* Remove standings for fixtures in this Division */
        $sql = "DELETE standings FROM standings LEFT JOIN fixtures ON standings.fixtureID = fixtures.id LEFT JOIN divisions ON fixtures.divisionID = divisions.id WHERE divisions.seasonID = :seasonID"; 
        Database::Execute($sql,["seasonID" => $season->id]);
        /* Capture all Fixtures with Scores */
        $fixtures = Fixture::Played($season);
        foreach ($fixtures as $fixture) $fixture->fixturePoints();
    }

}