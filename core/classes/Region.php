<?php

class Region extends Base {

    protected $dbTable = "regions";
    protected $name;  

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function __toString() {
        return "{$this->name}";
    }
    function Save() {
        $rlt = parent::Save();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }

}