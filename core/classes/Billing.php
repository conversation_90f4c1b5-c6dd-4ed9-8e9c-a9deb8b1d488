<?php

class Billing {

    protected $production;
    protected $logging = [];

    ## Stage 1 - Fixture Invoicing
    ## Fixture::Billable()

    ## Stage 2a - Fetch Team Balances - Finance::Balances()
    ## Stage 2b - Create Billing Intentions including:
    ## - If balance <= 0 set Team's AgedDate to today and no further action, else...
    ## - Check if any existing open Payment Intents for this Team
    ## - if PaymentIntent exists and matches current balance, no further action, else
    ## - If PaymentIntent exists (and therefore DOES NOT match current balance - cancel it
    ## - Get PaymentUser from TeamManagers. If no PaymentUser, exit
    ## - Get PaymentMethod, if no PaymentMethod, exit
    ## - Confirm PaymentMethod owned by PaymentUser, if not, exit
    ## - Check if Team opted for Full Balance Payment (FBP)
    ## - Create PaymentIntent

    ## Stage 3 - Charge PaymentIntents

    function __construct (Bool $production = false) {
        $this->production = $production;
        $this->Log(__CLASS__." at " . date('h:ia d/n/Y') . ". Mode: " . $this->Mode());
        $this->Run();
    }

    function Mode() {
        return ($this->production === true) ? "Live" : "Test";
    }

    function __toString() {
        $output = '';
        foreach ($this->logging as $l) {
            $output .= <<<OUTPUT
                $l<br>
            OUTPUT;
        }
        return $output;
    }

    function Log(String $message) : Bool {
        $this->logging[] = $message;
        return true;
    }

    function Run() {
        $this->Stage1();
        $this->Stage2();
    }

    function Stage1() {
        $this->Log(__METHOD__);
        $invoices = Fixture::Billable(); # Fixtures unbilled are invoiced IF the Team isn't a WildCard
        if ($invoices) {
            foreach ($invoices as $fixtureID => $invoice) {
                $msg = "Fixture ID {$fixtureID}";
                if (isset($invoice['home'])) $msg .= ". Home team Inv ID {$invoice['home']['id']} {$invoice['home']['note']}";
                if (isset($invoice['away'])) $msg .= ". Away team Inv ID {$invoice['away']['id']} {$invoice['away']['note']}";
                $this->Log($msg);
            }
        }
    }

    function Stage2() {
        $this->Log(__METHOD__);
        ## Get Balances
        $overdues = Finance::Balances();
        $this->Log("Balance to Bill " . array_sum($overdues));
        foreach ($overdues as $teamID => $balance) {
            $team = new Team($teamID);
            $msg = "$team balance $balance";
            # Below Zero?
            if ($balance <= 0) {
                $msg .= ". Balance <= 0 : No Action";
                if ($this->production === true) {
                    $msg . ". Aged Date reset";
                    $team->setAgedDate();
                } 
                // $this->Log($msg);
                continue;
            }
            # Pending PaymentIntent?
            $pending = StripePayment::TeamPendingTotal($team);
            if ($pending) {
                $this->Log($pending,true);
                $this->Log($msg);
                continue;
            }
            $treasurer = $team->getPayer();
            $this->Log("Billing Party".print_r($treasurer,true));
            $this->Log($msg);

        }
    }
}