<?php

class Upload extends Base {
    protected $dbTable = "uploads";

    protected $type;
    protected $path;
    protected $content_id;

    protected $dbFields = [
        "type",
        "path",
        "content_id"
    ];

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    /**
     * Save multiple files for an incident report
     * 
     * @param array $files Array of uploaded files
     * @param int $incidentId The incident report ID
     * @return array Array of saved file paths
     */
    public static function saveIncidentFiles($files, $incidentId) {
        $uploadDir = getcwd() . '/public/uploads/';

        // Create upload directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0775, true);
        }

        $savedFiles = [];
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
        $maxFileSize = 5 * 1024 * 1024; // 5MB

        foreach ($files['name'] as $key => $name) {
            if ($files['error'][$key] == UPLOAD_ERR_OK) {
                $fileType = mime_content_type($files['tmp_name'][$key]);
                $fileSize = $files['size'][$key];

                if (!in_array($fileType, $allowedTypes)) {
                    continue;
                }

                if ($fileSize > $maxFileSize) {
                    continue;
                }

                $fileName = date("Y-m-d_H-i-s") . '-' . basename($name);
                $uploadFile = $uploadDir . $fileName;

                if (move_uploaded_file($files['tmp_name'][$key], $uploadFile)) {
                    $filePath = getDomainPrefix() . 'hub.leagues4you.co.uk/public/uploads/' . $fileName;

                    // Save to database
                    $upload = new Upload();
                    $upload->type = 'incident';
                    $upload->path = $filePath;
                    $upload->content_id = $incidentId;
                    $upload->save();

                    $savedFiles[] = $filePath;
                }
            }
        }

        return $savedFiles;
    }

    /**
     * Get all files for an incident report
     * 
     * @param int $incidentId The incident report ID
     * @return array Array of file paths
     */
    public static function getIncidentFiles($incidentId) {
        $sql = "SELECT path FROM uploads WHERE type = 'incident' AND content_id = :incidentId";
        $uploads = static::Query($sql, ["incidentId" => $incidentId]);

        $files = [];
        foreach ($uploads as $upload) {
            $files[] = $upload->path;
        }

        return $files;
    }

    /**
     * Delete files for an incident report
     * 
     * @param int $incidentId The incident report ID
     * @return bool Success status
     */
    public static function deleteIncidentFiles($incidentId) {
        $sql = "DELETE FROM uploads WHERE type = 'incident' AND content_id = :incidentId";
        return static::Query($sql, ["incidentId" => $incidentId]);
    }

    /**
     * Get file ID by path
     * 
     * @param string $path The file path
     * @return int|null The file ID or null if not found
     */
    public static function getFileIdByPath($path) {
        $sql = "SELECT id FROM uploads WHERE path = :path LIMIT 1";
        $result = static::Query($sql, ["path" => $path]);
        return $result ? $result[0]->id : null;
    }

    /**
     * Delete a single file
     * 
     * @param int $fileId The file ID
     * @return bool Success status
     */
    public static function deleteFile($fileId) {
        // First get the file path
        $sql = "SELECT path FROM uploads WHERE id = :fileId LIMIT 1";
        $result = static::Query($sql, ["fileId" => $fileId]);

        if ($result) {
            $filePath = $result[0]->path;

            // Convert URL path to server path
            $serverPath = str_replace(
                getDomainPrefix() . 'hub.leagues4you.co.uk/public/uploads/',
                getcwd() . '/public/uploads/',
                $filePath
            );

            // Delete physical file if it exists
            if (file_exists($serverPath)) {
                unlink($serverPath);
            }

            // Delete from database
            $sql = "DELETE FROM uploads WHERE id = :fileId";
            return static::Query($sql, ["fileId" => $fileId]);
        }

        return false;
    }
}
