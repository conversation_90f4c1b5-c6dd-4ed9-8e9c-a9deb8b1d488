<?php
/**
 * Test for Team Management amendments eg new treasurer, new captain, season approval, new payment method etc
 * Auto-approve if no StripePaymentMethodID?
 * Check PaymentMethod is valid via Stripe?
 * General availability = Launch date - 4 weeks?
 * How to calculate max teams? Gen. Avail. AutoApprove stops when?
 * Approval Confirmation Email
 */

class TeamManagement extends Base {

    protected $dbTable = "teamManagement";
    protected $approved;
    protected $teamID, $captainID, $treasurerID;
    protected $stripePaymentIntentID;
    protected $seasonID, $divisionID;
    protected $notes;
    protected $dbFields = ["teamID", "captainID", "treasurerID", "stripePaymentMethodID","seasonID","divisionID","notes","approved"];

    protected $team, $captain, $treasurer, $season, $division;

    function getTeam() {
        if (!$this->team && $this->teamID) $this->team = new Team($this->teamID);
        return $this->team;
    }
    
    function getCaptain() {
        if (!$this->captain && $this->captainID) $this->captain = new User($this->captainID);
        return $this->captain;
    }
    
    function getTreasurer() {
        if (!$this->treasurer && $this->treasurerID) $this->treasurer = new User($this->treasurerID);
        return $this->treasurer;
    }

    function getSeason() {
        if (!$this->season && $this->seasonID) $this->season = new Season($this->seasonID);
        return $this->season;
    }

    function getDivision() {
        if (!$this->division && $this->divisionID) $this->division = new Season($this->divisionID);
        return $this->division;
    }

    function CheckApproval() {
        if ($this->approved) return; # Already Approved
        $this->getSeason(); # Get Season for this Team Management record
        if (!$this->season) return;
        $this->season->getLeague(); # Get the parent League
        if (!$this->season->league) return;
        $currentSeason = Season::Current($this->season->league); # Grab the current/live season
        if (!$currentSeason) return;
        $currentTeams = TeamSeason::Season($currentSeason); # Grab teams in current season
        if (!$currentTeams) return;
        foreach ($currentTeams as $currentTeam) { 
            if ($currentTeam->id == $this->teamID) { # Is this team in the current season?
                $this->approved = date('Y-m-d H:i:s');
                $this->notes = ($this->notes) ? "{$this->notes} Priority approved." : "Priority approved.";
                $this->Save(); # Auto-approved
                return true;
            }
        }
        $generalAvailability = strtotime("-4 weeks", strtotime($this->season->launchDate));
        if (time() > $generalAvailability) {
            $this->notes = ($this->notes) ? "{$this->notes} GA approved." : "GA approved.";
            $this->approved = date('Y-m-d H:i:s');
            $this->Save(); # Auto-approved
            return true;
        }
    }

    function SendApproval() {

    }

    function ApiData() {
        $this->getTeam();
        $this->getCaptain();
        $this->getTreasurer();
        $this->getSeason();
        $this->getDivision();
        return [
            "id" => $this->id,
            "team" => ($this->team) ? $this->team->ApiData() : null,
            "captain" => $this->captain->ApiOutput(),
            "treasurer" => ($this->treasurer) ? $this->treasurer->ApiData() : $this->captain->ApiOutput(),
            "league" => ($this->league) ? $this->league->ApiOutput() : null,
            "season" => ($this->season) ? $this->season->ApiOutput() : null,
            "division" => ($this->division) ? $this->division->ApiData() : null,
            "stripePaymentMethodID" => $this->stripePaymentMethodID,
            "approved" => $this->approved,
            "deleted" => $this->deleted,
            "notes" => $this->notes,
        ];
    }

    function ConfirmationEmail() {
        
    }

    static function Add (Team $team, User $captain = null, String $stripePaymentMethodID = null, Season $season = null, Division $division = null, User $treasurer = null) {
        $sql = "SELECT * FROM `teamManagement` WHERE `teamID` = :teamID AND `deleted` IS NULL";
        $rlt = static::Query($sql,["teamID" => $team->id]);
        $amendments = [];
        if (isset($rlt[0])) { // A record exists - check for a variation
            $teamManagement=$rlt[0];
            $captainID = (is_object($captain) && $captain->id) ? $captain->id : null;
            $treasurerID = (is_object($treasurer) && $treasurer->id) ? $treasurer->id : null;
            $seasonID = (is_object($season) && $season->id) ? $season->id : null;
            $divisionID = (is_object($division) && $division->id) ? $division->id : null;
            if ($teamManagement->captainID != $captainID) {
                $amendment = "Captain amended from ";
                $amendment .= (!$teamManagement->captainID) ?" no-one" : new User($teamManagement->captainID);
                $amendment .= " to ";
                $amendment .= (!$captainID) ?" no-one" : new User($captainID);
                $amendments[] = $amendment;
            }
            if ($teamManagement->treasurerID != $treasurerID) {
                $amendment = "Treasurer amended from ";
                $amendment .= (!$teamManagement->treasurerID) ?" no-one" : new User($teamManagement->treasurerID);
                $amendment .= " to ";
                $amendment .= (!$treasurerID) ?" no-one" : new User($treasurerID);
                $amendments[] = $amendment;
            }
            if ($teamManagement->stripePaymentMethodID != $stripePaymentMethodID) {
                $amendment = "Stripe Payment Method amended from ";
                $amendment .= (!$teamManagement->stripePaymentMethodID) ?" nothing" : $teamManagement->stripePaymentMethodID;
                $amendment .= " to ";
                $amendment .= (!$stripePaymentMethodID) ?" nothing" : $stripePaymentMethodID;
            }
            if ($teamManagement->seasonID != $seasonID) {
                $amendment = "Season amended from ";
                $amendment .= (!$teamManagement->seasonID) ?" none" : new Season($teamManagement->seasonID);
                $amendment .= " to ";
                $amendment .= (!$seasonID) ?" none" : new Season($seasonID);
                $amendments[] = $amendment;
            }
            if ($teamManagement->divisionID != $divisionID) {
                $amendment = "Division amended from ";
                $amendment .= (!$teamManagement->divisionID) ?" none" : new Division($teamManagement->divisionID);
                $amendment .= " to ";
                $amendment .= (!$divisionID) ?" none" : new Division($divisionID);
                $amendments[] = $amendment;
            }
            if (!$amendments) return;
            Database::Execute("UPDATE `teamManagement` SET `deleted` = NOW() WHERE `id` = {$teamManagement->id}");
        }
        $tm = new static();
        $tm->teamID = $team->id;
        if ($captain) $tm->captainID = $captain->id;
        if ($treasurer) $tm->treasurerID = $treasurer->id;
        if ($stripePaymentMethodID) $tm->stripePaymentMethodID = $stripePaymentMethodID;
        if ($season) $tm->seasonID = $season->id;
        if ($division) $tm->divisionID = $division->id;
        if ($amendments) $tm->notes = $amendments;
        $rlt = $tm->Save();
        if ($captain) Team::Follow ($team, $captain);
        return ($rlt['errors']) ? $rlt['errors'] : $tm;
    }

    static function CheckApprovals() {
        $sql = "SELECT * FROM `teamManagement` WHERE `deleted` IS NULL AND `approved` IS NULL";
        $rlt = static::Query($sql);
        if (!$rlt) return;
        $return = [];
        foreach ($rlt as $r) {
            $r->CheckApproval();
            $return = $r->ApiData();
        }
        return $return;
    }

    static function Team (Team $team) {
        $sql = "SELECT * FROM `teamManagement` WHERE `deleted` IS NULL AND `teamID` = {$team->id}";
        $rlt = static::Query($sql);
        return ($rlt) ? $rlt[0] : null;
    }

    static function User (User $user) {
        $sql = "SELECT * FROM `teamManagement` WHERE `deleted` IS NULL AND (`captainID` = {$user->id} OR `treasurerID` = {$user->id})";
        return static::Query($sql);
    }

}