<?php
class Db {

    private $pdo;
    private $statement;
    private $sql, $data;
    public $errors = [];
    public $result;
    public $rows = [];
    public $lastInsertID;
    public $affectedRows;
    public $params;

    function __construct(String $sql=null, Array $data = []) {
        global $config;
        if ($sql) {
            $this->sql = trim($sql); $this->data = $data;
            /* Write to Incremental SQL */
            # Microtime, Time, Date, SQL, Data
            try {
                $pdoOptions = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_EMULATE_PREPARES => true,
                    PDO::ATTR_PERSISTENT => true,
                ];
                $this->pdo = new \PDO("mysql:dbname={$config['db']['name']};host={$config['db']['host']}", $config['db']['username'], $config['db']['password'],$pdoOptions);
            } catch (\PDOException $e) {
                $this->errors[] = $e->getMessage();
            }
            try {
                $this->statement = $this->pdo->prepare($this->sql);
                $this->statement->execute($this->data);
                switch (strstr($this->sql," ",true)) {
                    case "INSERT":
                        $this->lastInsertID = $this->pdo->lastInsertId(); break;
                    case "UPDATE":
                    case "DELETE":
                        $this->affectedRows = $this->statement->rowCount(); break;
                    case "SELECT":
                        while ($row = $this->statement->fetch(PDO::FETCH_ASSOC)) $this->rows[] = $row;                    
                }
            } catch (\PDOException $e) {
                $this->errors[] = $e->getMessage();
                // $this->params = print_r($this->statement->debugDumpParams(),true);
            } catch (Exception $e) {
                $this->errors[] = $e->getMessage();
                // $this->params = print_r($this->statement->debugDumpParams(),true);
            }
        }
    }

    static function Setup () {
        $sqlTableData = __DIR__."/../Core/sqlTables.php";
        if (!file_exists($sqlTableData)) return "$sqlTableData does not exist";
        include($sqlTableData);
        if (!isset($sqlTables) || !is_array($sqlTables) || !$sqlTables) return "SQL Data missing or not an array";
        foreach ($sqlTables as $table => $fields) {
            $sql = "CREATE TABLE IF NOT EXISTS `$table` ("; $conn = null;
            foreach ($fields as $fieldName => $field) {
                $sql .= $conn . "`$fieldName` {$field['type']}"; 
                if (isset($field['attributes'])) $sql .= " {$field['attributes']}";
                if (isset($field['notNull'])) $sql .= " NOT NULL";
                if (isset($field['default'])) $sql .= " default {$field['default']}";
                if (isset($field['key'])) $sql .= " {$field['key']}";
                if (isset($field['extra'])) $sql .= " {$field['extra']}";
                if (isset($field['default'])) $sql .= " NOT NULL";
                $conn = ", ";
            }
            $sql .=")";
            $db = new static($sql);
        }
        if ($db->errors) {
            echo "$sql<br>";
            echo implode("<br>",$db->errors);
        }
    }

    static function Backup () {
        $obj = new static();
        global $config;
        $backupFolder = Filesystem::CheckFolder($config['system']['path']."app/backup/");
        $handle = opendir($backupFolder);
        $dateOfBackup = date('Ymd');
        // if (date('N')==6 || date('N')==7) return; # Not at weekend
        $fileRoot = $backupFolder."/".$dateOfBackup;
        $zipFile = $fileRoot.".zip"; if (file_exists($zipFile)) return;
        # Clear old backups
        while (false !== ($entry = readdir($handle))) {
            if ($entry === "." || $entry === ".." || $entry === $dateOfBackup.".zip") continue;
            unlink($backupFolder."/".$entry);
        }
        // mail("<EMAIL>","Cleared Backups",implode("\n",$log));
        $mysqlCommand = "(mysqldump -u{$config['db']['username']} -p'{$config['db']['password']}' {$config['db']['name']} > $fileRoot.sql) 2>&1";
        exec($mysqlCommand, $output, $result);
        if ($result===0) {
            $zip = new \ZipArchive;
            if ($zip->open($fileRoot.".zip", \ZipArchive::CREATE) === TRUE) {
                $zip->addFile($fileRoot.".sql",basename($fileRoot.".sql"));
                $zip->close();
                unlink($fileRoot.".sql");
                $subject = "Backup ".$config['system']['organisation']." ".$config['system']['name'];
                $message[] = "Hello";
                $message[] = "Backup file attached here";
                $message[] = "Many thanks";
                $message[] = $config['system']['url'];
                Email::Issue($subject,$message,[$config['system']['internalEmail'] => $config['system']['internalEmail']],[],[],[$fileRoot.".zip"]);
            }
        }
    }

    static function Restore ($filename = null) {
        if (!$filename)return;
        $command = "mysql -u root -p[Paswordofthedatabase] < $filename";
    }

}
