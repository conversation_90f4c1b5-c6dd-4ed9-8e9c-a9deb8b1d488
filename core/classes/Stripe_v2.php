<?php

/**
 * Notes
 * Set Constructor to FALSE for testing and TRUE for production.
 * This Class accepts a User object for StripeCustomer creation.
 * setStripeCustomer - returns stripeCustomerID. Returns existing or newly created
 * addPaymentCardForm - Provides form for User to submit card details for storage
 * getPaymentMethods - Returns listing of previously stored cards
 * removePaymentCard - Marks card deleted and also detaches with <PERSON><PERSON>.
 * createPaymentIntent
 * CRON - Confirmable() and Capturable()
 */

class Stripe_v2 {

    protected $mode;
    public $publicKey;
    protected $secretKey;
    protected $stripeCustomerID, $stripeCustomer;
    protected $stripeSetupIntent;

    function __construct(Bool $productionMode = false) {
        switch ($productionMode) {
            case true:
                $this->mode = "Production";
                $this->secretKey =  "***********************************************************************************************************";
                $this->publicKey = "pk_live_51HS2BSLOCeRFt5luhwcpIBSkz2TD2yx7ffCCqxXzZgrAPkHDRtZh3dVMKja5XUkBnYerPM8J7J6zZrKcPraEMmp400pOEmznjM";
                break;
            default:
                $this->mode = "Test";
                $this->secretKey = "sk_test_51HS2BSLOCeRFt5luAhd9SgeUjaWhre6JwnOX1xQRym68XE9LAoFnDJptNf8TKpazfBT19AP5iO4JRWkif9aR4RZp0064LeJz5B";
                $this->publicKey = "pk_test_51HS2BSLOCeRFt5luGoTqd5ssxwJw736SARhvCyuh5reIN19qRIFg2hMEsylHIIlVVVFuFgEORgNU41LYUtA1BiIp00u78WXhM3";
        }
        \Stripe\Stripe::setApiKey($this->secretKey);
        $this->client = new \Stripe\StripeClient($this->secretKey);
    }

    function setStripeCustomer (User $user) {
        /* Find Stripe Customer ID in local DB */
        $sql = "SELECT `stripeCustomerID` FROM `stripeCustomers` LEFT JOIN `users` ON `stripeCustomers`.`userID` = `users`.`id` WHERE `users`.`email` = '{$user->email}'";
        if ($this->mode === "Test") $sql .= " AND `stripeCustomers`.`testAccount` = 1";
        $db = new Db($sql);
        if (isset($db->rows[0])) return ($this->stripeCustomerID = $db->rows[0]['stripeCustomerID']);
        /* Not found - create new Stripe Customer */
        return $this->createStripeCustomer ($user);
    }

    function createStripeCustomer (User $user) {
        $this->stripeCustomer = $this->client->customers->create(["email" => $user->email]);
        $this->stripeCustomerID = $this->stripeCustomer->id;
        $sql = "INSERT INTO `stripeCustomers` SET `userID` = :userID, `stripeCustomerID` = :stripeCustomerID, `testAccount` = :testAccount, `mode` = :mode";
        $db = new Db($sql, ["userID" => $user->id, "stripeCustomerID" => $this->stripeCustomer->id, "testAccount" => ($this->mode==="Test" ? 1 : null), "mode" => ($this->mode==="Test" ? 2 : 1)]);
        return $this->stripeCustomerID;
    }

    function getStripeCustomer (User $user) {
        $testAccount = $this->mode==="Test" ? " = 1" : " IS NULL";
        $sql = "SELECT `stripeCustomerID` FROM `stripeCustomers` WHERE `userID` = {$user->id} AND `testAccount` $testAccount AND `deleted` IS NULL";
        $rlt = Database::Execute($sql, ["userID" => $user->id,  "testAccount" => ($this->mode==="Test" ? 1 : null)]);
        return $rlt['success']['rows'][0]['stripeCustomerID'] ?? null;
    }

    function fetchStripeCustomer() {
        if ($this->stripeCustomer) return $this->stripeCustomer;
        return ($this->stripeCustomer = $this->client->customers->retrieve($this->stripeCustomerID));
    }

    function getPaymentMethods (String $stripeCustomerID = null) {
        /* Return STRING on Error, ARRAY on success */
        if (!$this->stripeCustomerID && !$stripeCustomerID) return "No Stripe User set. Cannot retrieve payment cards";
        if (!$stripeCustomerID) $stripeCustomerID = $this->stripeCustomerID;
        $sql = "SELECT * FROM `stripePaymentMethods` WHERE `stripeCustomerID` = '{$stripeCustomerID}' ORDER BY `isDefault` DESC";
        $db = new Db($sql);
        return $db->rows;
    }

    function fetchPaymentMethods (String $stripeCustomerID) {
          return $this->client->paymentMethods->all([
            'customer' => $stripeCustomerID,
            'type' => 'card',
            'limit' => 100
          ]);
    }

    function createSetupIntent(String $stripeCustomerID = null) {
        if ($stripeCustomerID) $this->stripeCustomerID = $stripeCustomerID;
        $sql = "SELECT `setupIntentID` FROM `stripeSetupIntents` WHERE `stripeCustomerID` = :stripeCustomerID AND status = 'requires_payment_method' AND `testAccount` = :testAccount ORDER BY CREATED DESC";
        $rlt = Database::Execute($sql,["stripeCustomerID" => $this->stripeCustomerID, 'testAccount' => ($this->mode === "Test") ? 1 : null]);
        /* If we have a setup intent - check it is still 'requires_payment_method' before using it */
        if ($rlt['success']['rows']) {
            $setupIntent = $this->fetchSetupIntent($rlt['success']['rows'][0]['setupIntentID']);
            if ($setupIntent->status == 'requires_payment_method') return $setupIntent;
        } 
        try {
            $this->stripeSetupIntent = $this->client->setupIntents->create([
                "customer" => $this->stripeCustomerID,
                "payment_method_types" => ['card'],
                "usage" => "off_session"
            ]);
            $sql = "INSERT INTO `stripeSetupIntents` SET `setupIntentID` = :setupIntentID, `client_secret` = :client_secret,`stripeCustomerID` = :stripeCustomerID,`paymentMethodID` = :paymentMethodID, `status` = :status, `testAccount` = :testAccount";
            Database::Execute($sql,[
                "setupIntentID" => $this->stripeSetupIntent->id,"client_secret" => $this->stripeSetupIntent->client_secret,"stripeCustomerID" => $this->stripeSetupIntent->customer,"paymentMethodID" => $this->stripeSetupIntent->payment_method, 
                'status' => $this->stripeSetupIntent->status,
                'testAccount' => ($this->mode == "Test") ? 1 : null,
            ]);
            return $this->stripeSetupIntent;
        } catch (Exception $e) {
            return (String)$e->getMessage();
        }
    }

    function savePaymentMethod ($stripePaymentMethod) {
        /* Remove any pre-existing defaults - this new Payment Method will be new default */
        new Db("UPDATE `stripePaymentMethods` SET `isDefault` = NULL WHERE `stripeCustomerID` = '{$stripePaymentMethod->customer}'");
        $sql = "INSERT IGNORE INTO `stripePaymentMethods` SET `stripePaymentMethodID` = :stripePaymentMethodID, `stripeCustomerID` = :stripeCustomerID, `brand` = :brand, `exp_month` = :exp_month, `exp_year` = :exp_year, `funding` = :funding, `last4` = :last4, `isDefault` = 1";
        $db = new Db($sql, [
            "stripePaymentMethodID" => $stripePaymentMethod->id,
            "stripeCustomerID" => $stripePaymentMethod->customer,
            "brand" => $stripePaymentMethod->card->brand,
            "exp_month" => $stripePaymentMethod->card->exp_month,
            "exp_year" => $stripePaymentMethod->card->exp_year,
            "funding" => $stripePaymentMethod->card->funding,
            "last4" => $stripePaymentMethod->card->last4,
        ]);
    }

    function fetchSetupIntent (String $setupIntentID, Bool $forceSave = false) {
        $setupIntent = $this->client->setupIntents->retrieve($setupIntentID);
        $sql = "UPDATE `stripeSetupIntents` SET `status` = :status, `paymentMethodID` = :paymentMethodID WHERE setupIntentID = :setupIntentID";
        $rlt = Database::Execute($sql,["status" => $setupIntent->status, "paymentMethodID" => $setupIntent->payment_method, "setupIntentID" => $setupIntent->id]);
        if (($forceSave === true || $rlt["success"]["rowCount"]) && $setupIntent->payment_method) $this->fetchPaymentMethod($setupIntent->payment_method);
        // return \Stripe\SetupIntent::retrieve($setupIntentID);
        return $setupIntent;
    }

    function fetchPaymentMethod (String $paymentMethodID) {
        $pm = $this->client->paymentMethods->retrieve($paymentMethodID);
        $sql = "INSERT INTO stripePaymentMethods SET stripePaymentMethodID = :stripePaymentMethodID, stripeCustomerID = :stripeCustomerID, brand = :brand, exp_month = :exp_month, exp_year = :exp_year, funding = :funding, last4 = :last4, testAccount = :testAccount ON DUPLICATE KEY UPDATE stripeCustomerID = :stripeCustomerID, brand = :brand, exp_month = :exp_month, exp_year = :exp_year, funding = :funding, last4 = :last4, testAccount = :testAccount";
        $rlt = Database::Execute($sql,[
            "stripePaymentMethodID" => $pm->id,
            "stripeCustomerID" => $pm->customer,
            "brand" => $pm->card->brand, 
            "exp_month" => $pm->card->exp_month,
            "exp_year"  => $pm->card->exp_year,
            "funding"  => $pm->card->funding,
            "last4" => $pm->card->last4,
            "testAccount" => ($this->mode=="Test" ? 1 : null)
        ]);
        return $pm;
    }

    function addPaymentCardForm (String $formAction = null, String $successRedirect = null) {
        if (!$formAction) $formAction = $_SERVER['REQUEST_URI'];
        if (!$successRedirect) $successRedirect = $_SERVER['REQUEST_URI'];
        if (!$this->stripeSetupIntent) $this->createSetupIntent();
        return <<<HTML
            <form action="$formAction" id="payment-form" class="mx-2">
                <h3>Card Details</h3>
                <p>Please enter the details for your card below.</p>
                <div id="failureResult" class="bg-danger text-light d-none p-3 my-2"></div>
                <div id="card-element"></div>
                <button data-secret="{$this->stripeSetupIntent->client_secret}" type="button" id="card-button" class="btn btn-purple my-4">Save Card</button>
                <p title="{$this->stripeSetupIntent->id}">Ref: {$this->stripeSetupIntent->id}</p>
            </form>
            <script>
                var stripe = Stripe('{$this->publicKey}');
                var elements = stripe.elements();
                var cardElement = elements.create('card');
                cardElement.mount('#card-element');
    
                var cardholderName = document.getElementById('cardholder-name');
                var cardButton = document.getElementById('card-button');
                var clientSecret = cardButton.dataset.secret;
    
                cardButton.addEventListener('click', function(ev) {
                    stripe.confirmCardSetup(
                        clientSecret,{payment_method: {card: cardElement},}
                    ).then(function(result) {
                        if (result.error) {
                            document.getElementById("failureResult").innerHTML = "<b>Sorry.</b> We could not authenticate those card details. You can try again, try another card or contact your Co-ordinator for more help.<br>The message we received back from the card provider reads " + result.error.message;
                            document.getElementById("failureResult").classList.remove("d-none");
                        } else {
                            window.location.replace("$successRedirect?si={$this->stripeSetupIntent->id}");
                        }
                    });
                });
            </script>
        HTML;
    }

    function removePaymentCard (String $stripePaymentMethodID) {
        new Db("UPDATE `stripePaymentMethods` SET `deleted` = NOW() WHERE `stripePaymentMethodID` = '$stripePaymentMethodID'");
        $this->client->paymentMethods->detach($stripePaymentMethodID);
    }

    function fetchPaymentCards () {
        return \Stripe\PaymentMethod::all([
            'customer' => $this->stripeCustomer->id,
            'type' => 'card',
          ]);
    }

    function getPaymentMethod (String $paymentMethodID) {
        $sql = "SELECT * FROM `stripePaymentMethods` WHERE `stripePaymentMethodID` = :stripePaymentMethodID";

    }

    /* Payment Intents */
    function createPaymentIntent (Float $amount, String $description, String $stripePaymentMethodID = null, Array $metadata = [], Bool $immediate = false) {
        /* Returns PaymentIntent OBJECT on success, STRING on failure */
        $stripeData = [
            'amount' => ($amount*100),
            'currency' => 'gbp',
            'description' => $description,
            'capture_method' => ($immediate === true) ? 'automatic' : 'manual',
        ];
        if ($this->stripeCustomerID) $stripeData['customer'] = $this->stripeCustomerID;
        if ($stripePaymentMethodID) $stripeData['payment_method'] = $stripePaymentMethodID;
        if ($metadata) $stripeData['metadata'] = $metadata;
        try {
            $paymentIntent = $this->client->paymentIntents->create($stripeData);
            $this->savePaymentIntent ($paymentIntent);
            return $paymentIntent;
        } catch (Exception $e) {
            return (String) $e->getMessage();
        }
    }

    function CreatePI  (Float $amount, String $description, String $stripeCustomerID, String $stripePaymentMethodID = null) {
        $stripeData = [
            'amount' => ($amount*100),
            'payment_method' => $stripePaymentMethodID,
            'customer' => $stripeCustomerID,
            'currency' => 'gbp',
            'description' => $description,
            'capture_method' => 'manual',
        ];
        try {
            $paymentIntent = $this->client->paymentIntents->create($stripeData);
            $this->savePaymentIntent ($paymentIntent);
            return $paymentIntent;
        } catch (Exception $e) {
            return (String) $e->getMessage();
        }
    }

    function getPaymentIntent (String $stripePaymentIntentID) {
        $db = new Db("SELECT * FROM `stripePaymentIntents` WHERE `stripePaymentIntentID` = '$stripePaymentIntentID'");
        if (isset($db->rows)) return $db->rows[0];
    }

    function fetchPaymentIntent (String $stripePaymentIntentID) {
        $stripePaymentIntent = $this->client->paymentIntents->retrieve($stripePaymentIntentID);
        $this->savePaymentIntent ($stripePaymentIntent);
        return $stripePaymentIntent;
    }

    function updatePaymentIntent (String $stripePaymentIntentID, Array $data = []) {
        if (!$data) return;
        if (isset($data['amount'])) $data['amount'] *= 100;
        $stripePaymentIntent = $this->client->paymentIntents->update($stripePaymentIntentID,
            [$data]
        );
        $this->savePaymentIntent ($stripePaymentIntent);
    }

    function savePaymentIntent ($stripePaymentIntent) {
        $sql = "INSERT INTO `stripePaymentIntents` SET `stripePaymentIntentID` = :stripePaymentIntentID, `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `stripePaymentMethodID` = :stripePaymentMethodID, `amount` = :amount ON DUPLICATE KEY UPDATE `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `amount` = :amount";
        return new Db($sql,[
            "stripePaymentIntentID" => $stripePaymentIntent->id,
            "stripePaymentIntentStatus" => $stripePaymentIntent->status,
            "stripePaymentMethodID" => $stripePaymentIntent->payment_method,
            "amount" => ($stripePaymentIntent->amount / 100),
        ]);
    }

    function refundPaymentIntent (String $stripePaymentIntentID, Array $options = []) {
        $data = ['payment_intent' => $stripePaymentIntentID, "reason" => 'requested_by_customer'];
        if (isset($options['amount']) && $options['amount']) $data['amount'] = $options['amount'];
        try {
            $refund = $this->client->refunds->create($data);
            new Db("UPDATE `stripePaymentIntents` SET `refunded` = `refunded` + {$refund->amount} WHERE `stripePaymentIntentID` = '$stripePaymentIntentID'");
            return $refund;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    static function Confirmable() {
        $sql = "SELECT * FROM `stripePaymentIntents` WHERE `stripePaymentIntentStatus` = 'requires_confirmation'";
        $db = new Db($sql);
        if (!$db->rows) return;
        $stripe = new static();
        foreach ($db->rows as $r) {
            $pi = $stripe->client->paymentIntents->confirm($r['stripePaymentIntentID']);
            $stripe->savePaymentIntent($pi);
            $return[] = $pi;
        }
        return $return;
    }

    static function Capturable() {
        $sql = "SELECT * FROM `stripePaymentIntents` WHERE `stripePaymentIntentStatus` = 'requires_capture'";
        $db = new Db($sql);
        if (!$db->rows) return;
        $stripe = new static();
        foreach ($db->rows as $r) {
            $capture = ($r['captureAmount'] != $r['amount']) ? ["amount_to_capture" => ($r['captureAmount']*100)] : null;
            $pi = $stripe->client->paymentIntents->capture($r['stripePaymentIntentID'],$capture);
            $stripe->savePaymentIntent($pi);
            $return[] = $pi;
        }
        return $return;
    }

    static function Statuses() {
        return [
            "requires_payment_method" => ["editable" => true],
            "requires_confirmation" => ["editable" => false],
            "requires_action" => ["editable" => true],
            "processing" => ["editable" => false],
            "requires_capture" => ["editable" => false], 
            "canceled" => ["editable" => false],
            "succeeded" => ["editable" => false]
        ];
    }

    static function BillTeam (Team $team, Float $amount) {
        $team->getTeamManagers();
        $stripe = new static(true);
        $stripePaymentMethod = $team->paymentMethod();
        $pi = $stripe->CreatePI($amount,$team->fullName(),$stripe->getStripeCustomer($team->treasurer),$stripePaymentMethod);
        $sql = "INSERT INTO stripePayments SET stripePaymentIntentID = :stripePaymentIntentID, stripePaymentIntentStatus = :stripePaymentIntentStatus, stripePaymentMethod = :stripePaymentMethod, teamID = :teamID, total = :total";
        Database::Execute($sql,[
            "stripePaymentIntentID" => $pi->id,
            "stripePaymentIntentStatus" => $pi->status,
            "stripePaymentMethod" => $stripePaymentMethod,
            "teamID" => $team->id,
            "total" => $amount
        ]);
        return $pi;
    }

    static function GetUserByStripeID (String $stripeCustomerID) {
        $sql = "SELECT `userID` FROM `stripeCustomers` WHERE `stripeCustomerID` = :stripeCustomerID";
        $rlt = Database::Execute($sql,["stripeCustomerID" => $stripeCustomerID]);
        return $rlt['success']['rows'][0]['userID'] ?? null;
    }

}