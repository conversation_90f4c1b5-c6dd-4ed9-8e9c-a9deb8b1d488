<?php

class TasterSession extends Base {

    protected $dbTable = "tasterSession";
    protected $dbFields = [
        "cancelled",
        "refunded",
        "venueID",
        "sportID",
        "date",
        "time",
        "charge",
        "notes",
        "coordinatorID",
        "coordinatorName",
        "coordinatorEmail",
        "CoordinatorTel",
        "fullyBooked",
        "maxAttendees",
        "fbPageLink",
        "videoLink",
        "slug",
        'location',
        "is_wildcard",
        "is_tournament",
        "tournamentName",
        "numberOfWeeks",
    ];

    protected $venueID, $sportID, $date, $time, $charge, $notes;
    protected $productID = 1;
    protected $productName = "Couch2Court";
    protected $coordinatorID;
    protected $cancelled;
    protected $refunded;
    protected $fullyBooked;
    protected $maxAttendees;
    protected $is_wildcard;
    protected $is_tournament;
    protected $tournamentName;
    protected $numberOfWeeks;

    protected $venue, $sport, $coordinator;

    function __toString() {
        $return = "";
        $this->getVenue();
        if ($this->venue->name) $return .= " {$this->venue->name}";
        if ($this->date) $return .= " on " . date('jS F Y', strtotime($this->date));
        if ($this->time) $return .= " at " . date('g:ia', strtotime($this->time));
        return $return;
    }

    function attendeeCount() {
        return C2C_Booking::Total($this);
        // return TasterAttendee::Total($this);
    }

    function attendees() {
        /* Returns User objects */
        return C2C_Booking::Attendees($this);
        // return TasterAttendee::Taster($this);
    }

    function refundAttendees() {
        $bookers = C2C_Booking::Bookers($this);
        // return $bookers;
        // $attendees = $this->attendees();
        // if (!$attendees) return;
        if (!$bookers) return;
        $stripe = new Stripe_v2(true);
        $return = [];
        // foreach ($attendees as $attendee) {
        foreach ($bookers as $booker) {
            // $booker->getUser();
            // $rlt = $stripe->refundPaymentIntent($booker->stripePaymentIntentID);
            // $msg = "Refund {$booker->user} against {$booker->stripePaymentIntentID} ";
            // $msg .= ($rlt === true) ? "successful" : " failed $rlt";
            // $rlt = true;
            // if ($rlt === true) $return['cancellations'][] = $booker->SendCancellation();
            $return[] = $booker->Refund();
        }
        return $return;
    }

    function getAttendees() {
        /* Return C2C_Bookings */
        return C2C_Booking::Taster($this);
    }

    function formatDate() {
        return date('jS F Y', strtotime($this->date));
    }

    function formatTime() {
        return date("g:ia", strtotime($this->time));
    }

    function formatVenue(String $separator = ", ") {
        $this->getVenue();
        if (!$this->venue) return;
        return implode($separator, [$this->venue->name, $this->venue->address1, $this->venue->address2, $this->venue->town, strtoupper($this->venue->postcode)]);
    }

    function getVenue() {
        if (!$this->venueID) return;
        if (!$this->venue) $this->venue = new Venue($this->venueID);
        return $this->venue;
    }

    function getSport() {
        if (!$this->sportID) return;
        if (!$this->sport) $this->sport = new Sport($this->sportID);
        return $this->sport;
    }

    function getCoordinator() {
        if (!$this->coordinatorID) return;
        if (!$this->coordinator) $this->coordinator = new User($this->coordinatorID);
        return $this->coordinator;
    }

    function ApiOutput() {
        $this->getVenue();
        $sport = $this->getSport();
        $this->productName = $this->sport->tasterSessionName;
        $this->getCoordinator();
        $this->productName .= " {$this->venue->name} at " . date('g:ia \o\n jS F', strtotime($this->date . " " . $this->time));
        return [
            "id" => $this->id,
            "name" => $this->productName,
            "venue" => ($this->venue) ? $this->venue->ApiData() : null,
            "sport" => ($this->sport) ? $this->sport->ApiOutput() : null,
            "timing" => [
                "date" => $this->date,
                "time" => $this->time,
                "dateFormatted" => $this->formatDate(),
                "timeFormatted" => $this->formatTime()
            ],
            "charges" => [
                "cost" => $this->charge
            ],
            "coordinator" => [
                "name" => ($this->coordinator) ? "{$this->coordinator->firstname} {$this->coordinator->lastname}" : null,
                "email" => ($this->coordinator) ? $this->coordinator->email : null,
                "tel" => ($this->coordinator) ? $this->coordinator->mobile : null
            ],
            "fullyBooked" => ($this->fullyBooked) ? 1 : null,
            "slug" => $this->slug ?? '',
            "tournamentName" => $this->tournamentName ?? '',
            "numberOfWeeks" => $this->numberOfWeeks ?? '',
        ];
    }

    function ApiData() {
        $venue = $this->getVenue();
        $sport = $this->getSport();
        $this->productName = $this->sport->tasterSessionName;
        $name = $this->is_tournament ? $this->tournamentName : $this->productName;
        if ($this->venue) $name .= " {$this->venue->name} at " . date('g:ia \o\n jS F', strtotime($this->date . " " . $this->time));

        return [
            "id" => $this->id,
            "name" => $name,
            "date" => date('d/m/Y', strtotime($this->date)),
            "time" => date("g:ia", strtotime($this->time)),
            "cancelled" => $this->cancelled,
            "fullyBooked" => $this->fullyBooked,
            "venue" => ($this->venue) ? [
                "name" => $this->venue->name,
                "facilities" => $this->venue->getFaciities(),
                "address1" => $this->venue->address1,
                "address2" => $this->venue->address2,
                "town" => $this->venue->town,
                "postcode" => $this->venue->postcode,
                "name" => $this->venue->name,
            ] : [],

        ];
    }

    function ApiBasic() {
        return [
            'id' => $this->id,
            'sportID' => $this->sportID,
            'date' => $this->date,
            'time' => $this->time,
            'charge' => $this->charge
        ];
    }

    function toJson() {
        if (!$this->sport) $this->sport = new Sport($this->sportID);
        if (!$this->venue) $this->venue = new Venue($this->venueID);
        $this->formattedDate = date('d/m/Y', strtotime($this->date));
        $this->productName .= " {$this->venue->name} at " . date('g:ia \o\n jS F', strtotime($this->date . " " . $this->time));
        $this->coordinator = ($this->coordinatorID) ? new User($this->coordinatorID) : null;
        return json_encode([
            "id" => $this->id,
            "name" => $this->productName,
            "venue" => $this->venue->ApiData(),
            "sport" => $this->sport->ApiOutput(),
            "timing" => [
                "date" => $this->date,
                "time" => $this->time,
                "dateFormatted" => $this->formatDate(),
                "timeFormatted" => $this->formatTime()
            ],
            "charges" => [
                "cost" => $this->charge
            ],
            "coordinator" => [
                "name" => ($this->coordinator) ? "{$this->coordinator->firstname} {$this->coordinator->lastname}" : null,
                "email" => ($this->coordinator) ? $this->coordinator->email : null,
                "tel" => ($this->coordinator) ? $this->coordinator->mobile : null
            ],
            "fullyBooked" => ($this->fullyBooked) ? 1 : null,
        ]);
    }

    function Cancel() {
        $return = ["success" => null, "error" => null];
        if ($this->cancelled) {
            $return["error"] = "Already cancelled";
        } else {
            $this->cancelled = date('Y-m-d H:i:s');
            $this->Save();
            $bookers = C2C_Booking::Bookers($this);
            if ($bookers) {
                $sentUserIDs = [];
                foreach ($bookers as $booker) {
                    if (in_array($booker->userID, $sentUserIDs)) continue;
                    $booker->Cancel();
                    $sentUserIDs[] = $booker->userID;
                }
            }
            $return['success'] = $this->ApiData();
        }
        return $return;
    }

    function Refund() {
        $return = ["success" => null, "error" => null];
        if ($this->refunded) {
            $return["error"] = "Already refunded";
        } else {
            $this->refunded = date('Y-m-d H:i:s');
            $this->Save();
            $bookers = C2C_Booking::Bookers($this);
            if (!$bookers) return ["success" => "Nothing to refund"];
            $stripe = new Stripe_v2(true);
            foreach ($bookers as $booker) {
                $return["refunds"][] = $booker->Refund();
            }
        }
        return $return;
    }

    function StatusCheck() {
        $bookers = C2C_Booking::Bookers($this);
        if (!$bookers) return;
        foreach ($bookers as $c2c_booking) {
            $return[] = $c2c_booking->CheckPaymentStatus();
        }
        return $return;
    }

    static function Venue(Venue $venue, Bool $liveOnly = false) {
        if (!$venue->id) return [];
        $sql = "SELECT * FROM tasterSession WHERE venueID = {$venue->id} AND deleted IS NULL";
        if ($liveOnly === true) $sql .= " AND date >= '" . date('Y-m-d') . "'";
        $sql .= " ORDER BY date DESC";
        return static::Query($sql);
    }

    static function Sport(array $sportIds, string $startDate = null, bool $excludeCanceled = false) {
        if (empty($sportIds)) {
            return [];
        }

        $startDate ??= date('Y-m-d');

        $placeholders = implode(',', array_fill(0, count($sportIds), '?'));

        $sql = "
            SELECT t.*
            FROM tasterSession t
            INNER JOIN venues v ON t.venueID = v.id
            WHERE t.sportID IN ({$placeholders})
              AND t.date IS NOT NULL
              AND t.date >= ?
              AND (t.is_wildcard IS NULL OR t.is_wildcard != 1)
              AND (t.is_tournament IS NULL OR t.is_tournament != 1)
        ";

        if (!$excludeCanceled) {
            $sql .= " AND t.deleted IS NULL";
        }

        $sql .= " ORDER BY t.date ASC";

        $params = [...$sportIds, $startDate];

        return static::Query($sql, $params);
    }


    static function Available(String $from = null, ?String $sessionType = null, ?Int $coordinatorID = null) {
        if (!$from) $from = date('Y-m-d');

        $sql = "SELECT * FROM `tasterSession` WHERE `date` >= '$from' AND `deleted` IS NULL";

        // Filter by session type
        if ($sessionType === 'wildcard') {
            $sql .= " AND `is_wildcard` = 1";
        } elseif ($sessionType === 'tournament') {
            $sql .= " AND `is_tournament` = 1";
        } elseif ($sessionType === 'couch2court') {
            $sql .= " AND (`is_wildcard` IS NULL OR `is_wildcard` = 0) AND (`is_tournament` IS NULL OR `is_tournament` = 0)";
        }

        // Filter by coordinator
        if ($coordinatorID) {
            $sql .= " AND `coordinatorID` = $coordinatorID";
        }

        $sql .= " ORDER BY `date`";

        return static::Query($sql);
    }



    static function Coordinator(User $user, String $from = null) {
        if (!$from) $from = date('Y-m-d');
        $sql = "SELECT * FROM `tasterSession` WHERE `date` >= '$from' AND `deleted` IS NULL";
        if (!$user->isManager) $sql .= " AND `coordinatorID` = {$user->id}";
        $sql .= " ORDER BY `date`";
        return static::Query($sql);
    }

    static function CheckStatuses() {
        $tasters = static::Available();
        foreach ($tasters as $taster) $taster->StatusCheck();
    }

    function Booking(array $booker, array $attendees = [], String $paymentIntentID = null) {
        # Check if TasterID exists and available
        # Check received Firstname, Lastname, EMail, MObile for Booker
        # FetchCreate User for Booker
    }

    static function WildCard(array $sportIds, String $startDate = null, Bool $excludeCanceled = false) {
        return static::getSpecialSessions($sportIds, 'is_wildcard', $startDate, $excludeCanceled);
    }

    static function Tournament(array $sportIds, String $startDate = null, Bool $excludeCanceled = false) {
        return static::getSpecialSessions($sportIds, 'is_tournament', $startDate, $excludeCanceled);
    }

    /**
     * Get special sessions (WildCard or Tournament) based on type
     * @param array $sportIds Array of sport IDs to filter by
     * @param string $type The type of session ('is_wildcard' or 'is_tournament')
     * @param string|null $startDate Optional start date filter
     * @param bool $excludeCanceled Whether to exclude canceled sessions
     * @return array Array of TasterSession objects
     */
    private static function getSpecialSessions(array $sportIds, string $type, String $startDate = null, Bool $excludeCanceled = false) {
        if (empty($sportIds)) {
            return [];
        }

        if (!$startDate) $startDate = date('Y-m-d');

        $placeholders = implode(',', array_fill(0, count($sportIds), '?'));

        $sql = "SELECT 
                    tasterSession.*  
                FROM 
                    `tasterSession`
                INNER JOIN 
                    `venues` ON tasterSession.venueID = venues.id
                WHERE 
                    tasterSession.sportID IN ({$placeholders})
                    AND tasterSession.{$type} = 1 
                    AND tasterSession.`date` IS NOT NULL 
                    AND tasterSession.`date` >= ?";

        if ($excludeCanceled !== true) {
            $sql .= " AND tasterSession.deleted IS NULL";
        }

        $sql .= " ORDER BY date ASC";

        $params = [...$sportIds, $startDate];

        return static::Query($sql, $params);
    }

    public static function Details($slug) {
        $sql = "SELECT ts.date, ts.time, ts.charge, ts.location, ts.fbPageLink, ts.fullyBooked,
                u.firstname, u.profilePictureUrl, u.email, u.bio,
                 v.address1, v.town, v.postcode, v.name,
                l.playerRequestGroup AS league_fb_group
                FROM tasterSession ts
                INNER JOIN users u ON ts.coordinatorID = u.id
                INNER JOIN venues v ON ts.venueID = v.id
                LEFT JOIN leagues l ON ts.venueID = l.defaultVenueID
                WHERE ts.slug = :slug";

        $result = static::Query($sql, ["slug" => $slug]);

        return empty($result) ? null : $result[0];
    }

    public function isWildCard() {
        return $this->is_wildcard;
    }

    public function isFullyBooked() {
        return $this->fullyBooked;
    }

    public function getSessionType() {
        if ($this->is_wildcard) {
            return 'Wildcard';
        } elseif ($this->is_tournament) {
            return 'Tournament';
        } else {
            return 'Couch2Court';
        }
    }
}
