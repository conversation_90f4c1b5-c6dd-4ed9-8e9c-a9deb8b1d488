<?php

class Jwt extends Base {

    protected $dbTable = "jwt";
    protected $userID, $expiry, $ip, $agent, $counter;
    protected $dbFields = ["userID", "expiry", "ip", "agent", "counter"];

    private $secret = "okHfjuhagLJKJf39r3j3jn5l3rfasu0uf0a9sufkl344j8dhgf9alf9eh38f29fj";
    protected $signature;

    function __construct(Int $id = null) {
        if ($id) parent::__construct($id);
    }

    function Sql() {
        return [
            "id" => ["type" => "int(10) unsigned", "pk" => true],
            "created" => ["type" => "datetime", "default" => "current_timestamp()"],
            "updated" => ["type" => "datetime", "default" => "current_timestamp()", "extra" => "on update current_timestamp()"],
            "deleted" => ["type" => "datetime"],
            "expiry" => ["type" => "datetime"],
            "userID" => ["type" => "int(10) unsigned"],
            "ip" => ["type" => "varchar(20)"],
            "agent" => ["type" => "text"],
            "counter" => ["type" => "mediumint (9) unsigned"]
        ];
    }

    function __toString() {
        return $this->header() . "." . $this->payload() . "." . $this->signature();
    }

    function header() {
        return $this->encode(json_encode([
            'typ' => 'JWT',
            'alg' => 'HS256'
        ]));
    }

    function encode(String $string) {
        return $this->base64UrlEncode($string);
    }

    function Save() {
        if (!$this->expiry) $this->expiry = date('Y-m-d H:i:s', strtotime("+14 days"));
        parent::Save();
    }

    function payload() {
        return $this->base64UrlEncode(json_encode(["id" => $this->id]));
    }

    function signature() {
        return $this->base64UrlEncode($this->wrapEncode($this->header() . "." . $this->payload()));
    }

    function wrapEncode(String $string) {
        return hash_hmac('sha256', $string, $this->secret, true);
    }

    function base64UrlEncode(String $string) {
        return str_replace(
            ['+', '/', '='],
            ['-', '_', ''],
            base64_encode($string)
        );
    }

    static function Create(User $user, array $server = []) {
        $jwt = new static();
        $jwt->userID = $user->id;
        $jwt->expiry = date('Y-m-d H:i:s', strtotime("+14 days"));
        if (isset($_SERVER['REMOTE_ADDR'])) $jwt->ip = $_SERVER['REMOTE_ADDR'];
        if (isset($_SERVER['HTTP_USER_AGENT'])) $jwt->agent = $_SERVER['HTTP_USER_AGENT'];
        $rlt = $jwt->Save();
        if ($jwt->id) return $jwt->__toString();
    }
    static function Setup(User $user, array $server = []) {
        return static::Create($user, $server);
    }

    static function Validate(String $jwt) {
        $tokenParts = explode('.', $jwt);
        if (!is_array($tokenParts) || count($tokenParts) != 3) return "Invalid Token";
        $header = base64_decode($tokenParts[0]);
        $payload = base64_decode($tokenParts[1]);
        $payloadDecoded = json_decode($payload);
        if (!isset($payloadDecoded->id) || !$payloadDecoded->id) return "Malformed";
        $signatureProvided = $tokenParts[2];
        $obj = new static($payloadDecoded->id);
        if ($signatureProvided !== $obj->encode($obj->wrapEncode($obj->encode($header) . "." . $obj->encode($payload)))) return "Invalid";
        if ($obj->deleted) return "Deleted";
        if (empty($obj->expiry) || strtotime($obj->expiry) < time()) return "Expired";
        $obj->counter++;
        $obj->Save();
        return (int)$obj->userID;
    }

    static function Login(String $jwt) {
        if (is_string(($response = static::Validate($jwt)))) return $response;
        $user = new User($response);
        $user->Login(false);
        return true;
    }

    static function Logout(Int $userID) {
        $sql = "UPDATE `jwt` SET `deleted` = NOW() WHERE `userID` = $userID AND `deleted` IS NULL";
        Logging::Add($sql);
        new Db($sql);
        static::CookieRemove();
    }

    static function CookieStore(String $jwt) {
        setcookie("jwt", $jwt, time() + 1209600, "/", ".leagues4you.co.uk", true, false);
    }

    static function CookieRemove() {
        setcookie('jwt', '', -1, '/', '.leagues4you.co.uk', true);
    }
}
