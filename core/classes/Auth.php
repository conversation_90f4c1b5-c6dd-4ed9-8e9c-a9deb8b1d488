<?php

class Auth extends Base {
    private const TOKEN_EXPIRY_DAYS = 30;
    private const TOKEN_LENGTH = 32;

    /**
     * Validates user credentials
     * @param User $user
     * @param string $submittedPassword
     * @return bool
     */
    public static function CheckPassword(User $user, string $submittedPassword): bool {
        return $user->Authenticate($submittedPassword);
    }

    /**
     * Checks if user has developer access
     * @param User $user
     * @return bool
     */
    public static function isAuthor(User $user): bool {
        if (!$user || !$user->email) {
            return false;
        }
        return (bool)strpos($user->email, "@a2ztech.co.uk");
    }

    /**
     * Validates a device token for a given user
     * @param int $userId
     * @param string $token
     * @return bool
     * @throws Exception on database error
     */
    public static function ValidateDeviceToken(int $userId, string $token): bool {
        try {
            // Clean expired tokens first
            self::cleanExpiredTokens();

            // Check for valid token
            $sql = "SELECT id 
                    FROM device_tokens 
                    WHERE user_id = :userId 
                    AND token = :token 
                    AND expires_at > NOW()
                    LIMIT 1";

            $db = new Db($sql, [
                "userId" => $userId,
                "token" => $token
            ]);

            if (!$db->rows) {
                return false;
            }

            // Update last used timestamp
            self::updateTokenUsage($userId, $token);

            return true;
        } catch (Exception $e) {
            Logging::Add("Device token validation failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Creates a new device token for a user
     * @param int $userId
     * @return string The generated token
     * @throws Exception on token generation or database error
     */
    public static function CreateDeviceToken(int $userId): string {
        try {
            $token = self::generateSecureToken();

            $sql = "INSERT INTO device_tokens 
                    (user_id, token, expires_at) 
                    VALUES 
                    (:userId, :token, DATE_ADD(NOW(), INTERVAL :days DAY))";

            new Db($sql, [
                "userId" => $userId,
                "token" => $token,
                "days" => self::TOKEN_EXPIRY_DAYS
            ]);

            return $token;
        } catch (Exception $e) {
            Logging::Add("Device token creation failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Revokes all device tokens for a user
     * @param int $userId
     * @return bool
     */
    public static function RevokeAllDeviceTokens(int $userId): bool {
        try {
            $sql = "DELETE FROM device_tokens WHERE user_id = :userId";
            $db = new Db($sql, ["userId" => $userId]);
            return $db->affectedRows > 0;
        } catch (Exception $e) {
            Logging::Add("Failed to revoke device tokens: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generates a cryptographically secure token
     * @return string
     * @throws Exception if random_bytes fails
     */
    private static function generateSecureToken(): string {
        return bin2hex(random_bytes(self::TOKEN_LENGTH));
    }

    /**
     * Removes expired tokens from database
     * @return void
     */
    private static function cleanExpiredTokens(): void {
        try {
            $sql = "DELETE FROM device_tokens WHERE expires_at < NOW()";
            new Db($sql);
        } catch (Exception $e) {
            Logging::Add("Failed to clean expired tokens: " . $e->getMessage());
        }
    }

    /**
     * Updates the last_used_at timestamp for a token
     * @param int $userId
     * @param string $token
     * @return void
     */
    private static function updateTokenUsage(int $userId, string $token): void {
        try {
            $sql = "UPDATE device_tokens 
                    SET last_used_at = NOW() 
                    WHERE user_id = :userId 
                    AND token = :token";

            new Db($sql, [
                "userId" => $userId,
                "token" => $token
            ]);
        } catch (Exception $e) {
            Logging::Add("Failed to update token usage: " . $e->getMessage());
        }
    }
}
