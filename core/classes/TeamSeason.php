<?php
/**
 * Teams can request to access a Season
 * Coordinatros can invite a team to be in a Season
 * 3 methods a Team can participate in a Season
 * 1. Requested by Team then Accepted by Coordinator
 * 2. Invited by Coordinator then Accepted by Team
 * 3. Coordinator overrides and Team immediately placed into Season
 */
class TeamSeason extends Base {

    protected $dbTable = "teamSeasons";
    protected $dbFields = ["teamID","wildcard","captainID","treasurerID","seasonID","divisionID","statusID","requested","invited","accepted"];

    # Properties
    protected $teamID, $wildcard;
    protected $captainID, $treasurerID;
    protected $seasonID, $divisionID;
    protected $statusID;
    protected $requested, $invited, $accepted;

    # Fetched
    protected $league, $season, $team;

    # Calculated
    protected $invoice_url;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function CalculateTotals() {

    }

    function ApiData() {
        return [
            'id' => (int)$this->id,
            'teamID' => (int)$this->teamID,
            'wildcard' => $this->wildcard,
            "seasonID" => (int)$this->seasonID,
            "divisionID" => (int)$this->divisionID
        ];
    }

    function getLeague() {
        $this->getSeason();
        if (!$this->season) return;
        if (!$this->league) $this->league = new League($this->season->leagueID);
        return $this->league;
    }

    function getSeason() {
        if (!$this->season) $this->season = new Season($this->seasonID);
        return $this->season;
    }

    function getTeam() {
        if (!$this->team) $this->team = new Team($this->teamID);
        return $this->team;
    }

    function getTeamManagers() {
        if (!$this->team) $this->team = new Team($this->teamID);
        if (!$this->team) return;
        return $this->team->getTeamManagers();
    }

    function getInvoiceURL() {
        if (!$this->id) return;
        if (!$this->invoice_url) {
            $this->invoice_url = "https://lockerroom.bloomnetball.co.uk/download.php?documentType=seasoninvoice&documentID={$this->id}";
        }
        return $this->invoice_url;
    }

    function __toString() {
        $this->getSeason();$this->getTeam();
        return "{$this->team} - {$this->season}";
    }

    function FixtureCount() {
        $sql = "SELECT COUNT(`fixtures`.`id`) AS `total` FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `fixtures`.`deleted` IS NULL AND `divisions`.`seasonID` = {$this->seasonID} AND (`fixtures`.`home` = {$this->teamID} OR `fixtures`.`away` = {$this->teamID})";
        $db = new Db($sql);
        return (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : null;
    }

    function ConfirmationEmail () {
        $this->getLeague();
        $this->getTeam();
        $managers = TeamFollower::Current($this->team);
        // $captainsPack = $this->league->getCaptainsPack();
        $coordinator = $this->league->getCoordinator();
        $this->getSeason();
        $this->season->getVenue();
        $this->team->getTeamManagers();
        // return $team;
        $message[] = "Hi " . $this->team->captain->firstname;
        $message[] = "Thank you for entering {$this->league}.";
        $message[] = "Please find a guide to our leagues <a href=\"https://cdn.bloomnetball.co.uk/netball-squad-guide\">here</a>, we really recommend giving this a read and sharing it with your teammates. It can also be found in the Locker Room. It's nothing heavy but does contain information you need to know.";
        $message[] = "Below are the details that you've submitted to us:";
        $message[] = "Team Name: {$this->team->name}";
        $message[] = "League Name: {$this->league->name}";
        $message[] = "Venue: ".trim($this->season->venue->name).", ".trim($this->season->venue->town).", {$this->season->venue->postcode}";
        $message[] = "Start Date: " . date('d/m/Y',strtotime($this->season->launchDate));
        $message[] = "Fixture cost: " . iconv('utf-8', 'cp1252', "£") . "{$this->season->fixtureCharge} (This is billed to your card).";
        $message[] = "Match Official cost: " . iconv('utf-8', 'cp1252', "£") . "{$this->season->officialCharge} (This is paid directly to the officials before each fixture).";
        $message[] = "You can find a copy of our terms and conditions <a href=\"https://cdn.bloomnetball.co.uk/netball-terms-and-conditions\">here</a> as well as in the Locker Room";
        $message[] = "Your fixtures for the forthcoming season will be released a few days before the league launch date. You'll be able to find these, together with tables and results and pretty much anything else to do with the league in the Locker Room.  It's a great idea for your teammates to sign up to the Locker Room so they can see all the information too.";
        $message[] = "Your local coordinator's name is {$coordinator->firstname} {$coordinator->lastname}. Your coordinator will be in touch with you in the next couple of days to introduce themselves to you, but if you need to you can reach your coordinator by emailing {$coordinator->email}.";
        $message[] = "We are super chuffed you've decided to play in our leagues, and we know you're going to absolutely rock!";
        $message[] = "We wish you the very best for the season ahead.";
        $message[] = "Team Bloom Netball";
        // $cc = $bcc = [];
        $captain = array_shift($managers)->getUser();
        $to = [$captain->email => $captain->__toString()];
        if ($managers) {
            $treasurer = array_shift($managers)->getUser();
            $cc[$treasurer->email] = $treasurer->__toString();
        }
        $cc[$coordinator->email] = $coordinator->__toString();
        $bcc = [];
        return Email::Issue("Thank you",$message,$to,$cc,$bcc);
    }

    function FullBalancePayment() {
        $remaining = Schedule::Unbilled($this);
        return ($remaining) ? count($remaining) * $this->getSeason()->fixtureCharge : null;
    }

    function FullBalanceToPay() {
        if (!$this->getTeam()) return "No team to check";
        $remaining = Schedule::Unbilled($this);
        $seasonBilling = ($remaining) ? count($remaining) * $this->getSeason()->fixtureCharge : 0;
        $outstanding = Finance::Balance($this->getTeam());
        return [
            "season" => $seasonBilling, 
            "outstanding" => $outstanding, 
            "total" => ($seasonBilling + $outstanding)
        ];
    }

    static function Lookup (Int $teamID) {
        $sql = "SELECT * FROM `teamSeasons` WHERE `teamID` = :teamID";
        $db = new Database($sql, ["teamID" => $teamID]);
        return $db->rows;
    }

    static function Invite (Team $team, Season $season) {
        $sql = "INSERT INTO `teamSeasons` SET `teamID` = :teamID, `seasonID` = :seasonID, `invited` = NOW() ON DUPLICATE KEY UPDATE `accepted` = NULL, `invited` = NOW()";
        $rlt = new Db($sql,[
            "teamID" => $team->id,
            "seasonID" => $season->id,
        ]);
        $subject = "Invitation to join " . $season->__toString() . " " . $season->getLeagueName();
        $message = ["Hi","You've been invited to join " . $season->getLeagueName() . " for " . $season->__toString(),"To confirm your team for the Season, please logon to the Locker Room at https://lockerroom.bloomnetball.co.uk, select your team and click the Confirm button",$sql,"Thanks","The Team at Bloom Netball"];
        $captain = new User($team->getCaptain());
        $treasurer = new User($team->getTreasurer());
        $to["<EMAIL>"] = "A2Z Tech";
        $cc = $bcc = [];
        // $to[$captain->getEmail()] = $captain->__toString();
        // $cc[$treasurer->getEmail()] = $treasurer->__toString();
        // $bcc["<EMAIL>"] = "L4Y";
        $result = Email::Issue($subject,$message,$to,$cc,$bcc);
        if ($result === true) {
            echo "Send Success";
        } else {
            dump($result);
        }
    }

    static function Request (Team $team, Season $season) {
    }

    static function Accept (Team $team, Season $season) {
    }

    static function Enter (Team $team, Season $destinationSeason, Season $sourceSeason = null) {
        $destinationTeams = TeamSeason::TeamsInSeason($destinationSeason);
        if (in_array($team,$destinationTeams)) return;
        $divisionID = Division::Default($destinationSeason);
        $sourceTeamSeason = static::TeamSeason($team,$sourceSeason);
        $data = [
            "teamID" => $team->id,
            "seasonID" => $destinationSeason->id,
            "divisionID" => $divisionID,
            // "captainID" => $captainID,
            // "treasurerID" => $treasurerID,
            // "statusID" => $statusID,
            "wildcard" => ($sourceTeamSeason && $sourceTeamSeason->wildcard == 1) ? 1 : null
        ];
        $sql = "INSERT INTO `teamSeasons` SET `teamID` = :teamID, `seasonID` = :seasonID, `divisionID` = :divisionID, `wildcard` = :wildcard ON DUPLICATE KEY UPDATE `deleted` = NULL, `divisionID` = :divisionID, `wildcard` = :wildcard";
        return Database::Execute($sql,$data);
    }

    static function Add (Int $teamID, Int $seasonID, Int $divisionID, Int $captainID = null, Int $treasurerID = null, Int $statusID = 1, Int $wildcard = null) {
        $data = [
            "teamID" => $teamID,
            "seasonID" => $seasonID,
            "divisionID" => $divisionID,
            "captainID" => $captainID,
            "treasurerID" => $treasurerID,
            "statusID" => $statusID,
            "wildcard" => $wildcard
        ];
        $sql = "INSERT INTO `teamSeasons` SET `teamID` = :teamID, `seasonID` = :seasonID, `captainID` = :captainID, `treasurerID` = :treasurerID,`divisionID` = :divisionID,`statusID` = :statusID, `wildcard` = :wildcard ON DUPLICATE KEY UPDATE `deleted` = NULL, `captainID` = :captainID, `treasurerID` = :treasurerID, `divisionID` = :divisionID, `statusID` = :statusID, `wildcard` = :wildcard";
        $db = new Db($sql,$data);
        if ($db->lastInsertID) {
            $team = new Team($teamID);
            $season = new Season($seasonID);
            $league = new League ($season->leagueID);
            $user = User::AuthUser();
            Logging::Add("$team resubscribed to $league $season by $user");
        }
        if ($db->errors) return implode("<br>",$db->errors);
        return ($db->lastInsertID) ? $db->lastInsertID : $db->affectedRows;
    }

    static function Season (Season $season) {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` WHERE `teamSeasons`.`seasonID` = {$season->id} AND `teamSeasons`.`deleted` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return [];
        foreach ($db->rows as $r) {
            $team = new Team();
            $team->Load($r);
            $return[] = $team;
        }
        return $return;
    }

    static function SeasonListing (Season $season) {
        $sql = "SELECT `teamSeasons`.* FROM `teamSeasons` LEFT JOIN `teams` ON `teamSeasons`.`teamID` = `teams`.`id` WHERE `teamSeasons`.`seasonID` = {$season->id} AND `teamSeasons`.`deleted` IS NULL AND `teams`.`deleted` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return [];
        $return = [];
        foreach ($db->rows as $r) {
            $index = $r['id'];
            $return[$index]['teamSeason'] = new static();
            $return[$index]['teamSeason']->Load($r);
            $return[$index]['team'] = new Team($r['teamID']); 
            $return[$index]['division'] = new Division($r['divisionID']); 
            $return[$index]['captain'] = new User($r['captainID']); 
            $return[$index]['treasurer'] = new User($r['treasurerID']); 
        }
        return $return;
    }

    static function SeasonCount (Season $season) {
        $sql = "SELECT COUNT(`teams`.`id`) AS `total` FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` WHERE `teamSeasons`.`seasonID` = {$season->id} AND `teamSeasons`.`deleted` IS NULL";
        $db = new Db($sql);
        return (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
    }

    static function Division (Division $division) {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` WHERE `teamSeasons`.`divisionID` = {$division->id} AND `teamSeasons`.`deleted` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return [];
        foreach ($db->rows as $r) {
            $team = new Team();
            $team->Load($r);
            $return[] = $team;
        }
        return $return;
    }

    static function Resubscribed (Team $team, League $league) {
        /**
         * Returns NULL if no Next Season
         * Returns FALSE if not Resubscribed
         * Returns TRUE if re-subscribed
         */
        $nextSeason = Season::Next($league);
        if (!$nextSeason) return "No next Season";
        $subscribedSeasons = static::Team($team);
        foreach ($subscribedSeasons as $subscribedSeason) {
            if ($subscribedSeason->seasonID == $nextSeason->id) return true;
        }
        return false;
    }

    static function Team (Team $team) {
        $sql = "SELECT * FROM teamSeasons WHERE `teamID` = {$team->id} AND `deleted` IS NULL ORDER BY `created`";
        return static::Query($sql);
    }

    static function TeamRemove (Int $teamID, Int $seasonID) {
        $sql = "UPDATE `teamSeasons` SET `deleted` = NOW() WHERE `teamID` = :teamID AND `seasonID` = :seasonID AND `deleted` IS NULL";
        return new Db($sql,["teamID" => $teamID, "seasonID" => $seasonID]);
    }

    static function Current (Team $team) {
        /* Returns Current and Next seasons */
        $sql = "SELECT `teamSeasons`.* FROM `teamSeasons` LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `teamSeasons`.`teamID` = :teamID AND `seasonStatus`.`active` = 1";
        $db = new Db($sql,["teamID" => $team->id]);
        if (!$db->rows) return;
        foreach ($db->rows as $r) {
            $t = new static();
            $t->Load($r);
            $return[] = $t;
        }
        return $return;
    }

    static function Live (Team $team) {
        $sql = "SELECT `teamSeasons`.* FROM `teamSeasons` LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `teamSeasons`.`teamID` = :teamID AND `seasonStatus`.`live` = 1";
        $rlt = static::Query($sql,["teamID" => $team->id]);
        return $rlt[0] ?? null;
    }

    static function TeamSeason (Team $team, Season $season) {
        $sql = "SELECT `teamSeasons`.* FROM `teamSeasons` WHERE `teamID` = :teamID AND `seasonID` = :seasonID ORDER BY deleted IS NULL";
        $rlt = static::Query($sql,["teamID" => $team->id,"seasonID" => $season->id]);
        return $rlt[0] ?? null;
    }
    
    static function Next (Team $team) {
        $sql = "SELECT `teamSeasons`.* FROM `teamSeasons` LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `teamSeasons`.`teamID` = :teamID AND `seasonStatus`.`active` = 1 AND seasonStatus.live IS NULL";
        $rlt = static::Query($sql,["teamID" => $team->id]);
        return $rlt[0] ?? null;
    }

    static function LookupID (Season $season, Team $team) {
        $sql = "SELECT * FROM `teamSeasons` WHERE `seasonID` = {$season->id} AND `teamID` = {$team->id}";
        $db = new Db($sql);
        if (!$db->rows) return;
        $teamSeason = new static();
        $teamSeason->Load($db->rows[0]);
        return $teamSeason;
    }

    static function FetchInvoice (Season $season, Team $team) {
        $teamSeason = static::LookupID($season,$team);
        if (!$teamSeason) return;
        return new PDF\SeasonInvoice($teamSeason);
    }

    static function Resubscribe (Team $team, Season $season = null) {
        // if (!$season) {
        $league = new League($team->leagueID);
        $nextSeason = Season::Next($league);
        // return "Resubscribe $team to $nextSeason in {$league}";
        // }
        // $sql = "SELECT * FROM `teamSeasons` WHERE `teamID` = {$team->id} AND `seasonID` = {$season->id}";
        // $rlt =new Db($sql);
        // if (!$rlt->rows) return "Could not find the TeamSeason record for $team and $season";
        // $nextSeason = Season::Next(new League($season->leagueID));
        // if (!$nextSeason) return "No Season available to re-subscribe to";
        // if ($season->id == $nextSeason->id) return "Already re-subscribed";
        
        return static::Add($team->id,$nextSeason->id,Division::Default($nextSeason),$team->captainID,$team->treasurerID);
        // \Messaging\Add("Resubscribed $team to $nextSeason");
    }

    static function Unsubscribe (Team $team, Season $season) {
        new Db("UPDATE `teamSeasons` SET `deleted` = NOW() WHERE `teamID` = {$team->id} AND `seasonID` = {$season->id}");
        \Messaging\Add("$team unsubscribed from $season");
    }

    static function isWildcard (Team $team, Season $season) {
        $sql = "SELECT `wildcard` FROM `teamSeasons` WHERE `teamID` = {$team->id} AND `seasonID` = {$season->id}";
        $db = new Db($sql);
        if (isset($db->rows[0]['wildcard']) && $db->rows[0]['wildcard'] == 1) return true;
    }

    static function IsIn (Team $team, Season $season) {
        $sql = "SELECT id FROM teamSeasons WHERE `teamID` = :teamID AND `seasonID` = :seasonID AND deleted IS NULL";
        $rlt = Database::Execute($sql,["teamID" => $team->id,"seasonID" => $season->id]);
        return $rlt['success']['rows'][0]['id'] ?? null;
    }

    static function MigrateToV2 () {
        $sql = "SELECT `teamSeasons`.*, teams.lockerRoomVersion, teams.name FROM `teamSeasons` LEFT JOIN teams ON teamSeasons.teamID = teams.id LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` = 1 AND (teams.lockerRoomVersion IS NULL OR teams.lockerRoomVersion <> 2)";
        return static::Query($sql);
    }

    static function MigrateToV2_Next () {
        $sql = "SELECT `teamSeasons`.*, teams.lockerRoomVersion, teams.name FROM `teamSeasons` LEFT JOIN teams ON teamSeasons.teamID = teams.id LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL AND (teams.lockerRoomVersion IS NULL OR teams.lockerRoomVersion <> 2)";
        return static::Query($sql);
    }

    static function MigrateToV2_Pending () {
        $sql = "SELECT `teamSeasons`.*, teams.lockerRoomVersion, teams.name FROM `teamSeasons` LEFT JOIN teams ON teamSeasons.teamID = teams.id LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` WHERE (`seasons`.`statusID` IS NULL OR `seasons`.`statusID` = 0) AND (teams.lockerRoomVersion IS NULL OR teams.lockerRoomVersion <> 2)";
        return static::Query($sql);
    }

    static function TeamsInSeason ($season) {
        $sql = "SELECT teams.* FROM `teamSeasons` LEFT JOIN teams ON teamSeasons.teamID = teams.id WHERE `teamSeasons`.`seasonID` = :seasonID AND `teamSeasons`.deleted IS NULL AND `teams`.`deleted` IS NULL";
        return Team::Query($sql,["seasonID" => $season->id]);
    }

    static function SeasonsTeams ($season) {
        # Return Array of CaptainUser, TreasurerUser, SquadUsers, Division, PaymentCard
        $sql = "SELECT `id`, `teamID`, `divisionID`, `wildcard` FROM `teamSeasons` WHERE `seasonID` = :seasonID AND deleted IS NULL";
        $rlt = Database::Execute($sql,["seasonID" => $season->id]);
        $return = [];
        if (!$rlt['success'] || !$rlt['success']['rows']) return $return;
        foreach ($rlt['success']['rows'] as $r) {
            $team = new Team($r['teamID']);
            $captain = (($c=TeamFollower::Captain($team))) ? new User($c) : null;
            $treasurer = TeamFollower::Treasurer($team);
            $paySource = TeamFollower::PaymentSource($team);
            $return[] = [
                "id" => $r['id'],
                "team" => $team,
                "division" => new Division($r['divisionID']),
                "wildcard" => $r['wildcard'],
                "captain" => $captain,
                "treasurer" => ($treasurer) ? $treasurer : $captain,
                "paymentSource" => $team->PaymentCard(),
                "payInFull" => TeamFollower::PaymentOption($team)
            ];
        }
        return $return;
    }

    static function SeasonStatusReport () {
        $sql = "SELECT leagues.id AS leagueID, leagues.name AS leagueName, seasons.id AS seasonID, seasons.name AS seasonName, seasonStatus.name AS statusName FROM leagues LEFT JOIN seasons ON leagues.id = seasons.leagueID LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id ORDER BY `leagues`.`name` ASC";
        $rlt = Database::Execute($sql);
        $return['summary'] = ["leagues" => 0, "Live" => 0, "Next" => 0, "Dormant" => 0];
        foreach ($rlt['success']['rows'] as $r) {
            if (!isset($return['results'][$r['leagueID']])) {
                $return['summary']['leagues']++;
                $return['results'][$r['leagueID']] = [
                    'name' => $r['leagueName'],
                    'Live' => null,
                    'Next' => null,
                ];
            } 
            if ($r['statusName']=="Live" || $r['statusName'] == "Next") {
                $return['summary'][$r['statusName']]++;
                $return['results'][$r['leagueID']][$r['statusName']] = ["id" => $r['seasonID'], "name" => $r['seasonName']];
            }
        }
        foreach ($return['results'] as $leagueID => $results) {
            if (!$results['Live'] && !$results['Next']) $return['summary']['Dormant']++;
        }
        return $return;
    }

    static function Orphans() {
        $sql = "SELECT * FROM teamSeasons WHERE teamID NOT IN (SELECT id FROM teams)";
        return Database::Execute($sql);
    }

    static function ToggleWildcard (TeamSeason $teamSeason) {
        $teamSeason->wildcard = ($teamSeason->wildcard == 1) ? null : 1;
        $rlt = $teamSeason->Save();
        return $teamSeason;
    }

}