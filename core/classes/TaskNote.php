<?php

class TaskNote extends Base {

    protected $dbTable = "taskNotes", $dbOrder = ["name" => "ASC"];

    protected $dbFields = ["description"];
    protected $description;

    function __construct(Int $id = null) {
        parent::__construct($id);
        switch ($this->status) {
            case 1: $this->statusText = "Join Waiting List"; break;
            case 2: $this->statusText = "Closed for Registration"; break;
            default: $this->statusText = "Open for Registration";
        }
    }
    function __toString() {
        return "{$this->description}";
    }
    /* Getters */
    /* Checkers */
    /* Doers */
    function Save() {
        return parent::Save();
    }
}