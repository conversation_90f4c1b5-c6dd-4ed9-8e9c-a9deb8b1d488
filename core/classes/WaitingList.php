<?php

class WaitingList extends Base {

    protected $dbTable = "waitingList";
    protected $dbFields = ["leagueID", "teamID","captainID"];
    protected $dbSetup = [
        "leagueID" => ["type" => "int(10) unsigned"],
        "teamID" => ["type" => "int(10) unsigned"],
        "captainID" => ["type" => "int(10) unsigned"],
    ];

    protected $teamID, $leagueID, $captainID;

    protected $league, $team, $captain;

    function getLeague() {
        if (!$this->league) $this->league = new League($this->leagueID);
        return $this->league;
    }
    function getTeam() {
        if (!$this->team) $this->team = new Team($this->teamID);
        return $this->team;
    }
    function getCaptain() {
        if (!$this->captain) $this->captain = new User($this->captainID);
        return $this->captain;
    }
    static function Add (Team $team, League $league, User $captain) {
        $sql = "INSERT INTO `waitingList` (`teamID`, `leagueID`,`captainID`) VALUES (:teamID, :leagueID, :captainID) ON DUPLICATE KEY UPDATE `captainID` = :captainID, `deleted` = NULL";
        $db = new Db($sql, ["teamID" => $team->id, "leagueID" => $league->id, "captainID" => $captain->id]);
        $coordinator = new User($league->getCoordinatorID());
        $to = [$captain->email => $captain->__toString()];
        $cc = [$coordinator->email => $coordinator->__toString()];
        $message[] = "Hi {$captain->firstname}";
        $message[] = "Thanks for adding $team to the waiting list for $league";
        $message[] = "If you need to get in touch, your League Coordinator is $coordinator";
        $message[] = "Many thanks";
        $message[] = "Leagues 4 You";
        Email::Issue("Waiting List Confirmation for $team",$message,$to,$cc);
    }
    static function League (League $league) {
        $sql = "SELECT * FROM `waitingList` WHERE `leagueID` = :leagueID AND `deleted` IS NULL";
        return static::Query($sql,["leagueID" => $league->id]);
    }
    static function LeagueTotal (League $league) {
        $sql = "SELECT COUNT(`teamID`) AS `total` FROM `waitingList` WHERE `leagueID` = :leagueID AND `deleted` IS NULL";
        $db = new Db($sql,["leagueID" => $league->id]);
        if (isset($db->rows[0]['total'])) return $db->rows[0]['total'];
    }
    static function Archive (Int $waitingListID) {
        new Db("UPDATE `waitingList` SET `deleted` = NOW() WHERE `id` = $waitingListID");
    }
    static function AddToSeason (Int $waitingListID, Int $seasonID) {
        $waitingList = new static($waitingListID);
        $team = new Team($waitingList->teamID);
        $season = new Season($seasonID);
        TeamSeason::Add ($team->id, $seasonID, Division::Default($season), $team->captainID, $team->treasurerID);
        static::Archive($waitingListID);
        \Messaging\Add("$team added from Waiting List to $season");
    }
}