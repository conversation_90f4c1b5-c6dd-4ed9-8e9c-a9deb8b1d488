<?php

class Schedule extends Base {

    protected $dbTable = "schedule";
    protected $bookingID, $fixtureID;
    protected $offset, $duration;
    protected $dbFields = ["bookingID","offset","startTime","duration","fixtureID"];

    public $booking, $fixture, $venue;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function __toString() {
        $booking = new Booking($this->bookingID);
        return $booking->getStartDate('d/m/Y') . " "  . substr($this->startTime,0,5). " " . $booking->pitchCourt;
    }

    function Save() {
        $rlt = parent::Save();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }

    function getBooking () {
        if (!$this->booking) $this->booking = new Booking($this->bookingID);
        return $this->booking;
    }

    function getFixture () {
        if (!$this->fixture) $this->fixture = new Fixture($this->fixtureID);
        return $this->fixture;
    }

    function ApiOutput() {
        $booking = $this->getBooking();
        $fixture = $this->getFixture();
        $home = new Team($fixture->home);
        $away = new Team($fixture->away);
        $venue = new Venue($booking->getVenueID());
        return [
            "id" => $this->id,
            "startTime" => $this->startTime,
            "startDate" => $booking->getStartDate(),
            "home" => $home->__toString(),
            "homeTeamID" => $home->id,
            "away" => $away->__toString(),
            "awayTeamID" => $away->id,
            "pitchCourt" => $booking->getPitchCourt(),
            "venue" => $venue->__toString()
        ];
    }

    /* Getters */
    function getStartTime() { return substr($this->startTime,0,5);}
    function getBookingID() { return $this->bookingID;}
    function getFixtureID() { return $this->fixtureID;}
    /* Setters */
    function setStartTime($startTime) { $this->startTime = $startTime;}
    function setFixtureID(Int $fixtureID) { $this->fixtureID = $fixtureID;}

    /* Switching Fixtures between Slots */
    static function Swap (Int $firstID, Int $secondID) {
        $schedule1 = new static($firstID);
        $schedule2 = new static($secondID);
        $fixture1 = $schedule1->getFixtureID();
        $fixture2 = $schedule2->getFixtureID();
        $schedule1->setFixtureID($fixture2);
        $schedule2->setFixtureID($fixture1);
        $schedule1->Save();$schedule2->Save();
    }

    static function SwitchOptions (Schedule $schedule) {
        $booking = new Booking($schedule->bookingID);
        $sql = "SELECT `schedule`.* FROM `schedule` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `bookings`.`deleted` IS NULL AND `schedule`.`deleted` IS NULL AND `bookings`.`startDate` >= NOW() AND `bookings`.`leagueID` = " . $booking->getLeagueID() . " ORDER BY `bookings`.`startDate`, `bookings`.`startTime`";
        $db = new Db($sql);if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s;
        }
        return $return;
    }

    static function BookingUsage (Booking $booking) {
        // $sql = "SELECT (`offset` + `duration`) AS `used` FROM `schedule` WHERE `bookingID` = {$booking->id} AND `deleted` IS NULL ORDER BY `offset` DESC LIMIT 1";
        $sql = "SELECT SUM(`duration`) AS `used` FROM `schedule` WHERE `bookingID` = {$booking->id} AND `deleted` IS NULL"; #echo $sql."<br>";
        $db = new Db($sql);
        $return = [
            "total" => $booking->getDuration(),
            "available" => $booking->getDuration(),
            "used" => 0
        ];
        if (isset($db->rows[0]['used'])) $return['used'] = $db->rows[0]['used'];
        $return['available'] -= $return['used'];
        return $return;
    }

    static function forSeason (Season $season) {
        $sql = "
        SELECT `schedule`.* FROM `schedule`
            LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
            LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            WHERE `divisions`.`seasonID` = {$season->id}
            AND `schedule`.`deleted` IS NULL
            AND `bookings`.`deleted` IS NULL
            AND `fixtures`.`deleted` IS NULL
            ORDER BY `bookings`.`startDate`, `schedule`.`startTime`";
        // echo $sql;
        $db = new Db($sql);if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $schedule = new static();
            $schedule->Load($r);
            $schedule->booking = new Booking($schedule->bookingID);
            $schedule->fixture = new Fixture($schedule->fixtureID);
            $schedule->venue = new Venue($schedule->booking->getVenueID());
            ($return) ? array_push($return, $schedule) : $return[] = $schedule;
        } 
        return $return;
    }

    static function Unbilled (TeamSeason $teamSeason) {
        $sql = "
        SELECT `fixtures`.* FROM `schedule`
            LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
            LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            WHERE `divisions`.`seasonID` = :seasonID
            AND `schedule`.`deleted` IS NULL
            AND `bookings`.`deleted` IS NULL
            AND ((`fixtures`.`home` = :teamID AND `fixtures`.homeTrans IS NULL) OR (`fixtures`.`away` = :teamID AND fixtures.awayTrans IS NULL))
            ORDER BY `bookings`.`startDate`, `schedule`.`startTime`";
        return Fixture::Query($sql, ["seasonID" => $teamSeason->seasonID, "teamID" => $teamSeason->teamID]);
        return $rlt;
    }

    static function TeamSeason (TeamSeason $teamSeason) {
        $sql = "
        SELECT `schedule`.* FROM `schedule`
            LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
            LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            WHERE `divisions`.`seasonID` = :seasonID
            AND `schedule`.`deleted` IS NULL
            AND `bookings`.`deleted` IS NULL
            AND (`fixtures`.`home` = :teamID OR `fixtures`.`away` = :teamID)
            ORDER BY `bookings`.`startDate`, `schedule`.`startTime`";
        // echo $sql;
        $rlt = Database::Execute($sql,["seasonID" => $teamSeason->seasonID, "teamID" => $teamSeason->teamID]);
        if (!$rlt['success']['rows']) return;
        $return = [];
        foreach ($rlt['success']['rows'] as $r) {
            $schedule = new static();
            $schedule->Load($r);
            $schedule->booking = new Booking($schedule->bookingID);
            $schedule->fixture = new Fixture($schedule->fixtureID);
            $schedule->venue = new Venue($schedule->booking->getVenueID());
            ($return) ? array_push($return, $schedule) : $return[] = $schedule;
        } 
        return $return;
    }

    static function seasonTotal (Season $season) {
        $sql = "
            SELECT COUNT(`schedule`.`id`) AS `total` FROM `schedule`
            LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            WHERE `divisions`.`seasonID` = :seasonID
            AND `schedule`.`deleted` IS NULL
            AND `fixtures`.`deleted` IS NULL
            ";
        $db = new Db($sql, ["seasonID" => $season->id]);if (!$db->rows) return;
        return ($db->rows) ? $db->rows[0]['total'] : 0;
    }

    static function SeasonCount (Season $season) {
        $sql = "
        SELECT SUM(`schedule`.`duration`) AS `totalMinutes` FROM `schedule`
        LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
        LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
        WHERE `divisions`.`seasonID` = :seasonID
        AND `schedule`.`deleted` IS NULL
        AND `fixtures`.`deleted` IS NULL
        ";
    $db = new Db($sql, ["seasonID" => $season->id]);if (!$db->rows) return;
    return (isset($db->rows[0]['totalMinutes'])) ? $db->rows[0]['totalMinutes'] : null;
    }

    static function Clear (Season $season) {
        if (!$season->id) return "No Season ID";
        // new Db("DELETE schedule FROM schedule LEFT JOIN fixtures ON schedule.fixtureID = fixtures.id LEFT JOIN divisions ON fixtures.divisionID = divisions.id WHERE divisions.seasonID = {$season->id} AND `fixtures`.`homeTrans` = NULL AND `fixtures`.`awayTrans` = NULL");
        // $sql = "UPDATE schedule LEFT JOIN fixtures ON schedule.fixtureID = fixtures.id LEFT JOIN divisions ON fixtures.divisionID = divisions.id SET `schedule`.`testDate` = NOW() WHERE divisions.seasonID = {$season->id} AND `fixtures`.`homeTrans` = NULL AND `fixtures`.`awayTrans` = NULL";
        // return new Db($sql);
        return static::ClearUnbilled($season);
    }

    static function ClearUnbilled (Season $season, Bool $override = false) {
        /* Returns STRING error or INT affectedRows */
        $sql = "UPDATE `schedule` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` LEFT JOIN divisions ON fixtures.divisionID = divisions.id SET `schedule`.`deleted` = NOW() WHERE `schedule`.`deleted` IS NULL AND divisions.seasonID = {$season->id}";
        if ($override !== true) $sql .= " AND `fixtures`.`homeTrans` IS NULL AND `fixtures`.`awayTrans` IS NULL";
        // echo "$sql<br>";
        $db = new Db($sql);
        if ($override === true) {
            $sql = "SELECT `fixtures`.`homeTrans`, `fixtures`.`awayTrans` FROM fixtures LEFT JOIN divisions ON fixtures.divisionID = divisions.id WHERE divisions.seasonID = :seasonID";
            // echo "$season->id: $sql<br>";
            $rlt = Database::Execute($sql,["seasonID" => $season->id]);
            if ($rlt['success'] && $rlt['success']['rows']) {
                foreach ($rlt['success']['rows'] as $r) {
                    if ($r['homeTrans']) {
                        $homeTrans = new Finance($r['homeTrans']);
                        if (!$homeTrans->creditID) Finance::createCredit($homeTrans);
                    }
                    if ($r['awayTrans']) {
                        $awayTrans = new Finance($r['awayTrans']);
                        if (!$awayTrans->creditID) Finance::createCredit($awayTrans);
                    }
                }
            }
        }
        return ($db->errors) ? implode(". ",$db->errors) : (int)$db->affectedRows;
    }
    
    // static function ClearUnbilled (Season $season) {
    //     /* Returns STRING error or INT affectedRows */
    //     $sql = "UPDATE `schedule` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` LEFT JOIN divisions ON fixtures.divisionID = divisions.id SET `schedule`.`deleted` = NOW() WHERE `schedule`.`deleted` IS NULL AND divisions.seasonID = {$season->id} AND `fixtures`.`homeTrans` IS NULL AND `fixtures`.`awayTrans` IS NULL";
    //     $db = new Db($sql);
    //     return ($db->errors) ? implode(". ",$db->errors) : (int)$db->affectedRows;
    // }
    
    static function NextSlot (Int $bookingID) {
        $sql = "SELECT startTime, duration FROM `schedule` WHERE `bookingID` = :bookingID AND `deleted` IS NULL ORDER BY `startTime` DESC LIMIT 1";
        $db = new Db($sql,["bookingID" => $bookingID]);
        if (!$db->rows) return;
        return date('H:i:s',strtotime("+{$db->rows[0]['duration']} minutes", strtotime($db->rows[0]['startTime'])));
    }

    static function SlotOptions (Booking $booking) {
        $sql = "SELECT * FROM `schedule` WHERE `bookingID` = {$booking->id} AND `deleted` IS NULL ORDER BY `startTime`";
        $db = new Db($sql);
        $startDate = $booking->getStartDate();
        $startTime = $booking->getStartTime();
        $duration = $booking->getDuration();
        $endTime = date('Y-m-d H:i',strtotime("+$duration minutes",strtotime("$startDate $startTime")));
        $lastSlotTime = date('Y-m-d H:i',strtotime("-40 minutes",strtotime($endTime)));
        $duration = $booking->getDuration();
        $bookedSlots = [];
        if ($db->rows) {
            foreach ($db->rows as $r) {
                $bookedSlots[] = [
                    "from" => date('Y-m-d H:i',strtotime("$startDate {$r['startTime']}")),
                    "to" => date('Y-m-d H:i',strtotime("+{$r['duration']} minutes",strtotime("$startDate {$r['startTime']}")))
                ];
            }
        }
        $slots = [];
        for ($i=0; $i<=$booking->getDuration(); $i+=5) {
            $timeOption = date('Y-m-d H:i', strtotime("+ $i minutes", strtotime("$startDate $startTime")));
            $slots[$timeOption] = true;
            foreach ($bookedSlots as $bookedSlot) {
                if (($timeOption >= $bookedSlot['from'] && $timeOption < $bookedSlot['to']) || $timeOption > $lastSlotTime) {
                    unset($slots[$timeOption]);
                    break;
                }
            }
        }
        return ($slots) ? array_keys($slots) : null;
    }

    static function Fixture (Fixture $fixture) {
        $sql = "SELECT * from `schedule` where `fixtureID` = {$fixture->id} AND `deleted` IS NULL";
        $db = new Db($sql); if (!$db->rows) return;
        $schedule = new static();
        $schedule->Load($db->rows[0]);
        return $schedule;
    }

    static function FixtureOptions (Fixture $fixture, Booking $booking) {
        /* Get duration for Fixture */
        /* Get Booking broken into 5 minute segments (the Booking Map) */
        $slotArray = $booking->Slots();
        /* Get Fixtures already allocated to this Booking */
        // $fixtures = $booking->Fixtures();
        /* Loop through Booking Map marking each 5 minute segment as true where Fixture Blocked */
        $slotOptions = [];
        if ($slotArray) {
            foreach ($slotArray as $k => $v) {
                if ($v['availableMinutes'] >= $fixture->duration) $slotOptions[] = $v;
            }
        }
        /* Start from end, work backwards, incrementing contigiousSubtotal */
        /* if contigiousSubtotal >= fixture->duration ? true : false */
        return $slotOptions;
    }

    /* Calculate the Start and End date for a Season based on fixtures in the Schedule */
    static function StartEndDates (Season $season) {
        $sql = "SELECT MIN(`startDate`) AS `start`, MAX(`startDate`) AS `end` FROM bookings LEFT JOIN schedule ON `bookings`.`id` = `schedule`.`bookingID` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `divisions`.`seasonID` = :seasonID AND `bookings`.`deleted` IS NULL AND `schedule`.`deleted` IS NULL AND `fixtures`.`deleted` IS NULL";
        $db = new Db($sql,["seasonID" => $season->id]);
        if (!$db->rows) return;
        return $db->rows[0];
    }

    static function BetweenDates (String $startDate, String $endDate) {
        $sql = "SELECT schedule.* FROM schedule LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `bookings`.`startDate` >= '$startDate' AND `bookings`.`startDate` <= '$endDate' ORDER BY `bookings`.`startDate`,`schedule`.`startTime`";
        return static::Query($sql);
    }

    static function forDivision (Division $division, Bool $futureOnly = false) {
        $sql = "SELECT `schedule`.* FROM `schedule`
            LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
            LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            LEFT JOIN `seasons` ON `divisions`.`seasonID` = `seasons`.`id`
            WHERE `fixtures`.`divisionID` = {$division->id} 
            -- AND `seasons`.`status` = 1 
            AND `fixtures`.`deleted` IS NULL 
            AND `schedule`.`deleted` IS NULL
            AND `bookings`.`deleted` IS NULL";
        if ($futureOnly === true) $sql .= " AND `bookings`.`startDate` >= '".date('Y-m-d')."'"; 
        $sql .= " ORDER BY `bookings`.`startDate`, `schedule`.`startTime`";
            // echo $sql;
        // $db = new Db($sql,["divisionID" => $division->id]);
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s->ApiOutput();
        } 
        return $return;
    }

    /* Changing Fixture to Open Slot */
    static function Amend (Array $data) {
        /** 
         * Expects/Checks Schedule ID, Booking ID and StartTime
         * Checks for some change (eg if Booking & StartTime the same - no action)
         * If Booking ID same and time changes - just amend the StartTime
         * Returns INT or STRING from Base for SUCCESS or FAILURE.
         */
        // Valid Schedule?
        if (!isset($data['id']) || !$data['id'] || !($schedule= new static($data['id']))) return "No valid Schedule passed";
        // Valid Booking?
        if (!isset($data['bookingID']) || !$data['bookingID'] || !($booking= new Booking($data['bookingID']))) return "No valid Booking passed";
        // Valid StartTime?
        if (!isset($data['offset']) || !is_numeric($data['offset'])) return "No valid offset passed";

        $data['startTime'] = date('H:i:s',strtotime("+{$data['offset']} minutes",strtotime($booking->startTime)));

        // Is this a change?
        if ($schedule->bookingID == $data['bookingID'] && $schedule->startTime == $data['startTime']) return "Same Booking and Start Time - no changes made";


        // Start Time only changed?
        if ($schedule->bookingID == $data['bookingID']) {
            /* Just changing the time */
            $msg = "Re-scheduled from ".substr($schedule->startTime,0,5)." to {$data['startTime']}";
            $schedule->offset = $data['offset'];
            $schedule->startTime = $data['startTime'];
            $rlt = $schedule->Save();
            \Messaging\Add($msg);
            return (is_int($rlt)) ? $msg : $rlt;
        }

        /* Booking ID must have changed */
        /* Get Old Booking */
        $oldBooking = new Booking($schedule->bookingID);
        // Get Fixture
        $fixture = new Fixture($schedule->getFixtureID());
        $message = "$fixture rescheduled for " . $booking->getStartDate('D jS F')." at {$data['startTime']}";

        $schedule->bookingID = $data['bookingID'];
        $schedule->startTime = $data['startTime'];
        $schedule->offset = $data['offset'];

        /* If the Schedule Save fails - return Error message */
        if (is_string($rlt = $schedule->Save())) return $rlt;
        
        /* Set the Reschedule Reason */
        if (isset($data['reason']) && $data['reason']) {
            $fixture->rescheduleReason = ($fixture->rescheduleReason) ? $fixture->rescheduleReason . ". " . $data['reason'] : $data['reason'];
            $fixture->Save();
        }
        /* Email Team Captains? */
        
        /* If new Booking Date is in the future, adjust Billing */
        if ($booking->getStartDate() > $oldBooking->getStartDate() && $booking->getStartDate() > date('Y-m-d')) {
            
            /* Raise Credit Note for EACH team's Transaction if it exists AND not already credited */
            /* Find the Home and Away Transactions  */
            
            // Home Transaction...
            $homeTransactionID = $fixture->getHomeTrans();
            $homeTransaction = new Finance($homeTransactionID);
            // Away Transaction
            $awayTransactionID = $fixture->getAwayTrans();
            $awayTransaction = new Finance($awayTransactionID);
           
            $creditHome = Finance::createCredit($homeTransaction);

            if (is_string($creditHome)) {
                $message .= ". Could not credit " . $fixture->getHomeTeamName().". $creditHome";
            } else {
                // Remove Home Transaction ID from Fixture to enable billing for this Fixture to get picked up later
                $fixture->setHomeTrans(); // Sending no value to set as NULL
                $message .= ". " . $fixture->getHomeTeamName() . " credited for Fixture"; 
            }
            $intent = StripePayment::TeamPending($fixture->getHomeTeam());
            if (isset($intent['stripePaymentIntentID'])) {
                $stripePayment = StripePayment::byPaymentIntent($intent['stripePaymentIntentID']);
                $stripePayment->Cancel();
                $message .= "The Pending Payment for " . $fixture->getHomeTeamName() . " has been cancelled";
            }
            $creditAway = Finance::createCredit($awayTransaction);
            if (is_string($creditAway)) {
                $message .= ". Could not credit " . $fixture->getAwayTeamName().". $creditAway";
            } else {
                $fixture->setAwayTrans(); // Sending no value to set as NULL
                $message .= ". " . $fixture->getAwayTeamName() . " credited for Fixture";
            } 
            $intent = StripePayment::TeamPending($fixture->getAwayTeam());
            if (isset($intent['stripePaymentIntentID'])) {
                $stripePayment = StripePayment::byPaymentIntent($intent['stripePaymentIntentID']);
                $stripePayment->Cancel();
                $message .= "The Pending Payment for " . $fixture->getAwayTeamName() . " has been cancelled";
            }
        } 
        return $message;
    }

    static function Slots (Booking $booking) {
        /* Returns STRING on Error. ARRAY with Results */
        $sql = "SELECT * FROM `schedule` WHERE `bookingID` = :bookingID AND `deleted` IS NULL";
        $db = new Db($sql,["bookingID" => $booking->id]);
        // Tools::Dump($db);
        if ($db->errors) return implode(". ",$db->errors);
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s;
        } 
        return $return;
    }

}