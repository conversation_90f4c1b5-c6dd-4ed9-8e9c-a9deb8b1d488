<?php

class StripeCustomer extends Base {

    protected $dbTable = "stripeCustomers";
    protected $userID;
    protected $stripeCustomerID;  
    protected $dbFields = ["userID", "stripeCustomerID"];

    protected $stripe;

    function __toString() { 
        return "{$this->stripeCustomerID}";
    }
    
    function fetchStripe() {
        return Stripe::getStripeCustomer($this->stripeCustomerID);
    }

    function paymentHistory() {
        return Stripe::getStripeCustomerPaymentHistory($this->stripeCustomerID);
    }

    static function User (User $user) {
        return static::Query("SELECT * FROM `stripeCustomers` WHERE `userID` = :userID",["userID" => $user->id]);
    }

}