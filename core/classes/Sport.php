<?php

class Sport extends Base {

    protected $dbTable = "sports";
    protected $dbFields = ["name","duration","webImage","captainsPack","termsConditions","tasterSessionName","teamSignupImage","playerSignupImage","videoExplainer"];
    protected $dbOrder = ["name" => "ASC"];
    protected $name;
    protected $gender, $duration;
    protected $webImage, $captainsPack;
    protected $tasterSessionName;
    protected $termsConditions;
    protected $teamSignupImage, $playerSignupImage, $videoExplainer;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }


    function __toString() {
        return $this->name;
    }

    function Save() {
        $rlt = parent::Save();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }

    /* Getters */
    function getName() { return $this->name;}
    function getDuration() { return $this->duration;}
    function getCaptainsPack() { return $this->captainsPack;}
    function getWebImage() { return $this->webImage;}

    function TermsURL() {
        return $this->termsConditions;
    }

    function ApiOutput() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "img" => $this->webImage,
            "captainsPack" => $this->captainsPack,
            "termsConditions" => $this->termsConditions,
            "urlref" => strtolower(str_replace([" ","-"],["",""],$this->name)),
            "tasterSessionName" => $this->tasterSessionName
        ];
    }

    function ApiData() {
        return [
            "id" => $this->id,
            "name" => $this->getName(),
            "img" => $this->getWebImage(),
            "duration" => $this->duration,
            "urlref" => strtolower(str_replace([" ","-"],["",""],$this->name)),
            "tasterSessionName" => $this->tasterSessionName
        ];
    }

    static function ApiListing() {
        $sql = "SELECT * FROM `sports` ORDER BY `name`";
        $rlt = new Db($sql);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s->ApiOutput();
        }
        return $return;
    }

    static function PlayerGuides() {
        $sql = "SELECT name, captainsPack FROM sports WHERE captainsPack IS NOT NULL";
        $rlt = Database::Execute($sql);
        if ($rlt['success']) return $rlt['success']['rows'];
    }

}