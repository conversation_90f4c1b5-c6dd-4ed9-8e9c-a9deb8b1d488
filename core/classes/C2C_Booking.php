<?php

use Stripe_v2 as Stripe;

class C2C_Booking extends Base {

    protected $dbTable = "c2c_bookings";
    protected $dbFields = ["tasterID", "userID", "firstname", "lastname", "email", "mobile", "stripePaymentIntentID", "stripePaymentIntentClientSecret", "discountCode", "stripeStatus", "confirmIssued", "cancelIssued", "refundIssued", "data"];

    protected $firstname, $lastname, $email, $mobile;
    protected $confirmIssued, $cancelIssued, $refundIssued;
    protected $data;

    // Initialize stripeProduction based on environment
    protected $stripeProduction;

    protected $tasterID, $discountCode;
    protected $stripePaymentIntentID, $stripePaymentIntentClientSecret, $stripeStatus;

    protected $user, $taster, $attendees;

    protected $discount;

    protected $paymentIntent;

    public function __construct($id = null) {
        // Set Stripe mode based on environment before any other initialization
        $this->stripeProduction = Environment::isProduction();

        parent::__construct($id);
    }

    function CheckPaymentStatus() {
        $this->stripeStatus = null;
        $return = [];
        if (!$this->stripePaymentIntentID) {
            $return = ["error" => "No Payment Intent"];
        } else {
            $stripe = new Stripe($this->stripeProduction);
            $pi = $stripe->fetchPaymentIntent($this->stripePaymentIntentID);
            if (!is_object($pi)) {
                $return = ["error" => "Invalid Payment Intent"];
            } else {
                if ($this->stripeStatus != $pi->status) {
                    $this->stripeStatus = $pi->status;
                    new Db("UPDATE `{$this->dbTable}` SET `stripeStatus` = '{$this->stripeStatus}' WHERE `id` = {$this->id}");
                }
                $return = ["success" => $this->stripeStatus];
            }
        }
        return $return;
    }

    public function getData() {
        if (!$this->data) return [];
        return json_decode($this->data, true);
    }

    private function getTournamentEmailData() {
        $taster = $this->getTaster();
        $venue = $taster->getVenue();
        $data = $this->getData();
        $this->calculatePrice();

        return [
            "team_name" => $data['teamName'],
            "tournament_name" => $taster->tournamentName,
            "tournament_date" => $taster->formatDate(),
            "start_time" => $taster->formatTime(),
            "venue_name" => $venue->name,
            "venue_address" => $venue->address1,
            "postcode" => $venue->postcode,
            "entry_cost" => "£" . number_format($this->price, 2),
            "coordinator_email" => $taster->getCoordinator()->email,
        ];
    }

    private function getRegularEmailData($full_names) {
        $taster = $this->getTaster();
        return [
            "first_name" => $this->firstname,
            "full_name" => $full_names,
            "location" => $taster->formatVenue(),
            "date" => $taster->formatDate() . " at " . $taster->formatTime(),
            "lc_name" => $taster->getCoordinator()->firstname,
            "email" => $taster->getCoordinator()->email,
        ];
    }

    function SendConfirmation($output = false, $override = false) {
        if ($this->confirmIssued && $override !== true) return;
        if (!$this->stripePaymentIntentID) return;

        /* Check Payment */
        $stripe = new Stripe($this->stripeProduction);
        $pi = $stripe->fetchPaymentIntent($this->stripePaymentIntentID);
        if (!is_object($pi)) return "Problem with Payment Intent";
        if ($pi->status != "succeeded" && $pi->status != "requires_capture") return "Payment Intent status {$pi->status} incorrect";

        $this->stripeStatus = $pi->status;
        $cc = [];
        $full_names = $this->firstname . " " . $this->lastname;
        $coordinator = $this->getTaster()->getCoordinator();
        $taster = $this->getTaster();
        $sport = $taster->getSport();
        $venue = $taster->getVenue();

        $attendees = $this->getAttendees();
        $subject = "{$sport->tasterSessionName} Registration";

        if ($attendees) {
            foreach ($attendees as $attendee) {
                $full_names .= ", {$attendee->firstname}";
            }
        }

        if ($coordinator) {
            $cc[$coordinator->email] = $coordinator;
        }

        $to[$this->email] = ($this->firstname || $this->lastname) ? trim("{$this->firstname} {$this->lastname}") : $this->email;

        $bcc = [
            "<EMAIL>" => "Charlotte Waugh",
        ];

        $emailData = $taster->is_tournament
            ? $this->getTournamentEmailData()
            : $this->getRegularEmailData($full_names);

        $template = $taster->is_tournament ? "tournament_confirmation" : "c2c_confirmation";
        $emailSubject = $taster->is_tournament ? "Tournament Registration" : $subject;

        $rlt = Email::Issue($emailSubject, ["template"], $to, $cc, $bcc, [], $template, $emailData);

        if ($rlt) {
            new Db("UPDATE `c2c_bookings` SET `stripeStatus` = '{$this->stripeStatus}', `confirmIssued` = NOW() WHERE `id` = {$this->id}");
        }

        return [
            "rlt" => $rlt,
            "subject" => $emailSubject,
            "message" => [],
            "to" => $to,
            "cc" => $cc,
            "bcc" => $bcc,
        ];
    }

    function Cancel($sendEmail = true, $force = false) {
        $return = ["success" => null, "warning" => null, "error" => null];
        $taster = $this->getTaster();

        $this->getTaster()->getCoordinator();
        $this->getUser();

        if (!$this->taster) {
            $return['error'] = "No Taster";
        } elseif (!$this->user) {
            $return['error'] = "No User";
        } elseif ($this->cancelIssued && $force !== true) {
            $return['warning'] = "Already cancelled {$this->cancelIssued}";
        } else {
            $sport = $taster->getSport();
            $journeyText = "netball";
            if ($sport->id == 9) $journeyText = "football";

            $subject = "Couch2Court Cancellation";
            $message[] = "Hi {$this->user->firstname}";
            $message[] = "Unfortunately we have had to cancel our planned session at " . $this->taster->formatVenue() . "  on " . $this->taster->formatDate() . " due to circumstances beyond our control";
            $message[] = "We're so sorry and apologise for any disappointment caused.";
            $message[] = "Payments made via our website have been refunded and should appear between 5-10 days in your account.";
            $message[] = "We really hope to bring $journeyText to your local area in the future but until then please keep an eye out for updates on our website https://www.bloomnetball.co.uk and many thanks again for taking an interest.";
            $message[] = "Best wishes";
            $message[] = "The Bloom Netball Team";
            $message[] = "W: https://www.bloomnetball.co.uk";
            $to[$this->user->email] = $this->user->email;
            $cc = [
                $this->taster->coordinator->email => "{$this->taster->coordinator->firstname} {$this->taster->coordinator->lastname}"
            ];
            $bcc = [
                "<EMAIL>" => "Charlotte Waugh",
            ];
            if ($sendEmail === true) $emailRlt = Email::Issue($subject, $message, $to, $cc, $bcc);
            $sql = "UPDATE `c2c_bookings` SET `cancelIssued` = NOW() WHERE `id` = {$this->id}";
            new Db($sql);
            $return['success'] = true;
        }
        return $return;
    }

    function getTaster() {
        if (!$this->taster) $this->taster = new TasterSession($this->tasterID);
        return $this->taster;
    }

    function getUser() {
        if (!$this->userID) $this->SynchroniseUser();
        if ($this->userID && !$this->user) $this->user = new User($this->userID);
        return $this->user;
    }

    function getAttendees() {
        /* Returns list of C2C_Attendee objects */
        if (!$this->attendees) $this->attendees = C2C_Attendee::C2C_Booking($this);
        return $this->attendees;
    }

    function SynchroniseUser() {
        if ($this->userID || !$this->email) return;
        $user = User::SearchCreate([
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "email" => $this->email,
            "mobile" => $this->mobile,
        ]);
        if (is_object($user) && $user->id) {
            // echo "Looked up User {$user->id}<br>";
            $this->userID = $user->id;
            $this->Save();
            return;
        }
    }

    function calculatePrice() {
        $this->getTaster();
        $this->getAttendees();
        $this->price = $this->taster->charge;

        if ($this->attendees) {
            $this->price += count($this->attendees) * $this->taster->charge;
        }

        if ($this->discountCode && strtoupper($this->discountCode) === "C2CFREE") {
            // Full discount for "C2CFREE" code
            $this->fullPrice = $this->price;
            $this->discount = $this->fullPrice;
            $this->price = 0;
        } elseif ($this->discountCode && substr(strtoupper($this->discountCode), 0, 8) === "DISCOUNT") {
            if (is_numeric($discounting = substr($this->discountCode, 8)) && $discounting > 0 && $discounting <= 100) {
                $this->fullPrice = $this->price;
                $this->discount = round($this->fullPrice * ($discounting / 100), 2);
                $this->price = $this->fullPrice - $this->discount;
            }
        }

        return $this->price;
    }


    function getStripePaymentIntent() {
        if (!$this->email) return;
        $stripe = new Stripe($this->stripeProduction);
        /* Lookup */
        if (!($user = User::EmailLookup($this->email))) { # No User by Email
            if (User::fromMobile($this->mobile)) $this->mobile = null; # Found mobile though so scrub it.
            $user = User::Create([
                "firstname" => $this->firstname,
                "lastname" => $this->lastname,
                "email" => $this->email,
                "mobile" => $this->mobile
            ]);
        }
        $stripe->setStripeCustomer($user);
        if ($this->stripePaymentIntentID) {
            $pi = $stripe->fetchPaymentIntent($this->stripePaymentIntentID);
            if (!$this->stripeStatus || $this->stripeStatus != $pi->status) {
                $this->stripeStatus = $pi->status;
                $this->Save();
            }
            if ($this->stripeStatus && $pi->amount != $this->price && Stripe::Statuses()[$this->stripeStatus]['editable'] === true) $stripe->updatePaymentIntent($this->stripePaymentIntentID, ["amount" => $this->price]);
            return $this->stripePaymentIntentID;
        }
        $paymentIntent = $stripe->createPaymentIntent($this->price, "Couch2Court {$this->tasterID}", null, [], true);
        $this->stripePaymentIntentID = $paymentIntent->id;
        $this->stripePaymentIntentClientSecret = $paymentIntent->client_secret;
        $this->stripeStatus = $paymentIntent->status;
        $this->Save();
        return ["success" => $this->stripePaymentIntentID];
    }

    function ApiData() {
        $this->getTaster();
        $this->getAttendees();
        if ($this->attendees) {
            foreach ($this->attendees as $attendee) $booking_attendees[] = $attendee->ApiData();
        } else $booking_attendees = [];
        $this->calculatePrice();
        $rlt = $this->getStripePaymentIntent();
        if (isset($rlt['error'])) return $rlt['error'];
        $stripe = new Stripe($this->stripeProduction);
        if ($this->stripePaymentIntentID && !$this->stripePaymentIntentClientSecret) {
            $pi = $stripe->fetchPaymentIntent($this->stripePaymentIntentID);
            $this->stripePaymentIntentClientSecret = $pi->client_secret;
            $this->Save();
        }
        $getCoordinator = $this->taster->getCoordinator();

        $coordinator = ($getCoordinator) ? [
            'name' => $getCoordinator->firstname,
            'lastname' => $getCoordinator->lastname,
            'mobile' => $getCoordinator->mobile,
            'email' => $getCoordinator->email,
        ] : null;

        return [
            "id" => $this->id,
            "created" => $this->created,
            "updated" => $this->updated,
            "tasterID" => $this->tasterID,
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "email" => $this->email,
            "mobile" => $this->mobile,
            "data" => $this->data,
            "taster" => ($this->taster) ? $this->taster->ApiData() : null,
            "attendees" => $booking_attendees,
            "charge" => $this->price,
            "fullPrice" => $this->fullPrice,
            "discount" => $this->discount,
            "discountCode" => $this->discountCode,
            "stripePaymentIntentID" => $this->stripePaymentIntentID,
            "stripeClientSecret" => $this->stripePaymentIntentClientSecret,
            "stripePublicKey" => $stripe->publicKey,
            "stripeStatus" => $this->stripeStatus,
            "coordinator" => $coordinator,
            "tournamentName" => $this->taster->tournamentName,
            "numberOfWeeks" => $this->taster->numberOfWeeks,
        ];
    }

    function cancelRefund() {
        $this->getTaster();
        $this->getAttendees();
    }

    function Refund() {
        if (!$this->stripePaymentIntentID) return ["error" => "No stripe payment intent ID"];
        $stripe = new Stripe(true);
        /* ERROR = String Failure Message. SUCCESS = Stripe Refund Object */
        return $stripe->refundPaymentIntent($this->stripePaymentIntentID);
    }

    static function Unconfirmeds() {
        $sql = "SELECT * FROM `c2c_bookings` WHERE `deleted` IS NULL AND`confirmIssued` IS NULL AND (`stripeStatus` = 'requires_capture' OR `stripeStatus` = 'succeeded')";
        return static::Query($sql);
    }

    static function TasterSession(TasterSession $tasterSession) {
        return static::Query("SELECT * FROM `c2c_bookings` WHERE `tasterID` = {$tasterSession->id} AND `deleted` IS NULL AND (`userID` IS NOT NULL OR `email` IS NOT NULL)");
    }

    static function TasterNumbers(TasterSession $tasterSession) {
        // Confirmed: Stripe Status = 'succeeded' or 'requires_capture'
        $sql1 = "
            SELECT 
                COALESCE(SUM(total), 0) AS total 
            FROM (
                SELECT COUNT(cb.`id`) AS total 
                FROM `c2c_bookings` cb 
                WHERE cb.`tasterID` = {$tasterSession->id} 
                    AND cb.`deleted` IS NULL 
                    AND (cb.`userID` IS NOT NULL OR cb.`email` IS NOT NULL) 
                    AND cb.`stripeStatus` IS NOT NULL 
                    AND (cb.`stripeStatus` = 'succeeded' OR cb.`stripeStatus` = 'requires_capture')
    
                UNION ALL
    
                SELECT COUNT(ca.`id`) AS total 
                FROM `c2c_attendees` ca 
                INNER JOIN `c2c_bookings` cb ON ca.bookingID = cb.`id`
                WHERE cb.`tasterID` = {$tasterSession->id} 
                    AND cb.`deleted` IS NULL 
                    AND ca.`deleted` IS NULL
                    AND cb.`stripeStatus` IS NOT NULL 
                    AND (cb.`stripeStatus` = 'succeeded' OR cb.`stripeStatus` = 'requires_capture')
            ) AS combined;
        ";

        // Unconfirmed:  Stripe Status != 'succeeded' or 'requires_capture'
        $sql2 = "
            SELECT 
                COALESCE(SUM(total), 0) AS total 
            FROM (
                SELECT COUNT(cb.`id`) AS total 
                FROM `c2c_bookings` cb 
                WHERE cb.`tasterID` = {$tasterSession->id} 
                    AND cb.`deleted` IS NULL 
                    AND (cb.`stripeStatus` != 'succeeded' AND cb.`stripeStatus` != 'requires_capture')
    
                UNION ALL
    
                SELECT COUNT(ca.`id`) AS total 
                FROM `c2c_attendees` ca 
                INNER JOIN `c2c_bookings` cb ON ca.bookingID = cb.`id`
                WHERE cb.`tasterID` = {$tasterSession->id} 
                    AND cb.`deleted` IS NULL 
                    AND ca.`deleted` IS NULL
                    AND (cb.`stripeStatus` != 'succeeded' AND cb.`stripeStatus` != 'requires_capture')
            ) AS combined;
        ";

        $db1 = new Db($sql1);
        $db2 = new Db($sql2);

        return [
            'confirmed' => isset($db1->rows[0]['total']) ? $db1->rows[0]['total'] : 0,
            'unconfirmed' => isset($db2->rows[0]['total']) ? $db2->rows[0]['total'] : 0,
        ];
    }


    static function Total(TasterSession $tasterSession) {
        $bookings = static::TasterSession($tasterSession);
        if (!$bookings) return 0;
        $count = 0;
        foreach ($bookings as $booking) {
            $count++; # One for the Booking person
            $count += C2C_Attendee::Total($booking);
        }
        return $count;
    }

    static function Bookers(TasterSession $tasterSession) {
        $sql = "SELECT * FROM `c2c_bookings` WHERE `tasterID` = {$tasterSession->id} AND `deleted` IS NULL AND (`userID` IS NOT NULL OR `email` IS NOT NULL)";
        $rlt = static::Query($sql);
        if (!$rlt) return [];
        $return = [];
        foreach ($rlt as $r) {
            if (!$r->userID) $r->SynchroniseUser();
            $return[] = $r;
        }
        return $return;
    }

    static function Attendees(TasterSession $tasterSession) {
        $sql = "SELECT * FROM `c2c_bookings` WHERE `tasterID` = {$tasterSession->id} AND `deleted` IS NULL AND (`userID` IS NOT NULL OR `email` IS NOT NULL)";
        $rlt = static::Query($sql);
        if (!$rlt) return [];
        $return = [];
        foreach ($rlt as $r) {
            if (!$r->userID) $r->SynchroniseUser();
            $r->getAttendees();
            $return[] = $r;
        }
        return $return;
    }

    static function Create(TasterSession $tasterSession, array $booker, String $discountCode = null, array $attendees = []) {
        $tasterSession = new static();
        $tasterSession->tasterID = $tasterSession->id;
        $user = User::SearchCreate($booker);
        $tasterSession->userID = $user->id;
        foreach ($booker as $k => $v) $tasterSession->$k = $v;
        $tasterSession->discountCode = $discountCode;
        $tasterSession->Save();
        if ($attendees) {
            foreach ($attendees as $attendee) {
                $attendeeUser = User::SearchCreate($attendee);
                $c2c_attendee = new C2C_Attendee();
                $c2c_attendee->bookingID = $tasterSession->id;
                $c2c_attendee->userID = $attendeeUser->id;
                foreach ($attendee as $k => $v) $c2c_attendee->$k = $v;
                $c2c_attendee->Save();
            }
        }
        $tasterSession->calculatePrice();
        $tasterSession->getStripePaymentIntent();
    }
}
