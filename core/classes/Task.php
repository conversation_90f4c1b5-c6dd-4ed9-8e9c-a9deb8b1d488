<?php

class Task extends Base {

    protected $dbTable = "tasks", $dbOrder = ["name" => "ASC"];

    protected $dbFields = ["category","name", "ownerID","teamID","leagueID","description","closed"];
    protected $name, $ownerID, $teamID, $leagueID, $description;

    function __construct(Int $id = null) {
        parent::__construct($id);
        switch ($this->status) {
            case 1: $this->statusText = "Join Waiting List"; break;
            case 2: $this->statusText = "Closed for Registration"; break;
            default: $this->statusText = "Open for Registration";
        }
    }
    function __toString() {
        return "{$this->name}";
    }
    /* Getters */
    function getName() { return $this->name;}
    function getOwnerID() { return $this->ownerID;}
    function getTeamID() { return $this->teamID;}
    function getLeagueID() { return $this->leagueID;}
    function getDescription() { return $this->description;}
    /* Checkers */
    /* Doers */
    function Save() {
        return parent::Save();
    }
    static function Categories() {
        return [
            1 => "Finance",
            2 => "Team"
        ];
    }
}