<?php

class LeagueNote extends Base {

    protected $dbTable = "leagueNotes";
    protected $leagueID, $userID;
    protected $text;
    protected $dbFields = ["leagueID", "userID","text"];

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function getCreated() {
        return date('g:ia jS F',strtotime($this->created));
    }
    function getUser() {
        return ($this->userID) ? $this->userID : "System";
    }
    function getText() {
        return $this->text;
    }
    function Save() {
        parent::Save();
    }
    static function Add (League $league, String $text, Int $userID = null) {
        $latest = static::Latest($league);
        if ($latest && $latest['text']==$text) return;
        $sqlData["leagueID"] = $league->id;
        $sqlData["text"] = $text;
        if ($userID) $sqlData['userID'] = $userID;
        $rlt = new Db('leagueNotes', $sqlData);
        \Messaging\Add($text,"info");
    }
    static function Clear () {
        $sql = "DELETE FROM `leagueNotes` WHERE `created` < DATE(NOW()) - INTERVAL 7 DAY";
       new Db($sql);       
    }
    static function League (League $league) {
        $sql = "SELECT * FROM `leagueNotes` WHERE `leagueID` = {$league->id} ORDER BY `created` DESC";
        $db = new Db($sql);if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $ln = new static();
            $ln->Load($r);
            $return[] = $ln;
        }
        return $return;
    }
    static function Latest(League $league) {
        $sql = "SELECT * FROM `leagueNotes` WHERE `leagueID` = :leagueID ORDER BY `created` DESC LIMIT 1";
        $db = new Db($sql, ["leagueID" => $league->id]);if (!$db->rows) return;
        return $db->rows[0];
    }
}