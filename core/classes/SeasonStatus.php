<?php

class SeasonStatus extends Base {

    protected $dbTable = "seasonStatus";
    protected $active, $live;

    // function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() { return "{$this->name}";}

    function isNextSeason() {
        return ($this->active == 1 && !$this->live) ? true : false;
    }

    function ApiData() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "isCurrent" => ($this->active && $this->live) ? true : false,
            "isNext" => ($this->active && !$this->live) ? true : false
        ];
    }
}