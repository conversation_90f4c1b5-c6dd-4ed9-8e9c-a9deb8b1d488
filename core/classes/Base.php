<?php

class Base {

    protected $dbTable, $dbKey = "id", $dbOrder = [];
    protected $created, $updated, $deleted;
    public $id;
    protected $name;
    protected $dbFields = [];

    function __construct($id = null) {
        if ($id && $this->dbTable && $this->dbKey) {
            $db = new Db($sql = "SELECT * FROM `$this->dbTable` WHERE `$this->dbKey` = $id");
            if ($db->rows) $this->Load($db->rows[0]);
        }
    }

    function Load(array $data, $enumerate = false) {
        foreach ($data as $k => $v) {
            $this->{$k} = ($v) ? $v : null;
        }
    }

    function Save() {
        if (!$this->dbFields) return \Messaging\Add("No database fields setup for "  . get_called_class());
        $sql = null;
        $sqlData = [];
        foreach ($this->dbFields as $field) {
            if ($sql) $sql .= ",";
            $sql .= "`$field` = :$field";
            $sqlData[$field] = $this->$field;
        }
        if (!$this->{$this->dbKey}) {
            $sql = "INSERT INTO `$this->dbTable` SET $sql";
            $db = new Db($sql, $sqlData);
            if (!$db->errors) {
                $this->{$this->dbKey} = $db->lastInsertID;
                return (int)$db->lastInsertID;
            } else return  implode(". ", $db->errors);
        } else {
            $sql = "UPDATE `$this->dbTable` SET $sql WHERE `$this->dbKey` = :$this->dbKey";
            $sqlData[$this->dbKey] = $this->{$this->dbKey};
            $db = new Db($sql, $sqlData);
            if (!$db->errors) {
                return (int)$db->affectedRows;
            } else return  implode(". ", $db->errors);
        }
    }

    function __get($value) {
        if (isset($this->$value)) return $this->$value;
    }

    function __set($property, $value) {
        $this->$property = $value;
    }

    function __clone() {
        $this->id = null;
    }
    function getID() {
        return $this->id;
    }
    function getName() {
        return $this->name;
    }
    function Delete(String $log_message = null) {
        new Db("UPDATE `{$this->dbTable}` SET `deleted` = NOW() WHERE `{$this->dbKey}` = {$this->id}");
        if ($log_message) Logging::Add($log_message);
    }

    function Sql_Confirm() {
    }

    function Sql_Create(Bool $overwrite = false) {
        $sql = "CREATE ";
        if ($overwrite === true) $sql .= "OR REPLACE ";
        $sql .= "TABLE `{$this->dbTable}` (";
        $conn = null;
        foreach ($this->sql() as $k => $v) {
            $sql .= $conn . "`$k` {$v['type']}";
            if (isset($v['default']) && $v['default']) $sql .= " DEFAULT {$v['default']}";
            if (isset($v['extra']) && $v['extra']) $sql .= " {$v['extra']}";
            if (isset($v['pk'])) $sql .= " PRIMARY KEY AUTO_INCREMENT";
            if (!$conn) $conn = ", ";
        }
        $sql .= ")";
        return $sql;
    }

    static function Fetch() {
        $o = new static();
        $orderBy = ($o->dbOrder) ? "ORDER BY " . key($o->dbOrder) . " " . current($o->dbOrder) : null;
        $sql = "SELECT `$o->dbKey` FROM `$o->dbTable` $orderBy";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $return[] = new static($r['id']);
        }
        return $return;
    }

    static function Archive(Int $id) {
        $obj = new static();
        $sql = "UPDATE `{$obj->dbTable}` SET `deleted` = NOW() WHERE `id` = $id";
        new Db($sql);
    }

    static function Unarchive(Int $id) {
        $obj = new static();
        $sql = "UPDATE `{$obj->dbTable}` SET `deleted` = NULL WHERE `id` = $id";
        new Db($sql);
    }

    static function Duplicate(Int $id) {
        $orig = new static($id);
        $new = clone ($orig);
        $new->Save();
        return $new;
    }

    static function Listing(Int $limit = null, Int $start = null) {
        $obj = new static();
        $sql = "SELECT * FROM {$obj->dbTable}";
        if ($obj->dbOrder) {
            $sql .=  " ORDER BY ";
            foreach ($obj->dbOrder as $field => $sortOrder) {
                $sql .=  "$field $sortOrder";
            }
        }
        if ($limit) {
            $sql .= ($start) ? " LIMIT $start,$limit" : " LIMIT $limit";
        }
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $o = new static();
            $o->Load($r);
            (!$return) ? $return[] = $o : array_push($return, $o);
        }
        return $return;
    }

    static function Query(String $sql = null, array $data = []) {
        $obj = new static();
        if (!$sql) $sql = "SELECT * FROM `{$obj->dbTable}`";
        $db = new Db($sql, $data);
        // echo $sql;
        if (!$db->rows) return [];
        // Tools::Dump($db);
        $return = [];
        foreach ($db->rows as $r) {
            $o = new static();
            $o->Load($r);
            $return[] = $o;
        }
        return $return;
    }

    // static function Live () {
    //     $obj = new static();
    //     $sql = "SELECT * FROM {$obj->dbTable}";
    //     $sql .= " WHERE `deleted` IS NULL";
    //     if ($obj->dbOrder) {
    //         $sql .=  " ORDER BY ";
    //         foreach ($obj->dbOrder as $field => $sortOrder) {
    //             $sql .=  "$field $sortOrder";
    //         }
    //     }
    //     return static::Query($sql);
    // }

    static function Undeleted() {
        $obj = new static();
        $sql = "SELECT * FROM {$obj->dbTable}";
        $sql .= " WHERE `deleted` IS NULL";
        if ($obj->dbOrder) {
            $sql .=  " ORDER BY ";
            foreach ($obj->dbOrder as $field => $sortOrder) {
                $sql .=  "$field $sortOrder";
            }
        }
        return static::Query($sql);
    }
}
