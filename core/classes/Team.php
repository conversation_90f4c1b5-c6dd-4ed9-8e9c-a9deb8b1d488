<?php

class Team extends Base {

    protected $dbTable = "teams";
    protected $dbOrder = ["name" => "ASC"];
    protected $name;
    protected $seasonID, $divisionID;
    protected $captainID, $treasurerID;
    protected $treasurerStripePaymentMethodID;
    protected $treasurerErrorText, $treasurerErrorStamp;
    protected $paymentDays;
    protected $nextConfirmed, $nextConfirmedBy;
    protected $wildcard;
    protected $dbFields = ["name", "leagueID", "seasonID", "divisionID", "captainID", "treasurerID", "deleted", "treasurerErrorText", "treasurerErrorStamp", "agedDate", "nextConfirmed", "wildcard"];

    protected $leagueName;
    protected $league, $season, $division;
    protected $management;
    protected $teamManagers;
    protected $lockerRoomVersion;
    protected $agedDebt;

    protected $currentSeason, $nextSeason;

    # User objects for Captain and Manager
    protected $captain, $treasurer, $coordinator;

    protected $teamCaptain;
    protected $stripePaymentMethodID;
    protected $payInFull;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function getCurrentSeason() {
        $this->getLeague();
        if (!$this->currentSeason) $this->currentSeason = Season::Current($this->league);
        return $this->currentSeason;
    }

    function getNextSeason() {
        $this->getLeague();
        if ($this->league && !$this->nextSeason) $this->nextSeason = Season::Next($this->league);
        return $this->nextSeason;
    }

    function haveReentered() {
        $nextSeason = $this->getNextSeason();
        if (!$nextSeason) return false;
        return (TeamSeason::IsIn($this, $nextSeason)) ? true : false;
    }

    function CanReenter() {
        # Returns STRING error 
        # TRUE if already entered
        # Season object if possible
        # Does the League have a Next
        $this->getLeague();
        if (!$this->league) return null;
        $next = Season::Next($this->league);
        if (!$next) return null; # No Next season
        # There is a next season so return ARRAY with Next seasonID and
        # with entered value of teamSeasonID (if already entered) else NULL
        return (TeamSeason::IsIn($this, $next)) ? null : $next;
    }

    function getPayer() {
        return TeamFollower::Payer($this);
        # Return v2 Payer
        if ($this->lockerRoomVersion == 2) return TeamFollower::Payer($this);
        # Return v1 Treasurer
        if ($this->treasurerID) return new User($this->treasurerID);
        # Return v1 Captain
        return new User($this->captainID);
    }

    function __toString() {
        return "{$this->name}";
    }

    function fullName() {
        return "{$this->name} | " . $this->getLeagueName();
    }

    function getLeague() {
        if (!$this->league && $this->leagueID) $this->league = new League($this->leagueID);
        return $this->league;
    }

    function getSeason() {
        if (!$this->season && $this->seasonID) $this->season = new Season($this->seasonID);
        return $this->season;
    }

    function getDivision() {
        if (!$this->division && $this->divisionID) $this->division = new Division($this->divisionID);
        return $this->division;
    }

    function getLeagueName() {
        $this->getLeague();
        if ($this->league) return $this->league->__toString();
    }

    function getManagement() {
        // if ($this->management) $this->management = TeamManagement::Team($this);
        // return $this->management;
    }

    function Save($noLog = false) {
        if (!$this->leagueID) return "Team must be allocated to a League";
        $this->name = str_replace(["`"], ["'"], $this->name);
        $rlt = parent::Save();
        if ($noLog !== true) Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }

    function defaultDivisionAllocation() {
        $divisions = Division::forSeason(new Season($this->seasonID));
        end($divisions);
        $endDivision = current($divisions);
        $divisionID = $endDivision->getID();
        $sql = "UPDATE `teams` SET `divisionID` = $divisionID WHERE `id` = {$this->id}";
        new Db($sql);
    }

    function getCurrentCaptain() {
        if (!$this->captain) $this->captain = User::TeamCaptain($this);
        return $this->captain;
    }

    function getCurrentTreasurer() {
        if (!$this->treasurer) $this->treasurer = User::TeamTreasurer($this);
        return $this->treasurer;
    }

    /* Getters */
    function getLeagueID() {
        return $this->leagueID;
    }
    function getSeasonID() {
        return $this->seasonID;
    }
    function getDivisionID() {
        return $this->divisionID;
    }
    function getDivisionName() {
        return new Division($this->divisionID);
    }
    function getCaptain() {
        return $this->captainID;
    }
    function getTreasurer() {
        if ($this->lockerRoomVersion == 2) {
            // echo "v2";
            $treasurer = TeamFollower::Payer($this);
            if ($treasurer) return $treasurer->id;
        } else {
            // echo "v1";
            $treasurer = new User($this->treasurerID);
            if (!$treasurer->stripeCustomerID()) return;
            return $this->treasurerID;
        }
    }

    function getTreasurerID() {
        return $this->getTreasurer();
        return $this->treasurerID;
    }

    function getTreasurerErrorText() {
        return $this->treasurerErrorText;
    }
    function getTreasurerErrorStamp() {
        return $this->treasurerErrorStamp;
    }

    function getTeamTreasurer() {
        if (($tft = TeamFollower::Treasurer($this))) return $tft;
        if ($this->getTreasurerID()) return new User($this->getTreasurerID());
        return new User($this->captainID);
    }

    function getTeamCaptain() {
        if (!$this->teamCaptain) {
            $captainID = TeamFollower::Captain($this);
            // return $captainID;
            $this->teamCaptain = new User($captainID);
        }
        return $this->teamCaptain;
    }

    function getTeamManagerIDs() {
        $return = [
            'captainID' => null,
            'treasurerID' => null,
        ];
        if (!$this->teamManagers) $this->teamManagers = TeamFollower::Current($this);
        foreach ($this->teamManagers as $tm) {
            if ($tm->isCaptain == 1) $return['captainID'] = $tm->userID;
            if ($tm->isTreasurer == 1) $return['treasurerID'] = $tm->userID;
        }
        return $return;
    }

    function getTeamManagers() {
        // $return['captain'] = new User(TeamFollower::Captain($this));
        // $return['treasurer'] = (($t=TeamFollower::Treasurer($this))) ? new User($t) : $return['captain'];
        // return $return;
        // if ($this->lockerRoomVersion == 2) {
        # Returns Array of TeamFollower
        if (!$this->teamManagers) $this->teamManagers = TeamFollower::Current($this);
        // return $this->teamManagers;
        foreach ($this->teamManagers as $tm) {
            // if ($tm->stripePaymentMethodID) $this->stripePaymentMethodID = $tm->stripePaymentMethodID;
            // if ($tm->payInFull == 1) $this->payInFull = 1;
            if ($tm->isCaptain == 1) $this->captain = new User($tm->userID);
            if ($tm->isTreasurer == 1) $this->treasurer = new User($tm->userID);
        }
        if (!$this->treasurer) $this->treasurer = $this->captain;
        return [
            "captain" => $this->captain,
            "treasurer" => $this->treasurer,
        ];
        // } else {
        //     $this->captain = $this->getTeamCaptain();
        //     $this->treasurer = $this->getTeamTreasurer();
        // }
    }

    function getCoordinatorID() {
        $this->getLeague();
        return ($this->league->getCoordinatorID()) ? $this->league->getCoordinatorID() : 1;
    }

    function getCoordinator() {
        if (!$this->coordinator) {
            $this->getLeague();
            $this->coordinator = $this->league->getCoordinator();
        }
        return $this->coordinator;
    }

    function getAgedDate(String $format = 'Y-m-d') {
        return ($this->agedDate) ? date($format, strtotime($this->agedDate)) : null;
    }

    function agedDays() {
        if (!$this->agedDate) return 0;
        return floor((time() - strtotime($this->agedDate)) / 86400);
    }

    function setTreasurerError(String $text) {
        $this->treasurerErrorText = $text;
        if (!$this->treasurerErrorStamp) $this->treasurerErrorStamp = date('Y-m-d H:i:s');
        $this->Save();
    }

    function clearTreasurerError() {
        $this->treasurerErrorText = $this->treasurerErrorStamp = null;
        $this->Save();
    }

    function paymentMethodStatus() {
        return ($this->treasurerStripePaymentMethodID) ? true : false;
    }

    function getStripePaymentMethodID() {
        return $this->treasurerStripePaymentMethodID;
    }

    function getStatus() {
        /* Allocated to League? */
        if (!$this->leagueID) return "No League Allocation";
        /* Allocated to Season? */
        if (!$this->seasonID) return "No Season Allocation";
        /* Has Captain? */
        if (!$this->captainID) return "Requires a Captain";
        /* Has Treasurer? */
        if (!$this->treasurerID) return "Requires a Treasurer";
        /* Captain Accepted Ts&Cs */

        /* Treasurer Applied Payment Card */
    }

    function PaymentToConfirm() {
        $ptc = StripePayment::Active($this);
        if ($ptc) {
            $pm = Stripe::getPaymentMethod($ptc->payment_method);
            $card = ucwords($pm->card->brand) . " " . ucwords($pm->card->funding) . " card ending " . $pm->card->last4 . " Exp " . $pm->card->exp_month . "/" . $pm->card->exp_year;
            return [
                "amount" => (float)$ptc->amount / 100,
                "client_secret" => $ptc->client_secret,
                'paymentIntentID' => $ptc->id,
                'payment_method' => $ptc->payment_method,
                'card' => $card,
                "public_key" => Stripe::ThePublicKey()
            ];
        }
        return;
    }

    function getAgedDebt() {
        if (!$this->agedDebt) $this->agedDebt = Finance::Balance($this);
        return $this->agedDebt;
    }

    function AgedDateEmail() {
        if (!($balance = $this->getAgedDebt()) || $balance <= 0) return "Nothing Owed";
        $managers = $this->getTeamManagers();
        if (!$managers['captain'] && !$managers['treasurer']) return "Nobody to email";
        if (!$managers['captain']) $managers['captain'] = $managers['treasurer'];
        $this->getLeague();
        $this->getCoordinator();
        $hi = $managers['captain']->firstname;
        if ($managers['treasurer'] && $managers['treasurer'] == $managers['captain']) {
            $to = [$managers['treasurer']->email => $managers['treasurer']->__toString()];
            $hi = $managers['treasurer']->firstname;
        } elseif ($managers['treasurer']) {
            $to = [$managers['treasurer']->email => $managers['treasurer']->__toString()];
            $hi = $managers['treasurer']->firstname;
            $cc = [$managers['captain']->email => $managers['captain']->__toString()];
        } else $to = [$managers['captain']->email => $managers['captain']->__toString()];
        $cc[$this->coordinator->email] = $this->coordinator->__toString();
        $subject = "Action needed";
        $message[] = "Hi {$hi}";
        $message[] = "Your team has a balance that we are unable to charge for.";
        $message[] = "We have outlined the details below.";
        $message[] = "$this in {$this->league} currently owe $balance which we have been unable to bill.";
        $message[] = "Please log into the <a href=\"https://lockerroom.bloomnetball.co.uk/\">locker room</a> and head to the admin section. You'll need to review the payment details that you have entered.";
        $message[] = "If you have any questions or need further assistance, then please contact your local Bloom Netball coordinator {$this->coordinator} {$this->coordinator->email} <NAME_EMAIL>";
        $message[] = "Many Thanks";
        $message[] = "Team Bloom Netball";
        $bcc = [];
        return Email::Issue($subject, $message, $to, $cc, $bcc);
    }

    /* Setters */
    function setAgedDate(String $agedDate = null) {
        $this->agedDate = ($agedDate) ? $agedDate : date('Y-m-d');
        $this->Save(true);
    }

    function TreasurerCheck() {
        if ($this->lockerRoomVersion == 2) return true;
        if (!$this->treasurerID) return "Missing Treasurer";
        $treasurer = new User($this->getTreasurerID());
        if (!$treasurer->id) return "Invalid Treasurer";
        if (!$treasurer->isActivated()) return "Treasurer User Not Activated";
        if (!$treasurer->stripeCustomerID()) return "{$treasurer} does not have a Stripe ID";
        if (!$this->treasurerStripePaymentMethodID) return "there is no payment card on file";
        $pm = Stripe::getPaymentMethod($this->treasurerStripePaymentMethodID);
        if ($pm->customer != $treasurer->stripeCustomerID()) {
            new Db("UPDATE `teams` SET `treasurerStripePaymentMethodID` = NULL WHERE `id` = {$this->id}");
            return "{$treasurer} does not own the Payment Card on file. Most often this happens where a Captain or Former Treasurer's card remains on file and the Treasurer has changed. This card has now been removed from our records.";
        }
        $expiryDate = date('Y-m-t', strtotime("{$pm->card->exp_year}-{$pm->card->exp_month}-01"));
        if ($expiryDate < date('Y-m-d')) return "the payment card for {$treasurer} has expired";
        return true;
    }

    function PaymentChecks() {
        $return = [
            'note' => null,
            'treasurer' => null,
            'paymentSource' => null,
            'paymentMethod' => null,
            'paymentCard' => [],
            'stripeCustomerIDs' => [],
            'stripeCustomerMatched' => null,
            'expiryDate' => null
        ];
        # Has a nominated Treasurer?
        $return['treasurer'] = TeamFollower::Payer($this);
        # Nominated Treasurer a valid user?
        # Team has PaymentSource?
        $return['paymentSource'] = TeamFollower::PaymentSource($this);
        // return $return;
        # Does Stripe hold this Payment Method?
        if ($return['paymentSource']) {
            try {
                $return['paymentMethod'] = Stripe::getPaymentMethod($return['paymentSource']->stripePaymentMethodID);
            } catch (Exception $e) {
                $return['note'] = ($return['note']) ? "{$return['note']}. " . $e->getMessage() : $e->getMessage();
            }
            # Treasurer has a StripeCustomerID?
            $return['stripeCustomerIDs'] = $return['treasurer']->getStripeCustomerIDs();
            # Payment Source owned by Treasurer?
            if (!$return['stripeCustomerIDs']) {
                $return['note'] = ($return['note']) ? "{$return['note']}. No Stripe Customer IDs" : "No Stripe Customer IDs";
            } else {
                foreach ($return['stripeCustomerIDs'] as $s) {
                    if ($s['stripeCustomerID'] == $return['paymentMethod']->customer) {
                        $return['stripeCustomerMatched'] = true;
                        break;
                    }
                }
            }
            # Payment Source Unexpired
            $return['expiryDate'] = date('Y-m-t', strtotime("{$return['paymentMethod']->card->exp_year}-{$return['paymentMethod']->card->exp_month}-01"));
            if (!$return['expiryDate'] || $return['expiryDate'] < date('Y-m-d')) {
                $return['note'] = ($return['note']) ? "{$return['note']}. Payment card expired" : "Payment card expired";
            }
            $stripe = new StripeConnection();
            $return['paymentCard'] = $stripe->PaymentMethod_ApiData($return['paymentSource']->stripePaymentMethodID);
        }
        return $return;
    }

    function ApiData() {
        # This data is for Managers and Squad alike - no sensitive info in this API response
        $this->getLeague();
        $this->getTeamManagers();

        $teamSeasons = TeamSeason::Current($this);
        $season = $division = null;
        if (isset($teamSeasons[0])) {
            $season = new Season($teamSeasons[0]->seasonID);
            $division = new Division($teamSeasons[0]->divisionID);
        }
        $issues = $this->Checks();
        $alerts = ($issues) ? count($issues) : null;
        // $card = [];
        // if ($this->stripePaymentMethodID) {
        //     $stripe = new StripeConnection();
        //     $card = $stripe->PaymentMethod_ApiData($this->stripePaymentMethodID);
        // }
        // $reenterSeason = $this->CanReenter();
        // if (is_object($reenterSeason)) {            
        //     $v = $reenterSeason->getVenue();
        //     $seasonRenter = $reenterSeason->ApiBasic();
        //     $seasonRenter['venue'] = $reenterSeason->venue->ApiBasic();
        // } else $seasonRenter = null;
        // $terms = ($this->league) ? $this->league->TermsURL() : null;
        $captain = ($this->captain) ? $this->captain->ApiOutput() : null;
        return [
            "id" => $this->id,
            "name" => $this->name,
            "league" => ($this->league) ? $this->league->ApiBrief() : null,
            "season" => ($season) ? $season->ApiData() : null,
            "division" => ($division) ? $division->ApiData() : null,
            "captain" => $captain,
            "treasurer" => ($this->treasurer)
                ? $this->treasurer->ApiOutput() :
                $captain,
            "alerts" => $alerts,
            // "stripePaymentMethodID" => $this->stripePaymentMethodID,
            // "card" => $card,
            // "payInFull" => TeamFollower::PayingInFull($this),
            // "canReenter" => $this->CanReenter(),
            // "terms" => $terms ?? null,
        ];
    }

    function ApiOutput() {
        $ts = TeamSeason::Team($this);
        $teamSeasons = [];
        foreach ($ts as $teamS) {
            $season = new Season($teamS->seasonID);
            $division = new Division($teamS->divisionID);
            $teamSeasons[] = ["season" => $season->ApiOutput(), "division" => $division->ApiData()];
        }
        return [
            "id" => $this->id,
            "name" => $this->name,
            "teamSeasons" => $teamSeasons,
        ];
    }

    function ApiBasic() {
        $teamManagers = $this->getTeamManagers();
        // exit(Tools::Dump($teamManagers['captain']));
        $managers = [];
        if ($teamManagers) {
            if ($teamManagers['captain']) $managers['captain'] = $teamManagers['captain']->ApiData();
            if ($teamManagers['treasurer']) $managers['treasurer'] = $teamManagers['treasurer']->ApiData();
            // $managers['treasurer'] = $teamManagers['treasurer']->ApiData();
        }
        return [
            "id" => $this->id,
            "leagueID" => $this->leagueID,
            "leagueName" => $this->leagueName,
            "fullName" => "{$this->name} | {$this->leagueName}",
            "name" => $this->name,
            "managers" => $managers,
        ];
    }

    function paymentMethod() {
        return $this->PaymentSource();
        if ($this->lockerRoomVersion == 2) {
            return $this->PaymentSource();
        } else return $this->treasurerStripePaymentMethodID;
    }

    function PaymentSource() {
        if (!$this->stripePaymentMethodID) {
            $paySource = TeamFollower::PaymentSource($this);
            if ($paySource) $this->stripePaymentMethodID = $paySource->stripePaymentMethodID;
        }
        return $this->stripePaymentMethodID;
    }

    function PaymentCard() {
        if ($this->PaymentSource()) {
            $stripe = new StripeConnection();
            return $stripe->PaymentMethod_ApiData($this->stripePaymentMethodID);
        }
    }

    function treasurerStripeCustomerID() {
        $this->getTeamManagers();
        return $this->treasurer->stripeCustomerID;
    }

    function Checks() {
        $this->PaymentSource();
        if (!$this->stripePaymentMethodID) $return[] = "No payment method on file";
        if ($this->stripePaymentMethodID) {
            $stripe = new StripeConnection();
            $card = $stripe->PaymentMethod_ApiData($this->stripePaymentMethodID);
            $expiryDate = strtotime(date('Y-m-t', strtotime("{$card['exp_year']}-{$card['exp_month']}-01 23:23:59")));
            if ($expiryDate < time()) $return[] = "Payment card has expired";
        }
        return $return ?? null;
    }

    function FullBalancePaymentValue() {
        # Get Current Season
        $currentSeason = $this->getCurrentSeason();
        if (!$currentSeason) return;
        $teamSeason = TeamSeason::TeamSeason($this, $currentSeason);
        if (!$teamSeason) return;
        return $teamSeason->FullBalancePayment();
    }

    /* Statics */
    static function forSeason(Season $season) {
        return TeamSeason::Season($season);
        // if (!$season->getID()) return;
        // $sql = "SELECT * FROM `teams` WHERE `seasonID` = " . $season->getID() . "  AND `deleted` IS NULL ORDER BY `name`";
        // // $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` WHERE `teamSeasons`.`seasonID` = " . $season->getID() . "  AND `teamSeasons`.`deleted` IS NULL ORDER BY `teams`.`name` GROUP BY `teams`.`id`";
        // $db = new Db($sql);if (!$db->rows) return;
        // $return = [];
        // foreach ($db->rows as $r) {
        //     $t = new static();
        //     $t->Load($r);
        //     ($return) ? array_push($return,$t) : $return[] = $t;
        // }  
        // return $return;
    }

    static function byDivision(Int $divisionID, $returnObjects = true) {
        $teams = TeamSeason::Division(new Division($divisionID));
        $return = [];
        foreach ($teams as $team) {
            $return[] = ($returnObjects === true) ? $team : $team->id;
        }
        return $return;
        // $sql = "SELECT `id` FROM `teams` WHERE `divisionID` = $divisionID AND `deleted` IS NULL ORDER BY `name`";
        // // $sql = "SELECT `teamID` AS `id` FROM `teamSeasons` WHERE `divisionID` = $divisionID AND `deleted` IS NULL ORDER BY `name`";
        // $db = new Db($sql);if (!$db->rows) return;
        // $return = [];
        // foreach ($db->rows as $r) {
        //     $return[] = ($returnObjects === true) ? new static($r['id']) : $r['id'];
        // } 
        // return $return;
    }

    function DivisionName() {
        return "";
    }

    function Captain() {
        $user = new User($this->captainID);
        return $user->__toString();
    }

    function Treasurer() {
        $user = new User($this->treasurerID);
        return $user->__toString();
    }

    function DuplicateCheck() {
        // Lookup teams in this league, with this name
        $sql = "SELECT `id`,`deleted` FROM `teams` WHERE `leagueID` = :leagueID AND `name` = :name";
        $db = new Db($sql, ["leagueID" => $this->leagueID, "name" => $this->name]);
        // Tools::Dump($db);
        if (!$db->rows) return false; # No team with that name in this league
        if ($this->id && $db->rows[0]['id'] == $this->id) return false; # Found the same team
        return true;
    }

    function Deletable() {
        $sql = "SELECT `id` FROM `finances` WHERE `teamID` = $this->id";
        $rlt = new Db($sql);
        $return['finances'] = $rlt->rows;
        $sql = "SELECT `id` FROM `fixtures` WHERE `teamID` = $this->id";
        $rlt = new Db($sql);
        $return['fixtures'] = $rlt->rows;
        $sql = "SELECT `id` FROM `standings` WHERE `teamID` = $this->id";
        $rlt = new Db($sql);
        $return['standings'] = $rlt->rows;
        $sql = "SELECT `id` FROM `stripePayments` WHERE `teamID` = $this->id";
        $rlt = new Db($sql);
        $return['stripePayments'] = $rlt->rows;
        $sql = "SELECT `id` FROM `teamFollowers` WHERE `teamID` = $this->id";
        $rlt = new Db($sql);
        $return['teamFollowers'] = $rlt->rows;
        $sql = "SELECT `id` FROM `teamSeasons` WHERE `teamID` = $this->id";
        $rlt = new Db($sql);
        $return['teamSeasons'] = $rlt->rows;
        return $return;
    }

    static function SeasonTeams(Season $season) {
        $sql = "SELECT 
                    teams.id, 
                    teams.name, 
                    teamSeasons.divisionID, 
                    teamSeasons.id AS teamSeasonID, 
                    divisions.name AS divisionName, 
                    teamSeasons.wildcard 
                FROM 
                    teams 
                LEFT JOIN 
                    teamSeasons ON teams.id = teamSeasons.teamID 
                LEFT JOIN 
                    divisions ON teamSeasons.divisionID = divisions.id 
                WHERE 
                    teams.deleted IS NULL 
                    AND teamSeasons.deleted IS NULL 
                    AND teamSeasons.seasonID = {$season->id}";
        // echo $sql;
        $rlt = Database::Execute($sql);
        if (!$rlt['success']['rows']) return [];
        foreach ($rlt['success']['rows'] as $r) {
            $team = new static($r['id']);
            $managers = $team->getTeamManagers();
            if (isset($managers['captain'])) {
                $captain = [
                    'id' => $managers['captain']->id,
                    'name' => $managers['captain']->__toString(),
                    'firstname' => $managers['captain']->firstname,
                    'lastname' => $managers['captain']->lastname,
                    'email' => $managers['captain']->email,
                    'mobile' => $managers['captain']->mobile,
                ];
            } else $captain = [];
            if (isset($managers['treasurer'])) {
                $treasurer = [
                    'id' => $managers['treasurer']->id,
                    'name' => $managers['treasurer']->__toString(),
                    'firstname' => $managers['treasurer']->firstname,
                    'lastname' => $managers['treasurer']->lastname,
                    'email' => $managers['treasurer']->email,
                    'mobile' => $managers['treasurer']->mobile,
                ];
            } else $treasurer = $captain;
            $return[] = [
                'id' => $r['id'],
                'name' => $r['name'],
                'team' => $team,
                'teamSeasonID' => $r['teamSeasonID'],
                'divisionName' => $r['divisionName'],
                'seasonID' => $season->id,
                'divisionID' => $r['divisionID'],
                'wildcard' => $r['wildcard'],
                'payInFull' => TeamFollower::PayingInFull($team),
                'paymentSource' => $team->PaymentCard(),
                'captain' => $captain,
                'treasurer' => $treasurer
            ];
        }
        return $return;
    }

    static function Search(String $searchTerm, Int $pageNo = 1, Int $limit = 100, $includeLeagueName = false) {
        $offset = (($pageNo - 1) * $limit);
        $limit++;
        $sql = "SELECT `teams`.id AS `teamID`, `teams`.`name` AS `teamName`, `teams`.`deleted` AS `teamDeleted`, `leagues`.`id` AS `leagueID`, `leagues`.`name` AS `leagueName` FROM `teams` LEFT JOIN `leagues` ON `teams`.`leagueID` = `leagues`.`id` WHERE `teams`.`name` LIKE '%$searchTerm%'";
        if ($includeLeagueName === true) $sql .= " OR `leagues`.`name` LIKE '%$searchTerm%'";
        $sql .= " ORDER BY `teams`.`name` LIMIT $offset, $limit";
        // return static::Query($sql);
        $db = new Db($sql);
        return $db->rows;
    }

    static function Report() {
        $sql = "SELECT `teams`.`id`, `teams`.`name`,`teams`.`leagueID`, `leagues`.`name` AS `leagueName`,`seasons`.`name` as `seasonName`,  `divisions`.`name` AS `divisionName`, `treasurerStripePaymentMethodID`, `teams`.`captainID`, `teams`.`treasurerID`, `leagues`.`coordinator` from `teams` LEFT JOIN `leagues` ON `teams`.`leagueID` = leagues.id LEFT JOIN `seasons` ON `teams`.`seasonID` = `seasons`.`id` LEFT JOIN `divisions` ON `teams`.`divisionID` = `divisions`.`id` WHERE `teams`.`deleted` IS NULL";
        $db = new Db($sql);
        return $db->rows;
    }

    static function inLeaguesReport() {
        $sql = "SELECT count(`teams`.`id`) AS `total`, `leagues`.`id`, `leagues`.`name`, `leagues`.`playingsurface`, `leagues`.`status`, `leagues`.`defaultDay`, `leagues`.`launchDate` FROM `teams` LEFT JOIN `leagues` ON `teams`.`leagueID` = `leagues`.`id` WHERE `leagues`.`visible` = 1 AND `teams`.`deleted` IS NULL GROUP BY `teams`.`leagueID` ORDER BY `launchDate` ASC, `total` DESC";
        $db = new Db($sql);
        return $db->rows;
    }

    static function TreasurerChecks() {
        /**
         * Loads all Live teams
         * Empty stripePaymentMethodID = Request to allocate card
         * If payment method no longer valid - Request to update details  
         */
        $teams = static::Listing();
        if (!$teams) return;
        $return = [];
        foreach ($teams as $team) {
            if ($team->deleted) continue;
            // Fetch League and Coordinator
            $league = new League($team->getLeagueID());
            $coordinatorID = $league->getCoordinatorID();
            // Run the Checks
            $errorMessage = $team->TreasurerCheck();
            // If Checks are OK ....
            if ($errorMessage === true) {
                // If an error previously exists, clear it.
                if ($team->getTreasurerErrorStamp()) $team->clearTreasurerError();
                continue;
            }
            if ($team->getTreasurerErrorStamp()) {
                // If an error pre-exists ...
                $return[$coordinatorID][] = $team->fullName() . " has payment issue from " . date('l jS F', strtotime($team->getTreasurerErrorStamp())) . " : " . $team->getTreasurerErrorText();
            } else {
                // This is a new error
                $return[$coordinatorID][] = $team->fullName() . " has a new payment issue. $errorMessage";
            }
            // Ensure the latest error message is applied (Date only updated if new)
            $team->setTreasurerError($errorMessage);
        }
        if ($return) {
            foreach ($return as $coordinatorID => $issues) {
                Email::Issue("Team Payment Issues for UserID $coordinatorID ", $issues, ["<EMAIL>" => "a2ztech",]);
            }
        }
    }

    static function AgedDebtors() {
        $sql = "SELECT `teams`.`id`, `teams`.`agedDate`, SUM(`finances`.`total`) AS `balance` FROM teams INNER JOIN `finances` ON `teams`.`id` = `finances`.`teamID` WHERE `agedDate` < CURDATE() - INTERVAL 1 DAY GROUP BY `teams`.`id` ORDER BY `balance` DESC";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $return[$r['id']] = [
                "balance" => $r['balance'],
                "agedDate" => $r['agedDate'],
                "agedDays" => (int)(round(time() - strtotime($r['agedDate'])) / (60 * 60 * 24)),
                "pending" => StripePayment::TeamPendingTotal(new Team($r['id']))
            ];
        }
        return $return;
    }

    static function AgedDebtorReports() {
        $sql = "SELECT `teams`.`id`, `teams`.`agedDate`, SUM(`finances`.`total`) AS `balance`, SUM(`stripePayments`.`total` / 100) as pending FROM teams LEFT JOIN `finances` ON `teams`.`id` = `finances`.`teamID` LEFT JOIN stripePayments ON teams.id = stripePayments.teamID WHERE `agedDate` < '" . date('Y-m-d') . "' GROUP BY `teams`.`id` ORDER BY `balance` DESC";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $return[$r['id']] = [
                "balance" => $r['balance'],
                "pending" => $r['pending'],
                "agedDate" => $r['agedDate'],
                "agedDays" => (int)(round(time() - strtotime($r['agedDate'])) / (60 * 60 * 24))
            ];
        }
        return $return;
    }

    static function Resubscribe(Int $teamID) {
        $sql = "UPDATE `teams` SET `nextConfirmed` = NOW(), `nextConfirmedBy` = " . User::AuthUserID() . " WHERE `id` = $teamID";
        $rlt = new Db($sql);
    }

    static function League(League $league) {
        $sql = "SELECT * FROM `teams` WHERE `leagueID` = {$league->id} AND `deleted` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return;
        foreach ($db->rows as $r) {
            $team = new static();
            $team->Load($r);
            $return[] = $team;
        }
        return $return;
    }

    static function LeagueTeams(League $league) {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` WHERE `seasons`.`leagueID` = {$league->id}";
        return static::Query($sql);
        $db = new Db($sql);
        if (!$db->rows) return;
        foreach ($db->rows as $r) {
            $team = new static();
            $team->Load($r);
            $return[] = $team;
        }
        return $return;
    }

    static function LeagueCount(League $league) {
        $sql = "SELECT COUNT(`id`) AS `total` FROM `teams` WHERE `leagueID` = {$league->id} AND `deleted` IS NULL";
        $db = new Db($sql);
        return (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
    }

    static function Live(User $coordinator = null) {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id`";
        if ($coordinator && !$coordinator->isAdmin) $sql .= "  LEFT JOIN `leagues` ON `seasons`.leagueID` = `leagues`.`id`";
        $sql .= " WHERE `seasons`.`statusID` = 1 AND `teamSeasons`.`deleted` IS NULL AND `teams`.`deleted` IS NULL";
        if ($coordinator && !$coordinator->isAdmin) $sql .= " AND `leagues`.`coordinator` = {$coordinator->id}";
        return static::Query($sql);
    }

    static function TotalDebt() {
        $debtors = static::AgedDebtors();
        $total = 0;
        if ($debtors) {
            foreach ($debtors as $debtor) {
                if ($debtor['balance'] <= 0) continue;
                $total += $debtor['balance'];
            }
        }
        return $total;
    }

    static function LiveTeams(League $league) {
        $sql = "SELECT `teams`.* FROM `teams` LEFT JOIN `teamSeasons` ON `teams`.`id` = `teamSeasons`.`teamID` LEFT JOIN `seasons` ON `teamSeasons`.`seasonID` = `seasons`.`id` WHERE `seasons`.`leagueID` = {$league->id} AND `seasons`.`statusID` = 1 AND `teamSeasons`.`deleted` IS NULL AND `teams`.`deleted` IS NULL ORDER BY `teams`.`name`";
        return static::Query($sql);
    }

    static function GetTeamsByLeague(League $league, $liveOnly = true) {
        if ($liveOnly) {
            return static::LiveTeams($league);
        }

        $sql = "SELECT `teams`.* FROM `teams` WHERE `teams`.`leagueID` = {$league->id} AND `teams`.`deleted` IS NULL ORDER BY `teams`.`name`";
        return static::Query($sql);
    }

    static function DebtorAlerts() {
        $liveTeams = static::Live();
        $teamDebtors = static::AgedDebtors();
        $alerts = [];
        foreach ($liveTeams as $team) {
            if (!isset($teamDebtors[$team->id]['balance']) || $teamDebtors[$team->id]['balance'] <= 49) continue;

            $check = $team->TreasurerCheck();

            $league = new League($team->leagueID);
            $coordinator = new User($league->coordinator);
            $captain = ($team->captainID) ? new User($team->captainID) : null;
            $treasurer = ($team->treasurerID && (!$captain || $team->treasurerID != $team->captainID)) ? new User($team->treasurerID) : null;
            $msg = "$team in $league";

            if (isset($teamDebtors[$team->id]['balance'])) $msg .= " currently owe {$teamDebtors[$team->id]['balance']}";

            if ($check !== true) $msg .= " which cannot be billed because $check ";

            $alerts[$league->coordinator][] = $msg;
            if ($captain) $alerts[$captain->id][] = $msg;
            if ($treasurer) $alerts[$treasurer->id][] = $msg;
            Logging::Add($msg);
        }
        foreach ($alerts as $userID => $messages) {
            $user = new User($userID);
            $messages = array_merge(["Hi {$user->firstname},", "Your team/teams have a balance that we are unable to charge for..", "We have outlined the details below."], $messages);
            $messages = array_merge($messages, ["Please log in to your locker room to access your payment settings. If you have any questions or need further assistance then please contact your local Bloom Netball coordinator <NAME_EMAIL>", "Many Thanks", "Bloom Netball"]);
            $to = [$user->email => $user->__toString()];
            $cc = ($user->isAdmin == 1) ? ["<EMAIL>" => "Charlotte Waugh"] : [];
            $bcc = [];
            Email::Issue("Balance Alert", $messages, $to, $cc, $bcc);
        }
    }

    static function Profanity(String $teamName) {
        // Returns TRUE if OK or STRING of Error
        $profanity = ["bastard", "beaver", "beef curtains", "bellend", "bloodclaat", "clunge", "cock", "dick", "dickhead", "fanny", "flaps", "gash", "knob", "minge", "prick", "punani", "pussy", "snatch", "twat", "arsehole", "bint", "bitch", "bollox", "bollock", "bullshit", "feck", "munter", "piss", "shit", "tits", "arse", "bloody", "bugger", "cow", "crap", "damn", "ginger", "git", "god", "jesus", "christ", "minger", "sod", "cunt", "motherfucker", "fuk", "fcuk", "fuck", "fucking", "wank", "penis"];
        $permitted = true; # Ever the optimist
        if (strlen($teamName) < 4) return "too short";

        $name = strtolower($teamName);
        foreach ($profanity as $word) {
            $sample1 = substr($name, 0, strlen($word));
            $sample2 = substr($name, -strlen($word));
            if ($sample1 == $word) {
                $permitted = "is being refused because of our profanity filter (1)";
                break;
            } elseif ($sample2 == $word) {
                $permitted = "is being refused because of our profanity filter (2)";
                break;
            } elseif (strpos($name, " $word") != false) {
                $permitted = "is being refused because of our profanity filter (3)";
                break;
            } elseif (strpos($name, "$word ") != false) {
                $permitted = "is being refused because of our profanity filter (4)";
                break;
            }
        }

        return $permitted;
    }

    static function Following(User $user) {
        $sql = "SELECT teams.*, teamFollowers.isCaptain, teamFollowers.isTreasurer, teamFollowers.stripePaymentMethodID, teamManagement.seasonID, teamManagement.divisionID, teamManagement.paySeason FROM teams LEFT JOIN teamFollowers ON teams.id = teamFollowers.teamID LEFT JOIN teamManagement ON teams.id = teamManagement.teamID WHERE userID = {$user->id} AND teamFollowers.deleted IS NULL AND teams.deleted IS NULL AND teamManagement.deleted IS NULL";
        return static::Query($sql);
    }

    static function Follow(Team $team, User $user) {
        return TeamFollower::Follow($team, $user);
        /*
        TeamFollower::Current($this);
        # Already a TeamManager
        $sql = "SELECT * FROM `teamFollowers` WHERE `userID` = {$user->id} AND `teamID` = {$team->id} AND deleted IS NULL";
        $rlt = static::Query($sql);
        if (isset($rlt[0])) {

            new Db("UPDATE `teamFollowers` SET `deleted` = NULL, notes = 'Refollowed' WHERE id = {$rlt[0]->id}");
            return "Refollowed";
        } else {
            new Db("INSERT INTO `teamFollowers` SET `userID` = {$user->id}, `teamID` = {$team->id}, notes = 'Followed'");
            return "Followed";
        }
        */
    }

    static function Unfollow(Team $team, User $user) {
        return TeamFollower::UnFollow($team, $user);
        /*
        return Database::Execute("UPDATE `teamFollowers` SET `deleted` = NOW() WHERE teamID = :teamID AND userID = :userID",["teamID" => $team->id,"userID" => $user->id]);
        */
    }

    static function User(User $user) {
        $sql = "SELECT `teams`.* FROM `teamFollowers` LEFT JOIN `teams` ON `teamFollowers`.`teamID` = `teams`.`id` WHERE `teamFollowers`.`deleted` IS NULL AND `teamFollowers`.`userID` = {$user->id}";
        return static::Query($sql);
    }

    static function Managed(User $user) {
        $sql = "SELECT `teams`.*, teamManagement.stripePaymentMethodID FROM `teams` LEFT JOIN `teamManagement` ON `teams`.`id` = `teamManagement`.`teamID` WHERE (`teamManagement`.`captainID` = {$user->id} OR `teamManagement`.`treasurerID` = {$user->id}) AND `teams`.`deleted` IS NULL AND `teamManagement`.`deleted` IS NULL";
        // return $sql;
        return static::Query($sql);
    }

    static function AddCardPayment(Int $userID, Int $teamID, String $stripePaymentMethodID) {
        /* Is there a pre-existing live record for this teamID */
        $sql = "SELECT * FROM teamFollowers WHERE `teamID` = :teamID AND `userID` = :userID AND `deleted` IS NULL";
        $rlt = Database::Execute($sql, ["teamID" => $teamID, "userID" => $userID]);
        if (!$rlt['success']['rows'] || count($rlt['success']['rows']) > 1) {
            /* How could this occur? */
        }
        Database::Execute("UPDATE `teamFollowers` SET deleted = NOW() WHERE `id` = :id", ["id" => $rlt['success']['rows'][0]['id']]);
        Database::Execute("INSERT INTO `teamFollowers` SET `userID` = :userID, `teamID` = :teamID, `isCaptain` = :isCaptain, `isTreasurer` = 1, `stripePaymentMethodID` = :stripePaymentMethodID", ["userID" => $userID, "teamID" => $teamID, "isCaptain" => ($rlt['success']['rows'][0]['isCaptain']), "stripePaymentMethodID" => $stripePaymentMethodID]);
    }

    static function TeamsWithCardsWhereCaptainIsTreasurer() {
        $sql = "SELECT * FROM teams WHERE treasurerStripePaymentMethodID IS NOT NULL AND treasurerStripePaymentMethodID <> '' AND (captainID = treasurerID OR treasurerID IS NULL)";
        return static::Query($sql);
    }

    static function AgedDebtorEmails() {
        $agedDebtors = static::AgedDebtors();
        foreach ($agedDebtors as $teamID => $agedDebtor) {
            if (!$agedDebtor['pending']) {
                $team = new static($teamID);
                $return[$team->__toString()] = $team->AgedDateEmail();
            }
        }
        return $return;
    }

    /*
    @overide
    */
    static function Listing(Int $limit = null, Int $start = null) {
        $obj = new static();
        $sql = "SELECT teams.*, leagues.name AS leagueName FROM teams LEFT JOIN leagues ON teams.leagueID = leagues.id ORDER BY leagues.name, teams.name";
        if ($limit) {
            $sql .= ($start) ? " LIMIT $start,$limit" : " LIMIT $limit";
        }
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $o = new static();
            $o->Load($r);
            (!$return) ? $return[] = $o : array_push($return, $o);
        }
        return $return;
    }

    static function AlphaListing(Int $limit = null, Int $start = null) {
        $obj = new static();
        $sql = "SELECT teams.*, leagues.name AS leagueName FROM teams LEFT JOIN leagues ON teams.leagueID = leagues.id ORDER BY teams.name, leagues.name";
        if ($limit) {
            $sql .= ($start) ? " LIMIT $start,$limit" : " LIMIT $limit";
        }
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $o = new static();
            $o->Load($r);
            (!$return) ? $return[] = $o : array_push($return, $o);
        }
        return $return;
    }
}
