<?php

use Codebase\Sage;

class PurchaseTransaction extends Base {

    protected $dbTable = "pi";
    protected $dbFields = ["typeID","taxDate","venueID","reference","total","filename","balanced","totalAllocated","posted","imported"];

    protected $folder = "/var/www/html/core/filestore/transactions/pi";
    protected $nominalCode = 6901;

    protected $typeID, $taxDate, $venueID, $reference, $total, $filename;
    protected $posted, $imported;
    protected $balanced;
    protected $totalAllocated;
    
    protected $type, $items, $closed;

    protected $venue;
    protected $itemsTotal;
    protected $canPost;

    protected $venueName, $coordinatorName;

    protected $log = [];

    function Save() {
        $type = $this->getType();
        $this->total = ($type->action == "-") ? -abs($this->total) : abs($this->total);
        $this->checkFilenameExists();
        return parent::Save();
    }

    function isPostable() {
        if (!$this->itemsTotal) $this->itemsTotal = PurchaseTransactionItem::PurchaseTransactionTotal($this);
        $this->log[] = "Items Total {$this->itemsTotal}";
        $venue = $this->getVenue();
        $this->canPost = true; # Optimistic
        if (!$this->total) {
            $this->canPost = "No total"; $this->log[] = $this->canPost;
        } elseif ($this->posted) {
            $this->canPost = "Already Posted"; $this->log[] = $this->canPost;
        } elseif (!$venue->sageAccount) {
            $this->canPost = "{$venue} does not have a defined Sage Account"; $this->log[] = $this->canPost;
        } elseif (!$venue->purchaseTerms) {
            $this->canPost = "{$venue} does not have a defined purchase terms"; $this->log[] = $this->canPost;
        } elseif (!$this->getItems()) {
            $this->canPost = "No Items defined"; $this->log[] = $this->canPost;
        } elseif (abs($this->total) != abs($this->itemsTotal)) {
            $this->canPost = abs($this->total) - abs($this->itemsTotal) . " remaining to allocate"; $this->log[] = $this->canPost;
        } elseif (!$this->filestoreFiles()) {
            $this->canPost = "Requires a copy of the transaction uploaded";
        }
    }

    function setPosted() {
        $this->posted = date('Y-m-d H:i:s');
        $this->Save();
    }

    function setUnposted() {
        $this->posted = null;
        $this->Save();
    }
    
    function filestoreFolder() {
        $destinationSubfolders = [
            CORE_FOLDER,
            "filestore",
            "purchaseTransaction",
            $this->id
        ];
        return implode(DIRECTORY_SEPARATOR, $destinationSubfolders);
    }

    function filestoreFiles() {
        $files = (file_exists($this->filestoreFolder())) ? scandir($this->filestoreFolder()) : [];
        return (count($files)>2) ? array_diff($files, array('..', '.')) : [];
    }

    function filestoreFile (String $fileExtension) {
        $counter = ($this->filestoreFiles()) ? count($this->filestoreFiles()) : 0;
        return "purchaseTransaction_{$this->id}_{$counter}.{$fileExtension}";
    }

    function setImported() {
        $this->imported = date('Y-m-d H:i:s');
        $this->Save();
    }

    function ApiData() {
        return [
            "id" => $this->id,
            "venue" => $this->getVenue()->ApiData(),
        ];
    }

    function ApiBasic() {
        $this->isPostable();
        if (!$this->venueName && $this->getVenue()) $this->venueName = $this->venue->name;
        return [
            'id' => (int)$this->id,
            'venueName' => (string)$this->venueName,
            'coordinatorName' => (string)$this->coordinatorName,
            'typeID' => (int)$this->typeID,
            'taxDate' => $this->taxDate,
            'venueID' => (int)$this->venueID,
            'reference' => $this->reference,
            'total' => (float)$this->total,
            'itemsTotal' => (float)$this->itemsTotal,
            'canPost' => $this->canPost,
            'posted' => $this->posted,
            'imported' => $this->imported,
            'files' => (array)$this->filestoreFiles(),
            "log" => $this->log,
        ];
    }

    function ApiFull() {
        $return = $this->ApiBasic();
        $items = $this->getItems();
        $pi_items = [];
        foreach ($items as $item) $pi_items[] = $item->ApiData();
        $return['items'] = $pi_items;
        return $return;
    }

    function checkFilenameExists() {
        if ($this->downloadFilename() && file_exists($this->downloadFilename())) return true;
    }

    function downloadFilename() {
        return ($this->filename) ? $this->folder . DIRECTORY_SEPARATOR . $this->filename : null;
    }

    function getType() {
        if (!$this->type) $this->type = new TransactionType($this->typeID);
        return $this->type;
    }

    function getItems() {
        if (!$this->items) $this->items = PurchaseTransactionItem::PurchaseTransaction($this);
        return $this->items;
    }

    function getVenue() {
        if (!$this->venue) $this->venue = new Venue($this->venueID);
        return $this->venue;
    }

    function getAction() {
        if (!$this->type) $this->type = new TransactionType($this->typeID);
        return $this->type->action;
    }

    function getFile() {
        return ($this->filename) ? '<form action="download.php" method="post"><input type="hidden" name="download" value="'.$this->folder . DIRECTORY_SEPARATOR . $this->filename .'"><button class="btn btn-sm btn-info">Download</button></form>' : null;
    }

    function isClosed() {
        /*
            Returns TRUE or FALSE
            If balanced and has document file defined/exists
        */
        $eval = $this->Evaluation();
        if ($eval !== true) return $eval;
        return $this->checkFilenameExists();
    }

    function Evaluation (Bool $forceRecalculation = false) {
        /*
            Returns TRUE, FALSE or FLOAT
            If already balanced.... true
            Get Allocation Total
            If Total == Allocation Total update and true
            return difference
        */
        if (!$this->id) return false;
        if ($this->balanced && $forceRecalculation !== true) return true;
        $this->totalAllocated = PurchaseTransactionItem::PurchaseTransactionTotal($this);
        // if (!$this->total || !$this->totalAllocated) return false;
        if ($this->total == $this->totalAllocated) {
            $this->balanced = date('Y-m-d H:i:s');
            $this->Save();
            return true;
        } elseif ($this->balanced) {
            $this->balanced = null;
        }
        $this->Save();
        return (float)($this->total - $this->totalAllocated);
    }

    function ImportData() {
        $type = $this->getType();
        $items = $this->getItems();
        $venue = $this->getVenue();
        $return = [];
        // if ($venue->paymentTerms == 4) {
            # Structured Payment
            foreach ($items as $item) {
                $return[] = [
                    $type->shortCode,
                    $venue->sageAccount,
                    $this->nominalCode,
                    null,
                    // date('d/m/Y',strtotime($this->posted)),
                    date('d/m/Y',strtotime($this->taxDate)),
                    $this->reference,
                    "{$this->reference}-{$this->id} in {$item->month}/{$item->year}",
                    abs($item->total),
                    "T9",
                    "0"
                ];
            }
        // } 
        return $return;
    }

    static function Venue (Venue $venue) {
        $sql = "SELECT * FROM `pi` WHERE `venueID` = {$venue->id} AND `deleted` IS NULL ORDER BY `taxDate` DESC";
        $rlt = new Db($sql);
        if (!$rlt->rows) return [];
        foreach ($rlt->rows as $r) {
            $pi = new static();
            $pi->Load($r);
            $return[] = $pi;
        }
        return $return;
    }

    static function VenueTotal (Venue $venue, String $startDate = null, String $endDate = null) {
        $sql = "SELECT SUM(`total`) AS `total` FROM `pi` WHERE `deleted` IS NULL AND `venueID` = {$venue->id}";
        if ($startDate) $sql .= " AND `taxDate` >= '$startDate'";
        if ($endDate) $sql .= " AND `taxDate` <= '$endDate'";
        // echo $sql;
        $rlt = new Db($sql);
        return (isset($rlt->rows[0]['total'])) ? $rlt->rows[0]['total'] : 0;
    }

    static function VenueTotals (Int $year, Int $month) {
        $sql = "SELECT `pi`.venueID, venues.name AS venueName, SUM(`total`) AS `total` FROM `pi` LEFT JOIN venues ON `pi`.venueID = venues.id WHERE `pi`.`deleted` IS NULL AND `pi`.taxDate >= :startDate AND `pi`.taxDate <= :endDate GROUP BY `pi`.venueID";
        $rlt = Database::Execute($sql,[
            'startDate' => "{$year}-{$month}-01",
            'endDate' => date('Y-m-t',strtotime("{$year}-{$month}-01"))
        ]);
        return $rlt['success']['rows'] ?? [];
    }

    static function VenueSubTotals (Int $year = null, Int $month = null) {
        if (!$month) { $month = date('n'); $year = date('Y');}
        if (!$year) $year = date('Y'); 
        $sql = "SELECT pi.venueID, venues.name AS venueName, SUM(pi_items.total) AS total, users.email AS coordinatorEmail, CONCAT_WS(' ',users.firstname,users.lastname) AS coordinatorName FROM pi_items LEFT JOIN pi ON pi_items.mainID = pi.id LEFT JOIN venues ON pi.venueID = venues.id LEFT JOIN users ON venues.coordinatorID = users.id WHERE `pi`.`deleted` IS NULL AND `pi_items`.`deleted` IS NULL AND pi_items.month = :month AND pi_items.year = :year GROUP BY pi.venueID";
        $rlt = Database::Execute($sql,["month" => $month, "year" => $year]);
        return $rlt['success']['rows'] ?? [];
    }

    static function calculateImportFileData (String $maxImportDate) {
        $sql = "SELECT COUNT(`id`) AS total FROM pi WHERE deleted IS NULL AND imported IS NULL AND taxDate <= :maxImportDate";
        $rlt = Database::Execute($sql,["maxImportDate" => $maxImportDate]);
        return $rlt['success']['rows'][0]['total'] ?? 0;
    }

    static function createImportFileData (String $maxImportDate=null, Int $limit = null, Bool $commit = true) {
        
        if (!$maxImportDate) $maxImportDate = date('Y-m-d');
        // $sql = "SELECT pi.* FROM pi LEFT JOIN pi_terms ON pi.typeID = pi_terms.id WHERE pi_terms.isImportable = 1 AND pi.deleted IS NULL AND pi.posted IS NOT NULL AND pi.imported IS NULL AND pi.taxDate <= :maxImportDate ORDER BY pi.created ASC";
        $sql = "SELECT pi.* FROM pi LEFT JOIN venues ON pi.venueID = venues.id LEFT JOIN pi_terms ON venues.purchaseTerms = pi_terms.id WHERE pi_terms.isImportable = 1 AND pi.deleted IS NULL AND pi.posted IS NOT NULL AND pi.imported IS NULL AND pi.taxDate <= :maxImportDate ORDER BY pi.created ASC";

        $sqlData["maxImportDate"] = $maxImportDate;
        if ($limit) $sql .= " LIMIT $limit";
        $return['purchaseTransactions'] = static::Query("$sql",$sqlData);
        if ($commit === true) {
            $return['imported'] = date('Y-m-d H:i:s');
            foreach ($return['purchaseTransactions'] as $purchaseTransaction) {
                $updateSql = "UPDATE pi SET imported = :imported WHERE id = :id";
                $rlt = Database::Execute($updateSql,["imported" => $return['imported'], "id" => $purchaseTransaction->id]);
            }
            return $return;
        } else $return['imported'] = null;
        return $return;
    }
    
    static function PrePays () {
        return PurchaseTransactionItem::PrePays();
    }

    static function generateImportFileData (String $imported) {
        $sql = "SELECT * FROM pi WHERE imported = :imported";
        $rlt = static::Query($sql,["imported" => $imported]);
        $return = [];
        foreach ($rlt as $r) {
            $importLines = $r->ImportData();
            foreach ($importLines as $importLine) $return[] = $importLine;
        } 
        return $return;

    }

    static function listOfImports () {
        $sql = "SELECT imported, COUNT(imported) AS total FROM pi WHERE imported IS NOT NULL GROUP BY imported ORDER BY imported DESC";
        $rlt = Database::Execute($sql);
        return $rlt['success']['rows'] ?? [];
    }

    static function ImportedData (String $imported) {
        $sql = "SELECT * FROM pi WHERE imported = :imported";
        return static::Query($sql,["imported" => $imported]);
    }

    static function PeriodicImport () {
        $import = static::createImportFileData();
        $importStamp = $import['imported'];
        $importTransactions = static::ImportedData($importStamp);
        $importData = [];
        foreach ($importTransactions as $importTransaction) $importData = array_merge($importData,$importTransaction->ImportData());
        $sage = new Sage();
        $importFile = $sage->BuildPurchaseTransactionCSV_File($importStamp,$importData);
        return $sage->SendPurchaseTransactionFile($importFile);
    }

}