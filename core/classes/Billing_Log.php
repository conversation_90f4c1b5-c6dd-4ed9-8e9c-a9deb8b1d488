<?php

class Billing_Log extends Base {

    protected $dbTable = "billing_log";
    protected $date, $teamID, $treasurerID, $balance, $stripePaymentID, $note, $fullRun, $statusID;
   
    protected $team, $leagueID, $league, $treasurer;

    function Save() {
        if (!$this->date) $this->date = date('Y-m-d');
        parent::Save();
    }

    function getTeam() {
        if (!$this->team && $this->teamID) $this->team = new Team($this->teamID);
        return $this->team;
    }

    function getLeague() {
        $this->getTeam();
        if (!$this->league && $this->team && $this->team->leagueID) $this->league = new League($this->team->leagueID);
        return $this->league;
    }

    function getTreasurer() {
        if (!$this->treasurer && $this->treasurerID) $this->treasurer = new User($this->treasurerID);
        return $this->treasurer;
    }

    function ApiData() {
        $this->getLeague();
        $this->getTreasurer();
        return [
            "id" => $this->id,
            "date" => $this->date,
            "date_formatted" => date('d/m/Y',strtotime($this->date)),
            "team" => ($this->team) ? $this->team->__toString() : null,
            "leagueID" => ($this->league) ? (float)$this->league->id : null,
            "league" => ($this->league) ? $this->league->__toString() : null,
            "agedDate" => ($this->team) ? $this->team->agedDate : null,
            "agedDate_formatted" => ($this->team && $this->team->agedDate) ? date('d/m/Y',strtotime($this->team->agedDate)) : null,
            "agedDays" => ($this->team) ? $this->team->agedDays() : null,
            "treasurer" => ($this->treasurer) ? $this->treasurer->__toString() : null,
            "balance" => $this->balance,
            "stripePaymentID" => $this->stripePaymentID,
            "note" => $this->note,
            "fullRun" => $this->fullRun,
            "statusID" => $this->statusID,
        ];
    }

    static function Batch (Array $data = []) {
        /**
         * Expects...
         * date
         * teamID
         * treasurerID
         * balance
         * stripePaymentID
         * message => note
         * fullRun
         * statusID
         **/
        if (!$data) return;
        $sql = "INSERT INTO `billing_log` (`date`,`teamID`,`treasurerID`,`balance`,`stripePaymentID`,`note`,`fullRun`,`statusID`) VALUES ";
        $connector = null;
        foreach ($data as $d) {
            $date = (isset($d['date']) && $d['date']) ? $d['date'] : date('Y-m-d');
            $teamID = (isset($d['teamID']) && $d['teamID'] && is_numeric($d['teamID'])) ? $d['teamID'] : "NULL";
            $treasurerID = (isset($d['treasurerID']) && is_numeric($d['treasurerID'])) ? $d['treasurerID'] : "NULL";
            $balance = (isset($d['balance']) && is_numeric($d['balance'])) ? $d['balance'] : "NULL";
            $stripePaymentID = (isset($d['stripePaymentID']) && $d['stripePaymentID']) ? "'{$d['stripePaymentID']}'" : "NULL";
            $note = (isset($d['note']) && $d['note']) ? "'{$d['note']}'" : "NULL";
            $fullRun = (isset($d['fullRun']) && $d['fullRun'] == 1) ? 1 : 0;
            switch ($d['statusID']) {
                case 0: $statusID = 0;break;
                case 1: $statusID = 1;break;
                default: $statusID = "NULL";
            }
            $sql .= $connector . "('$date',$teamID,$treasurerID,$balance,$stripePaymentID,$note,$fullRun,$statusID)";
            $connector = ",";
        }
        Database::Execute($sql);
        // Tools::Dump(new Db($sql));
    }

    static function ClearTestRuns () {
        new Db("DELETE FROM `billing_log` WHERE `fullRun` <> 1");
    }

    static function Date (String $date = null) {
        if (!$date) $date = date('Y-m-d');
        $sql = "SELECT * FROM billing_log WHERE date = :date";
        return static::Query($sql,["date" => $date]);
    }
}