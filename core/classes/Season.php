<?php

class Season extends Base {

    protected $dbTable = "seasons";
    protected $leagueID, $rounds = 1, $duration = 40;
    protected $seasonalID, $yearID; 
    protected $nextID,$nextProcessed;
    protected $fixtureCharge, $officialCharge;
    public $autoFixture, $autoSchedule, $autoBilling;
    protected $name;  
    protected $startBookingID;
    protected $launchDate;
    protected $minTeams, $maxTeams;
    protected $autoRollForward;
    protected $openForRegistration;
    protected $venueID, $defaultDay, $inside;
    protected $timeStarts, $timeEnds;
    protected $playingSurface;
    protected $rollForward;

    /**
     * Status
     * 0 or NULL = Next
     * 1 = Live
     * 2 = Closed (Completed normally or early)
     */
    protected $status, $statusID;
    protected $locked, $lockStamp;
    protected $dbFields = ["leagueID", "nextID","seasonalID","yearID","duration","name","launchDate","rounds","fixtureCharge","officialCharge","startBookingID","schedulingNotes","statusID","locked", "lockStamp","minTeams", "maxTeams","autoRollForward","openForRegistration","venueID","defaultDay","inside","timeStarts", "timeEnds", "playingSurface","rollForward"];

    public $schedulingNotes;

    public $minProfitability = 60; # Total invoicing / Total Booking cost. 
    protected $totalTeams;

    protected $league;
    protected $venue;
    protected $teams = [];
    protected $divisions = [];
    protected $tables = [];
    protected $standings = [];

    protected $startBooking;

    function getVenue() {
        if (!$this->id || !$this->venueID) return;
        if (!$this->venue && $this->venueID) $this->venue = new Venue($this->venueID);
        return $this->venue;
    }

    function __construct(Int $id = null) { parent::__construct($id);}

    function Save() {
        if (!$this->leagueID) return (\Messaging\Add("Season must have a League ID")) ;
        /* 
            if AutoRollForward and Season Live - RollForward if not already
        */
        if (!$this->minTeams || $this->minTeams < 6) $this->minTeams = 6;
        // if (!$this->autoRollForward) $this->autoRollForward = null;
        $this->autoRollForward = null;
        if ($this->statusID == 1) $this->openForRegistration = null;
        if ($this->openForRegistration == 1 && $this->statusID != 2) $this->statusID = 2;
        // if ($this->id && $this->autoRollForward && $this->statusID == 1) {
            /* Does League have Future Season? */
            // if (!static::hasFuture(new League($this->leagueID))) {
                /* Needs Rolling Forward */
                // static::RollForward($this);
                // \Messaging\Add("Rolled Season Forward");
            // }
        // }

        if ($this->openForRegistration) {
            new Db("UPDATE `seasons` SET `openForRegistration` = NULL WHERE leagueID = {$this->leagueID}");
        }
        
        if ($this->locked && !$this->lockStamp) {
            $this->lockStamp = date('Y-m-d H:i:s');
            $this->status = 1;
        }

        if ($this->lockStamp && $this->status!=1) $this->status = 1;
        $createDefaultDivision = (!$this->id) ? true : false;
        $this->schedulingNotes = null;
        if (!$this->status) $this->status = null;
        if (!$this->startBookingID) $this->startBookingID = null;
        if (!$this->name) $this->AutoName();
        if ($this->status == 1) {
            /* Any other Seasons in league with statusID = 1 [live] set to 4 [completed] */
            $sql = "UPDATE `seasons` SET `statusID` = 4 WHERE `leagueID` = {$this->leagueID} AND `status` = 1";
            if ($this->id) $sql .= " AND `id` <> {$this->id}";
            /* */
            new Db($sql);
        }
        $rlt = parent::Save();
        if ($this->id && $createDefaultDivision ===true) $this->defaultDivision();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }

    function totalCharge() {
        return $this->fixtureCharge + $this->officialCharge;
    }

    function isEditable() {
        if (!$this->status) $this->status = new SeasonStatus($this->statusID);
        return (!$this->statusID || !is_object($this->status) || ($this->status->active && !$this->status->live)) ? true : false;
    }

    function ApiOutput() {
        $this->getStatus();
        $this->getLeague();
        return [
            "id" => $this->id,
            "name" => $this->name,
            "league" => ($this->league) ? $this->league->ApiBrief() : null,
            "status" => (is_object($this->status)) ? $this->status->ApiData() : $this->status
        ];
    }

    
    function getStatus() {
        if (!$this->status && $this->statusID) $this->status = new SeasonStatus($this->statusID);
        return $this->status;
    }

    function FullDayText() {
        switch ($this->defaultDay) {
            case "mo": return "Monday";
            case "tu": return "Tuesday";
            case "we": return "Wednesday";
            case "th": return "Thursday";
            case "fr": return "Friday";
            case "sa": return "Satday";
            case "su": return "Sunday";
            return "a day to be arranged";
        }
    }
    function ApiBasic() {
        switch ($this->defaultDay) {
            case "mo": $day = "Monday"; break;
            case "mo": $day = "Tuesday"; break;
            case "mo": $day = "Wednesday"; break;
            case "mo": $day = "Thursday"; break;
            case "mo": $day = "Friday"; break;
            case "mo": $day = "Satday"; break;
            case "mo": $day = "Sunday"; break;
            default: $day = "a day to be arranged";
        }
        $this->getCoordinator();
        if ($this->coordinator) {
            $coordinator = [
                "firstname" => $this->coordinator->firstname,
                "lastname" => $this->coordinator->lastname,
                "email" => $this->coordinator->email,
            ];
        } else $coordinator = null;
        return [
            "id" => $this->id,
            "name" => $this->name,
            "status" => $this->statusID,
            "day" => $day,
            "launchDate" => $this->getLaunchDate(),
            "fixtureCharge" => $this->getFixtureCharge(),
            "coordinator" => ($this->coordinator) ? [
                "firstname" => $this->coordinator->firstname,
                "lastname" => $this->coordinator->lastname,
                "email" => $this->coordinator->email,
            ]:null
        ];
    }

    function ApiData() {
        $divisions = Division::forSeason($this);
        if ($divisions) {
            foreach ($divisions as $division) {
                $divisionData[] = $division->ApiOutput();
            }
        } else $divisionData = [];
        return [
            "id" => $this->id,
            "name" => $this->name,
            "status" => $this->statusID,
            "duration" => $this->duration,
            "fixtureCharge" => $this->fixtureCharge,
            "officialCharge" => $this->officialCharge,
        ];
    }

    function resolvePlayingSurface() {
        switch ($this->playingSurface) {
            case 1: return 'Indoor';
            case 2: return 'Outdoor';
            default: return 'TBC';
        }
    }

    function ApiData2() {
        $this->getStatus();
        $divisions = Division::forSeason($this);
        if ($divisions) {
            foreach ($divisions as $division) {
                $divisionData[] = $division->ApiOutput();
            }
        } else $divisionData = [];
        return [
            "id" => $this->id,
            "name" => $this->name,
            "status" => ($this->status) ? $this->status->ApiData() : null,
            "divisions" => $divisionData
        ];
    }

    function Utilisation() {
        if (!$this->totalTeams) $this->teamCount();
        $this->maxTeams = ($this->totalTeams >= 10) ? $this->totalTeams : 10;
        return ($this->totalTeams / $this->maxTeams) * 100;
    }

    function AutoName() {
        /*
        Define and Store startMonth and startYear
         
        Find Current Season for this League
        
        Make this yearSeasonID, last yearSeasonID + 1
        if yearSeasonID > 4, { yearSeasonID = 1 && yea

        */
        $sql = "SELECT * FROM `seasons` WHERE `leagueID` = {$this->leagueID}";
        $db = new Db($sql);
        if ($db->rows) {
            $seasonNumber = count($db->rows) + 1;
            foreach ($db->rows as $r) {
                $names[] = $r['name'];
            }
            $this->name = "Season $seasonNumber";
            while (in_array($this->name,$names)) {
                $seasonNumber ++;
                $this->name = "Season $seasonNumber";
            }
        } else $this->name = "Season 1";
        // switch ($this->seasonalID) {
        //     case 1:
        //         $this->name = "Spring {$this->yearID}"; break;
        //     case 2:
        //         $this->name = "Summer  {$this->yearID}"; break;
        //     case 3:
        //         $this->name = "Autumn {$this->yearID}"; break;
        //     default:
        //         $this->name = "Winter {$this->yearID}";
        // }
    }

    function __toString() { 
        return "{$this->name}";
    }

    function Started() {
        return false;
        if ($this->startDate && $this->startDate <= date('Y-m-d')) return true;
    }

    function defaultDivision() {
        $division = new Division();
        $divisionData = [
            "seasonID" => $this->id,
            "name" => "Division 1"
        ];
        $division->Load($divisionData);
        $division->Save();
    }

    function getDuration() { return $this->duration;}

    function setSchedulingNotes(String $text) {
        if ($this->lockStamp) return "Season Locked - No Changes";
        $this->schedulingNotes = $text;
        new Db("UPDATE `seasons` SET `schedulingNotes` = :text WHERE `id` = :id",["text" => $text, "id" => $this->id]);
    }

    function clearUnbilled (Bool $override = false) {
        return Schedule::ClearUnbilled($this,$override);
        return "Clear All Unbilled Fixtures Scheduled in Season {$this->id}";
    }
    
    // function clearAll () {
    //     return Schedule::ClearAll($this);
    //     return "Clear All Unbilled Fixtures Scheduled in Season {$this->id}";
    // }

    function completeSchedule(Bool $commit = false) {
        $fixtures = Fixture::UnscheduledList($this);
        if (!$fixtures) return;
        $bookings = $rlt = [];
        foreach ($fixtures as $fixture) {
            $log[$fixture->id]['fixture'] = $fixture;
            $division = $fixture->getTheDivision();
            $bookings = Booking::Open($this); # Open Bookings Only
            $log[$fixture->id]['bookings'] = $bookings;
            $log[$fixture->id]['note'][] = "Working with " . count($bookings)." booking(s)<br>";
            foreach ($bookings as $booking) {
                $log[$fixture->id]['note'][] = "Trying {$booking}<br>";
                if ($division->venueID && $division->venueID != $booking->venueID) continue;
                $log[$fixture->id]['note'][] ="Duration {$booking->duration}. Used {$booking->used}. Required {$fixture->duration}<br>";
                if (($booking->duration - $booking->used) >= $fixture->duration) {
                    $startTime = (!$booking->used) ? date('H:i:s',strtotime($booking->startDate ." " . $booking->startTime)) : date('H:i:s',strtotime("+{$booking->used} minutes",strtotime($booking->startDate ." " . $booking->startTime)));
                    $log[$fixture->id]['note'][] ="Scheduled at $startTime<br>";
                    $sql = "INSERT INTO `schedule` (`bookingID`,`fixtureID`,`offset`,`startTime`,`duration`) VALUES (:bookingID, :fixtureID, :offset, :startTime, :duration)";
                    $sqlData = [
                        "bookingID" => $booking->id,
                        "fixtureID" => $fixture->id,
                        "offset" => $booking->used,
                        "startTime" => $startTime,
                        "duration" => $fixture->duration
                    ];
                    $log[$fixture->id]['sql'][] = $sql;
                    $log[$fixture->id]['sqlData'][] = $sqlData;
                    if ($commit === true) $log[$fixture->id]['rlt'] = Database::Execute($sql,$sqlData);
                    break;
                } else $log[$fixture->id]['note'][] =  "No Space. Used {$booking->used} of {$booking->duration}<br>";
            }
        }
        return $log;
    }

    function runSchedule ($commitResult = false) {
        // if (!User::isAuthor()) return "Not permitted";
        // if ($this->lockStamp) return "Season Locked - No Changes";
        if (!$this->startBookingID) return "Season must have a Booking defined as the Season start point before Scheduling can be run";
        // Clear pre-existing schedule
        // Schedule::Clear($this);
        $bookings = Booking::SeasonOptions($this); #return $bookings;
        // exit(\Tools\Dump($bookings));
        if (!is_array($bookings)) return $bookings;

        $fixtures = Fixture::Season($this,true);#return $fixtures;
        $fixtures = array_values($fixtures); // Reset array keys
        // $fixtures = Fixture::Season($this,false);#return $fixtures;
       
        // exit(\Tools\Dump($fixtures));
        if (!is_array($fixtures))return "No fixtures to schedule";
        $log[] = "Programming " . count($fixtures)." weeks of fixtures";
        $bookingsPlanner = [];
        // if (count($fixtures) > count($bookings)) {
        //     $required = count($fixtures) - count($bookings);
        //     return $required . " more booking weeks required to accomodate Season fixtures";
        // }
        for ($i = 0; $i < count($fixtures); $i++) { // Looping through Fixtures by Round...
            $roundID = $i +1;
            $log[] = "Week $roundID";
            // echo "{$fixtures[$i]['required']} fixtures required<br>";
            if (!isset($fixtures[$i]['required']))  {
                $log[] = "Round {$roundID} fixtures are already accounted for";
                continue; // If this rounds fixtures are already accounted for - move on.
            }
            if ($fixtures[$i]['required'] <= $bookings[$i]['available']) { // If booking availability exceeds fixture needs [all in minutes] ....
                // echo "Week $i Req: {$fixtures[$i]['required']}. Avail: {$bookings[$i]['available']}<br>";
                $fixtureCounter = 1;
                foreach ($fixtures[$i]['fixtures'] as $fixture) { // Loop each round's fixtures
                    $log[] = "$fixtureCounter Fixture $fixture"; $fixtureCounter++;
                    if (!isset($divisions[$fixture->divisionID])) $divisions[$fixture->divisionID] = new Division($fixture->divisionID);
                    reset($bookings[$i]['bookings']); // Start at the beginning of the bookings...
                    foreach ($bookings[$i]['bookings'] as $index => $booking) {
                        if ($divisions[$fixture->divisionID]->venueID && $booking->venueID != $divisions[$fixture->divisionID]->venueID) continue;
                        if ($booking->usage['available'] >= $this->getDuration()) {
                            $startTime = date('H:i:s',strtotime("+".$booking->usage['used']." minutes",strtotime($booking->getStartTime())));
                            $log[] = "Using Booking $booking at " . substr($startTime,0,5);
                            $bookingsPlanner[] = [
                                "bookingID" => $booking->id,
                                "fixtureID" => $fixture->id,
                                "offset" => $booking->usage['used'],
                                "startTime" => $startTime,
                                "duration" => $this->getDuration()
                            ];
                            $bookings[$i]['bookings'][$index]->used = $booking->usage['used'] += $this->getDuration();
                            $bookings[$i]['bookings'][$index]->available = $booking->usage['available'] -= $this->getDuration();
                            break;
                        }
                    }
                }
            } else {
                $err = "Week ". ($i+1) ." can accomodate {$bookings[$i]['available']} matches but needs to play {$fixtures[$i]['required']}";
                // echo $err."<br>";
                return $err;
            } 
        }
        if ($commitResult === true) {
            $sql = "INSERT INTO `schedule` (`bookingID`,`fixtureID`,`offset`,`startTime`,`duration`) VALUES (:bookingID, :fixtureID, :offset, :startTime, :duration)";
            foreach ($bookingsPlanner as $bp) {
                new Db($sql, [
                    "bookingID" => $bp['bookingID'],
                    "fixtureID" => $bp['fixtureID'],
                    "offset" => $bp['offset'],
                    "startTime" => $bp['startTime'],
                    "duration" => $bp['duration']
                ]);
            }
        }
        return ["bookingsPlanner" => $bookingsPlanner,"log" => $log, 'fixtures' => $fixtures];
    }

    /* Getters */
    function getLeague() {
        if (!$this->league) $this->league = new League($this->leagueID);
        return $this->league;
    }

    function getCoordinator() {
        if ($this->coordinator) return $this->coordinator;
        $this->getLeague();
        if ($this->league) $this->coordinator = $this->league->getCoordinator();
        return $this->coordinator;
    }

    function getLeagueID() { return $this->leagueID;}

    function getLeagueName() {
        $this->getLeague();
        return ($this->league) ? $this->league->__toString() : "Unknown";
    }
    function getRounds() { return $this->rounds;}
    function getStartBookingID() {return $this->startBookingID;}
    function getStartBooking() {
        if (!$this->startBooking && $this->startBookingID) $this->startBooking = new Booking($this->startBookingID);
        return $this->startBooking;
    }
    function getStartDate() {
        $startBooking = new Booking($this->startBookingID);
        return $startBooking->getStartDate();
    }
    function getFixtureCharge() { 
        if ($this->fixtureCharge) return $this->fixtureCharge;
        return 27;        
    }
    function getOfficialCharge() {return $this->officialCharge;}

    function getAutoFixture() {return $this->autoFixture;}

    function getAutoSchedule() {return $this->autoSchedule;}

    function getAutoBilling() {return $this->autoBilling;}

    function getStatusID() { return $this->statusID;}

    function getDivisionCount() {
        return Division::SeasonTotal($this);
    }

    function getStatusText() { 
        if (!$this->statusID) return;
        return new SeasonStatus($this->statusID);
        return ($this->status==1) ? "Live" : "Open";
    }

    function SetNextID () {
        if ($this->statusID != 1) return;
        $sql = "SELECT `id` FROM `seasons` WHERE `statusID` = 2 AND `leagueID` = {$this->leagueID}";
        $db = new Db($sql);
        if (!isset($db->rows[0]['id'])) return;
        if ($this->nextID != $db->rows[0]['id']) new Db("UPDATE `seasons` SET `nextID` = {$db->rows[0]['id']} WHERE `id` = {$this->id}");
    }

    function getLaunchDate(String $format = null) {
        if (!$this->startBookingID) return $this->launchDate;
        $booking = new Booking($this->startBookingID);
        if (!$booking || !$booking->id) return null;
        return $booking->getStartDate($format);
    }

    function teamCount($wildcard = true) {
        // $sql = "SELECT COUNT(`teams`.`id`) AS `total` FROM `teams` WHERE `teams`.`deleted` IS NULL AND `teams`.`seasonID` = :seasonID";
        $sql = "SELECT COUNT(`teamSeasons`.`teamID`) AS `total` FROM `teamSeasons` WHERE `teamSeasons`.`deleted` IS NULL AND `teamSeasons`.`seasonID` = :seasonID";
        if (!$wildcard) $sql .= " AND `teamSeasons`.`wildcard` IS NULL";
        $db = new Db($sql,["seasonID" => $this->id]);
        if ($db->rows) return ($this->totalTeams = $db->rows[0]['total']);
    }

    /* Checkers */
    function isLocked() {
        return ($this->lockStamp) ? true : false;
    }

    /* Setters */
    function setLeagueID(Int $leagueID) { 
        if ($this->locked) return "Season Locked";
        $this->leagueID = $leagueID;
    }

    function setName(String $name) { 
        if ($this->locked) return "Season Locked";
        $this->name = $name;
    }

    function RollTeamsForward() {
        $teams = Team::forSeason($this);
        foreach ($teams as $team) {
            if ($team->nextConfirmed) {
                $team->seasonID = $this->nextID;
                $team->divisionID = $team->nextConfirmed = $team->nextConfirmedBy = null;
                $team->nextConfirmed = $team->nextConfirmedBy = null;
                $team->Save();
            } 
        }
    }

    function NextStartDateValid () {
        // return;
        if ($this->statusID != 1) return;
        if (!$this->leagueID) return;
        $dates = Schedule::StartEndDates($this);
        if (!$dates || !$dates['end']) return;
        $league = new League($this->leagueID);
        if (!$league->launchDate) return;
        if (strtotime($dates['end']) > strtotime($league->launchDate)) return "$this finishes on ". date('d/m/Y',strtotime($dates['end']))." which is after the League Launch Date " . date('d/m/Y',strtotime($league->launchDate));
    }

    /* Calculations */
    function ProfitLoss() {
        /* Fixtures run from date -> date */
        // $dates = Schedule::StartEndDates($this);
        // echo "<!-- ";
        // print_r($dates);
        // echo "-->";
        // if (!$dates['start']) return ["profit" => null];
        // /* Get Booking Expenditure between dates */
        // $expenditure = Booking::SeasonCost($this,$dates['start'],$dates['end']);
        // echo "Season " . $this->getLeagueName() . " : Expenditure: $expenditure<br>";
        // /* Get Fixture Fees between dates */
        // $fixtureCount = Fixture::SeasonTotal($this);
        // $income = $fixtureCount * 27 * 2;
        // return [
        //     "startDate" => $dates['start'],
        //     "endDate" => $dates['end'],
        //     "expenditure" => $expenditure,
        //     "income" => $income,
        //     "profit" => ($income - $expenditure),
        //     "fixtures" => $fixtureCount
        // ];
        $expenditure = Booking::SeasonCost($this);
        $income = Fixture::SeasonCharges($this);
        return [
            "expenditure" => round($expenditure,2),
            "income" => round($income,2),
            "profit" => round(($profit=$income - $expenditure),2),
            "margin" => ($profit) ? round(($profit / $income)*100,2) : $profit
        ];
    }

    function Financials ($startDate, $endDate) {
        $sql = "SELECT SUM(`feeMatch`) AS `total` FROM `fixtures` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` LEFT JOIN `schedule` ON `fixtures`.`id` = `schedule`.`fixtureID` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` WHERE `divisions`.`seasonID` = {$this->id} AND `bookings`.`startDate` >= '$startDate' AND `bookings`.`startDate` <= '$endDate' AND `schedule`.`deleted` IS NULL";
        $db = new Db($sql);
        $invoiced = (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
        $sql = "SELECT SUM(`total`) AS `total` FROM `stripePayments` WHERE `created` >= '$startDate 00:00:00' AND `created` <= '$endDate 23:59:59' AND `teamID` IN (SELECT `teams`.`id` FROM `teams` WHERE `teams`.`seasonID` = {$this->id})";
        // echo $sql;
        $db = new Db($sql);
        $charged = (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
        return [
            "invoiced" => $invoiced*2,
            // "credited" => 2,
            "charged" => $charged,
            // "refunded" => 4
        ];
    }

    function getDivisions() {
        if (!$this->divisions) $this->divisions = Division::forSeason($this);
        return $this->divisions;
    }

    function getTables() {
        $this->getDivisions();
        if (!$this->tables) {
            foreach ($this->divisions as $division) {
                $this->tables[$division->name] = Standing::Division($division->id);
            }
        }
        return $this->tables;
    }

    function getTeams() {
        if (!$this->teams) {
            $this->teams = TeamSeason::TeamsInSeason($this);
        }
    }

    function getStandings() {
        $this->getDivisions();
        if (!$this->standings) {
            foreach ($this->divisions as $division) {
                $this->standings[$division->name] = Standing::forAPI($division);
            }
        }
        return $this->standings;
    }

    /* Statics */
    static function forLeague (League $league) {
        if (!$league->id) return;
        // $sql = "SELECT `id` FROM`seasons` WHERE `leagueID` = " . $league->id . " AND `deleted` IS NULL ORDER BY `statusID` DESC";
        $sql = "SELECT `seasons`.* FROM `seasons` WHERE `seasons`.`leagueID` = {$league->id} AND `seasons`.`deleted` IS NULL ORDER BY `created`";
        $db = new Db($sql); if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $return[$r['id']] = new static();
            $return[$r['id']]->Load($r);
        } 
        return $return;
    }

    static function Current (League $league) {
        $defaultSeasons = static::leagueDefaults($league);
        return $defaultSeasons['live'];
    }

    static function leagueDefaults (League $league) {
        // $return =[
        //     'live' => null,
        //     "next" => null
        // ];
        if (!$league->id) return;
        $return['live'] = Season::Live($league);
        $return['next'] = Season::Registration($league);
        return $return;
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `leagueID` = :leagueID AND `seasonStatus`.`active` = 1 ORDER BY `seasonStatus`.`live` DESC";

        // $sql = "SELECT `id`,`nextID` FROM `seasons` WHERE `leagueID` = :leagueID ORDER BY `status` DESC";
        $rlt = new Db($sql,["leagueID" => $league->id]);
        if (!$rlt->rows) return;
        $return['live'] = new static();
        $return['live']->Load($rlt->rows[0]);
        if (isset($db->rows[1])) {
            $return['next'] = new static();
            $return['next']->Load($db->rows[1]);
        }
        return $return;
        $return['live'] = new static($rlt->rows[0]['id']);
        if (isset($rlt->rows[0]['nextID']) && $rlt->rows[0]['nextID']) $return['next'] = new static($rlt->rows[0]['nextID']);
        return $return;
        // $sql = "SELECT `id` FROM`seasons` WHERE `leagueID` = :leagueID AND `deleted` IS NULL ORDER BY `id` ASC LIMIT 2";
        // $db = new Db($sql,["leagueID" => $league->id]);
        // if (!$db->rows) return;
        // $return["live"] = new static($db->rows[0]['id']);
        // if (isset($db->rows[1]['id']))$return["next"] = new static($db->rows[1]['id']);
        // return $return;
    }

    static function Default (League $league) {
        if (!$league->id) return;
        $sql = "SELECT * FROM `seasons` WHERE `leagueID` = :leagueID ORDER BY `status` DESC";
        $rlt = new Db($sql,["leagueID" => $league->id]);
        if (!$rlt->rows) return;
        foreach ($rlt->rows as $r) {
            if ($r['statusID'] == 1 && $r['nextID']) return $r['nextID'];
        }
        return array_pop($rlt->rows)['id'];
    }

    static function AutoFixture(Season $season) {
        if ($season->locked) return "Season Locked";
        // echo __METHOD__ . ":".__LINE__."<br>";
        $divisions = Division::forSeason($season);
        if ($divisions) {
            foreach ($divisions as $division) {
                $autofixtureResult = Fixture::Autofixture($division);
                // $autofixtureResult = Fixture::Autofixture($division->getID());
                if (is_string($autofixtureResult)) \Messaging\Add($autofixtureResult,"warning");
            }  
        }
    }

    static function DefaultLeague(Int $leagueID) {
        // $sql = "SELECT `id` FROM `seasons` WHERE `deleted` IS NULL AND `leagueID` = :leagueID AND `startDate` IS NULL ORDER BY `created` ASC LIMIT 1";
        // $sql = "SELECT `id` FROM `seasons` WHERE `deleted` IS NULL AND `leagueID` = :leagueID AND `startDate` IS NULL ORDER BY `status` DESC, `created` LIMIT 1";
        // $db = new Db($sql,["leagueID" => $leagueID]); if (!$db->rows) return;
        $league = new League($leagueID);
        $seasons = static::leagueDefaults($league);
        if (isset($seasons['live']->id)) return $seasons['live']->id;
        // return $db->rows[0]['id'];
    }

    static function Lock(Int $seasonID) {
        $season = new static($seasonID);
        if ($season->lockStamp) return;
        $season->locked = 1;
        $season->status = 1;
        $season->Save();
    }

    static function SetNext($currentSeasonID, $nextSeasonID) {
        new Db("UPDATE `seasons` SET `nextID` = $nextSeasonID WHERE `id` = $currentSeasonID");
    }

    static function RollingForward () {
        $sql = "SELECT * FROM `seasons` WHERE `status` = 1 AND `nextID` IS NOT NULL";
        $rlt = new Db($sql);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s;
        }
        return $return;
    }

    static function OperationalReport() {
        $sql = "SELECT `seasons`.`id` AS `seasonID`, `leagues`.`name` AS `league`, `seasons`.`name` AS `season`, `bookings`.`startDate`, `leagues`.`defaultDay`, `leagues`.`playingSurface`, MAX(`fixtures`.`weekNo`) AS `totalWeeks` FROM `seasons` LEFT JOIN `leagues` ON `seasons`.`leagueID` = `leagues`.`id` LEFT JOIN `bookings` ON `seasons`.`startBookingID` = `bookings`.`id` LEFT JOIN `divisions` ON `seasons`.`id` = `divisions`.`seasonID` LEFT JOIN `fixtures` ON `divisions`.`id` = `fixtures`.`divisionID` WHERE `seasons`.`statusID` = 1 GROUP BY `seasons`.`id`";
        $db = new Db($sql);
        return $db->rows;
    }

    static function PandLReport() {
        $sql = "SELECT `seasons`.`id` AS `seasonID`, `bookings`.`startDate`, `leagues`.`defaultDay`, `leagues`.`playingSurface`, MAX(`fixtures`.`weekNo`) AS `totalWeeks` 
        FROM `seasons` 
        LEFT JOIN `leagues` ON `seasons`.`leagueID` = `leagues`.`id` 
        LEFT JOIN `bookings` ON `seasons`.`startBookingID` = `bookings`.`id` 
        LEFT JOIN `divisions` ON `seasons`.`id` = `divisions`.`seasonID` 
        LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` 
        LEFT JOIN `fixtures` ON `divisions`.`id` = `fixtures`.`divisionID` 
        WHERE (`seasonStatus`.`active` = 1 OR `seasons`.`statusID` IS NULL) 
        GROUP BY `seasons`.`id`";
        $db = new Db($sql);
        return $db->rows;
    }

    static function hasFuture (League $league) {
        $sql = "SELECT `seasons`.* FROM `seasons` WHERE `leagueID` = :leagueID AND `statusID` = 2 AND `deleted` IS NULL";
        $db = new Db($sql,["leagueID" => $league->id]);
        if ($db->rows) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }
    
    static function RollForward (Season $season) {
        $newSeason = clone $season;
        $newSeason->id = $newSeason->startBookingID = $newSeason->locked = $newSeason->lockStamp = $newSeason->created = $newSeason->updated = null;
        $newSeason->statusID = 2;
        $newSeason->AutoName();
        $newSeason->Save();
        $season->nextID = $newSeason->id;
        $season->Save();
        $lastBooking = Booking::LastScheduledBooking($season);
        $launchDate = date('Y-m-d',strtotime('+7 days',strtotime($lastBooking->startDate)));
        new Db("UPDATE `leagues` SET `launchDate` = '$launchDate' WHERE `id` = {$season->leagueID}");
        return $newSeason;
    }

    static function Live (League $league) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`live` = 1 AND `seasons`.`leagueID` = {$league->id}";
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }

    static function Next (League $league) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL AND `seasons`.`leagueID` = {$league->id}";
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }

    static function Cancelled (League $league = null) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` IS NULL";
        if ($league) $sql .= " AND `seasons`.`leagueID` = {$league->id}";
        // echo $sql."<br>";
        $db = new Db($sql);
        if ($db->rows) {
            foreach ($db->rows as $r) {
                $season = new static();
                $season->Load($db->rows[0]);
                $return[] = $season;
            }
        }
        return $return;
    }

    static function LiveList(User $coordinator = null) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id`";
        if ($coordinator && !$coordinator->isAdmin) $sql .= " LEFT JOIN `leagues` ON `seasons`.`leagueID` = `leagues`.`id`";
        $sql .= " WHERE `seasonStatus`.`live` = 1";
        if ($coordinator && !$coordinator->isAdmin) $sql .= " AND `leagues`.`coordinator` = {$coordinator->id}";
        return static::Query($sql);
        // $db = new Db($sql);
        // if (isset($db->rows[0])) {
        //     $season = new static();
        //     $season->Load($db->rows[0]);
        //     return $season;
        // }
    }

    static function Open () {
        $sql = "SELECT * FROM `seasons` WHERE `statusID` = 1";
        $db = new Db($sql);
        if (!$db->rows) return [];
        foreach ($db->rows as $r) {
            $season = new static();
            $season->Load($r);
            $return[] = $season;
        }
        return $return;
    }

    static function Registration (League $league) {
        $sql = "SELECT * FROM `seasons` WHERE `openForRegistration` = 1 AND `leagueID` = {$league->id}";
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }

    static function Active (League $league) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasons`.`leagueID` = {$league->id} AND (`seasonStatus`.`active` = 1 OR `seasons`.`statusID` IS NULL) ORDER BY `created` ASC LIMIT 1";
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }

    static function Actives (League $league) {
        $sql = "SELECT `seasons`.* FROM `seasons` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasons`.`leagueID` = {$league->id} AND (`seasonStatus`.`active` = 1 OR `seasons`.`statusID` IS NULL) ORDER BY `created` ASC";
        return static::Query($sql);
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }

    }

    static function Following (Season $season) {
        $sql = "SELECT * FROM `seasons` WHERE `leagueID` = {$season->leagueID} AND `deleted` IS NULL AND `created` > '{$season->created}' ORDER BY `created` ASC LIMIT 1";
        $db = new Db($sql);
        if ($db->rows) {
            $return = new static();
            $return->Load($db->rows[0]);
            return $return;
        }
    }

    static function hasOFR (League $league) {
        $sql = "SELECT * FROM `seasons` WHERE (`openForRegistration` = 1 OR `statusID` = 2) AND `leagueID` = {$league->id} AND `deleted` IS NULL";
        $rlt = static::Query($sql);
        return (isset($rlt[0])) ? $rlt[0] : null;
        // $db = new Db($sql);
        // if (isset($db->rows[0])) {
        //     $season = new static();
        //     $season->Load($db->rows[0]);
        //     return $season;
        // }
    }

    static function Team (Team $team, $statusIds = []) {
        $sql = "SELECT seasons.* FROM seasons LEFT JOIN teamSeasons ON seasons.id = teamSeasons.seasonID WHERE teamSeasons.teamID = {$team->id} AND `teamSeasons`.`deleted` IS NULL AND `seasons`.`deleted` IS NULL";
        if ($statusIds) $sql .= " AND `seasons`.`statusID` IN (" . implode(",",$statusIds) . ")";
        return static::Query($sql);
    }

    static function AllLive () {
        $sql = "SELECT seasons.* FROM seasons LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id WHERE seasons.deleted IS NULL AND seasonStatus.live = 1";
        return static::Query($sql);
    }

    static function AllOpen () {
        $sql = "SELECT seasons.* FROM seasons LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id WHERE seasons.deleted IS NULL AND seasonStatus.active = 1 AND seasonStatus.live IS NULL";
        return static::Query($sql);
    }

    static function nextOrLive(League $league) {
        $sql = "SELECT 
            `seasons`.*, 
            CASE 
                WHEN 
                    `seasonStatus`.`live` = 1 
                THEN 'live' 
                WHEN 
                    `seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL 
                THEN 'next' 
            END AS `session_status` 
        FROM 
            `seasons` 
        LEFT JOIN 
            `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` 
        WHERE 
            `seasons`.`leagueID` = {$league->id}
            AND( 
                (`seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL) 
                OR `seasonStatus`.`live` = 1 
            ) 
        ORDER BY 
            `seasons`.`launchDate` DESC;";
    
        $db = new Db($sql);
        if (isset($db->rows[0])) {
            $season = new static();
            $season->Load($db->rows[0]);
            return $season;
        }
    }
}