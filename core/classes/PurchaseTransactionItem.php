<?php

class PurchaseTransactionItem extends Base {

    protected $dbTable = "pi_items";
    protected $dbFields = ["mainID","month","year","vatCode","total"];

    protected $mainID, $month, $year, $vatCode, $total;
    
    protected $purchaseTransaction;

    // function Save() {
    //     if (!$this->mainID) return "Cannot Save without an associated Purchase Transaction";
    //     if (!$this->month) return "Cannot Save without a designated Month";
    //     if (!$this->year) return "Cannot Save without a designated Year";
    //     if (!$this->vatCode) return "Cannot Save without a VAT Code";
    //     if (!$this->total) return "Cannot Save without a total";
    //     $pi = new PurchaseTransaction($this->mainID);
    //     $piTotalThusFar = static::PurchaseTransactionTotal($pi);
    //     if ($this->total > (($max=$pi->total - $piTotalThusFar))) return "Cannot allocate that much against Purchase Transaction {$pi->id} (Max $max)";
    //     parent::Save();
    // }

    function Save() {
        $pt = $this->getPurchaseTransaction();
        $type = $pt->getType();
        if ($type->action == "-") $this->total = -abs($this->total);
        return parent::Save();
    }

    function ApiData() {
        return [
            'id' => $this->id,
            'mainID' => (int)$this->mainID ?? null,
            'month' => (int)$this->month ?? null,
            'year' => (int)$this->year ?? null,
            'vatCode' => $this->vatCode,
            'total' => (float)$this->total ?? null,
        ];
    }

    function getPurchaseTransaction() {
        if (!$this->purchaseTransaction) return ($this->purchaseTransaction = new PurchaseTransaction($this->mainID));
    }

    function Delete(String $log_message = null) {
        $log_message = get_class($this). " deleted by " . User::AuthUser();
        parent::Delete($log_message);
        $this->getPurchaseTransaction();
        $this->purchaseTransaction->Evaluation(true);
    }

    static function PurchaseTransaction (PurchaseTransaction $purchaseTransaction) {
        if (!$purchaseTransaction->id) return [];
        $sql = "SELECT * FROM `pi_items` WHERE `mainID` = {$purchaseTransaction->id} AND `deleted` IS NULL ORDER BY year ASC, month ASC";
        return static::Query($sql);
    }

    static function PurchaseTransactionTotal (PurchaseTransaction $purchaseTransaction) {
        $sql = "SELECT SUM(`total`) AS `total` FROM `pi_items` WHERE `mainID` = $purchaseTransaction->id AND `deleted` IS NULL";
        $rlt = new Db($sql);
        if (!isset($rlt->rows[0]['total']) || !$rlt->rows[0]['total']) return 0;
        return $rlt->rows[0]['total'];
    }

    static function SagePurchaseImportFiles () {
        $folder = CORE_FOLDER . implode(DIRECTORY_SEPARATOR,["filestore","transactions","sage_import"]);
        $files = scandir($folder);
        foreach ($files as $k => $v) {
            if ($v =="." || $v == "..") unset($files[$k]);
        }
        return $files;
    }

    static function SagePurchaseImportData () {
        $db = new Db("SELECT `pi`.`id` AS `piID`, `pi`.`total` AS `piTotal`, `pi`.`venueID`, `pi`.`taxDate`, `pi`.`reference`, `pi_items`.`id` AS `piItemID`, `pi_items`.* FROM `pi` LEFT JOIN `pi_items` ON `pi`.`id` = `pi_items`.`mainID` LEFT JOIN `venues` ON `pi`.`venueID` = `venues`.`id` WHERE `pi`.`deleted` IS NULL AND `pi_items`.`deleted` IS NULL AND `pi`.`sageImport` IS NULL");
        if ($db->rows) return $db->rows;
    }

    static function SagePurchaseImportCreate (Array $data = []) {
        $taxTreatment = [
            "T2" => ["rate" => 20]
        ];
        $sagePurchaseNominalRef = 4000; $sagePurchaseTransactionPrefix = "PI-";
        $headerRow = [
            "Type","Account Reference", "Nominal A/C Ref","Department Code","Date","Reference","Details","Net Amount","Tax Code","Tax Amount","Exchange Rate","Extra Reference"
        ];
        $added = []; $dateStamp = date('YmdHis');
        foreach ($data as $d) {
            $venue = new Venue($d['venueID']);
            /* Do not import without a Venue Sage Accoutn Reference */
            // if (!$venue->sageAccount) continue;
            $taxAmount = 0;
            $extra = $netAmount = $d['total']; 
            if ($d['vatCode']=="T2") {
                $taxAmount = $netAmount * .2;
                $netAmount *= .8;
            }
            $datarows[] = [
                "PI",$venue->sageAccount,$sagePurchaseNominalRef,"",date('d/m/Y',strtotime($d['taxDate'])),"$sagePurchaseTransactionPrefix{$d['id']}",$d['reference'],$netAmount, $d['vatCode'], $taxAmount,"",""
            ];
            $added[] = $d['id'];
        }
        if (!$added) return [];
        $filename = CORE_FOLDER . implode(DIRECTORY_SEPARATOR,["filestore","transactions","sage_import"]) . DIRECTORY_SEPARATOR . $dateStamp.".csv";
        $csvOutput = implode(",",$headerRow)."\n";
        foreach ($datarows as $d) {
            $csvOutput .= implode(",",$d)."\n";
        }
        $rlt = @file_put_contents($filename,$csvOutput);
        if ($rlt) {
            $sql = "UPDATE `pi` SET `sageImport` = '".date('Y-m-d H:i:s',strtotime($dateStamp))."'";
            $conn = " WHERE ";
            foreach ($added as $a) {
                $sql .= $conn . "`id` = $a";
                $conn = " OR ";
            }
            echo ($sql."<br>");
        }
        return $datarows;
    }

    static function TotalMonthYear (Venue $venue, Int $month, Int $year) {
        $sql = "SELECT SUM(`pi_items`.`total`) AS `total` FROM `pi_items` LEFT JOIN `pi` ON `pi_items`.`mainID` = `pi`.`id` WHERE `pi`.`venueID` = {$venue->id} AND `pi`.`deleted` IS NULL AND `pi_items`.`deleted` IS NULL AND `pi_items`.`month` = $month AND `pi_items`.`year` = $year";
        // echo $sql;
        $db = new Db($sql);
        return (isset($db->rows[0]['total']) && $db->rows[0]['total']) ? $db->rows[0]['total'] : 0;
    }

    static function VenuePeriodTotal (Venue $venue, Int $year, Int $month) {
        $sql = "SELECT SUM(`pi_items`.`total`) AS `totalPurchased` FROM `pi_items` LEFT JOIN `pi` ON `pi_items`.`mainID` = `pi`.`id` WHERE `pi`.`venueID` = :venueID AND `pi`.`deleted` IS NULL AND `pi_items`.`deleted` IS NULL AND `pi_items`.`month` = :month AND `pi_items`.`year` = :year";
        $rlt = Database::Execute($sql,[
            'venueID' => $venue->id,
            'month' => $month,
            'year' => $year
        ]);
        return ($rlt['error']) ? $rlt['error']['message'] : (float)$rlt['success']['rows'][0]['totalPurchased'];
    }

    static function PrePays () {
        $sql = "SELECT pi_items.*, pi.taxDate, venues.id AS venueID, venues.name AS venueName, venues.sageAccount, pi.reference FROM pi_items LEFT JOIN pi ON pi_items.mainID = pi.id LEFT JOIN venues ON pi.venueID = venues.id LEFT JOIN pi_terms ON venues.purchaseTerms = pi_terms.id WHERE pi.deleted IS NULL AND pi_items.deleted IS NULL AND pi_terms.isImportable IS NULL ORDER BY pi_items.mainID, pi.created DESC";
        $rlt = Database::Execute($sql);
        return $rlt['success']['rows'] ?? $rlt['error']['message'];
    }
}