<?php

class Trigger extends Base {

    protected $dbTable = "triggers";

    protected $dbFields = ["name", "completed"];

    static function Set (String $name) {
        /**
         * If <PERSON><PERSON> already planned?  Returns TRUE
         * If Trigger inserted?         Returns INT
         * else                         Returns ARRAY with DB errors
         */
        $sql = "SELECT `id` FROM `triggers` WHERE `name` = :name AND `completed` IS NULL";
        $db = new Db($sql,["name" => $name]);
        if ($db->rows) return true;
        $sql = "INSERT INTO `triggers` SET `name` = :name";
        $db = new Db($sql,["name" => $name]);
        return ($db->errors) ? $db->errors : $db->lastInsertID;
    }

    static function Run () {
        /**
         * If DB errors             returns ARRAY of DB errors
         * If no triggers to run    returns FALSE
         * 
         */
        $sql = "SELECT `id`,`name` FROM `triggers` WHERE `completed` IS NULL";
        $db = new Db($sql); if ($db->errors) return $db->errors;
        if (!$db->rows) return false;
        foreach ($db->rows as $r) {
            call_user_func($r['name']);
            new Db("UPDATE `triggers` SET `completed` = NOW() WHERE `id` = :id",["id" => $r['id']]);
        }
    }

    static function Clear () {
        $sql = "DELETE FROM `triggers` WHERE `created` < '".date('Y-m-d',strtotime("28 days"))."'";
        $db = new Db($sql);
    }

}