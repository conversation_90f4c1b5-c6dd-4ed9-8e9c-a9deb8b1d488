<?php

class DocLibraryType {

    use GeneralTraits;

    protected $dbTable = "doc_types";
    protected $dbFields = [
        "id" => ["type" => "int unsigned", "extra" => "primary key auto_increment"],
        "created" => ["type" => "DATETIME", "default" => "current_timestamp()"],
        "updated" => ["type" => "DATETIME", "default" => "current_timestamp()", "extra" => "ON UPDATE current_timestamp()"],
        "deleted" => ["type" => "DATETIME"],
        "name" => ["type" => "tinytext"],
        "slug" => ["type" => "tinytext"],
    ];

    protected $id;
    protected $name;

    function Save() {
        $this->slug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $this->name)));
        if ($this->id) {
            Database::Execute("UPDATE `doc_types` SET `name` = :name, `slug` = :slug WHERE `id` = :id",["name" => $this->name, "slug" => $this->slug, "id" => $this->id]);
        } else Database::Execute("INSERT INTO `doc_types` SET `name` = :name, `slug` = :slug",["name" => $this->name, "slug" => $this->slug]);
    }

    function __toString() {
        return "{$this->name}";
    }

    function ApiData() {
        return [
            "name" => $this->name,
            "slug" => "https://cdn.leagues4you.co.uk/{$this->slug}"
        ];
    }



    static function Listing () {
        $sql = "SELECT * FROM `doc_types` WHERE `deleted` IS NULL";
        return static::Query($sql);
    }

    static function FromName(String $name) {
        $sql = "SELECT * FROM doc_types WHERE name = :name";
        $rlt = static::Query($sql,["name" => $name]);
        return ($rlt) ? $rlt[0] : null;
    }

    static function FromSlug(String $slug) {
        $sql = "SELECT * FROM doc_types WHERE slug = :slug";
        $rlt = static::Query($sql,["slug" => $slug]);
        return ($rlt) ? $rlt[0] : null;
    }

    static function UploadForm (String $target = null) {
        // Form
        $form = new Form();
        $form->action = $target;
        $form->method = "post";
        $form->enctype = "multipart/form-data";
        // Select
        $select1 = new Select();
        $select1->name = "doc[typeID]";
        foreach (static::Listing() as $type) $select1->data[$type->id] = $type->name;
        $form->addChild($select1);
        // Input
        $input1 = new Input();
        $input1->id = "upload";
        $input1->name = "file";
        $input1->type = "file";
        // Input
        $input2 = new Input();
        $input2->name = "btn";
        $input2->type = "submit";
        $input2->value = "upload";
        $form->addChild($input1);
        $form->addChild($input2);
        echo $form;
    }

}