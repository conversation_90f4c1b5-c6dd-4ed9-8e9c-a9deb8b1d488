<?php

class DocLibraryVersion   {
    
    use GeneralTraits;
    
    const DB_TABLE = 'docs';
    protected $dbTable = 'docs';
    protected $dbFields = [
        "id" => ["type" => "int unsigned", "extra" => "primary key auto_increment"],
        "created" => ["type" => "DATETIME", "default" => "current_timestamp()"],
        "updated" => ["type" => "DATETIME", "default" => "current_timestamp()", "extra" => "ON UPDATE current_timestamp()"],
        "deleted" => ["type" => "DATETIME"],
        "typeID" => ["type" => "tinyint unsigned"],
        "filename" => ["type" => "tinytext"],
        "version" => ["type" => "mediumint unsigned"],
        "filedata" => ["type" => "longblob"],
    ];

    protected $typeID;
    protected $filename;
    protected $version;
    protected $filedata;
    protected $contentType;

    protected $folder;

    protected $type;

    function __construct() {
        $this->folder = Tools::CheckFolder(__DIR__);
    }

    function getType() {
        if (!$this->type && $this->typeID) $this->type = new DocLibraryType($this->typeID);
        return $this->type;
    }

    function __toString() {
        $this->getType();
        return "{$this->type} v{$this->version}";
    }

    function __Output() {
        header("Content-Type:{$this->contentType}");
        header("Content-Length: " . strlen($this->filedata));
        return $this->filedata;
    }

    function toFile(String $folder, String $filename = 'doc.') {

    }
    
    static function Upload (Array $data, Array $file) {
        $obj = new static();
        foreach ($data as $k => $v) $obj->$k = $v;
        // return ["error" => $data];
        $sql = "SELECT COUNT(`id`) AS `count` FROM `docs` WHERE `typeID` = :typeID";
        $rlt = Database::Execute($sql,["typeID" => $data['typeID']]);
        if ($rlt['error']) {
            if (strpos($rlt['error']['message'],"Base table or view not found")) Database::Setup(static::DB_TABLE,static::$dbFields);
            return ["error" => $rlt['error']];
        } 
        $version = (isset($rlt['success']['rows'][0]['count'])) ? $rlt['success']['rows'][0]['count'] + 1 : 1;
        // Get Filename, File
        // $fileData = file_get_contents($file['tmp_name']);
        $destinationFilename = strtolower(str_replace([" "],["_"],$obj->__toString()));
        $destinationFilename .= ".".pathinfo($file['name'], PATHINFO_EXTENSION);
        $rlt = move_uploaded_file($file['tmp_name'], "{$obj->folder}/$destinationFilename");
        Tools::Dump($destinationFilename);
        $sql = "INSERT INTO `docs` SET `typeID` = :typeID, `filename` =  :docFilename, `version` = :docVersion";
        $sqlData = [
            "typeID" => $data['typeID'], 
            "docFilename" => $destinationFilename, 
            "docVersion" => $version,
            // "contentType" => mime_content_type($file['tmp_name'])
        ];
        // Tools::Dump($sqlData);
        return Database::Execute($sql,$sqlData);
    }

    static function Fetch (DocLibraryType $docLibraryType, Int $version = null) {
        $data['typeID'] = $docLibraryType->id;
        $sql = "SELECT * FROM `docs` WHERE `typeID` = :typeID";
        if ($version) {
            $sql .= " AND `version` = :version";
            $data['version'] = $version;
        } else $sql .= " AND `deleted` IS NULL";
        $sql .= " ORDER BY `version` DESC LIMIT 1";
        $rlt = Database::Execute($sql,$data);
        if (!$rlt['success']['rows']) return;
        $doc = new static();
        $doc->arrayLoad($rlt['success']['rows'][0]);
        return $doc;
    }

    static function Listing() {
        $docTypes = DocLibraryType::Listing ();
        $return = [];
        foreach ($docTypes as $docType) {
            $rlt = static::Fetch($docType);
            if ($rlt) $return[] = $rlt;
        }
        return $return;
    }

}