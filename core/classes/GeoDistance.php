<?php
/** 
 * Based on the Haversine formula
 * https://stackoverflow.com/questions/27928/calculate-distance-between-two-latitude-longitude-points-haversine-formula
 * See https://en.wikipedia.org/wiki/Haversine_formula and
 * http://www.movable-type.co.uk/scripts/latlong.html
 */

class GeoDistance {

    protected $startLat, $startLng;
    protected $endLat, $endLng;

    function __construct() {

    }
    function startPoint(Float $lat, Float $lng) {
        $this->startLat = $lat;
        $this->startLng = $lng;
    }
    function endPoint(Float $lat, Float $lng) {
        $this->endLat = $lat;
        $this->endLng = $lng;
    }
    function directDistance() {
        $r = 6371; // Radius of the earth in km
        $dLat = $this->deg2rad($this->endLat - $this->startLat);  // deg2rad below
        $dLon = $this->deg2rad($this->endLng-$this->startLng); 
        $a = sin($dLat/2) * sin($dLat/2) + cos($this->deg2rad($this->startLat)) * cos($this->deg2rad($this->endLat)) * sin($dLon/2) * sin($dLon/2); 
        $c = 2 * atan2(sqrt($a), sqrt(1-$a)); 
        $d = $r * $c; // Distance in km
        return ($d *.8);
    }
    function deg2rad ($deg) {
        return $deg * (pi()/180);
    }
}