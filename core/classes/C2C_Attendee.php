<?php

class C2C_Attendee extends Base {
    
    protected $dbTable = "c2c_attendees";
    protected $dbFields = ["bookingID","userID", "firstname","lastname","email","mobile"];

    protected $firstname,$lastname,$email,$mobile;

    protected $bookingID, $userID;

    protected $booking, $user;

    function getBooking() {
        if (!$this->booking) $this->booking = new C2C_Booking($this->bookingID);
        return $this->booking;
    }
    
    function ApiData() {
        return [
            "id" => $this->id,
            "created" => $this->created,
            "updated" => $this->updated,
            "bookingID" => $this->tasterID,
            "userID" => $this->userID,
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "email" => $this->email,
            "mobile" => $this->mobile,
        ];
    }

    function getUser() {
        if (!$this->userID) $this->SynchroniseUser();
        if ($this->userID && !$this->user) $this->user = new User($this->userID);
        return $this->user;
    }

    function SynchroniseUser() {
        if ($this->userID || !$this->email) return;
        $user = User::SearchCreate([
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "email" => $this->email,
            "mobile" => $this->mobile,
        ]);
        if (is_object($user) && $user->id) {
            // echo "Looked up User {$user->id}<br>";
            $this->userID = $user->id;
            $this->Save();
            return;
        }
    }

    static function C2C_Booking (C2C_Booking $c2c_booking) {
        return static::Query("SELECT * FROM `c2c_attendees` WHERE `bookingID` = {$c2c_booking->id} AND `deleted` IS NULL");
    }

    static function Total (C2C_Booking $c2c_booking) {
        $sql = "SELECT COUNT(`id`) AS `total` FROM `c2c_attendees` WHERE `bookingID` = {$c2c_booking->id} AND `deleted` IS NULL AND (`userID` IS NOT NULL OR `email` IS NOT NULL)";
        $db = new Db($sql);
        return (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
    }

}