<?php

class WebContact extends Base {

    protected $dbTable = "webContacts";
    protected $dbOrder = ["created" => "DESC"];
    protected $dbFields = ["name","email","mobile","message"];
    protected $name, $email, $mobile, $message;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    /* Getters */
    function getName() { return $this->name;}
    function getEmail() { return $this->email;}
    function getMobile() { return $this->mobile;}
    function getMessage() { return $this->message;}
    /* Modifiers */
    function received() { return date('H:ia jS F Y',strtotime($this->created));}
    /* Statics */
    static function Add (Array $data = []) {
        $sql = null;
        foreach ($data as $k => $v) $sql .= (!$sql) ? "`$k` = :$k" : ", `$k` = :$k";
        $sql = "INSERT INTO `webContacts` SET $sql";
        $db = new Db($sql,$data);
        $message[] = [
            "From" => $data['name'],
            "Email" => $data['email'],
            "Mobile" => $data['mobile'],
            "Message" => $data['message']
        ];
        Email::Issue ("Contact from Bloom Netball website", $message, ["email" => "<EMAIL>", "name" => "Bloom Netball"]);
        return $db;
    }

    static function markSpam(Int $id) {
        new Db("UPDATE `webContacts` SET `spam` = 1 WHERE `id` = $id");
    }
    static function markCompleted(Int $id) {
        new Db("UPDATE `webContacts` SET `completed` = 1 WHERE `id` = $id");
    }
    static function Listing(?int $limit = null, ?int $start = null) {
        $db = new Db("SELECT * FROM `webContacts` WHERE `spam` IS NULL AND `completed` IS NULL ORDER BY `created` DESC");
        if ($db->rows) {
            $return = [];
            foreach ($db->rows as $r) {
                $wc = new static();
                $wc->Load($r);
                $return[] = $wc;
            }
            return $return;
        }
    }

    static function SendAll() {
        $webContacts = static::Listing();
        $html = "<table><thead>";
        $html .= "<tr>";
        $html .= "<th>Timestamp</th>";
        $html .= "<th>Name</th>";
        $html .= "<th>Email</th>";
        $html .= "<th>Mobile</th>";
        $html .= "<th>Message</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody>";
        foreach ($webContacts as $webContact) {
            $html .= "<tr>";
            $html .= "<td>{$webContact->created}</td>";
            $html .= "<td>{$webContact->name}</td>";
            $html .= "<td>{$webContact->email}</td>";
            $html .= "<td>{$webContact->mobile}</td>";
            $html .= "<td>{$webContact->message}</td>";
            $html .= "</tr>";    
        }
        $html .= "</tbody>";
        $html .= "</table>";
        echo $html;
    }
}