<?php

class Tools {

    static function Dump ($data) {
        echo "<pre>"; print_r($data); echo "</pre>";
    }
    static function Message(String $text, $level = "info") {
        Logging::Add("Message: $text ($level) for ".Auth::AuthUser());
        $_SESSION['messaging'][$level][] = $text;
    }
    static function Messages() {
        if (isset($_SESSION['messaging']) && $_SESSION['messaging']) {
            foreach ($_SESSION['messaging'] as $level => $messages) {?>
             <div class="alert alert-<?php echo $level;?> alert-dismissible fade show" role="alert"><?php 
                foreach ($messages as $message) {
                    echo "$message<br>";
                }?>
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div><?php
            }
            unset($_SESSION['messaging']);
        }
    }
    static function CheckFolder (String $folder) {
        // echo "Received $folder<br>";
        /* Reverse Backslash to Forward Slash [for Windows] */
        // $folder = str_replace(["/"],["\\"],$folder);
        /* Add trailing slash if not present */
        if (substr($folder,0,1)=="/") $folder = substr($folder,1);
        if (substr($folder,-1)=="/") $folder = substr($folder,0,strlen($folder)-1);
        $elements = explode("/",$folder);
        $folderToCheck = null;
        foreach($elements as $element) {
            $folderToCheck .= ($folderToCheck) ? "/$element" : $element;
            // echo "Trying $folderToCheck<br>";
            if ($element == "..") continue;
            if ($folderToCheck && !file_exists($folderToCheck)) {
                $old = umask(0);
                mkdir($folderToCheck,0775);
                umask($old);
            } 
        }
        return "$folderToCheck/";
    }

    static function HtmlEscape($string) {
        if (empty($string)) return $string;
        return htmlspecialchars($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}