<?php

trait GeneralTraits {

    function __construct(Int $id = null) {
        if ($id) $this->dbLoad($id,$this->dbTable);
    }

    function dbLoad (Int $id, String $dbTable, String $primaryKeyField = "id") {
        $sql = "SELECT * FROM `$dbTable` WHERE `$primaryKeyField` = $id";
        $rlt = Database::Execute($sql, ["primaryKeyField" => $id]);
        if ($rlt['success']['rows']) $this->arrayLoad($rlt['success']['rows'][0]);
    }

    function arrayLoad (Array $data = []) {
        foreach ($data as $k => $v) $this->$k = $v;
    }
    
    function __get($property) {
        return $this->$property;
    }

    function __set($property,$value) {
        $this->$property = $value;
    }

    static function Query (String $sql = null, Array $data = []) {
        $obj = new static();
        if (!$sql) $sql = "SELECT * FROM `{$obj->dbTable}`";
        $db = new Db($sql, $data);
        // echo $sql;
        if (!$db->rows) return [];
        // Tools::Dump($db);
        $return = [];
        foreach ($db->rows as $r) {
            $o = new static();
            $o->arrayLoad($r);
            $return[] = $o;
        }
        return $return;
    }

    static function By (String $value, String $by) {
        $obj = new static();
        if (!$obj->dbTable) return;
        $sql = "SELECT * FROM `{$obj->dbTable}` WHERE `$by` = :$by";
        $rlt = static::Query($sql,[$by => $value]);
        return ($rlt) ? $rlt[0] : null;
    }

}