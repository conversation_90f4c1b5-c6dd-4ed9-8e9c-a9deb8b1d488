<?php

class League extends Base {

    protected $dbTable = "leagues", $dbOrder = ["name" => "ASC"];
    protected $sportID, $regionID;
    protected $defaultVenueID, $defaultDay;
    protected $coordinator;
    protected $visible;
    protected $name;
    protected $launchDate, $launchTime, $playingSurface, $totalPrice;
    protected $dbFields = ["sportID", "regionID", "name", "coordinator", "defaultVenueID", "defaultDay", "visible", "status", "launchDate", "launchTime", "playingSurface", "totalPrice", "notes", "url_slug", "playerRequestGroup"];
    protected $status, $statusText;
    protected $notes;
    protected $url_slug;
    protected $playerRequestGroup;

    protected $venue;
    protected $leagueCoordinator;

    function __construct(Int $id = null) {
        parent::__construct($id);
        // switch ($this->status) {
        //     case 1: $this->statusText = "Join Waiting List"; break;
        //     case 2: $this->statusText = "Closed for Registration"; break;
        //     default: $this->statusText = "Open for Registration";
        // }
    }

    function __toString() {
        return "{$this->name}";
    }
    /* Getters */
    function getRegionID() {
        return $this->regionID;
    }
    function getSportID() {
        return $this->sportID;
    }
    function getDefaultVenueID() {
        return $this->defaultVenueID;
    }
    function getVenue() {
        if (!$this->venue) $this->venue = new Venue($this->defaultVenueID);
        return $this->venue;
    }

    function getVenueName() {
        $this->getVenue();
        if ($this->venue) return $this->venue->name;
    }

    function getDefaultDay() {
        return $this->defaultDay;
    }
    function getLaunchDate() {
        return $this->launchDate;
    }
    function getLaunchTime() {
        return $this->launchTime;
    }
    function getPlayingSurface() {
        return $this->playingSurface;
    }
    function getPlayingSurfaceText() {
        return ($this->playingSurface) ? static::Surfaces()[$this->playingSurface]['name'] : null;
    }

    function getCoordinatorID() {
        return $this->coordinator;
    }

    function getCoordinator() {
        if (!$this->leagueCoordinator) $this->leagueCoordinator = new User($this->coordinator);
        return $this->leagueCoordinator;
    }

    function getStatus() {
        return;
        return $this->status;
    }
    function getTotalPrice() {
        return $this->totalPrice;
    }

    function getPlanningDay() {
        if (!$this->defaultDay) return;
        return static::Days()[$this->defaultDay];
    }

    function getSport() {
        if (!$this->sport) $this->sport = new Sport($this->sportID);
        return $this->sport;
    }

    function TermsURL() {
        $this->getSport();
        return $this->sport->termsConditions;
    }

    function getUrl() {
        return ($this->url_slug) ? $this->url_slug : $this->setUrl();
    }

    function setUrl() {
        $slug = trim($this->name); // Trim whitespace
        $slug = preg_replace('/[\'’]/', '', $slug); // Remove apostrophes
        $slug = preg_replace('/&/', 'and', $slug); // Replace & with 'and'
        $slug = preg_replace('/\s+/', '-', $slug); // Replace spaces with hyphens
        $slug = preg_replace('/[^a-zA-Z0-9\-]/', '', $slug); // Remove non-alphanumeric characters except hyphens
        $slug = strtolower($slug); // Convert to lowercase
        return $slug;
    }

    function TransferMe() {
        $season = Season::Active($this);
        if (!$season) return;
        $teams = Team::League($this);
        if (!$teams) return;
        foreach ($teams as $team) {
            if (!$team->nextConfirmed) continue;
            $divisionID = Division::Default($season);
            TeamSeason::Add($team->id, $season->id, $divisionID, $team->captainID, $team->treasurerID);
        }
    }

    function getCaptainsPack() {
        $sport = new Sport($this->sportID);
        return $sport->getCaptainsPack();
    }

    /* Checkers */
    function isVisible() {
        return ($this->visible == 1) ? true : false;
    }
    function isActive() {
        return (Season::Active($this)) ? true : false;
    }

    /* Doers */
    function Save() {
        // $createDefaultSeason = (!$this->id) ? true : false;
        if (!$this->url_slug) $this->url_slug = $this->setUrl();
        $rlt = parent::Save();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
        // if ($this->id && $createDefaultSeason ===true) $this->defaultSeason();
    }

    function defaultSeason() {
        switch (date('n')) {
            case 3:
            case 4:
            case 5:
                $seasonName = "Spring " . date('Y', strtotime("+ 1 year"));
                break;
            case 6:
            case 7:
            case 8:
                $seasonName = "Summer " . date('Y');
                break;
            case 9:
            case 10:
            case 11:
                $seasonName = "Autumn " . date('Y');
                break;
            default:
                $seasonName = "Winter " . date('Y');
        }
        $season = new Season();
        $seasonData = [
            "leagueID" => $this->id,
            "name" => "$seasonName Season",
            "rounds" => 1,
            "fixtureCharge" => 27,
            "officialCharge" => 5,
            "startDate" => null
        ];
        $season->Load($seasonData);
        $season->Save();
    }

    function CheckForDefaultSeason() {
        $seasons = Season::forLeague($this);
        if (!$seasons) {
            $season = new Season();
            $season->leagueID = $this->id;
            $season->yearID = date('Y');
            if (date('n') >= 12 || date('n') <= 2) {
                $season->seasonalID = 1;
                $season->yearID++;
            } elseif (date('n') >= 3 && date('n') <= 5) {
                $season->seasonalID = 2;
            } elseif (date('n') >= 6 && date('n') <= 8) {
                $season->seasonalID = 3;
            } else $season->seasonalID = 4;
            $season->AutoName();
            $season->Save();
            // echo "<pre>";
            // print_r($season);
            // exit("</pre>");
        }
    }

    function InfoApi() {
        $this->getSport();
        $registrationSeason = Season::nextOrLive($this);
        if ($registrationSeason) {
            $venue = $registrationSeason->getVenue();
            $registration = [
                'id' => $registrationSeason->id,
                'name' => $registrationSeason->name,
                'day' => $registrationSeason->FullDayText(),
                'startTime' => date('g:ia', strtotime($registrationSeason->launchDate . " " . $registrationSeason->timeStarts)),
                'endTime' => date('g:ia', strtotime($registrationSeason->launchDate . " " . $registrationSeason->timeEnds)),
                'situated' => $registrationSeason->resolvePlayingSurface(),
                'fixtureCharge' => (float) $registrationSeason->fixtureCharge,
                'launchDate' => $registrationSeason->launchDate,
                'venue' => $venue->ApiBasic(),
                'status' => $registrationSeason->session_status
            ];
        } else $registration = null;
        $liveSeason = Season::Live($this) ?? Season::Next($this);
        $divisions = ($liveSeason) ? $liveSeason->getDivisions() : [];
        $coordinator = $this->getCoordinator();
        $live = ($liveSeason) ? [
            'id' => $liveSeason->id,
            'tables' => $liveSeason->getStandings()
        ] : [];
        return [
            'id' => $this->id,
            'name' => $this->name,
            'enterTeamImgUrl' => $this->sport->teamSignupImage,
            'joinTeamImgUrl' => $this->sport->playerSignupImage,
            'videoexplainer' => $this->sport->videoExplainer,
            'registration' => $registration,
            'live' => $live,
            'facebook' => [
                'generalGroup' => [
                    'name' => 'leagues4you',
                    'url' => 'https://www.facebook.com/leagues4you/'
                ],
                'playerRequestGroup' => ($this->playerRequestGroup) ? $this->playerRequestGroup : 'https://www.facebook.com/leagues4you/',
            ],
            'coordinator' => [
                'profilePictureUrl' => $coordinator->profilePictureUrl ?? 'https://leagues4you.co.uk/wp-content/uploads/2022/07/missing-profile-pic-1.jpg',
                'firstname' => $coordinator->firstname,
                'email' => $coordinator->email,
                'bio' => $coordinator->bio
            ],
            'nearby' => [
                ['id' => 1, 'name' => 'League 1'],
                ['id' => 2, 'name' => 'League 2'],
                ['id' => 3, 'name' => 'League 3'],
                ['id' => 4, 'name' => 'League 4'],
                ['id' => 5, 'name' => 'League 5'],
                ['id' => 6, 'name' => 'League 6'],
                ['id' => 7, 'name' => 'League 7'],
                ['id' => 8, 'name' => 'League 8'],
                ['id' => 9, 'name' => 'League 9'],
                ['id' => 10, 'name' => 'League 10'],
            ]
        ];
    }

    function ApiOutput() {
        // $venueID = $this->getDefaultVenueID(); 
        // if ($venueID) {
        //     $venue = new Venue($venueID);
        //     $venueOutput = $venue->ApiOutput();
        // } else $venueOutput = null;

        $live = Season::Live($this);
        $ofr = Season::hasOFR($this);

        switch ($ofr->defaultDay) {
            case "mo":
                $day = "Monday";
                break;
            case "tu":
                $day = "Tuesday";
                break;
            case "we":
                $day = "Wednesday";
                break;
            case "th":
                $day = "Thursday";
                break;
            case "fr":
                $day = "Friday";
                break;
            case "sa":
                $day = "Saturday";
                break;
            case "su":
                $day = "Sunday";
                break;
        }

        if ($ofr->launchDate && $ofr->launchTime) {
            $launch = $ofr->launchDate . " " . $ofr->launchTime;
        } else $launch = null;

        $sport = new Sport($this->sportID);
        // $seasonData = Season::leagueDefaults($this);
        // return ["seasons" => $seasonData];
        // if (!$seasonData) return [];
        // echo "<pre>"; print_r($seasonData); exit("</pre>");
        // $divisions = ($seasonData["live"]) ? Division::forSeason($seasonData["live"]) : [];
        $divisions = ($live) ? Division::forSeason($live) : [];
        if ($divisions) {
            foreach ($divisions as $k => $v) {
                $divisions[$k] = $v->ApiOutput();
            }
        }

        $return = [
            "id" => $this->id,
            "name" => $this->name,
            "sport" => [
                "id" => $this->sportID,
                "name" => $sport->getName(),
                "img" => $sport->getWebImage(),
            ],
            "status" => null,
            "ofr" => ($ofr && $ofr->id) ? $ofr->name : null,
            "statusText" => $this->statusText,
            "venue" => $venueOutput,
            "day" => $day,
            "days" => [
                "mo" => ($ofr->defaultDay == "mo") ? true : false,
                "tu" => ($ofr->defaultDay == "tu") ? true : false,
                "we" => ($ofr->defaultDay == "we") ? true : false,
                "th" => ($ofr->defaultDay == "th") ? true : false,
                "fr" => ($ofr->defaultDay == "fr") ? true : false,
                "sa" => ($ofr->defaultDay == "sa") ? true : false,
                "su" => ($ofr->defaultDay == "su") ? true : false
            ],
            "startTime" => ($ofr->launchTime) ? substr($ofr->launchTime, 0, 5) : "TBA",
            "playingSurface" => ($ofr->playingSurface) ? static::Surfaces()[$ofr->playingSurface]['name'] : "TBA",
            "fixtureCharge" => $ofr->totalCharge(),
            "nearby" => $this->fetchNearby(),
            "launch" => $launch
        ];
        if ($live || $ofr) {
            // if (isset($seasonData['live']) && $seasonData['live']) {
            $return1 = [
                "seasons" => [
                    "live" => ($live) ? $live->ApiOutput() : null,
                ],
                "divisions" => $divisions,
                "duration" => $live->getDuration(),
            ];
            //     array_merge($return,$return1);
        }
        return $return;
    }

    function ApiData($withDivisions = false) {
        $sport = new Sport($this->sportID);
        // $actives = Season::Actives($this);
        $ofr = Season::hasOFR($this);
        $liveSeason = Season::Live($this);
        if (!$ofr && !$liveSeason) return;
        $season = ($ofr) ? $ofr : $liveSeason;
        if (!$season) return;
        // Tools::Dump($ofr);
        // return $ofr;
        // $venue = new Venue($this->defaultVenueID);
        // if (isset($season->defaultDay)) {
        switch ($season->defaultDay) {
            case "mo":
                $day = "Monday";
                break;
            case "tu":
                $day = "Tuesday";
                break;
            case "we":
                $day = "Wednesday";
                break;
            case "th":
                $day = "Thursday";
                break;
            case "fr":
                $day = "Friday";
                break;
            case "sa":
                $day = "Saturday";
                break;
            case "su":
                $day = "Sunday";
                break;
            default:
                $day = null;
        }
        // } else $day = null;

        if ($liveSeason && $withDivisions === true) {
            $liveDivisions = Division::forSeason($liveSeason);
            foreach ($liveDivisions as $liveDivision) {
                $divisions[] = $liveDivision->ApiOutput();
                // $divisions[] = ["id" => $liveDivision->id];
            }
        } else $divisions = [];
        // $divisions = [];
        // $registrationSeason = Season::Registration($this);
        $season->getVenue();
        // $proximates = ($season->venue && $season->venue->lat) ? Venue::Proximates($season->venue->lat,$season->venue->lng) : [];
        // if ($proximates) {
        //     foreach ($proximates as $proximate) $nearby[] = ["id" => $proximate->id, "name" => $proximate->name];
        // } else $nearby = [];
        return [
            "id" => $this->id,
            "name" => $this->name,
            "sport" => $sport->ApiData(),
            "status" => $this->status,
            "statusText" => $this->statusText,
            "ofr" => ($ofr && $ofr->id) ? $ofr->name : null,
            "url" => $this->getUrl(),
            "termsUrl" => $this->TermsURL(),
            // "venue" => $venue->ApiData(),
            "day" => $day,
            "days" => [
                "mo" => ($season->defaultDay == "mo") ? true : false,
                "tu" => ($season->defaultDay == "tu") ? true : false,
                "we" => ($season->defaultDay == "we") ? true : false,
                "th" => ($season->defaultDay == "th") ? true : false,
                "fr" => ($season->defaultDay == "fr") ? true : false,
                "sa" => ($season->defaultDay == "sa") ? true : false,
                "su" => ($season->defaultDay == "su") ? true : false
            ],
            "startTime" => ($ofr && $ofr->timeStarts) ? substr($ofr->timeStarts, 0, 5) : "TBA",
            "playingSurface" => ($season && $season->playingSurface) ? static::Surfaces()[$season->playingSurface]['name'] : "TBA",
            "fixtureCharge" => ($ofr) ? $ofr->totalCharge() : null,
            "nearby" => [],
            "launch" => ($ofr && $ofr->launchDate) ? $ofr->launchDate : null,
            "seasons" => [
                "live" => ($liveSeason) ? $liveSeason->ApiData() : [],
                "registration" => ($ofr) ? $ofr->ApiData() : [],
            ],
            "divisions" => $divisions
        ];
    }

    function ApiBrief() {
        return [
            "id" => $this->id,
            "name" => $this->name
        ];
    }

    function ApiBasic() {
        return [
            "id" => $this->id,
            "name" => $this->name
        ];
    }

    function fetchNearby() {
        $sql = "SELECT * FROM `leagues` WHERE `visible` = 1 AND `defaultVenueID` IN (SELECT `venue2` FROM `venueDistances` WHERE `venue1` = {$this->defaultVenueID} AND `distance` <= 50)";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $l = new static();
            $l->Load($r);
            // $venue = new Venue($r['defaultVenueID']);
            // $return[] = [
            //     "id" => $r['id'],
            //     "name" => $r['name'],
            //     "url" => $r['url_slug'],
            //     "venue" => $venue->getName()
            // ];
            $return[] = [
                "id" => $l->id,
                "name" => $l->name,
                "url" => $l->getUrl(),
                "venue" => $l->getVenueName(),
            ];
        }
        return $return;
    }

    function isLive() {
        /* Does this league have a currently Live season */
    }

    function isOpen() {
        /* Does this league have an "Open for Registration" season */
        /* Returns Season object */
        return Season::hasOFR($this);
    }

    /* Reference Data */
    static function Surfaces() {
        return [
            1 => ["id" => 1, "name" => "Indoor"],
            2 => ["id" => 2, "name" => "Outdoor"],
            3 => ["id" => 3, "name" => "Indoor/Outdoor"]
        ];
    }

    static function Statuses() {
        /* Status held at Season level */
        return [];
        return [
            0 => "Open for Registration",
            1 => "Join Waiting List",
            2 => "Closed for Registration"
        ];
    }

    static function Days() {
        return [
            "mo" => "Monday",
            "tu" => "Tuesday",
            "we" => "Wednesday",
            "th" => "Thursday",
            "fr" => "Friday",
            "sa" => "Saturday",
            "su" => "Sunday"
        ];
    }

    static function DefaultVenue(Int $venueID, $liveOnly = true) {
        $sql = "SELECT * FROM `leagues` WHERE `defaultVenueID` = :venueID";
        if ($liveOnly === true) $sql .= " AND `visible` = 1";
        $db = new Db($sql, ["venueID" => $venueID]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $league = new static();
            $league->Load($r);
            ($return) ? $return[] = $league : array_push($return, $league);
        }
        return $return;
    }
    /* Queries */
    // static function byVenue(Int $venueID) {
    //     $sql = "SELECT * FROM `leagues` WHERE `venueID`";
    // }
    static function byName(String $name) {
        $sql = "SELECT * FROM `leagues` WHERE `name` LIKE :name";
        $db = new Db($sql, ["name" => $name]);
        // var_dump($db->rows);
        return $db->rows;
    }

    static function Teams(League $league) {
        // $seasons = Season::forLeague($league);
        $season = Season::Current($league);
        // if ($seasons) {
        //     $season = array_shift($seasons);
        if ($season) return Team::forSeason($season);
        // }
    }

    static function Statements(League $league) {
        $teams = static::Teams($league);
        if (!$teams) return "No teams in League";
        $return = [];
        foreach ($teams as $team) {
            $return[$team->id] = Finance::Statement($team->id);
        }
        return $return;
    }

    static function byVenue(Venue $venue) {
        $sql = "SELECT * FROM `leagues` WHERE `defaultVenueID` = :venueID";
        $rlt = new Db($sql, ["venueID" => $venue->id]);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $l = new static();
            $l->Load($r);
            $return[] = $l;
        }
        return $return;
    }

    static function liveByVenue(Venue $venue) {
        $sql = "SELECT `leagues`.* FROM `leagues` LEFT JOIN `seasons` ON `leagues`.`id` = `seasons`.`leagueID` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `leagues`.`deleted` IS NULL AND `seasons`.`deleted` IS NULL AND `seasons`.`venueID` = :venueID AND `seasonStatus`.`active` = 1";
        $rlt = new Db($sql, ["venueID" => $venue->id]);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $l = new static();
            $l->Load($r);
            $return[$r['id']] = $l;
        }
        return $return;
    }

    static function liveByDivisionVenue(Venue $venue) {
        $sql = "SELECT DISTINCT `leagues`.* 
                FROM `leagues`
                WHERE `leagues`.`deleted` IS NULL
                AND EXISTS (
                    SELECT 1
                    FROM `seasons`
                    LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id`
                    WHERE `seasons`.`leagueID` = `leagues`.`id`
                    AND `seasons`.`deleted` IS NULL
                    AND ((`seasonStatus`.`live` = 1) OR (`seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL))
                    AND (
                        (`seasons`.`venueID` = :venueID AND EXISTS (
                            SELECT 1 FROM `venues` vs 
                            WHERE vs.`id` = `seasons`.`venueID` AND vs.`status` = 1 AND vs.`deleted` IS NULL
                        ))
                        OR EXISTS (
                            SELECT 1 FROM `divisions`
                            JOIN `venues` vd ON vd.`id` = `divisions`.`venueID`
                            WHERE `divisions`.`seasonID` = `seasons`.`id`
                            AND `divisions`.`deleted` IS NULL
                            AND `divisions`.`venueID` = :venueID
                            AND vd.`status` = 1 AND vd.`deleted` IS NULL
                        )
                        OR EXISTS (
                            SELECT 1 FROM `bookings`
                            JOIN `venues` vb ON vb.`id` = `bookings`.`venueID`
                            WHERE `bookings`.`leagueID` = `leagues`.`id`
                            AND `bookings`.`deleted` IS NULL
                            AND `bookings`.`venueID` = :venueID
                            AND vb.`status` = 1 AND vb.`deleted` IS NULL
                        )
                    )
                )";
        $rlt = new Db($sql, ["venueID" => $venue->id]);
        $return = [];
        if ($rlt->rows) {
            foreach ($rlt->rows as $row) {
                $league = new static();
                $league->Load($row);
                $return[] = $league;
            }
        }
        return $return;
    }

    static function registerableByVenue(Venue $venue) {
        $sql = "SELECT `leagues`.* FROM `leagues` LEFT JOIN `seasons` ON `leagues`.`id` = `seasons`.`leagueID` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `leagues`.`deleted` IS NULL AND `seasons`.`deleted` IS NULL AND `seasons`.`venueID` = :venueID AND `seasonStatus`.`active` = 1 AND seasonStatus.live IS NULL";
        $rlt = new Db($sql, ["venueID" => $venue->id]);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $l = new static();
            $l->Load($r);
            $return[$r['id']] = $l;
        }
        return $return;
    }

    static function SearchRegisterable(String $searchTerm) {
        $sql = "SELECT leagues.id as leagueID, leagues.name AS leagueName, venues.id AS venueID, venues.name AS venueName, venues.town AS venueTown, venues.postcode AS venuePostcode, seasons.id AS seasonID, seasons.name AS seasonName, seasons.launchDate, seasons.defaultDay, seasons.fixtureCharge, seasons.officialCharge FROM seasons LEFT JOIN leagues ON seasons.leagueID = leagues.id LEFT JOIN venues ON seasons.venueID = venues.id LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id WHERE leagues.deleted IS NULL AND seasons.deleted IS NULL AND venues.deleted IS NULL AND seasonStatus.active = 1 AND seasonStatus.live IS NULL AND leagues.name LIKE :searchTerm";
        $rlt = Database::Execute($sql, ["searchTerm" => "%$searchTerm%"]);
        return $rlt['success']['rows'] ?? [];
    }

    static function SearchRegisterableByLatLng(Float $lat, Float $lng, Float $distance = 25) {
        $sql = "SELECT leagues.id as leagueID, leagues.name AS leagueName, venues.id AS venueID, venues.name AS venueName, venues.town AS venueTown, venues.postcode AS venuePostcode, seasons.id AS seasonID, seasons.name AS seasonName, seasons.launchDate, seasons.defaultDay, seasons.fixtureCharge, seasons.officialCharge FROM seasons LEFT JOIN leagues ON seasons.leagueID = leagues.id LEFT JOIN venues ON seasons.venueID = venues.id LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id WHERE leagues.deleted IS NULL AND seasons.deleted IS NULL AND venues.deleted IS NULL AND seasonStatus.active = 1 AND seasonStatus.live IS NULL AND ST_Distance_Sphere(point(venues.`lng`, venues.`lat`), point(:lng, :lat)) <= (:distance * 1000 * 0.621371) ORDER BY ST_Distance_Sphere(point(venues.`lng`, venues.`lat`), point(:lng, :lat)) ASC";
        $rlt = Database::Execute($sql, [
            "lng" => $lng,
            "lat" => $lat,
            "distance" => $distance
        ]);
        return $rlt['success']['rows'] ?? [];
    }

    static function OpReport() {
        $sql = "SELECT * FROM `leagues` ORDER BY `name`";
        return static::Query($sql);
    }

    static function Open() {
        // $sql = "SELECT * FROM `leagues` WHERE `visible` = 1 AND (`status` IS NULL OR `status` = 0) ORDER BY `name` ASC";
        // $db = new Database($sql);
        // return $db->rows;        
        $sql = "SELECT leagues.* FROM leagues LEFT JOIN seasons ON leagues.id = seasons.leagueID WHERE seasons.openForRegistration = 1 ORDER BY `leagues`.`name`";
        $db = new Db($sql);
        return $db->rows;
    }

    static function Coordinator(User $user) {
        $sql = "SELECT `leagues`.* FROM `leagues` ";
        if (!$user->isManager) $sql .= " WHERE `coordinator` = {$user->id}";
        $sql .= " ORDER BY `name`";
        return static::Query($sql);
        // $db = new Db($sql);
        // $return = [];
        // if ($db->rows) {
        //     foreach ($db->rows as $r) {
        //         $l = new static();
        //         $l->Load($r);
        //         $return[] = $l;
        //     }
        // } 
        // return $return;
    }

    static function Report(array $data = []) {
        /* Required Inputs */
        if (!isset($data['startDate']) || !$data['startDate']) return "No Start Date";
        if (!isset($data['endDate']) || !$data['endDate']) return "No End Date";
        /* Intended Return
            Per League
            - Booking Utilisation
            - Profitability
        */
        $sql = "SELECT `leagues`.* FROM `leagues` WHERE ";
    }

    static function Search(String $name) {
        $sql = "SELECT * FROM `leagues` WHERE `name` LIKE '%:name%'";
        $rlt = Database::Execute($sql, ["name" => $name]);
        $return = [];
        if ($rlt['success']['rows']) {
            foreach ($rlt['success']['rows'] as $r) {
                $l = new static();
                $l->Load($r);
                $return[] = $l;
            }
        }
        return $return;
    }

    static function fromSlug(String $url_slug) {
        $sql = "SELECT * FROM leagues WHERE url_slug = :url_slug";
        $rlt = static::Query($sql, ["url_slug" => $url_slug]);
        return $rlt[0] ?? null;
    }

    // static function LocalRegisterableLeagues (String $postcode, int $distance = 20) {

    //     (ST_Distance_Sphere(point(`lng`, `lat`), point({$coords->lng}, {$coords->lat}))/1609.34)
    //     (ST_Distance_Sphere(point(`lng`, `lat`), point({$coords->lng}, {$coords->lat}))/1609.34)
    //     $sql = "SELECT `id`, `name`, `url_slug` FROM leagues LEFT JOIN seasons ON leagues.id = seasons.leagueID LEFT JOIN teamSeasons ON seasons.id = teamSeasons.seasonID LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id LEFT JOIN venues ON seasons.venueID = venues.id WHERE leagues.deleted IS NULL AND seasons.deleted IS NULL AND seasonStatus.active = 1 AND seasonStatus.live IS NULL ";
    // }
}
