<?php

class Finance  extends Base {
    
    protected $dbTable = "finances";
    protected $dbFields = ["ledger","type","category","taxDate","teamID","description","total","fixtureID","creditID"];
    protected $ledger; # Sales or Purchase
    protected $type; # Transaction or Payment
    protected $category; # Transaction : Invoice or Credit. Payment: Payment or Refund
    protected $taxDate;
    protected $description, $teamID;
    protected $total;
    protected $fixtureID;
    protected $creditID;
    protected $stripePaymentIntentID, $stripePaymentIntentStatus;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        if (!$this->ledger) $this->ledger = "S";
        if ($this->ledger=="S" && ($this->category == "C" || $this->category == "P")) $this->total = -abs($this->total);
        $rlt = parent::Save();
        if (User::AuthUser()) Logging::Add(__CLASS__ . " {$this->id} saved by " . User::AuthUser());
        return $rlt;
    }
    /* Getters */
    function getLedgerName() { return ($this->ledger=="P") ? "Purchase" : "Sales";}
    function getTypeName() {return ($this->type=="P") ? "Payment" : "Transaction";} 
    function getCategoryID() { return $this->category;}
    function getCategoryName() { 
        switch ($this->category) {
            case "I": return "Invoice";
            case "C": return "Credit";
            case "P": return "Payment";
            case "R": return "Refund";
        }
    }
    function getTaxDate(String $format = null) { return (!$format) ? $this->taxDate : date($format,strtotime($this->taxDate));}
    function getDescription() { return $this->description;}
    function getTeamID() { return $this->teamID;}
    function getTeam() { 
        $team = new Team($this->teamID);
        return $team->__toString();
    }
    function getTotal() { return $this->total;}
    function getStripePaymentIntentID() { return $this->stripePaymentIntentID;}
    function getStripePaymentIntentStatus() { return $this->stripePaymentIntentStatus;}
    function getCreditID() { return $this->creditID;}

    function setStripePaymentIntentID(String $stripePaymentIntentID) {
        $this->stripePaymentIntentID = $stripePaymentIntentID;
    }

    function setStripePaymentIntentStatus(String $stripePaymentIntentStatus) {
        $this->stripePaymentIntentID = $stripePaymentIntentStatus;
    }
    /* Statics */
    static function Transactions (Int $teamID = null) {
        $sql = "SELECT * FROM `finances` WHERE `ledger` = 'S' AND `type` = 'T' AND `deleted` IS NULL";
        if ($teamID) $sql .= " AND `teamID` = $teamID";
        $sql .= " ORDER BY `taxDate` ASC";
        $db = new Db($sql);
        if (!$db) return;
        $return = [];
        foreach ($db->rows as $r) {
            $f = new static();
            $f->Load($r);
            (!$return) ? $return[] = $f : array_push($return,$f);
        }
        return $return;
    }

    static function Payments (Int $teamID = null) {
        $sql = "SELECT * FROM `finances` WHERE `ledger` = 'S' AND `type` = 'P' AND `deleted` IS NULL";
        if ($teamID) $sql .= " AND `teamID` = $teamID";
        $sql .= " ORDER BY `taxDate` ASC";
        $db = new Db($sql);
        if (!$db) return;
        $return = [];
        foreach ($db->rows as $r) {
            $f = new static();
            $f->Load($r);
            (!$return) ? $return[] = $f : array_push($return,$f);
        }
        return $return;
    }

    static function Credits (Int $teamID = null) {
        $sql = "SELECT * FROM `finances` WHERE `ledger` = 'S' AND `type` = 'T' AND `category` = 'C' AND `deleted` IS NULL";
        if ($teamID) $sql .= " AND `teamID` = $teamID";
        $sql .= " ORDER BY `taxDate` ASC";
        $db = new Db($sql);
        if (!$db) return;
        $return = [];
        foreach ($db->rows as $r) {
            $f = new static();
            $f->Load($r);
            (!$return) ? $return[] = $f : array_push($return,$f);
        }
        return $return;
    }

    static function Statement (Int $teamID, $newestFirst = false) {
        $sql = "SELECT * FROM `finances` WHERE `ledger` = 'S' AND `teamID` = :teamID AND `taxDate` <= NOW() AND `deleted` IS NULL";
        $sql .= ($newestFirst === true) ? " ORDER BY `taxDate` DESC, `type`" : " ORDER BY `taxDate`, `type`";
        // echo $sql."<br>";
        $db = new Db($sql,["teamID" => $teamID]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $t = new static();
            $t->Load($r);
            (!$return) ? $return[] = $t : array_push($return,$t);
        }
        // Dump($return);
        return $return;
    }

    static function ApiStatement (Team $team) {
        $output = [];
        if (($statement=static::Statement($team->id,true))) {
            $output['total'] = 0 ;
            foreach ($statement as $s) {
                $output['items'][] = [
                    "date" => $s->taxDate,
                    "description" => $s->description,
                    "amount" => $s->total,
                ];
                $output['total'] += $s->total;
            }
        }
        return $output;
    }

    static function Create(Int $teamID, Float $total, String $description, String $taxDate, $category = "I", $ledger = "S", $type="T") {
        $transaction = new static();
        $transaction->teamID = $teamID;
        $transaction->total = $total;
        $transaction->description = $description;
        $transaction->taxDate = ($taxDate) ? $taxDate : date('Y-m-d');
        $transaction->category = $category;
        $transaction->ledger = $ledger;
        $transaction->type = $type;
        $transaction->Save();
        return $transaction;
    }

    static function RequiresPayment() {
        /**
         * WRONG
         * CALCULATE AMOUNT DUE UP TO NOW PER TEAM
         * THEN, RAISE A PI AS A PAYMENT
         */
        return;
        $sql = "SELECT * FROM `finances` WHERE `stripePaymentIntentID` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return;
        foreach ($db->rows as $r) {
            // Get Team
            $team = new Team($r['teamID']);
            $treasurer = new User($team->getTreasurer());
            $stripeCustomerID = $treasurer->stripeCustomerID();
            // echo "Transaction {$r['id']} for " . $team . " paid by " . $treasurer . " ($stripeCustomerID)<br>";
            if ($r['id']==1047) {
                $transaction = new static();
                $transaction->Load($r);
                $paymentIntent = Stripe::CreatePaymentIntent($stripeCustomerID,$r['total'],$r['description']);
                $transaction->setStripePaymentIntentID($paymentIntent->id);
                $transaction->setStripePaymentIntentStatus($paymentIntent->status);
                $transaction->Save();
            }
        }
    }

    static function Balance (Team $team) {
        $sql = "SELECT SUM(`total`) AS `balance` FROM `finances` WHERE `ledger` = 'S' AND `taxDate` <= NOW() AND `deleted` IS NULL AND `teamID` = :teamID GROUP BY `teamID`";
        $rlt = Database::Execute($sql,["teamID" => $team->id]);
        return $rlt['success']['rows'][0]['balance'] ?? null;
    }

    static function Balances (Int $teamID = null) {
        $sql = "SELECT `teamID`, SUM(`total`) AS `balance` FROM `finances` WHERE `ledger` = 'S' AND `taxDate` <= NOW() AND `deleted` IS NULL";
        if ($teamID) $sql .= " AND `teamID` = $teamID";
        $sql .= " GROUP BY `teamID` ORDER BY `balance` DESC";
        $db = new Db($sql); if (!$db->rows) return;
        if ($teamID) return $db->rows[0]['balance'];

        $return = [];
        foreach ($db->rows as $r) {
            $return[$r['teamID']] = $r['balance'];
        }
        $sql = "SELECT `id` FROM `teams` WHERE `id` NOT IN (SELECT DISTINCT `teamID` FROM `finances`)";
        $db = new Db($sql);
        if ($db->rows) {
            foreach ($db->rows as $r) {
                $return[$r['id']] = 0;
            }
        }
        return $return;
    }

    static function BalancesDue() {
        $sql = "SELECT `teamID`, SUM(`total`) AS `balance` FROM `finances` WHERE `ledger` = 'S' AND `taxDate` < NOW()  AND `deleted` IS NULL GROUP BY `teamID` ORDER BY `balance` DESC";
        $db = new Db($sql); 
        if ($db->errors) throw new Exception(implode(" ",$db->errors));
        if (!$db->rows) return;
        $return = array();
        foreach ($db->rows as $r) {
            if ($r['balance']<=0) continue;
            $return[$r['teamID']] = $r['balance'];
        }
        return $return;        
    }
    
    static function BalancePayments() {
        $log[] = "Running Balance Payments";
        $balances = static::Balances();
        if (!$balances) {
            $log[] = "Nothing to pay"; return $log;
        }
        $log[] = count($balances) . " to settle totalling " . array_sum($balances);
        foreach ($balances as $teamID => $total) {
            $team = new Team($teamID);
            if ($team->getLeagueID() != 90) continue;
            $log[] = "Processing $team for $total";
            if ($total <0) {
                $log[] = "Zero or below - skipping";
                continue;
            }
            $treasurer = new User($team->getTreasurer());
            if (!$treasurer) {
                $log[] = "Could not identify Treasurer";
                continue;
            }
            $log[] = "Treasurer is $treasurer";
            $stripeCustomerID = $treasurer->stripeCustomerID();
            if (!$stripeCustomerID) {
                $log[] = "Treasurer does not have a Stripe Reference - possibly because no card details have been submitted yet";
                continue;
            }
            if (!$team->paymentMethod()) {
                $log[] = "$team does not have a valid payment method to use.";
                continue;
            }
            $statement = static::Statement($teamID);
            if (!$statement) {
                $log[] = "A balance appears to be due but the Statement has no items";
                continue;
            }
            $sqlData = [
                "billed" => date('Y-m-d H:i:s'),
                "teamID" => $teamID
            ];
            $sql = "UPDATE `finances` SET `billed` = :billed WHERE `teamID` = :teamID AND `billed` IS NULL AND `taxDate` <= NOW()"; 
            $log[] = $sql;
            $db = new Db($sql,$sqlData);
            $sql = "SELECT `id`, `total` FROM `finances` WHERE `billed` = :billed AND `teamID` = :teamID AND `deleted` IS NULL";
            $rlt = new Db($sql,$sqlData);
            if (!$rlt->rows) {
                $log[] = "Encountered a problem setting, retrieving transactions to bill";
                continue;
            }
            // Create a new stripePayment
            $sp = new Db("INSERT INTO `stripePayments` (`id`) VALUES (DEFAULT)");
            $sql = "INSERT INTO `stripePaymentItems` SET `stripePaymentID` = :stripePaymentID, `transactionID` = :transactionID";
            $log[] = "New local Stripe Payment recorded as ID {$sp->lastInsertID}";
            $total = 0; $transactions = [];
            foreach ($rlt->rows as $r) {
                $log[] = "Including Transaction ID {$r['id']} for a total of {$r['total']}";
                $dbRlt = new Db($sql,["stripePaymentID" => $sp->lastInsertID,"transactionID" => $r['id']]);
                if ($dbRlt->errors) {
                    $log[] = array_merge($log, $dbRlt->errors);
                    return $log;
                }
                $total += $r['total'];
                $transactions[$r['id']] = $r['total'];
            }
            // Create Stripe Payment Intent
            $pi = Stripe::CreatePaymentIntent($stripeCustomerID,$total,$team->__toString(),$transactions);
            $log[] = "New Stripe Payment Intent created {$pi->id}";
            $sql = "UPDATE `stripePayments` SET `stripePaymentIntentID` = :stripePaymentIntentID, `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `stripePaymentMethod` = :stripePaymentMethod WHERE `id` = :id";
            new Db($sql,["stripePaymentIntentID" => $pi->id,"stripePaymentIntentStatus" => $pi->status, "stripePaymentMethod" => $team->paymentMethod(), "id" => $sp->lastInsertID]);
            $intent = Stripe::PaymentIntentPaymentMethod($pi->id,$team->paymentMethod());
            $log[] = "Applied Payment Method " . $team->paymentMethod();
            $sql = "UPDATE `stripePayments` SET `stripePaymentIntentStatus` = :stripePaymentIntentStatus WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
            new Db($sql,["stripePaymentIntentStatus" => $intent->status,"stripePaymentIntentID" => $pi->id]);
            $confirmIntent = Stripe::ConfirmIntent($pi->id);
            $log[] = "Confirmation of Purchase Intent resulted in {$confirmIntent->status}";
            $sql = "UPDATE `stripePayments` SET `stripePaymentIntentStatus` = :stripePaymentIntentStatus WHERE `id` = :id";
            new Db($sql,["stripePaymentIntentStatus" => $confirmIntent->status]);
        }
        return $log;
    }

    static function PaymentMethods() {
        $sql = "SELECT `id`, `stripePaymentIntentID` FROM `stripePayments` WHERE `stripePaymentIntentStatus` = 'requires_payment_method'";
        $db = new Db($sql);
        if (!$db->rows) return;
        foreach($db->rows as $r) {
            
        }
    }

    static function PaymentItems(String $stripePaymentIntent) {

    }

    static function StripePaymentStatus(String $pi, String $status) {
        $sql = "UPDATE `stripePayments` SET `stripePaymentIntentStatus` = :stripePaymentIntentStatus WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
        new Db($sql,["stripePaymentIntentStatus" => $status,"stripePaymentIntentID" => $pi]);
    }

    static function createCredit(Finance $transaction) {
        # Uses $transaction has basis for creating a matching Credit
        /* Returns STRING on failure, Finance Object on Success */
        if ($transaction->total <=0) return "No transaction value to credit";
        if ($transaction->creditID) return "Transaction already credited";
        // Tools::Dump($transaction);
        $credit = new static();
        $credit->ledger = "S";
        $credit->type = "T";
        $credit->category = "C";
        $credit->taxDate = date('Y-m-d');
        $credit->description = "Credit {$transaction->id} {$transaction->description}";
        $credit->teamID = $transaction->teamID;
        $credit->total = $transaction->total;
        $rlt = $credit->Save(); 
        if (is_string($rlt)) return $rlt;
        $transaction->creditID = $credit->id;
        $rlt = $transaction->Save();
        if (is_string($rlt)) return $rlt;
        // Tools::Dump($credit);
        return $credit;
    }

    static function SeasonTotalInPeriod (Season $season, String $startDate, String $endDate) {
        $sql = "SELECT `category`, SUM(`finances`.`total`) AS `total` FROM `finances` LEFT JOIN `teams` ON `finances`.`teamID` = `teams`.`id` WHERE `teams`.`seasonID` = {$season->id} AND `finances`.`created` >= '$startDate 00:00:00' AND `finances`.`created` <= '$endDate 23:59:59' AND `finances`.`deleted` IS NULL GROUP BY `finances`.`category`";
        // echo $sql."<br>";
        $db = new Db($sql);
        if ($db->errors) exit(implode(" ",$db->errors));
        if ($db->rows) {
            foreach ($db->rows as $r) $return[strtolower(substr($r['category'],0,1))] = abs($r['total']);
            // Tools::Dump($return);
            return $return;
        }
    }

    static function FullBalanceBilling() {
        // static function Unbilled (TeamSeason $teamSeason) {
        //     $sql = "
        //     SELECT `fixtures`.* FROM `schedule`
        //         LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
        //         LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
        //         LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
        //         WHERE `divisions`.`seasonID` = :seasonID
        //         AND `schedule`.`deleted` IS NULL
        //         AND `bookings`.`deleted` IS NULL
        //         AND ((`fixtures`.`home` = :teamID AND `fixtures`.homeTrans IS NULL) OR (`fixtures`.`away` = :teamID AND fixtures.awayTrans IS NULL))
        //         ORDER BY `bookings`.`startDate`, `schedule`.`startTime`";
        //     return Fixture::Query($sql, ["seasonID" => $teamSeason->seasonID, "teamID" => $teamSeason->teamID]);
        //     return $rlt;
        // }
    }

    static function Alerts() {
        $balances = Team::AgedDebtors(); $total = 0;
        foreach ($balances as $teamID => $financials) {
            $to = $cc = $message = [];
            if ($financials['balance'] <= 0) continue;
            $team = new Team($teamID);
            $pending = StripePayment::TeamPendingTotal($team);
            if ($pending) continue;
            $managers = $team->getTeamManagers();
            if (!$managers['captain'] && !$managers['treasurer']) continue;
            $team->getLeague();
            $team->getCoordinator();
            $hi = $managers['captain']->firstname;
            if ($managers['treasurer'] && $managers['treasurer'] == $managers['captain']) {
                $to = [$managers['treasurer']->email => $managers['treasurer']->__toString()];
                $hi = $managers['treasurer']->firstname;
            } elseif ($managers['treasurer']) {
                $to = [$managers['treasurer']->email => $managers['treasurer']->__toString()];
                $hi = $managers['treasurer']->firstname;
                $cc = [$managers['captain']->email => $managers['captain']->__toString()];
            } else $to = [$managers['captain']->email => $managers['captain']->__toString()];
            $cc[$team->coordinator->email] = $team->coordinator->__toString();
            $subject = "Action needed";
            $message[] = "Hi {$hi}";
            $message[] = "Your team has a balance that we are unable to charge for.";
            $message[] = "We have outlined the details below.";
            $message[] = "$team in {$team->league} currently owe {$financials['balance']} which we have been unable to bill.";
            $message[] = "Please log into the <a href=\"https://lockerroom.bloomnetball.co.uk/\">locker room</a> and head to the admin section. You'll need to review the payment details that you have entered.";
            $message[] = "If you have any questions or need further assistance, then please contact your local Bloom Netball coordinator {$team->coordinator} {$team->coordinator->email} <NAME_EMAIL>";
            $message[] = "Many Thanks";
            $message[] = "Team Bloom Netball";
            $return[] = [
                "to" => $to,
                "cc" => $cc ?? null,
                "subject" => $subject,
                "message" => $message
            ];
        }
        return $return;
    }

}