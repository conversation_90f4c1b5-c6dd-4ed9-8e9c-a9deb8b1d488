<?php

class Logging extends Base {

    protected $dbTable = "logging";
    protected $dbFields = ["message"];

    function __toString() {
        return "{$this->message}";
    }
    function getCreated($dateFormat = 'H:i:s d/m/Y') {
        return ($this->created) ? date($dateFormat, strtotime($this->created)) : null;
    }

    static function Add(String $message = null, Bool $isAdmin = false) {
        if (!$message) return;
        $sql = "INSERT INTO `logging` SET `message` = :message, `isAdmin` = :isAdmin";
        new Db($sql, ["message" => $message, "isAdmin" => (int)$isAdmin]);
    }
}
