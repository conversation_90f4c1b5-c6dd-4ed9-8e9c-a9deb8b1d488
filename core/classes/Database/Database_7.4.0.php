<?php

class Database {

    private $pdo, $dsn;
    private $statement;
    private $sql, $data;
    public $errors = [];
    public $result;
    public $rows = [];
    public $lastInsertID;
    public $affectedRows;
    public $params;
    protected $backupTables = [];
    public $logs = [];

    private static $instance = null;

    private function __construct($databaseName = null) {
        // if ($GLOBALS['config']['db']) {
            $dbsetup = json_decode(json_encode($GLOBALS['config']['db']));
        // } else {
            // $dbsetup = json_decode(json_encode([
            //     'database' => 'leagues4you',
            //     'hostname' => '127.0.0.1',
            //     'username' => 'leagues4you',
            //     'password' => '(S1mpl1f1c4t10n-Pr0gr4m!)',
            // ]));
        // }
        $pdoOptions = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_EMULATE_PREPARES => true
        ];
        $this->dsn = "mysql:dbname={$dbsetup->database};host={$dbsetup->hostname}";
        try {
            $this->pdo = new \PDO($this->dsn, $dbsetup->username, $dbsetup->password,$pdoOptions);
        } catch (Exception $e) {
            exit("Cannot progress " . $e->getMessage());
        }
    }

    static function Describe (String $tableName) {
        $sql = "DESCRIBE `$tableName`";
        $described = Database::Execute($sql);
        if (!$described) return;
        $return = [];
        foreach ($described as $d) $return[$d['Field']] = $d;
        return $return;
    }

    static function Upgrade() {
        $sqlRootFolder = APP_ROOT.DIRECTORY_SEPARATOR."Config".DIRECTORY_SEPARATOR."SQL".DIRECTORY_SEPARATOR;
        /* Tables - Return if not found */
        $jsonFile = $sqlRootFolder."sqlTables.json";
        if (!file_exists($jsonFile)) return "No SQL Tables JSON file $jsonFile";
        $data = json_decode(file_get_contents($jsonFile));
        if (!$data) return "No data located in JSON file $jsonFile";
        foreach ($data as $tableName => $d) {
            $existingFields = static::Describe($tableName);
            $sql = "ALTER TABLE `$tableName`"; $conn = null;
            foreach ($d as $fieldName => $field) {
                if (isset($field->key) && $field->key == "PRI") continue;
                if (array_key_exists($fieldName,$existingFields)) {
                    // Modify
                    $sql .= "$conn CHANGE COLUMN $fieldName $fieldName {$field->type}";
                } else {
                    // Add
                    $sql .= "$conn ADD COLUMN IF NOT EXISTS $fieldName {$field->type}";
                }
                if (isset($field->default) && $field->default) $sql .= " DEFAULT {$field->default}";
                if (isset($field->extra) && $field->extra) $sql .= " {$field->extra}";
                if (isset($field->key) && $field->key == "UNI") $sql .= " UNIQUE KEY";
                $conn = ",";
            }
            $rlt = Database::Execute($sql);
        }
    }

    static function Setup (String $tableName, Array $dbFields) {
        $sql = "CREATE TABLE IF NOT EXISTS `$tableName` ("; $conn = null;
        foreach ($dbFields as $fieldName => $field) {
            $sql .= $conn . "`$fieldName` {$field['type']}";
            if (isset($field['default']) && $field['default']) $sql .= " DEFAULT {$field['default']}";
            if (isset($field['extra']) && $field['extra']) $sql .= " {$field['extra']}";
            $conn = ",";
        }
        $sql .= ")";
        Tools::Dump(static::Execute($sql));
    }

    static function Backup (String $table = null) {
        $obj = new static();
        $dbsetup = json_decode(json_encode($GLOBALS['config']['db']));
        $backup_directory = __DIR__ . DIRECTORY_SEPARATOR . "backup";
        if (!file_exists($backup_directory)) {
            $oldmask = umask(0);
            mkdir($backup_directory,0775);
            umask($oldmask);
        } 
        $backup_folder = $backup_directory . DIRECTORY_SEPARATOR . date('Ymd');
        if (!file_exists($backup_folder)) {
            $oldmask = umask(0);
            mkdir($backup_folder,0775);
            umask($oldmask);
        }
            $data_tables = [
            'bookings','c2c_attendees','c2c_bookings',
            'divisions','finances','fixtures','leagues',
            'pi','pi_items','schedule','seasonStatus',
            'seasons','sports','standings','stripeCustomers',
            'stripePaymentIntents','stripePaymentItems',
            'stripePaymentMethods','stripePayments',
            'stripeSetupIntents','tasterAttendees',
            'tasterSession','teamFollowers','teamSeasons',
            'teams','transactionItems','transactionMain',
            'transactionTypes','users','venues'
        ];
        $structure_tables = ['jwt', 'logging'];
        foreach ($data_tables as $table) {
            $fileRoot = $backup_folder . DIRECTORY_SEPARATOR . "{$table}.sql";
            $return_var = NULL;
            $output = NULL;
            $mysqlCommand = "mysqldump -u {$dbsetup->username} -p'{$dbsetup->password}' {$dbsetup->database} $table > {$fileRoot}";
            // echo $mysqlCommand."<br>";
            exec($mysqlCommand, $output, $return_var);
            // echo "$output $return_var<br>";
        }
        foreach ($structure_tables as $table) {
            $fileRoot = $backup_folder . DIRECTORY_SEPARATOR . "{$table}.sql";
            $return_var = NULL;
            $output = NULL;
            $mysqlCommand = "mysqldump -u {$dbsetup->username} -p'{$dbsetup->password}' -d {$dbsetup->database} $table > {$fileRoot}";
            #echo $mysqlCommand;
            exec($mysqlCommand, $output, $return_var);
        }
        $zipFile = $backup_directory . DIRECTORY_SEPARATOR . "backup_leagues4you_".date('Ymd') . ".zip";
        $zip = new \ZipArchive;
        if ($zip->open($zipFile, \ZipArchive::CREATE) === TRUE) {
            $files = scandir($backup_folder);
            foreach ($files as $file) {
                if ($file == "." || $file == "..") continue;
                $zip->addFile($backup_folder. DIRECTORY_SEPARATOR .$file,basename($file));
            }
            $zip->close();
            $subject = "leagues4you Backup";
            $message[] = "Hello";
            $message[] = "Backup file attached here";
            $message[] = "Many thanks";
            $message[] = "leagues4you";
            Email::Issue($subject,$message,["<EMAIL>" => "A2Z Technology"],[],[],[$zipFile]);
        }
    }

    static function Schema (String $table = null) {
        global $config;
        if (!isset($config->system->production) || $config->system->production !== true) return;
        $obj = new static();
        $mysqlCommand = "(mysqldump -u{$config->db->username} -p'{$config->db->password}' --no-data --skip-comments {$config->db->database} > {$config->core->root}" . DIRECTORY_SEPARATOR . "core" . DIRECTORY_SEPARATOR . "Sql" . DIRECTORY_SEPARATOR . "schema.sql) 2>&1";
        exec($mysqlCommand, $output, $result);
    }

    static function Restore ($filename = null) {
        if (!$filename)return;
        $command = "mysql -u root -p[Paswordofthedatabase] < $filename"; 
        /*
        for FILE in *.sql; do mysql -u leagues4you -p'(S1mpl1f1c4t10n-Pr0gr4m!)' leagues4you < $FILE; done
        */
    }

    static function Execute (String $sql, Array $data = [],$databaseName = null) {
        if (!self::$instance) self::$instance = new static($databaseName);
        self::$instance->rows = [];
        self::$instance->affectedRows = self::$instance->lastInsertID = null;
        self::$instance->sql = trim($sql); 
        self::$instance->data = $data;
        $return = ["success" => [], "error" => []];
        try {
            self::$instance->statement = self::$instance->pdo->prepare(self::$instance->sql);
            self::$instance->statement->execute(self::$instance->data);
            switch (strstr(self::$instance->sql," ",true)) {
                case "INSERT": 
                    $return["success"]["lastInsertID"] = (int)self::$instance->pdo->lastInsertId();
                    break;
                case "UPDATE":
                case "DELETE":
                    $return["success"]["rowCount"] = (int)self::$instance->statement->rowCount();
                    break;
                case "SELECT":
                case "DESCRIBE":
                case "SHOW":
                    $rows = [];
                    while ($row = self::$instance->statement->fetch(PDO::FETCH_ASSOC)) {
                        $rows[] = $row;
                    }
                    $return["success"]["rows"] = (array)$rows;
                    break;
            }
        } catch (\PDOException $e) {
            $return["error"]["code"] = $e->getCode();
            $return["error"]["message"] = (string)$e->getMessage();
            $return["error"]["trace"] = (array)$e->getTrace();
        }
        return $return;
    }

    static function Check ($config = null) {
        try {
            $db = self::$instance = new static($config);
            return true;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    static function TableInit (String $tablename = null) {
        if (!$tablename) return "No table name";
        $sql = "CREATE TABLE `$tablename` (`id` int(10) unsigned primary key auto_increment, `created` datetime default current_timestamp(), `updated` datetime default current_timestamp() on update current_timestamp(), `deleted` datetime)";
        return Database::Execute($sql);
    }

    /* CRUD */
    static function Insert (String $sql, Array $data = []) : Int {
        if (!self::$instance) self::$instance = new static($databaseName);
        self::$instance->statement = self::$instance->pdo->prepare($sql);
        self::$instance->statement->execute($data);
        return (int)self::$instance->pdo->lastInsertId();
    }
    static function Update (String $sql, Array $data = []) : Int {
        if (!self::$instance) self::$instance = new static($databaseName);
        self::$instance->statement = self::$instance->pdo->prepare($sql);
        self::$instance->statement->execute($data);
        return (int)self::$instance->statement->rowCount();
    }
    static function Select (String $sql, Array $data = []) : Array {
        if (!self::$instance) self::$instance = new static($databaseName);
        self::$instance->statement = self::$instance->pdo->prepare($sql);
        self::$instance->statement->execute($data);
        $rows = [];
        while ($row = self::$instance->statement->fetch(PDO::FETCH_ASSOC)) {
            $rows[] = $row;
        }
        return $rows;
    }


}