<?php

class Venue extends Base {

    protected $dbTable = "venues";
    protected $dbOrder = ["name" => "ASC"];
    protected $dbFields = ["name", "address1", "address2", "town", "postcode", "facility_freeParking", "facility_paidParking", "facility_changingRooms", "facility_showers", "facility_cafe", "facility_bar", "lat", "lng", "sageAccount", "notes", "purchaseTerms", "coordinatorID", "status"];

    protected $name;
    protected $coordinatorID;
    protected $address1, $address2, $town, $postcode;
    protected $facility_freeParking, $facility_paidParking;
    protected $facility_changingRooms, $facility_showers;
    protected $facility_cafe, $facility_bar;
    protected $lat, $lng;
    protected $notes;
    protected $venueID;
    protected $sageAccount, $purchaseTerms;
    protected $status;

    protected $distance;

    protected $defaultLeagues; # Populated by getDefaultLeagues();
    protected $coordinator;

    public $nearby = []; # Populated by fetchNearby()
    protected $leagues;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function __toString() {
        return "{$this->name}";
    }

    function Save() {
        if (!$this->lat || !$this->lng) {
            $this->queryLatLng();
            Trigger::Set("System::BuildLiveLeaguesApi");
        }
        parent::Save();
    }

    function queryLatLng() {
        if (!$this->postcode) return;
        $url = "https://postcodes.io/postcodes/" . rawurlencode($this->postcode);
        $rlt = file_get_contents($url);
        if (!$rlt) return;
        $data = json_decode($rlt, true);
        if (!isset($data['result'])) return;
        $this->lat = $data['result']['latitude'];
        $this->lng = $data['result']['longitude'];
    }


    function getName() {
        return $this->name;
    }
    function getAddress1() {
        return $this->address1;
    }
    function getAddress2() {
        return $this->address2;
    }
    function getTown() {
        return $this->town;
    }
    function getPostcode() {
        return $this->postcode;
    }
    function getLat() {
        return $this->lat;
    }
    function getLng() {
        return $this->lng;
    }
    function facilityAvailable(String $facility) {
        return $this->$facility;
    }
    function getStatus() {
        return $this->status;
    }

    function fetchNearby($kilometres = 30) {
        $this->nearby = [];
        $sql = "SELECT `venue1`,`venue2`,`distance` FROM `venueDistances` WHERE (`venue1` = {$this->id} OR `venue2` = {$this->id}) AND `distance` <= $kilometres ORDER BY `distance` ASC";
        $rlt = new Db($sql);
        if ($rlt->rows) {
            foreach ($rlt->rows as $r) {
                if ($r['venue1'] != $this->id) {
                    $this->nearby[$r['venue1']] = $r['distance'];
                }
                if ($r['venue2'] != $this->id) {
                    $this->nearby[$r['venue2']] = $r['distance'];
                }
            }
        }
    }

    function getCoordinator() {
        if (!$this->coordinatorID) return null;
        if (!$this->coordinator && $this->coordinatorID) $this->coordinatorID = new User($this->coordinatorID);
        return ['email' => $this->coordinatorID->email, 'fullName' => "{$this->coordinatorID->firstname} {$this->coordinatorID->lastname}"];
    }

    function getDefaultLeagues($liveOnly = true) {
        return League::DefaultVenue($this->id, $liveOnly);
    }

    function getFaciities() {
        $return = [];
        if ($this->facility_freeParking == 1) $return[] = ["name" => "Free Parking", "icon" => "fas fa-parking"];
        if ($this->facility_paidParking == 1) $return[] = ["name" => "Paid Parking", "icon" => "fas fa-parking"];
        if ($this->facility_changingRooms == 1) $return[] = ["name" => "Changing Rooms", "icon" => "fas fa-person-booth"];
        if ($this->facility_showers == 1) $return[] = ["name" => "Showers", "icon" => "fas fa-shower"];
        if ($this->facility_cafe == 1) $return[] = ["name" => "Free Parking", "icon" => "fas fa-coffee"];
        if ($this->facility_bar == 1) $return[] = ["name" => "Free Parking", "icon" => "fas fa-glass-cheers"];
        return $return;
    }

    function ApiOutput(Int $sportID = null) {
        global $config;
        $base = [
            "id" => $this->id,
            "name" => $this->name,
            "address1" => $this->address1,
            "address2" => $this->address2,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "coordinatorID" => $this->coordinatorID,
            "coordinates" => ["lat" => $this->lat, "lng" => $this->lng],
            "facilities" => $this->getFaciities(),
            "leagues" => []
        ];
        // if ($this->defaultLeagues) {
        foreach ($this->getDefaultLeagues() as $league) {
            $return[] = $league;
            continue;
            if ($sportID && $sportID != $league->getSportID()) continue;
            $leagueOutput = $league->ApiOutput();
            if ($leagueOutput) {
                if (!isset($return)) $return = $base;
                $return["leagues"][$league->id] = $leagueOutput;
            }
        }
        // }
        return (isset($return)) ? $return : $base;
    }

    function getRiskAssesments() {

        $data = [];
        $sql = "SELECT r.*,u.firstname,u.lastname FROM `risk_assesments` r INNER JOIN `users` u ON `r`.`user_id` = `u`.`id` 
        WHERE `venue_id` = :venue_id AND `r`.`deleted` IS NULL";
        $params = [':venue_id' => $this->id];
        $rlt = Database::execute($sql, $params);

        if (isset($rlt['success']['rows']) && $rlt['success']['rows']) {
            foreach ($rlt['success']['rows'] as $r) {
                $st = new \RiskAssesment($r['id']);
                $st->firstname = $r['firstname'];
                $st->lastname = $r['lastname'];
                $data[] = $st;
            }
        }
        return $data;
    }

    function ApiData() {
        // This used by C2C and liveLeagues API
        $leagues = $this->getLeagues();
        $leagueOutput = [];
        if ($leagues) {
            foreach ($leagues as $l) $leagueOutput[] = $l->ApiData();
        }
        // if (!$leagueOutput) return;
        return [
            "id" => $this->id,
            "coordinatorID" => $this->coordinatorID,
            "coordinator" => $this->getCoordinator(),
            "name" => $this->name,
            "sageAccount" => $this->sageAccount,
            "address1" => $this->address1,
            "address2" => $this->address2,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "coordinates" => ["lat" => $this->lat, "lng" => $this->lng],
            "distance" => ($this->distance) ? $this->distance / 1000 : $this->distance,
            "facilities" => $this->getFaciities(),
            "leagues" => $leagueOutput,
        ];
    }

    function ApiBasic() {
        $tasterOutput = [];
        if ($tasters = TasterSession::Venue($this, true)) {
            foreach ($tasters as $taster) $tasterOutput[] = $taster->ApiBasic();
        }
        return [
            "id" => $this->id,
            "coordinatorID" => $this->coordinatorID,
            // "coordinator" => ($this->getCoordinator()) ? $this->getCoordinator()->ProfileBasic() : null,
            "name" => $this->name,
            "line1" => $this->address1,
            "address1" => $this->address1,
            "address2" => $this->address2,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "lat" => $this->lat,
            "lng" => $this->lng,
            "notes" => $this->notes,
            "sageAccount" => $this->sageAccount,
            "purchaseTerms" => $this->purchaseTerms,
            "coordinates" => ["lat" => $this->lat, "lng" => $this->lng],
            "tasters" => $tasterOutput,
            "facilities" => $this->getFaciities(),
            "status" => $this->status ?? 0,
        ];
    }

    function getLeagues($liveOnly = true) {
        // if (!$this->leagues) $this->leagues = League::byVenue($this);
        // if ($this->leagues && $liveOnly === true) {
        //     foreach ($this->leagues as $k => $l) {
        //         if ($l->deleted || $l->visible) unset($this->leagues[$k]);
        //     }
        // }
        if (!$this->leagues) $this->leagues = League::liveByDivisionVenue($this);
        return $this->leagues;
    }

    function getRegisterableLeagues() {
        return League::registerableByVenue($this);
        if (!$this->leagues) $this->leagues = League::liveByVenue($this);
        return $this->leagues;
    }

    static function CalculateDistances() {
        $venues = static::Listing();
        $geo = new GeoDistance();
        if (!$venues) return "No venues to calculate";
        $sql = "INSERT INTO `venueDistances` SET `venue1` = :venue1, `venue2` = :venue2, `distance` = :distance ON DUPLICATE KEY UPDATE `distance` = :distance";
        foreach ($venues as $venue1) {
            if (!$venue1->getLat() || !$venue1->getLng()) continue;
            foreach ($venues as $venue2) {
                if ($venue1->id == $venue2->id) continue;
                if (!$venue2->getLat() || !$venue2->getLng()) continue;
                $geo->startPoint($venue1->getLat(), $venue1->getLng());
                $geo->endPoint($venue2->getLat(), $venue2->getLng());
                $distance = round($geo->directDistance());
                $rlt = new Db($sql, ["venue1" => $venue1->id, "venue2" => $venue2->id, "distance" => $distance]);
            }
        }
        return "Calculated distances between " . count($venues) . " venues";
    }

    static function Proximates(Float $lat, Float $lng, Int $kilometres = 50) {
        $sql = "SELECT *, ST_Distance_Sphere(point(`lng`, `lat`), point($lng, $lat)) AS `distance` FROM `venues` WHERE `lat` IS NOT NULL AND `lng` IS NOT NULL AND `deleted` IS NULL AND ST_Distance_Sphere(point(`lng`, `lat`), point($lng, $lat)) < " . ($kilometres * 1000) . " ORDER BY `distance`";
        // echo $sql;
        return static::Query($sql);
    }

    static function Api(Int $sportID = null) {
        $venues = static::Listing();
        if (!$venues) return;
        $return = [];
        foreach ($venues as $venue) {
            // $venue = new static();
            // $venue->Load($row);
            $venue->getDefaultLeagues(true);
            if (!$venue->defaultLeagues) continue;
            $output = $venue->ApiOutput($sportID);
            if ($output) $return[] = $output;
        }
        return $return;
    }

    static function ApiListing() {
        // Get Venues
        $venues = static::Listing();
        if (!$venues) return;
        $return = [];
        foreach ($venues as $venue) {
            if ($venue->deleted) continue;
            $leagues = $venue->getLeagues();
            // if (!is_array($leagues) || count($leagues) == 0) continue;
            // echo $venue." has " . count($leagues) . " leagues<br>";
            if (!$leagues) continue;
            // echo $venue."<br>";
            // Tools::Dump($leagues);
            // echo "<hr>";
            // if ($leagues) {
            $rlt = $venue->ApiData();
            // if (!$rlt) continue;
            $return[] = $rlt;
            // $leagueData = [];
            // foreach ($leagues as $league) {
            //     $leagueData[] = $league->ApiData();
            // }
            // $return[$venue->id]['leagues'] = $leagueData;
            // } 
            // $leagues = League::liveByVenue($venue);
            // if ($leagues) {
            //     foreach ($leagues as $league) {
            //         if (!$league->visible) continue;
            //         $apiData['leagues'][] = $league->ApiData();
            //         $return[] = $apiData;
            //     }
            // }
        }
        return $return;
    }

    static function SeasonOptions(Season $season) {
        $venueIDs = Booking::SeasonVenues($season);
        $return = [];
        if ($venueIDs) {
            foreach ($venueIDs as $venueID) $return[] = new static($venueID);
        }
        return $return;
    }

    static function liveSeasonLeagueNameVenueSearch(String $leagueSearchTerm) {
        /* This returns data to the Public Api for League Searches */
        $sql = "SELECT DISTINCT `venues`.* FROM `venues` LEFT JOIN `seasons` ON `venues`.`id` = `seasons`.`venueID` LEFT JOIN `leagues` ON `seasons`.`leagueID` = `leagues`.`id` LEFT JOIN `seasonStatus` ON `seasons`.`statusID` = `seasonStatus`.`id` WHERE `seasonStatus`.`active` = 1 AND `seasonStatus`.`live` IS NULL AND `leagues`.`name` LIKE :leagueSearchTerm";
        $rlt = Database::Execute($sql, ["leagueSearchTerm" => "%$leagueSearchTerm%"]);
        $return = [];
        if ($rlt['success']['rows']) {
            foreach ($rlt['success']['rows'] as $r) {
                $v = new static();
                $v->Load($r);
                $return[] = $v;
            }
        }
        return $return;
    }

    static function SearchForRegistrationLeagues(String $leagueSearchTerm) {
        $sql = "SELECT venues.* FROM seasons LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id LEFT JOIN venues ON seasons.venueID = venues.id LEFT JOIN leagues ON seasons.leagueID = leagues.id WHERE seasons.deleted IS NULL AND venues.deleted IS NULL AND seasonStatus.active = 1 AND seasonStatus.live IS NULL AND `leagues`.`name` LIKE :leagueSearchTerm";
        return static::Query($sql, ["leagueSearchTerm" => "%$leagueSearchTerm%"]);
    }

    static function WebsiteList() {
        $sql = "SELECT venues.id AS venueID, venues.name AS venueName, venues.lat, venues.lng, venues.address1, venues.town, venues.postcode, sports.id as sportID, sports.name AS sportName, sports.webImage AS imgUrl, leagues.name AS leagueName, leagues.url_slug AS leageUrl, seasons.name,  seasons.defaultDay, seasonStatus.name AS seasonStatus FROM venues LEFT JOIN seasons ON venues.id = seasons.venueID LEFT JOIN leagues ON seasons.leagueID = leagues.id LEFT JOIN sports ON leagues.sportID = sports.id LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id WHERE seasonStatus.active = 1 ORDER BY venues.name";
        $rlt = Database::Execute($sql);
        return ($rlt['success']['rows']) ? $rlt['success']['rows'] : $rlt['error']['message'];
    }
    static function VenueWithActiveSeason() {
        $sql = "SELECT DISTINCT venues.id, venues.name FROM venues JOIN seasons ON seasons.venueID = venues.id LEFT JOIN seasonStatus ON seasons.statusID = seasonStatus.id AND seasonStatus.active = 1 WHERE venues.status = 1 ORDER BY venues.name ASC;";

        return static::Query($sql);
    }

    static function ActiveVenues() {
        $sql = "SELECT * FROM `venues` WHERE `status` = 1 AND `deleted` IS NULL ORDER BY `name` ASC";
        return static::Query($sql);
    }
}
