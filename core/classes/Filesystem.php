<?php

class Filesystem {


    static function CheckFolder (String $folder) {
        /* Add trailing slash if not present */
        if (substr($folder,0,1)=="/") $folder = substr($folder,1);
        if (substr($folder,-1)=="/") $folder = substr($folder,0,strlen($folder)-1);
        $elements = explode("/",$folder);
        $folderToCheck = null;
        foreach($elements as $element) {
            $folderToCheck .= "/".$element;
            if ($element == "..") continue;
            if (!file_exists($folderToCheck)) {
                $old = umask(0);
                mkdir($folderToCheck,0775);
                umask($old);
            } 
        }
        return "$folderToCheck/";
    }
}