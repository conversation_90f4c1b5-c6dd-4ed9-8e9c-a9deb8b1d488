<?php

class Address extends Base {

    protected $dbTable = "addresses";
    protected $userID;
    protected $line1, $line2, $town, $postcode;  
    protected $dbFields = ["userID", "line1", "line2", "town", "postcode"];

    function __toString() { 
        return trim("{$this->line1}" . " {$this->town}" . " {$this->postcode}");
    }

    function Save() {
        /* Updates invoke DELETE before CREATE */
        if (!$this->id) {
            $this->deleted = date('Y-m-d H:i:s');
            parent::Save();
            $this->id = $this->deleted = null;
        }
        parent::Save();
    }

    
    static function CreateUpdate (Int $userID, Array $address = []) {
        if (!isset($address['postcode']) || !$address['postcode']) return "Missing Postcode";
        $sql = "INSERT IGNORE INTO `addresses` SET `userID` = :userID, `postcode` = :postcode";
        return new Db($sql,["userID" => $userID, "postcode" => $address['postcode']]);
    }

}