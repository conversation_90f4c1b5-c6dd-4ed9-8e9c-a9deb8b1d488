<?php
/**
 * See https://openweathermap.org/api/one-call-api
 */
class Weather {

    protected $apiKey = "0296cbcecf4d6d32abdf602a595c669e";
    protected $endPoint = "http://api.openweathermap.org/data/2.5/onecall?units=metric";
    protected $iconUrl = "http://openweathermap.org/img/wn/<EMAIL>";
    public $json;
    protected $folder = "/var/www/html/hub/app/logs/weather/";
    protected $file;

    function __construct(Float $lat, Float $lng) {
        $this->lat = $lat;
        $this->lng = $lng;
        $this->folder .= date('Y-m-d')."/";
        \Tools\CheckFolder($this->folder);
        $this->file = $lat.$lng.".json";
        $this->Fetch();
    }
    function Fetch() {
        return;
        if (file_exists($this->folder.$this->file)) {
            $this->json = file_get_contents($this->folder.$this->file);
        } else {
            $this->json = file_get_contents($this->endPoint."&lat={$this->lat}&lon={$this->lng}&appid={$this->apiKey}");
            file_put_contents($this->folder.$this->file,$this->json);
        }
    }

} 