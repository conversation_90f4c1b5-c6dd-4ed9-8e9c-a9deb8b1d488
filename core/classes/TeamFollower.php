<?php

class TeamFollower extends Base {

    protected $teamID, $userID;
    protected $isCaptain, $isTreasurer;
    protected $treasurerAccepted, $treasurerInviter;
    protected $stripePaymentMethodID;
    protected $notes;
    protected $payInFull;

    protected $user;
    protected $team;
    protected $inviter;

    function getUser() {
        return $this->user ?? ($this->user = new User($this->userID));
    }

    function getTeam() {
        return $this->team ?? ($this->team = new Team($this->teamID));
    }

    function getInviter() {
        return $this->inviter ?? ($this->inviter = new User($this->treasurerInviter));
    }

    function TreasurerInvite_ApiData() {
        $this->getTeam();
        $this->getInviter();
        return [
            "id" => $this->id,
            "teamID" => $this->teamID,
            "team" => $this->team->ApiOutput() ?? null,
            "userID" => $this->userID,
            "inviterID" => $this->treasurerInviter,
            "inviter" => $this->inviter->ApiOutput() ?? null,
            "isTreasurer" => $this->isTreasurer,
            "treasurerInviter" => $this->treasurerInviter,
            "treasurerAccepted" => $this->treasurerAccepted,
        ];
    }

    static function Team(User $user) {
    }

    static function Add(Team $team, User $user, String $stripePaymentIntentID = null, Bool $isCaptain = false, Bool $isTreasurer = false, $payInFull = false) {
        $sql = "INSERT INTO `teamFollowers` SET `userID` = :userID, `teamID` = :teamID, `isCaptain` = :isCaptain, `isTreasurer` = :isTreasurer, notes = 'New Team Registration', payInFull = :payInFull";
        return Database::Execute($sql, ["userID" => $user->id, "teamID" => $team->id, "isCaptain" => $isCaptain, "isTreasurer" => $isTreasurer, "payInFull" => ($payInFull === true) ? 1 : null]);
    }

    static function PaymentMethod(Team $team, User $user, String $stripePaymentMethodID) {
        # is there currently a listing for this team/user?
        $current = static::Current($team, $user);
        # If there is a listing, archive it for a new card addition
        if ($current) Database::Execute("UPDATE `teamFollowers` SET deleted = NOW() WHERE teamID = :teamID AND userID = :userID", ["teamID" => $team->id, "userID" => $user->id]);
        # Check if this used to be a captain, if so, re-use the setting, otherwise not.
        $isCaptain = ($current && $current->isCaptain == 1) ? 1 : null;
        $treasurerAccepted = ($current && $current->treasurerAccepted) ? $current->treasurerAccepted : null;
        $sql = "INSERT INTO `teamFollowers` SET userID = :userID, teamID = :teamID, isCaptain = :isCaptain, isTreasurer = :isTreasurer, treasurerAccepted = :treasurerAccepted, stripePaymentMethodID = :stripePaymentMethodID, notes = 'Payment Card Added', payInFull = :payInFull";

        return Database::Execute($sql, [
            "userID" => $user->id,
            "teamID" => $team->id,
            "isCaptain" => $isCaptain,
            "isTreasurer" => 1,
            "treasurerAccepted" => $treasurerAccepted,
            "stripePaymentMethodID" => $stripePaymentMethodID,
            "payInFull" => ($payInFull === true) ? 1 : null
        ]);
    }

    static function CancelPaymentMethod(String $stripePaymentMethodID) {
        # Any TeamFollower records with this PaymentMethodID?
        $sql = "SELECT * FROM `teamFollowers` WHERE stripePaymentMethodID = :stripePaymentMethodID AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["stripePaymentMethodID" => $stripePaymentMethodID]);
        if (!$rlt['success']['rows']) return; # Nope
        Database::Execute("UPDATE `teamFollowers` SET deleted = NOW() WHERE deleted IS NULL AND stripePaymentMethodID = :stripePaymentMethodID", ["stripePaymentMethodID" => $stripePaymentMethodID]);
        # Recreate records without PaymentMethodID and a note explaining
        foreach ($rlt['success']['rows'] as $r) {
            $sql = "INSERT INTO `teamFollowers` SET userID = :userID, teamID = :teamID, isCaptain = :isCaptain, isTreasurer = :isTreasurer, treasurerAccepted = :treasurerAccepted, payInFull = null, notes = :notes";
            Database::Execute($sql, [
                "userID" => $r['userID'],
                "teamID" => $r['teamID'],
                "isCaptain" => $r['isCaptain'],
                "isTreasurer" => 1,
                "treasurerAccepted" => $r['treasurerAccepted'],
                "notes" => "Payment Card $stripePaymentMethodID removed",
            ]);
        }
        # Mark records with the PaymentMethodID as deleted
        $sql = "UPDATE `teamFollowers` SET deleted = NOW() WHERE stripePaymentMethodID = :stripePaymentMethodID AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["stripePaymentMethodID" => $stripePaymentMethodID]);
    }

    static function Current(Team $team, User $user = null) {
        $data = ['teamID' => $team->id];
        $sql = "SELECT * FROM teamFollowers WHERE teamID = {$team->id} AND deleted IS NULL AND (isCaptain = 1 OR (isTreasurer = 1 AND treasurerAccepted IS NOT NULL))";
        if ($user) {
            $sql .= " AND userID = {$user->id}";
            $data['userID'] = $user->id;
        }
        $sql .= " ORDER BY isCaptain IS NOT NULL";
        $rlt = static::Query($sql);
        if (!$rlt) return [];
        return ($user) ? $rlt[0] : $rlt;
    }

    static function Treasurer(Team $team) {
        $data = ['teamID' => $team->id];
        $sql = "SELECT userID FROM teamFollowers WHERE teamID = {$team->id} AND deleted IS NULL AND `isTreasurer` = 1 AND treasurerAccepted IS NOT NULL";
        $rlt = Database::Execute($sql, $data);
        return (isset($rlt['success']['rows'][0]['userID'])) ? new User($rlt['success']['rows'][0]['userID']) : null;
    }

    static function Payer(Team $team) {
        $data = ['teamID' => $team->id];
        $sql = "SELECT userID FROM teamFollowers WHERE teamID = :teamID AND deleted IS NULL AND ((`isTreasurer` = 1 AND treasurerAccepted IS NOT NULL) OR `isCaptain` = 1) ORDER BY isTreasurer IS NULL";
        $rlt = Database::Execute($sql, $data);
        if ($rlt['success'] && $rlt['success']['rows']) return new User($rlt['success']['rows'][0]['userID']);
    }

    static function InviteCaptain(Team $team, String $email, Bool $supressAlertEmail = false) {
        if (!$team->id) return ["error" => "Invalid team"];
        $user = User::EmailLookup($email);
        if (!$user) $user = User::Create(["email" => $email]);
        # Fetch current captain record
        $sql = "SELECT * FROM teamFollowers WHERE teamID = :teamID AND isCaptain = 1 AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["teamID" => $team->id]);
        $isTreasurer = 1; # Assume Captain is Treasurer, unless ...
        if (isset($rlt['success']['rows']) && $rlt['success']['rows']) {
            $teamFollowerRecord = $rlt['success']['rows'][0];
            $currentCaptainID = $teamFollowerRecord['userID'];
            $currentCaptain = new User($currentCaptainID);
            if ($currentCaptain->id == $user->id) return ["error" => "$user is already the team captain"];
            # If current Captain is NOT marked as Treasurer, preserve this state of affairs.
            # Basically, set the isTreasurer value to whatever already exists for the current captain
            $isTreasurer = $teamFollowerRecord['isTreasurer'];
            # Now, mark this Captaincy record as archived [ using deleted = NOW() ] and add a note to that effect
            $sql = "UPDATE teamFollowers SET deleted = NOW(), notes = 'Captain Resignation' WHERE id = :id";
            Database::Execute($sql, ["id" => $teamFollowerRecord['id']]);
            # Current Captain Resignation Email
            if ($supressAlertEmail !== true) $currentCaptain->CaptainResignationConfirm($team);
        }
        # Create new Captaincy record
        $sql = "INSERT INTO teamFollowers SET userID = :userID, teamID = :teamID, isCaptain = 1, isTreasurer = :isTreasurer, notes = :notes";
        $rlt = Database::Execute($sql, [
            "userID" => $user->id,
            "teamID" => $team->id,
            "isTreasurer" => $isTreasurer,
            "notes" => 'Captain Invitation'
        ]);
        if ($supressAlertEmail !== true) $user->CaptainInvitation($team);
        return $rlt;
    }

    static function InviteCaptain2(Team $team, User $user, Bool $supressAlertEmail = false) {
        # Fetch current captain record
        $sql = "SELECT * FROM teamFollowers WHERE teamID = :teamID AND isCaptain = 1 AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["teamID" => $team->id]);
        $isTreasurer = 1; # Assume Captain is Treasurer, unless ...
        if (isset($rlt['success']['rows']) && $rlt['success']['rows']) {
            $teamFollowerRecord = $rlt['success']['rows'][0];
            $currentCaptainID = $teamFollowerRecord['userID'];
            $currentCaptain = new User($currentCaptainID);
            if ($currentCaptain->id == $user->id) throw new Exception("$user is already the team captain");
            # If current Captain is NOT marked as Treasurer, preserve this state of affairs.
            # Basically, set the isTreasurer value to whatever already exists for the current captain
            $isTreasurer = $teamFollowerRecord['isTreasurer'];
            # Now, mark this Captaincy record as archived [ using deleted = NOW() ] and add a note to that effect
            $sql = "UPDATE teamFollowers SET deleted = NOW(), notes = 'Captain Resignation' WHERE id = :id";
            Database::Execute($sql, ["id" => $teamFollowerRecord['id']]);
            # Current Captain Resignation Email
            if ($supressAlertEmail !== true) $currentCaptain->CaptainResignationConfirm($team);
        }
        # Create new Captaincy record
        $sql = "INSERT INTO teamFollowers SET userID = :userID, teamID = :teamID, isCaptain = 1, isTreasurer = :isTreasurer, notes = :notes";
        $rlt = Database::Execute($sql, [
            "userID" => $user->id,
            "teamID" => $team->id,
            "isTreasurer" => $isTreasurer,
            "notes" => 'Captain Invitation'
        ]);
        if ($supressAlertEmail !== true) $user->CaptainInvitation($team);
        return $rlt;
    }

    static function Invitation(Team $team, User $user, Int $appointID = 1, Bool $supress = false) {
        /*
            1 = Captain & Treasurer
            2 = Captain
            3 = Treasurer
        */
        switch ($appointID) {
            case 1: # Captain & Treasurer
            default:
                $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE (isCaptain = 1 OR isTreasurer = 1) AND deleted IS NULL AND teamID = :teamID";
                $rlt = Database::Execute($sql, ["teamID" => $team->id]);
                if ($rlt['error']) return $rlt['error']['message'];
                // $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE isCaptain = 1 AND deleted IS NULL AND teamID = :teamID"; Database::Execute($sql,["teamID" => $teamID]);
                $sql = "INSERT INTO teamFollowers SET teamID = :teamID, userID = :userID, isCaptain = 1, notes = :notes";
                $rlt = Database::Execute($sql, ["teamID" => $team->id, "userID" => $user->id, "notes" => 'Captain & Treasurer Invitation']);
                if ($rlt['error']) return $rlt['error']['message'];
                if ($supress !== true) $rlt = $user->CaptainInvitation($team);
                break;
                // case 2: # Treasurer
                //     $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE isTreasurer = 1 AND isCaptain <> 1 AND deleted IS NULL AND teamID = :teamID"; Database::Execute($sql,["teamID" => $teamID]);
                //     $sql = "INSERT INTO teamFollowers SET teamID = :teamID, userID = :userID, isTreasurer = 1"; Database::Execute($sql,["teamID" => $teamID, "userID" => $user->id]);
                //     if ($supress !== true) $user->TreasurerInvitation($team);
                //     break;
                // case 3: # Captain and Treasurer
                //     $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE (isCaptain = 1 OR isTreasurer = 1) AND deleted IS NULL AND teamID = :teamID"; Database::Execute($sql,["teamID" => $teamID]);
                //     $sql = "INSERT INTO teamFollowers SET teamID = :teamID, userID = :userID, isCaptain = 1, isTreasurer = 1"; Database::Execute($sql,["teamID" => $teamID, "userID" => $user->id]);
                //     if ($supress !== true) $user->CaptainInvitation($team);
                //     break;
        }
        return [
            'teamID' => $team->id,
            'userID' => $user->id,
            'appointID' => $appointID,
            'isSuppressed' => $supress,
        ];
    }

    static function InviteTreasurer(String $email, Team $team, User $inviter = null, Bool $supressAlertEmail = false) {
        $user = User::EmailLookup($email);
        if (!$user) $user = User::Create(["email" => $email]);
        if (!$inviter) $inviter = $team->getTeamCaptain();
        $sql = "INSERT INTO teamFollowers SET userID = :userID, teamID = :teamID, isTreasurer = 1, treasurerInviter = :treasurerInviter, notes = 'Treasurer Invitation'";
        $rlt = Database::Execute($sql, ["userID" => $user->id, "teamID" => $team->id, "treasurerInviter" => $inviter->id]);
        $inviteID = $rlt['success']['lastInsertID'] ?? null;
        if ($supressAlertEmail !== true && $inviteID) {
            $coordinator = $team->getCoordinator();
            # Delete any unaccepted Treasurer Invites for this Team
            $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE teamID = :teamID AND deleted IS NULL AND isTreasurer = 1 AND isCaptain IS NULL AND treasurerAccepted IS NULL AND id <> $inviteID";
            Database::Execute($sql, ["teamID" => $team->id]);
            $subject = "Invitation to be a Treasurer";
            $message[] = "Hi {$user->firstname}";
            $message[] = "$inviter has invited you to become the Treasurer for $team";
            $message[] = "To confirm this, please logon to the LockerRoom at https://lockerroom.bloomnetball.co.uk";
            $message[] = "Many thanks";
            $message[] = "Bloom Netball";
            $message[] = "e: <EMAIL>";
            $message[] = "Invite #{$inviteID}";
            $to[$user->email] = $user->__toString();
            $cc = $bcc = [];
            if ($coordinator) {
                $cc = [$coordinator->email => $coordinator->__toString()];
            }
            Email::Issue($subject, $message, $to, $cc, $bcc);
        }
        return $inviteID;
    }

    static function TreasurerInvites(User $user) {
        $sql = "SELECT * FROM teamFollowers WHERE userID = :userID AND isTreasurer = 1 AND isCaptain IS NULL AND treasurerAccepted IS NULL AND deleted IS NULL";
        $rlt = static::Query($sql, ["userID" => $user->id]);
        return $rlt;
    }

    static function AcceptTreasurerInvite(Int $inviteID, User $user) {
        $sql = "UPDATE teamFollowers SET treasurerAccepted = NOW(), notes = CONCAT_WS('. ', `notes`,'Accepted') WHERE id = :id AND userID = :userID AND treasurerAccepted IS NULL AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["id" => $inviteID, "userID" => $user->id]);
        if ($rlt['success']['rowCount'] == 1) {
            # Any current Treasurer must be resigned
            $sql = "SELECT `teamID` FROM teamFollowers WHERE id = :id";
            $rlt2 = Database::Execute($sql, ["id" => $inviteID]);
            if ($rlt2['success']['rows']) {
                $team = new Team($rlt2['success']['rows'][0]['teamID']);
                static::TreasurerResign($team, $inviteID);
            }
            return true;
        } else return $rlt;
    }

    static function DeclineTreasurerInvite(Int $inviteID, User $user) {
        $sql = "UPDATE teamFollowers SET deleted = NOW(), notes = CONCAT_WS('. ', `notes`,'Declined') WHERE id = :id AND userID = :userID AND treasurerAccepted IS NULL AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["id" => $inviteID, "userID" => $user->id]);
        return ($rlt['success']['rowCount'] == 1) ? true : $rlt;
    }

    static function TreasurerRemove(Team $team) {
        Database::Execute("UPDATE teamFollowers SET deleted = NOW() WHERE deleted IS NULL AND isTreasurer = 1 AND teamID = {$team->id}");
    }

    static function TreasurerResign(Team $team, Int $excludeID) {
        $sql = "SELECT * FROM teamFollowers WHERE teamID = :teamID AND isTreasurer = 1 AND deleted IS NULL AND id <> :id";
        $rlt = Database::Execute($sql, ["teamID" => $team->id, "id" => $excludeID]);
        if ($rlt['success']['rows']) {
            foreach ($rlt['success']['rows'] as $r) {
                $sql = "INSERT INTO `teamFollowers` SET userID = :userID, teamID = :teamID, isCaptain = :isCaptain, stripePaymentMethodID = NULL, notes = 'Treasurer Resignation'";
                $return[1] = Database::Execute($sql, ["userID" => $r['userID'], "teamID" => $r['teamID'], "isCaptain" => $r['isCaptain']]);
                $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE id = :id";
                $return[2] = Database::Execute($sql, ["id" => $r['id']]);
            }
        }
        $return[3] = $rlt;
        return $return;
    }

    static function Captain(Team $team) {
        $sql = "SELECT userID FROM teamFollowers WHERE teamID = :teamID AND deleted IS NULL AND isCaptain = 1";
        $rlt = Database::Execute($sql, ["teamID" => $team->id]);
        return ($rlt['success']['rows']) ? $rlt['success']['rows'][0]['userID'] : null;
    }

    static function Managers(Team $team) {
        $sql = "SELECT users.* FROM users LEFT JOIN teamFollowers ON users.id = .teamFollowers.userID WHERE teamFollowers.teamID = :teamID AND teamFollowers.deleted IS NULL AND users.deleted IS NULL AND (teamFollowers.isCaptain = 1 OR teamFollowers.isTreasurer = 1)";
        // return Database::Execute($sql,["teamID" => $team->id]);
        return User::Query($sql, ["teamID" => $team->id]);
    }

    static function isManager(Team $team, User $user) {
        $sql = "SELECT users.* FROM users LEFT JOIN teamFollowers ON users.id = .teamFollowers.userID WHERE teamFollowers.teamID = :teamID AND teamFollowers.deleted IS NULL AND users.deleted IS NULL AND (teamFollowers.isCaptain = 1 OR teamFollowers.isTreasurer = 1) AND teamFollowers.userID = :userID";
        $rlt = Database::Execute($sql, ["teamID" => $team->id, "userID" => $user->id]);
        return ($rlt['success']['rows']) ? true : false;
    }

    static function isFollower(Team $team, User $user) {
        $sql = "SELECT users.* FROM users LEFT JOIN teamFollowers ON users.id = .teamFollowers.userID WHERE teamFollowers.teamID = :teamID AND teamFollowers.deleted IS NULL AND users.deleted IS NULL AND teamFollowers.userID = :userID";
        $rlt = Database::Execute($sql, ["teamID" => $team->id, "userID" => $user->id]);
        return ($rlt['success']['rows']) ? true : false;
    }

    static function Follow(Team $team, User $user) {
        if (!static::isFollower($team, $user)) {
            $sql = "INSERT INTO teamFollowers SET userID = :userID, teamID = :teamID, notes = 'Followed'";
            $rlt = Database::Execute($sql, ["userID" => $user->id, "teamID" => $team->id]);
            return ($rlt['error']) ? ["error" => $rlt['error']['message']] : ["success" => $rlt['success']['lastInsertID']];
        } else return ["warning" => "{$user} already following {$team}"];
    }

    static function UnFollow(Team $team, User $user) {
        if (static::isFollower($team, $user)) {
            $sql = "UPDATE teamFollowers SET deleted = NOW(), `notes` = CONCAT_WS('.',`notes`,' Unfollowed') WHERE userID = :userID AND teamID = :teamID AND deleted IS NULL";
            $rlt = Database::Execute($sql, ["userID" => $user->id, "teamID" => $team->id]);
            return ($rlt['error']) ? ["error" => $rlt['error']['message']] : ["success" => $rlt['success']['rowCount']];
        } else return ["warning" => "{$user} not following {$team}"];
    }

    static function PaymentOption(Team $team) {
        $data = ["teamID" => $team->id];
        $sql = "SELECT payInFull FROM teamFollowers WHERE teamID = :teamID AND deleted IS NULL AND stripePaymentMethodID IS NOT NULL";
        $rlt = static::Query($sql, $data);
        return $rlt[0]->payInFull ?? null;
    }

    static function TogglePayInFull(Team $team) {
        $data = ["teamID" => $team->id];
        $sql = "SELECT * FROM teamFollowers WHERE teamID = :teamID AND deleted IS NULL AND stripePaymentMethodID IS NOT NULL";
        $rlt = static::Query($sql, $data);
        if (!$rlt) return;
        $notes = ($rlt[0]->payInFull == 1) ? "Switching to Fixture Pay Option" : "Switching to Season Pay Option";
        Database::Execute("UPDATE teamFollowers SET deleted = NOW(), notes = '$notes' WHERE id = {$rlt[0]->id}", $data);

        $notes = ($rlt[0]->payInFull == 1) ? "Switched to Fixture Payment" : "Switched to Full Season Payment";
        $sql = "INSERT INTO teamFollowers SET userID = :userID, teamID = :teamID, isCaptain = :isCaptain, isTreasurer = :isTreasurer, treasurerAccepted = :treasurerAccepted, stripePaymentMethodID = :stripePaymentMethodID, payInFull = :payInFull, notes = :notes";
        $payInFull = ($rlt[0]->payInFull == 1) ? null : 1;
        $data = [
            "userID" => $rlt[0]->userID,
            "teamID" => $team->id,
            "isCaptain" => $rlt[0]->isCaptain,
            "isTreasurer" => 1,
            "treasurerAccepted" => $rlt[0]->treasurerAccepted,
            "stripePaymentMethodID" => $rlt[0]->stripePaymentMethodID,
            "payInFull" => $payInFull,
            "notes" => $notes,
        ];
        $newRlt = Database::Execute($sql, $data);
        if (isset($newRlt["success"]["lastInsertID"])) {
            $sql = "UPDATE teamFollowers SET deleted = NOW() WHERE deleted IS NULL AND teamID = :teamID AND userID = :userID AND id <> :newID";
            Database::Execute($sql, [
                "teamID" => $team->id,
                "userID" => $rlt[0]->userID,
                "newID" => $newRlt["success"]["lastInsertID"]
            ]);
        }
        return $payInFull;
    }

    static function PayingInFull(Team $team) {
        $sql = "SELECT `payInFull` FROM `teamFollowers` WHERE `teamID` = :teamID AND deleted IS NULL ORDER BY isTreasurer DESC LIMIT 1";
        $rlt = Database::Execute($sql, ["teamID" => $team->id]);
        return ($rlt['success']['rows'] && $rlt['success']['rows'][0]['payInFull'] == 1) ? 1 : null;
    }

    static function Invite(Int $inviteID) {
        $sql = "SELECT * FROM teamFollowers WHERE id = $inviteID";
        $rlt = Database::Execute($sql);
        return $rlt['success']['rows'][0] ?? null;
    }

    static function Migratev2(TeamSeason $teamSeason) {
        $team = $teamSeason->getTeam();
        $sql = "INSERT INTO `teamFollowers` (userID, teamID, isCaptain, isTreasurer,treasurerInviter, treasurerAccepted,stripePaymentMethodID) VALUES (:userID, :teamID, :isCaptain, :isTreasurer, :treasurerInviter, :treasurerAccepted,:stripePaymentMethodID)";
        $sqlDelete = "UPDATE `teamFollowers` SET deleted = NOW() WHERE userID = :userID AND isCaptain IS NULL AND isTreasurer IS NULL";
        # Captain
        $userID = $teamSeason->captainID;
        $treasurerID = $teamSeason->treasurerID;
        $teamID = $teamSeason->teamID;
        $isCaptain = 1;
        $isTreasurer = null;
        $treasurerAccepted = $treasurerInviter = null;
        if (!$teamSeason->treasurerID || $teamSeason->captainID == $teamSeason->treasurerID) {
            $isTreasurer = 1;
            $treasurerInviter = null;
            $treasurerAccepted = null;
            $stripePaymentMethodID = $teamSeason->team->treasurerStripePaymentMethodID;
        } else {
            $isTreasurer = null;
            $stripePaymentMethodID = null;
        }
        $return['captain'] = Database::Execute($sql, [
            "userID" => $userID,
            "teamID" => $teamID,
            "isCaptain" => $isCaptain,
            "isTreasurer" => $isTreasurer,
            "treasurerInviter" => $treasurerInviter,
            "treasurerAccepted" => $treasurerAccepted,
            "stripePaymentMethodID" => $stripePaymentMethodID,
        ]);
        Database::Execute($sqlDelete, ["userID" => $userID]);
        # Treasurer
        if ($teamSeason->treasurerID && $teamSeason->captainID != $teamSeason->treasurerID) {
            $userID = $teamSeason->treasurerID;
            $teamID = $teamSeason->teamID;
            $isCaptain = null;
            $isTreasurer = 1;
            $treasurerInviter = $teamSeason->captainID;
            $treasurerAccepted = date('Y-m-d H:i:s');
            $stripePaymentMethodID = $teamSeason->team->treasurerStripePaymentMethodID;
            $return['treasurer'] = Database::Execute($sql, [
                "userID" => $userID,
                "teamID" => $teamID,
                "isCaptain" => $isCaptain,
                "isTreasurer" => $isTreasurer,
                "treasurerInviter" => $treasurerInviter,
                "treasurerAccepted" => $treasurerAccepted,
                "stripePaymentMethodID" => $stripePaymentMethodID,
            ]);
            Database::Execute($sqlDelete, ["userID" => $userID]);
        } else $return['treasurer'] = null;
        Database::Execute("UPDATE teams SET lockerRoomVersion = 2 WHERE id = {$teamSeason->teamID}");
        return $return;
    }

    static function PaymentSource(Team $team) {
        $sql = "SELECT * FROM teamFollowers WHERE teamID = {$team->id} AND deleted IS NULL AND stripePaymentMethodID IS NOT NULL"; #echo $sql . "<br>";
        $rlt = static::Query($sql);
        return $rlt[0] ?? null;
    }
}
