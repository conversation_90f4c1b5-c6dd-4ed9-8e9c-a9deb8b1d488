<?php

class Division extends Base {

    protected $dbTable = "divisions";
    protected $dbFields = ["seasonID", "name","rounds","venueID"];

    protected $seasonID;
    protected $name;
    protected $rounds;
    protected $venueID;

    protected $season;

    public $table;

    function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() { return "{$this->name}";}
    function Save() {
        $rlt = parent::Save();
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }
    function getSeasonID() { return $this->seasonID;}
    function getSeason() {
        if (!$this->season) $this->season = new Season($this->seasonID);
        return $this->season;
    }
    function getLeagueID() {
        $season = $this->getSeason(); 
        return $season->getLeagueID();
    }
    function getDuration () { return 40;}
    function getPrice () { return 27;}
    function getMatchOfficialFee() { return 5;}
    function Standings() {
        $this->table = Standing::Division($this->id);
    }

    function ApiData() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            // "teams" => TeamSeason::Division($this),
        ];
    }

    function ApiOutput() {
        $table = Standing::Division($this->id);
        $teams = [];
        foreach ($table as $k => $v) {
            $team = new Team($v['teamID']);
            $teams[] = $team;
            $table[$k]['team'] = $team->__toString();
        }
        $teamData = [];
        foreach ($teams as $t) $teamData[] = $t->ApiOutput(); 
        return [
            "id" => $this->id,
            "name" => $this->name,
            "teams" => $teamData,
            "schedule" => Schedule::forDivision($this,true),
            "results" => Fixture::getResults($this),
            "table" => $table
        ];
    }

    static function SeasonTotal (Season $season) {
        if (!$season->id) return;
        $sql = "SELECT COUNT(`id`) AS `total` FROM `divisions` WHERE `seasonID` = :seasonID AND `deleted` IS NULL";
        $db = new Db($sql,["seasonID" => $season->id]);
        return (isset($db->rows[0]['total'])) ? $db->rows[0]['total'] : 0;
    }

    static function forSeason (Season $season) {
        if (!$season->id) return;
        $sql = "SELECT * FROM `divisions` WHERE `seasonID` = :seasonID AND `deleted` IS NULL ORDER BY `name`";
        $db = new Db($sql,["seasonID" => $season->id]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $d = new static();
            $d->Load($r);
            $return[] = $d;
        } 
        return $return;
    }

    static function Season (Season $season) {
        $sql = "SELECT * FROM divisions WHERE seasonID = :seasonID AND deleted IS NULL";
        return static::Query($sql,["seasonID" => $season->id]);
    }

    static function Remove(Int $divisionID) {
        // Division cannot be deleted if teams are still allocated
        $teams = Team::byDivision($divisionID,false);
        if ($teams) {
            $m = "Division cannot be deleted. It still contains " . count($teams);
            $m .= (count($teams) > 1) ? " teams" : " team";
            return $m;
        } 
        $db = new Db("UPDATE `divisions` SET `deleted` = NOW() WHERE `id` = $divisionID");
        if ($db->affectedRows) return true;
        // At least one division must remain
    }

    static function DefaultSeason (Int $seasonID) {
        $sql = "SELECT `id` FROM `divisions` WHERE `deleted` IS NULL AND `seasonID` = :seasonID ORDER BY `created` DESC LIMIT 1";
        $db = new Database($sql, ["seasonID" => $seasonID]);
        if ($db->rows) return $db->rows[0]['id']; 
    }

    static function Default (Season $season) {
        $divisions = static::forSeason($season);
        if (!$divisions) {
            $return = new static();
            $return->seasonID = $season->id;
            $return->name = "Division 1";
            $return->Save();
        } else $return = array_pop($divisions);
        return $return->id;
    }
    
    static function SeasonDefault (Season $season) {
        $divisions = static::forSeason($season);
        if (!$divisions) {
            $return = new static();
            $return->seasonID = $season->id;
            $return->name = "Division 1";
            $return->Save();
        } else $return = array_pop($divisions);
        return $return;
    }

}