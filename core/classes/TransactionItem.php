<?php

class TransactionItem extends Core\Base {

    protected $dbTable = "transactionItems", $dbKey = "id";
    protected $dbFields = ["main","teamID","fixtureID","qty","unit","total","description"];

    protected $id;
    protected $main;
    protected $teamID, $fixtureID;
    protected $qty, $unit, $total;
    protected $description;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function __toString() {
        return "Team: " . new Team($this->team) . ". Fixture: " . new Fixture($this->fixture) . " @ {$this->total}";
    }

    function Save() {
        parent::Save();
    }

    static function Totals (Int $transactionID) {
        $sql = "SELECT SUM(`qty`*`unit`) AS `total` FROM `transactionItems` WHERE `main` = $transactionID";
        $rlt = \Datastore\Query($sql);
        if ($rlt && is_array($rlt)) return $rlt[0]['total'];
    }

    static function Raise (Int $main, Int $teamID, Int $fixtureID, Float $amount ) {
        $fixture = new Fixture($fixtureID);
        $sql = "INSERT INTO `transactionItems` SET `main` = $main, `teamID` = $teamID, `fixtureID` = $fixtureID, `description` = '".$fixture->__toString()."', `qty` = 1, `unit` = $amount, `total` = $amount ON DUPLICATE KEY UPDATE `description` = '".$fixture->__toString()."', `total` = $amount";
    }

    static function isBilled (Int $fixtureID, Int $teamID) {
        $sql = "SELECT `transactionMain`.`id` FROM `transactionMain`, `transactionItems` WHERE `transactionMain`.`id` = `transactionItems`.`main` AND `transactionItems`.`fixtureID` = :fixtureID AND `transactionItems`.`teamID` = :teamID AND `transactionMain`.`issue` IS NOT NULL";
        $rlt = \Datastore\Query($sql,[
            "fixtureID" => $fixtureID,
            "teamID" => $teamID
        ]);
        if ($rlt && is_array($rlt) && isset($rlt[0]['id'])) return $rlt[0]['id'];
    }

    static function FixtureFees (Fixture $fixture) {
        $home = new Team($fixture->home);
        $away = new Team($fixture->away);
        $booking = new Booking($fixture->bookingID);
        $sqlData = [
            "fixture" => $fixture->id,
            "unit" => $fixture->total,
            "total" => $fixture->total,
            "qty" => 1,
            "description" => $fixture  . " : " .  date('jS F Y',strtotime($booking->date))
        ];
        $sql = "INSERT INTO `transactionItems` SET `main` = :main, `qty` = :qty, `description` = :description, `team` = :team, `fixture` = :fixture, `unit` = :unit, `total` = :total ON DUPLICATE KEY UPDATE `unit` = :unit, `total` = :total";
        /* If this team/fixture combo already exists against a CLOSED invoice - do not update */
        if (($homeTransID = static::isBilled((int)$fixture->id,(int)$home->id))) {
            Message::Set("$home already invoiced for this fixture on Invoice $homeTransID");
        } elseif ($home->captain && (($homeTransaction=Transaction::Raise($home->captain)) ) ) {
            $sqlData['team'] = $fixture->home; $sqlData['main'] = $homeTransaction->id;
            \Datastore\Insert($sql, $sqlData);
            $transactionHome->Save(); // Has effect of recalculating and saving total
        } else \Messaging\Add("Could not create a billing transaction for $home");

        if (($awayTransID = static::isBilled((int)$fixture->id,(int)$away->id))) {
            \Messaging\Set("$away already invoiced for this fixture on Invoice $awayTransID");
        } elseif ($away->captain && (( $awayTransaction=Transaction::Raise($away->captain)))) {
            $sqlData['team'] = $fixture->away; $sqlData['main'] = $awayTransaction->id;
            \Datastore\Insert($sql, $sqlData);
            $awayTransaction->Save(); // Has effect of recalculating and saving total
        } else \Messaging\Add("Could not create a billing transaction for $away");
    }

    static function byTransaction (Int $transactionID) {
        $sql = "SELECT `id` FROM `transactionItems` WHERE `main` = $transactionID";
        $rlt = \Datastore\Query($sql);
        if (!$rlt || !is_array($rlt) || count($rlt)==0) return;
        $return = [];
        foreach ($rlt as $r) $return[$r['id']] = new static($r['id']);
        return $return;
    }

}