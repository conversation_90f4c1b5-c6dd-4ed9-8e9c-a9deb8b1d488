<?php

class RiskAssesment extends Base {

    protected $dbTable = "risk_assesments";
    protected $dbFields = ['venue_id', 'date_of_assessment', 'data', 'status', 'created_at', 'updated_at', 'coordinatorID', 'coordinator', 'deleted', 'user_id'];

    protected $coordinatorID;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function getCoordinator() {
        if (!$this->coordinatorID) return;
        if (!$this->coordinator) $this->coordinator = new User($this->coordinatorID);
        return $this->coordinator;
    }

    function getUser() {
        if (!$this->user_id) return;
        if (!$this->user) $this->user = new User($this->user_id);
        return $this->user;
    }

    function getVenue() {
        if (!$this->venue_id) return;
        if (!$this->venue) $this->venue = new Venue($this->venue_id);
        return $this->venue;
    }

    public static function fetchAll() {
        $sql = "
        SELECT 
    venue.name AS venue,
    CONCAT(c.firstname, ' ', c.lastname) AS coordinator,
    CASE 
        WHEN risk.status = 2 THEN 'Yes' 
        ELSE 'No' 
    END AS Risk_Assessment_Published,
    risk.data AS Risk,
    risk.date_of_assessment AS Assessment_Publish_Date,
    DATE_ADD(risk.date_of_assessment, INTERVAL 1 YEAR) AS Upcoming_Review_Date,
    CASE 
        WHEN DATE_ADD(risk.date_of_assessment, INTERVAL 1 YEAR) < NOW() THEN 'Expired'
        WHEN risk.status = 1 THEN 'Draft'
        WHEN risk.status = 2 THEN 'Published'
        WHEN risk.status = 3 THEN 'Archived'
        ELSE 'N/A'
    END AS Risk_Status
FROM 
    venues AS venue
LEFT JOIN 
    risk_assesments AS risk ON venue.id = risk.venue_id AND risk.deleted IS NULL
LEFT JOIN 
    users AS c ON c.id = venue.coordinatorID
WHERE 
    (risk.deleted IS NULL OR risk.deleted = 0)  
    OR risk.id IS NULL;
 ";

        return Database::execute($sql);
    }

    public function getStatus() {
        if ($this->isExpired()) return "Expired";
        if ($this->status == 1) return "Draft";
        if ($this->status == 2) return "Published";
        if ($this->status == 3) return "Archived";
    }

    public function isExpired() {
        $date = new DateTime($this->date_of_assessment);
        $date->add(new DateInterval('P1Y'));
        return $date < new DateTime();
    }


    public function reviewDate() {
        $date = new DateTime($this->date_of_assessment);
        $date->add(new DateInterval('P1Y'));
        return $date->format('d-m-Y');
    }

    public function getDateOfAssessment() {
        return date('d-m-Y', strtotime($this->date_of_assessment));
    }

    function Save() {
        $rlt = parent::Save();
        return $rlt;
    }
}
