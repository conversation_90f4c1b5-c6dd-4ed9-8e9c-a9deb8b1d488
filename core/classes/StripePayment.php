<?php

class StripePayment extends Base {

    protected $dbTable = "stripePayments";

    protected $dbFields = ["alerted","stripePaymentIntentID", "stripePaymentIntentStatus","stripePaymentMethod","teamID","total","transactionID","failText"];

    protected $alerted;
    protected $stripePaymentIntentID, $stripePaymentIntentStatus, $stripePaymentMethod, $teamID, $total, $transactionID, $failText;

    protected $team;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function __toString() { return $this->name;}

    function Tabular() {
        $team = $this->getTeam();
        return [
            "stripePaymentIntentID" => $this->stripePaymentIntentID,
            "stripePaymentIntentStatus" => $this->stripePaymentIntentStatus,
            "stripePaymentMethod" => $this->stripePaymentMethod,
            "team" => $this->team,
            "total" => $this->total,
            "transactionID" => $this->transactionID,
        ];
    }

    function Check() {
        $intent = Stripe::getPaymentIntent($this->stripePaymentIntentID);
        if ($this->stripePaymentIntentStatus != $intent->status) return "Status should be {$intent->status}";
        if ($intent->payment_method != $this->stripePaymentMethod) return "Payment Method should be {$intent->payment_method}";
        if ($intent->amount != $this->total * 100) return "Payment Amount should be {$intent->amount}";
    }

    /* Getters */
    function Synchronise($verbose = false) {
        $intent = Stripe::getPaymentIntent($this->stripePaymentIntentID);
        if ($verbose === true) echo "StripePayment {$this->id} / {$this->stripePaymentIntentID}<br>";
        if ($verbose === true) echo "Current Local Status:{$this->stripePaymentIntentStatus} vs Actual Status: {$intent->status}<br>";
        if ($this->stripePaymentIntentStatus != $intent->status || ($intent->payment_method && $this->stripePaymentMethod != $intent->payment_method) || $intent->amount != $this->total * 100 || strtotime($this->created) != $intent->created) {
            if ($this->stripePaymentIntentStatus != $intent->status) {
                if ($verbose === true) echo "changed from {$this->stripePaymentIntentStatus} to {$intent->status}<br>";
                Logging::Add("Synchronisation changed Status of Stripe Payment ID {$this->stripePaymentIntentID} from {$this->stripePaymentIntentStatus} to {$intent->status}");
                $this->stripePaymentIntentStatus = $intent->status;
            }
            if ($intent->payment_method && $intent->payment_method != $this->stripePaymentMethod) {
                Logging::Add("Synchronisation changed Payment Method of Stripe Payment ID {$this->stripePaymentIntentID} from {$this->stripePaymentMethod} to {$intent->payment_method}");
                if ($verbose === true) echo "changed from {$this->stripePaymentMethod} to {$intent->payment_method}<br>";
                $this->stripePaymentMethod = $intent->payment_method;
            }
            if ($intent->amount != $this->total * 100) {
                Logging::Add("Synchronisation changed Payment Total of Stripe Payment ID {$this->stripePaymentIntentID} from {$this->total} to " . ($intent->amount / 100));
                if ($verbose === true) echo "changed from {$this->total} to " . ($intent->amount/100) . "<br>";
                $this->total = $intent->amount/100;
            }
            $this->Save();
            
        } elseif ($verbose === true)  echo "No changes";
        if ($verbose === true) echo "<br>";
    }

    function getTeam() {
        if (!$this->team && $this->teamID) $this->team = new Team($this->teamID);
        return $this->team;
    }

    function getTeamID() { return $this->teamID;}
    function getTotal() { return $this->total;}
    function getStripePaymentIntentStatus() { return $this->stripePaymentIntentStatus;}
    function getStripePaymentIntentID() { return $this->stripePaymentIntentID;}
    function getFailText() { return $this->failText;}
    /* Setters */
    /* Do-ers */

    function Cancel() {
        try {
            $rlt = Stripe::CancelPaymentIntent($this->stripePaymentIntentID);
            if ($rlt->status) {
                $this->stripePaymentIntentStatus = $rlt->status;
                $this->Save();
            }
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    function newTotal(Float $total) {
        try {
            $rlt = Stripe::updatePaymentIntentTotal($this->stripePaymentIntentID,$total);
            if ($rlt->status) {
                $this->stripePaymentIntentStatus = $rlt->status;
                $this->total = $total;
                $this->Save();
                return true;
            } else return $this->failText;
        } catch (Exception $e) {
            return $e->getMessage();
        }
        $this->Save();
    }

    function emailContentRequiresAction (User $user, Team $team) {
        return [
            'subject' => 'Action Required - ' . $team->name,
            'message' => [
                "Hi {$user->firstname}",
                "Unfortunately, we were unable to process your payment today for {$this->total}.",
                "Your bank have stipulated that they require us to perform a further security check. This is where you must approve the payment via your banking app or perhaps a code in text message from your card provider",
                "To complete this, the card holder should visit the Locker Room and from the Admin area, select Pay Now",
                "Thanks",
                "Bloom Netball"
            ]
        ];
    }

    static function FetchFor (String $stripePaymentIntentStatus = null, String $date = null) {
        if (!$date) $date = date('Y-m-d');
        $sql = "SELECT * FROM `stripePayments` WHERE `stripePaymentIntentStatus` = '$stripePaymentIntentStatus' AND `created` >= '$date 00:00:00' AND `created` <= '$date 23:59:59'";
        return static::Query($sql);
    }

    /* Statics */
    static function Create(Int $teamID, Float $total) {
        /**
         * Performs necessary checks before raising a Payment Intent
         * Returns STRING error message or INT db ID on SUCCESS.
         */
        if ($total<=0) return "Nil or negative balance - no billing required";
        $team = new Team($teamID);
        if (!($treasurerID = $team->getTreasurer())) return "No Team Treasurer";
        $treasurer = new User($treasurerID);
        if (!($stripeCustomerID = $treasurer->stripeCustomerID())) return "Treasurer does not have a Stripe Customer ID";
        if (!($paymentMethodID = $team->paymentMethod())) return "No Team Payment Method";
        $paymentMethod = Stripe::getPaymentMethod($paymentMethodID);
        /* Is Payment Method Valid? */
        $expiryDate = strtotime(date('Y-m-t',strtotime("{$paymentMethod->card->exp_year}-{$paymentMethod->card->exp_month}-01 23:23:59")));
        if ($expiryDate < time()) return "Payment card has expired";
        /* Does the Payment Method match the Customer */
        if ($paymentMethod->customer != $stripeCustomerID) return "Mis-match between Payment Method and Customer (Not this customer's card)";
        exit("Total: $total. Team: $team.");
        /* Cancel any unfinished Payment Intents */
        $sql = "SELECT `stripePaymentIntentID` FROM `stripePayments` WHERE `teamID` = :teamID AND (`stripePaymentIntentStatus` <> `canceled` AND `stripePaymentIntentStatus` <> `succeeded`)";
        $db1 = new Db($sql,["teamID" => $teamID]);
        if ($db1->rows) {
            foreach ($db1->rows as $r) {
                Stripe::CancelPaymentIntent($r['stripePaymentIntentID']);
                new Db("UPDATE `stripePayments` SET `stripePaymentIntentStatus` = 'canceled' WHERE `stripePaymentIntentID` = '{$r['stripePaymentIntentID']}'");
            }
        }
        /* Create PaymentIntent */
        try {
            $intent = Stripe::CreatePaymentIntent($stripeCustomerID,$total,$team->fullName(),$paymentMethodID);
            if (is_string($intent)) return $intent;
        } catch (Exception $e) {
            return $e->getMessage();
        }
        $sql = "INSERT INTO `stripePayments` SET `stripePaymentIntentID` = :stripePaymentIntentID, `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `stripePaymentMethod` = :stripePaymentMethod, `teamID` = :teamID, `total` = :total";
        $db2 = new Db($sql, [
            "stripePaymentIntentID" => $intent->id,
            "stripePaymentIntentStatus" => $intent->status,
            "stripePaymentMethod" => $paymentMethodID,
            "teamID" => $teamID,
            "total" => $total
        ]);
        if ($db2->errors) return implode(". ", $db2->errors);
        return (int)$db2->lastInsertID;
    }

    static function SetupPayment (String $stripeCustomerID, Float $total, Team $team, String $paymentMethodID) {
        /* Create PaymentIntent */
        try {
            $intent = Stripe::CreatePaymentIntent($stripeCustomerID,$total,$team->__toString(),$paymentMethodID);
            if (is_string($intent)) return $intent;
        } catch (Exception $e) {
            return $e->getMessage();
        }
        $sql = "INSERT INTO `stripePayments` SET `stripePaymentIntentID` = :stripePaymentIntentID, `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `stripePaymentMethod` = :stripePaymentMethod, `teamID` = :teamID, `total` = :total";
        $rlt = Database::Execute($sql, [
            "stripePaymentIntentID" => $intent->id,
            "stripePaymentIntentStatus" => $intent->status,
            "stripePaymentMethod" => $paymentMethodID,
            "teamID" => $team->id,
            "total" => $total
        ]);
        return (int)$rlt['success']['lastInsertID'] ?? $rlt['error'];
    }

    static function SynchroniseAll()
    {
        $sql = "SELECT * FROM `stripePayments` 
        WHERE `stripePaymentIntentStatus` <> 'canceled' 
        AND `stripePaymentIntentStatus` <> 'succeeded'
        AND `created` >= DATE_SUB(NOW(), INTERVAL 2 MONTH)";
        $db = new Db($sql);
        $paymentIntents = $db->rows;
        $batches = array_chunk($paymentIntents, 30);
        foreach ($batches as $batch) {
            foreach ($batch as $r) {
                $pi = new static();
                $pi->Load($r);
                $pi->Synchronise();
            }
        }
    }

    static function byPaymentIntent(String $stripePaymentIntentID) {
        $sql = "SELECT * FROM `stripePayments` WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
        $db = new Db($sql,["stripePaymentIntentID" => $stripePaymentIntentID]);
        if ($db->rows[0]) {
            $sp = new static();
            $sp->Load($db->rows[0]);
            return $sp;
        } 
    }

    static function ConfirmAll (Bool $production = false) {
        /*
            Returns an Array
            key = teamID
            value = Array (coordinatorID, teamName, result etc)
        */
        // Fetch Payment Intents that Require Confirmation
        $sql = "SELECT `stripePaymentIntentID`,`teamID`, `total` FROM `stripePayments` WHERE `stripePaymentIntentStatus` = 'requires_confirmation'";
        $db = new Db($sql);
        if (!$db->rows) return; // If none, return.
        $return = [];
        $confirmTotal = $failedTotal = 0;
        // Setup SQL Statement
        $sql = "UPDATE `stripePayments` SET `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `failText` = :failText, outcomeValue = :outcomeValue, outcomeReason = :outcomeReason WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
        // Loop through each Payment Intent
        foreach ($db->rows as $r) {
            // Set Base details for return values (Team, Coordinator, PI ID etc)
            $team = new Team($r['teamID']);
            $return[$r['teamID']] = [
                "pi" => $r['stripePaymentIntentID'],
                "total" => $r['total'],
                "coordinatorID" => $team->getCoordinatorID(),
                "teamName" => $team->__toString(),
                "outcomeValue" => null,
                "outcomeReason" => null,
            ];
            if ($production !== true) continue;
            
            // Attempt to Confirm ...
            try {
                $rlt = Stripe::ConfirmIntent($r['stripePaymentIntentID']);
                if (is_string($rlt)) {
                    // String Value returned indicates an Error message
                    // Record Error Message
                    new Db($sql,[
                        "stripePaymentIntentStatus" => "requires_payment_method", 
                        "failText" => $rlt, 
                        "stripePaymentIntentID" => $r['stripePaymentIntentID'], 
                        "outcomeValue" => $return[$r['teamID']]['outcomeValue'],
                        "outcomeReason" => $return[$r['teamID']]['outcomeReason']
                    ]);
                    $return[$r['teamID']]["result"] = false;
                    $return[$r['teamID']]["reason"] = $rlt;
                    // Cancel the Payment Intent
                    $pi = static::byPaymentIntent($r['stripePaymentIntentID']);
                } else {
                    // Payment Successful - record the fact
                    if (isset($intent->charges->data[0]->outcome->network_status)) {
                        $return[$r['teamID']]['outcomeValue'] = $intent->charges->data[0]->outcome->network_status;
                    }
                    if (isset($intent->charges->data[0]->outcome->reason)) {
                        $return[$r['teamID']]['outcomeReason'] = $intent->charges->data[0]->outcome->reason;
                    }
                    $confirmTotal += $r['total'];
                    new Db($sql,[
                        "stripePaymentIntentStatus" => $rlt->status, 
                        "stripePaymentIntentID" => $r['stripePaymentIntentID'],
                        "failText" => null,
                        "outcomeValue" => $return[$r['teamID']]['outcomeValue'],
                        "outcomeReason" => $return[$r['teamID']]['outcomeReason'] 
                    ]);
                    $return[$r['teamID']]['reason'] = "Payment Intent {$r['stripePaymentIntentID']} confirmed";
                    $return[$r['teamID']]["result"] = true;
                    $return[$r['teamID']]["reason"] = $msg = "Payment Intent Success {$r['stripePaymentIntentID']} for $team : {$rlt->status}";
                    // Logging::Add($msg);
                }
            } catch (Exception $e) {
                // An error occurred trying - capture and move on ...
                $failedTotal += $r['total'];
                if (isset($intent->charges->data[0]->outcome->network_status)) {
                    $return[$r['teamID']]['outcomeValue'] = $intent->charges->data[0]->outcome->network_status;
                }
                if (isset($intent->charges->data[0]->outcome->reason)) {
                    $return[$r['teamID']]['outcomeReason'] = $intent->charges->data[0]->outcome->reason;
                }
                new Db($sql,[
                    "stripePaymentIntentStatus" => "requires_payment_method", 
                    "stripePaymentIntentID" => $r['stripePaymentIntentID'],
                    "failText" => $e->getMessage(), 
                    "outcomeValue" => $return[$r['teamID']]['outcomeValue'],
                    "outcomeReason" => $return[$r['teamID']]['outcomeReason']
                ]);
                $return[$r['teamID']]["result"] = false;
                $return[$r['teamID']]["reason"] = $e->getMessage();
                $return[$r['teamID']]["reason"] = $msg = "Payment Intent Failure {$r['stripePaymentIntentID']} : " . $e->getMessage();
            }
        }
        if ($production === true) {
            StripePayment::setBillingTotals($confirmTotal,'preauth');
            $msg = "Billing Confirmed for ".date('d/m/Y')." is $confirmTotal. (Failed: $failedTotal)";
            // Logging::Add($msg);
            Email::Issue("Billing Confirmed ".date('d/m/Y'),["Hi",$msg,"Thanks","Bloom Netball"],["<EMAIL>" => "Charlotte Waugh"],["<EMAIL>" => "Richard Dakin"],["<EMAIL>" => "a2ztech",]);
        }
        static::BillLogging(3,$return);
        return $return;
    }

    static function CaptureAll(String $date = null) {
        $sql = "SELECT `stripePaymentIntentID`,`teamID`,`total` FROM `stripePayments` WHERE `stripePaymentIntentStatus` = 'requires_capture'";
        if ($date) $sql .= " AND created <= '$date 23:59:59'";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        $captured = 0;
        foreach ($db->rows as $r) {
            try {
                $intent = Stripe::CaptureIntent($r['stripePaymentIntentID']);
                $team = new Team($r['teamID']);
                if (is_string($intent)) {
                    $sql = "UPDATE `stripePayments` SET `failText` = :failText WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
                    new Db($sql,["failText" => $intent,"stripePaymentIntentID" =>$r['stripePaymentIntentID']]);
                    $msg = "{$r['stripePaymentIntentID']} Capture failed with $intent";
                    $return[] = $msg;
                    Logging::Add($msg);
                } else {
                    $sql = "UPDATE `stripePayments` SET `stripePaymentIntentStatus` = :stripePaymentIntentStatus WHERE `stripePaymentIntentID` = :stripePaymentIntentID";
                    new Db($sql,["stripePaymentIntentStatus" => $intent->status,"stripePaymentIntentID" =>$r['stripePaymentIntentID']]);
                    $captured += $r['total'];
                    $msg = "{$r['stripePaymentIntentID']} Capture succeeded";
                    $return[] = $msg;
                    Logging::Add($msg);
                    $team->setAgedDate();
                }
            } catch (Exception $e) {
                
            }
        }
        StripePayment::setBillingTotals($captured,'capture');
        return $return;
    }

    static function RaiseSuccessTransactions() {
        $sql = "SELECT * FROM `stripePayments` WHERE `stripePaymentIntentStatus` = 'succeeded' AND `transactionID` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        $sql = "UPDATE `stripePayments` SET `transactionID` = :transactionID WHERE `id` = :id";
        foreach ($db->rows as $r) {
            $transaction = Finance::Create($r['teamID'], $r['total'], "Payment. Thank you.", date('Y-m-d',strtotime($r['created'])), "P", "S", "P");
            $msg = "Stripe Payment Intent {$r['stripePaymentIntentID']} matched with Payment Transaction ID {$transaction->id}";
            Logging::Add($msg);
            $return[] = $msg;
            new Db($sql,["transactionID" => $transaction->id, "id" => $r['id']]);
        }
        return $return;
    }

    static function Pending () {
        // if (!$date) $date = date('Y-m-d');
        $sql = "SELECT * FROM `stripePayments` WHERE `stripePaymentIntentStatus` = 'requires_confirmation' OR  `stripePaymentIntentStatus` = 'requires_capture' OR `stripePaymentIntentStatus` = 'requires_payment_method'";
        $db = new Db($sql); if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            ($return) ? array_push($return,$s) : $return[] = $s;
        }
        return $return;
    }

    static function byDate (String $date = null) {
        if (!$date) $date = date('Y-m-d');
        $sql = "SELECT * FROM `stripePayments` WHERE created >= :startDate AND created <= :endDate";
        return static::Query($sql,["startDate" => "$date 00:00:00", "endDate" => "$date 23:59:59"]);
    }

    static function Latest (Team $team, String $status = 'requires_action', Int $maxAgeDays = 0) {
        $sql = "SELECT stripePaymentIntentID FROM stripePayments WHERE created >= :startDate AND stripePaymentIntentStatus = :status AND teamID = :teamID ORDER BY created DESC";
        $rlt = Database::Execute($sql,[
            "startDate" => date('Y-m-d 00:00:00'),
            "status" => $status,
            "teamID" => $team->id
        ]);
        if (!$rlt['success'] || !$rlt['success']['rows']) return;
        foreach ($rlt['success']['rows'] as $r) {
            $paymentIntent = Stripe::getPaymentIntent($r['stripePaymentIntentID']);
            if ($paymentIntent->status != $status) {
                Stripe::SyncPI($paymentIntent);
            } else return $paymentIntent;
        }
    }

    static function Active (Team $team) {
        return static::Latest($team,'requires_action',7);
    }

    static function CancelAllWithStatus (String $status = 'requires_payment_method') {
        $sql = "SELECT id, stripePaymentIntentID FROM stripePayments WHERE stripePaymentIntentStatus = '$status' AND transactionID IS NULL";
        $rlt = Database::Execute($sql);
        if (!$rlt['success']['rows']) return;
        $return = [];
        foreach ($rlt['success']['rows'] as $r) {
            $return[] = "Cancelling {$r['id']} : {$r['stripePaymentIntentID']}";
            $sp = new static($r['id']);
            $sp->Cancel();
            $sp->Save();
        }
        return $return;
    }

    static function TeamPending (Int $teamID) {
        $sql = "SELECT `id`, `stripePaymentIntentID`, `stripePaymentIntentStatus`,`total` FROM `stripePayments` WHERE (`stripePaymentIntentStatus` = 'requires_confirmation' OR `stripePaymentIntentStatus` = 'requires_capture' OR (`stripePaymentIntentStatus` = 'succeeded' AND 'transactionID' IS NULL)) AND `teamID` = :teamID";
        $db = new Db($sql,["teamID" => $teamID]);
        return (isset($db->rows[0])) ? $db->rows[0] : [];
    }

    static function TeamPendingTotal (Team $team) {
        $sql = "SELECT SUM(`total`) as total FROM `stripePayments` WHERE (`stripePaymentIntentStatus` = 'requires_confirmation' OR `stripePaymentIntentStatus` = 'requires_capture' OR (`stripePaymentIntentStatus` = 'succeeded' AND 'transactionID' IS NULL)) AND `teamID` = :teamID";
        $rlt = Database::Execute($sql,["teamID" => $team->id]);
        return $rlt['success']['rows'][0]['total'] ?? 0;
    }

    static function AmendTotal(String $stripePaymentIntentID, Float $total) {
        $stripePayment = static::byPaymentIntent($stripePaymentIntentID);
        $stripePayment->total = $total;
        try {
            $intent = Stripe::updatePaymentIntentTotal($stripePaymentIntentID,$total);
            if (is_string($intent)) return $intent;
            return $stripePayment->Save();
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    static function BillLogging (Int $stage, Array $data) {
        $loggingFolder = __DIR__. DIRECTORY_SEPARATOR . "billing-logging";
        if (!file_exists($loggingFolder)) {
            $old = umask(0);
            mkdir($loggingFolder,0775);
            umask($old);
        }
        $counter = 0; 
        $loggingFile = $loggingFolder . DIRECTORY_SEPARATOR . date('l')."_BillingStage".$stage."_".$counter.".txt";
        while (file_exists($loggingFile)) {
            // echo "Trying $loggingFile<br>";
            $counter++;
            $loggingFile = $loggingFolder . DIRECTORY_SEPARATOR . date('l')."_BillingStage".$stage."_".$counter.".txt";
        }
        $stream = fopen($loggingFile, 'w');
        $text = print_r($data,true);
        fwrite($stream,$text);
    }

    static function BillingStage1 ($createInvoices = false, String $date = null, Bool $thisDateOnly = false) {
        /* Any fixtures up to and including today that do not already have an Invoice - Invoice them */
        $fixturesBilled = Fixture::Billable($createInvoices, $date, $thisDateOnly);
        foreach ($fixturesBilled as $f) $logging[] = $f;
        $logging[] = "Team Debt " . StripePayment::setBillingTotals(Team::TotalDebt(),'debt',date('Y-m-d'));
        // static::BillLogging(1,$logging);
        return $fixturesBilled;
    }

    static function BillingStage2 ($raiseCharges = false, Bool $verbose = true) {
        return static::BillingStage2_v2($raiseCharges);
        /* Loop through all the balances and setup Billing Intentions for any that owe money */
        // Fetch All Balances
        $billLogs = [];
        $overdues = Finance::Balances(); 
        if ($verbose === true) echo count($overdues) . "to bill" . PHP_EOL;
        $alerts = $teamAlerts = [];
        $chargeTotal = $counter = 0;
        $limit = 10;
        // Loop through all
        foreach ($overdues as $teamID => $balance) {
            # Resolve Team
            $team = new Team($teamID);
            if ($verbose === true) echo "Team $team" . PHP_EOL;
            $billLogs[$teamID] = [
                'date' => date('Y-m-d'),
                'teamID' => $teamID,
                'treasurerID' => $team->getTreasurerID(),
                'balance' => $balance,
                'stripePaymentID' => null,
                'fullRun' => ($raiseCharges === true) ? 1 : 0,
                'statusID' => 0,
                'status' => null,
                'message' => null,
                'team' => $team,
                'league' => $team->getLeague(),
                'coordinator' => ($team->league && $team->league->coordinator) ? new User($team->league->coordinator) : null,
                'logResult' => null,
            ];
            
            // $billLogs[$teamID]['treasurerID'] = $team->getTreasurerID();
            // $league = new League($team->getLeagueID());
            // $league = $team->getLeague();
            // $teamAlerts[$team->id] = [
            //     'balance' => $balance,
            //     'status' => null,
            //     'message' => null,
            //     'team' => $team,
            //     'league' => $league,
            //     'coordinator' => ($league->coordinator) ? new User($league->coordinator) : null,
            //     'logResult' => null,
            //     'statusID' => null
            // ];
            // If balance = Zero or less - no charges due, set Team's Aged Days to today
            if ($balance <= 0) {
                if ($raiseCharges === true) $team->setAgedDate();
                $note = "Balance of $balance : No Action";
                $billLogs[$team->id]['logResult'] = $note;
                $billLogs[$teamID]['statusID'] = 0;
                if ($verbose === true) echo $note . PHP_EOL;
                continue;
            }
            
            // Perform Team Treasury Checks
            $check = $team->TreasurerCheck();
            
            if ($check !== true) {
                // exit("Treasurer Check failed for $team : $check");
                $team->setTreasurerError($check);
                $message = $team->fullName() . " could not be charged. $check";
                if ($raiseCharges === true) Logging::Add($message);
                $note = "Could not be charged. $check";
                $billLogs[$team->id]['logResult'] = $note;
                $billLogs[$teamID]['statusID'] = 0;
                if ($verbose === true) echo $note . PHP_EOL;
            } else {
                // echo "$team->id : $team : v";
                // echo ($team->lockerRoomVersion) ? $team->lockerRoomVersion : "1";
                // echo "<br>";
                /* Does this team already have a Pending Payment? */
                $currentlyPending = static::TeamPending($teamID);
                
                if (!$currentlyPending) { // Nothing Pending, create a Payment Intent
                    $note = "No payments pending the balance of $balance should be billed";
                    $billLogs[$team->id]['logResult'] = $note;
                    if ($verbose === true) echo $note . PHP_EOL;
                    if ($raiseCharges !== true) {
                        $billLogs[$teamID]['statusID'] = 1;
                        continue;
                    }
                    // exit(Tools::Dump($billLogs));
                    // $attempted += $balance;
                    try {
                        $stripePaymentID = static::Create($teamID,$balance);
                        if (!is_int($stripePaymentID)) {
                            // Attempt to create Payment Intention failed
                            // $failed += $balance;
                            $note = __FUNCTION__ . "  Billing Intention failed. $stripePaymentID. Error Code 1";
                            $message = $team->fullName() . " " . $note;
                            $billLogs[$team->id]['logResult'] = $note;
                            $billLogs[$teamID]['statusID'] = 0;
                            // $log[] = $message;
                            // $alerts[$league->getCoordinatorID()][] = $message;
                            // $billLogs[$team->id][] = $message;
                            Logging::Add($message);
                            if ($verbose === true) echo $note . PHP_EOL;
                        } else {
                            $note = "Billing Intention success. For $balance against Payment ID $stripePaymentID";
                            $billLogs[$teamID]['stripePaymentID'] = $stripePaymentID;
                            $billLogs[$teamID]['statusID'] = 1;
                            $billLogs[$team->id]['logResult'] = $note;
                            $chargeTotal += $balance;
                            if ($verbose === true) echo $note . PHP_EOL;
                        } 
                    } catch (Exception $e) {
                        $note =  __FUNCTION__ . " billing failed. ".$e->getMessage().". Error Code 2";
                        // Code Error
                        $billLogs[$team->id]['logResult'] = $note;
                        Logging::Add($message);
                        $billLogs[$teamID]['statusID'] = 0;
                        if ($verbose === true) echo $note . PHP_EOL;
                    } 
                } else {
                    $note = "Pending payment of {$currentlyPending['total']} exists so no billing action taken";
                    $billLogs[$team->id]['logResult'] = $note;
                    // $billLogs[$teamID]['note'] = $note;
                    $billLogs[$teamID]['stripePaymentID'] = $currentlyPending['id'];
                    $billLogs[$teamID]['statusID'] = 1;
                    if ($verbose === true) echo $note . PHP_EOL;
                }
                // else {
                //     $log[] = $team->fullName() . " already has a Payment in Process for $balance no action required";
                //     $already += $balance;
                // }
            }
            $counter ++;
            if (!$raiseCharges && $counter >= $limit) break;
        }
        if ($raiseCharges === true) {
            $msg = "Billing Intention for ".date('d/m/Y')." is $chargeTotal";
            Logging::Add($msg);
            StripePayment::setBillingTotals($chargeTotal,'intent',date('Y-m-d'));
            Billing_Log::Batch($billLogs);
            Email::Issue("Billing Intention ".date('d/m/Y'),["Hi",$msg,"Thanks","Bloom Netball"],["<EMAIL>" => "a2ztech",]);
        } 

        static::BillLogging(2,$billLogs);        

        return $billLogs;
    }

    static function BillingStage3 (Bool $production = false) {
        /* Run through all Intentions and Pre-Auth them */
        return static::ConfirmAll($production);
    }

    static function BillingStage4() {
        $log = StripePayment::CaptureAll();
        return $log;
    }

    static function BillingStage5() {
        $log = StripePayment::RaiseSuccessTransactions();
        return $log;
    }

    static function BillingAM() {
        $results = [];
        // Create Invoices for any  as yet unbilled fixtures up to/including today
        $billStage1 = static::BillingStage1();
        if ($billStage1) {
            foreach ($billStage1 as $fixtureID => $invoices) {
                $fixture = new Fixture($fixtureID);
                $league = new League($fixture->getLeagueID());
                $results[$league->getCoordinatorID()][] = "Fixture Invoice raised for $fixture (Fixture ID: $fixtureID. Home Invoice ID: {$invoices['home']}. Away Invoice ID: {$invoices['away']})";
            }
        }
        $billStage2 = static::BillingStage2();
        if ($billStage2) {
            foreach ($billStage2 as $coordinatorID => $messages) {
                foreach ($messages as $message) $results[$coordinatorID][] = $message;
            }
        }
        $billStage3 = static::BillingStage3();
        if ($billStage3) {
            foreach ($billStage3 as $teamID => $teamBilling) {
                if ($teamBilling['result']===true) {
                    // Successfully billed
                    $results[$teamBilling['coordinatorID']][] = "Successfully charged {$teamBilling['teamName']} {$teamBilling['total']}";
                } else $results[$teamBilling['coordinatorID']][] = "Failed payment attempt of {$teamBilling['total']} to  {$teamBilling['teamName']}. {$teamBilling['reason']}";
            }
        }
    }

    static function BillingEve() {
        StripePayment::CaptureAll();
        StripePayment::RaiseSuccessTransactions();
    }

    static function SeasonTotalInPeriod (Season $season, String $startDate, String $endDate) {
        $sql = "SELECT SUM(`stripePayments`.`total`) AS `total` FROM `stripePayments` LEFT JOIN `teams` ON `stripePayments`.`teamID` = `teams`.`id` WHERE (`stripePayments`.`stripePaymentIntentStatus` = 'requires_capture' OR `stripePayments`.`stripePaymentIntentStatus` = 'succeeded') AND `teams`.`seasonID` = {$season->id} AND `stripePayments`.`created` >= '$startDate 00:00:00' AND `stripePayments`.`created` <= '$endDate 23:59:59'";
        // echo $sql."<br>";
        $db = new Db($sql);
        if ($db->errors) exit(implode(" ",$db->errors));
        if ($db->rows) return $db->rows[0]['total'];
    }

    static function PaymentHistory (Int $teamID = null, String $startDate = null, String $endDate = null) {
        $connector = ($teamID) ? " WHERE `teamID` = $teamID" : null;
        $connector .= ($startDate) ? " AND `created` >= '$startDate 00:00:00'" : null;
        $connector .= ($endDate) ? " AND `created` <= '$endDate 23:59:59'" : null;
        $sql = "SELECT * FROM `stripePayments` $connector ORDER BY `created` ASC";
        $db = new Db($sql);
        return $db->rows;
    }

    static function setBillingTotals ($value, $type = 'intent', $date = null) {
        $types = ["fixtures","debt","intent","preauth","capture"];
        if (!in_array($type,$types)) return "Not a valid type ({$type})";
        if (!$date) $date = date('Y-m-d');
        $value = (int)$value;
        $sql = "INSERT INTO `billingTotals` SET `date` = '$date', `$type` = $value ON DUPLICATE KEY UPDATE `$type` = $value";
        new Db($sql);
    }

    static function FlushExpired () {
        $expired = static::Query("SELECT * FROM `stripePayments` WHERE (`stripePaymentIntentStatus` = 'requires_payment_method' OR `stripePaymentIntentStatus` = 'requires_action') AND `created` < '".date('Y-m-d H:i:s',strtotime("-1 day"))."'");
        if (!$expired) return;
        $stripe = new Stripe_v2(true);
        foreach ($expired as $p) {
            $rlt = $p->Cancel();
            $message = (is_string($rlt)) ? "PI {$p->stripePaymentIntentID} could not be cancelled : $rlt" : "PI {$p->stripePaymentIntentID} cancelled";
            Logging::Add($message,true);
            $return[] = $message;
        }
        return $return;
    }

    static function BillingStage2_v2 (Bool $production = false) {
        $overdues = Finance::Balances(); $billLogs = []; $chargeTotal = 0;

        foreach ($overdues as $teamID => $balance) {
            $team = new Team($teamID);
            if ($balance > 0) {
                $billLogs[] = ($billLog=static::BillStage2($team,$production));
                if (!$billLog['pending'] && !$billLog['stripePaymentID']) {
                    $message = [
                        "Hi",
                        "We tried to take a payment of {$balance} for $team today which failed",
                        "The error message was: {$billLog['note']}",
                        "We will re-attempt the payment tomorrow morning, please ensure the card is valid and has the required funds available",
                        "Many thanks",
                        "Bloom Netball"
                    ];
                } elseif ($billLog['stripePaymentID']) $chargeTotal += $billLog['balance'];
            } elseif ($production === true) $team->setAgedDate();
        }

        if ($production === true) {
            $msg = "Billing Intention for ".date('d/m/Y')." is $chargeTotal";
            Logging::Add($msg);
            StripePayment::setBillingTotals($chargeTotal,'intent',date('Y-m-d'));
            Billing_Log::Batch($billLogs);
            Email::Issue("Billing Intention ".date('d/m/Y'),["Hi",$msg,"Thanks","Bloom Netball"],["<EMAIL>" => "Charlotte Waugh"],["<EMAIL>" => "Richard Dakin"],[]);
        }
        return $billLogs;
    }

    static function BillStage2 (Team $team, Bool $production = false) {
        $balance = Finance::Balance($team);
        $log = [
            'mode' => ($production === true) ? "Live" : "Test",
            'teamID' => $team->id,
            'date' => date('Y-m-d'),
            'team' => $team->__toString(),
            'treasurerID' => null,
            'stripeCustomerID' => null,
            'balance' => $balance,
            'statusID' => null,
            'stripePaymentMethodID' => null,
            'stripePaymentIntentID' => null,
            'stripePaymentIntent' => null,
            'stripePaymentID' => null,
            'version' => null,
            'note' => null,
            'pending' => null,
            'stripePayID' => null,
            'fullRun' => 1,
            'payInFull' => null,
            'fullBalancePayment' => null,
            'fullBalanceToPay' => null,
            'fullBalancePaymentSeason' => null,
            'fullBalancePaymentText' => null
        ];

        if (!$team->id) {
            $log['note'] = "No Team found";
            $log['statusID'] = 0;
            return $log;
        }

        // if ($team->id == 1280) {
        //     $log['note'] = "Skipping";
        //     $log['statusID'] = 0;
        //     return $log;
        // }

        if ($balance <= 0) {
            if ($production === true) $team->setAgedDate();
            $log['note'] = "No Action";
            $log['balance'] = $balance;
            $log['statusID'] = 0;
            return $log;
        }
        
        $log['version'] = ($team->lockerRoomVersion) ? $team->lockerRoomVersion : 1;
        # Is there already a billing effort
        $log['pending'] = static::TeamPendingTotal($team);
        if ($log['pending']) {
            $pending = static::TeamPending($team->id);
            if ($pending) $log['stripePaymentIntentID'] = $pending['stripePaymentIntentID'];
            $log['note'] = "Payment event pending {$log['pending']}";
            $log['statusID'] = 2;
            return $log;
        }
        # Do we have a Treasurer?
        $treasurer = $team->getPayer();
        if ($treasurer && $treasurer->id) {
            $log['treasurerID'] = $treasurer->id;
        } else {
            $log['note'] = "No Treasurer";
            $log['statusID'] = 2;
            return $log;
        } 
        $log['stripePaymentMethodID'] = $team->paymentMethod();
        if (!$log['stripePaymentMethodID']) {
            $log['note'] = "No Payment option";
            $log['statusID'] = 2;
            return $log;
        }
        
        # Does Stripe hold this Payment Method?
        try {
            $pm = Stripe::getPaymentMethod($log['stripePaymentMethodID']);
        } catch (Exception $e) {
            $log['note'] = $e->getMessage();
            $log['statusID'] = 2;
            return $log;
        }
        if (!$pm) {
            $log['note'] = "The payment reference we hold on file ({$log['stripePaymentMethodID']}) does not appear to be a valid payment card type.";
            $log['statusID'] = 2;
            return $log;
        }
        if (!$pm->customer) {
            $log['note'] = "The payment reference we hold on file ({$log['stripePaymentMethodID']}) does not have an associated customer.";
            $log['statusID'] = 2;
            return $log;
        }

        # Does the Treasurer have authority for this card?
        # Does the Treasurer have a StripeCustomerID to match the StripeCustomerID on the Payment Method
        $log['stripeCustomerIDs'] = $treasurer->getStripeCustomerIDs();
        if (!$log['stripeCustomerIDs']) {
            $log['note'] = "No Stripe Customer ID on file for this Treasurer";
            return $log;
        }

        $stripeCustomerMatched = false;
        foreach ($log['stripeCustomerIDs'] as $s) {
            if ($s['stripeCustomerID'] == $pm->customer) {
                $stripeCustomerMatched = true;
                $log['stripeCustomerID'] = $s['stripeCustomerID'];
                break;
            } 
        }

        if ($stripeCustomerMatched === false) {
            $log['note'] = "Treasurer does not appear to own the payment card";
            return $log;
        }

        # Is this team in a Live league with FBP requested?
        $log['payInFull'] = TeamFollower::PayingInFull ($team);
        if ($log['payInFull']) {
            $log['fullBalancePaymentSeason'] = TeamSeason::Live($team);
            if ($log['fullBalancePaymentSeason']) {
                $log['fullBalancePayment'] = $log['fullBalancePaymentSeason']->FullBalancePayment();
                $log['fullBalanceToPay'] = $log['fullBalancePaymentSeason']->FullBalanceToPay();
                $balance = $log['fullBalanceToPay']['total'];
            }
        }

        if ($production !== true) return $log;
        /* Create PaymentIntent */
        try {
            $intent = Stripe::CreatePaymentIntent($log['stripeCustomerID'],$balance,$team->fullName(),$log['stripePaymentMethodID']);
            if (is_string($intent)) {
                $log['note'] = $intent;
                $log['statusID'] = 2;
                return $log;
            }
            $log['stripePaymentIntentID'] = $intent->id;
            $log['stripePaymentIntent'] = $intent;
        } catch (Exception $e) {
            $log['note'] = $e->getMessage();
            $log['statusID'] = 2;
            return $log;
        }
        
        $db = Database::Execute("INSERT INTO `stripePayments` SET `stripePaymentIntentID` = :stripePaymentIntentID, `stripePaymentIntentStatus` = :stripePaymentIntentStatus, `stripePaymentMethod` = :stripePaymentMethod, `teamID` = :teamID, `total` = :total", [
            "stripePaymentIntentID" => $intent->id,
            "stripePaymentIntentStatus" => $intent->status,
            "stripePaymentMethod" => $log['stripePaymentIntentID'],
            "teamID" => $team->id,
            "total" => $balance,
        ]);

        if ($db['error']) {
            $log['note'] = $db["error"]["message"];
            $log['statusID'] = 2;
            return $log;
        } else $log['stripePaymentID'] = $db['success']['lastInsertID'];


        return $log;
    }

    static function RemovePaymentIntentsNotPreAuthd (String $date) {
        $sql = "SELECT * FROM stripePayments WHERE created >= :date";
        $rlt = Database::Execute($sql,["date" => $date]);
        if (!isset($rlt['success']['rows']) || !$rlt['success']['rows']) return;
        $return = [];
        $updateSql = "UPDATE stripePayments SET stripePaymentIntentStatus = 'canceled' ";
        $conn = "WHERE";
        foreach ($rlt['success']['rows'] as $r) {
            $pi = Stripe::getPaymentIntent($r['stripePaymentIntentID']);
            if ($pi->status == 'requires_confirmation' || $pi->status == 'canceled') {
                if ($pi->status == 'requires_confirmation') $pi = Stripe::CancelPaymentIntent($r['stripePaymentIntentID']);
                $updateSql .= $conn . " stripePaymentIntentID = '{$r['stripePaymentIntentID']}' ";
                $conn = "OR";
            }
            $return['paymentIntents'][] = "{$pi->id} = {$pi->status}";
        }
        $return['sql'] = $updateSql;
        $return['db'] = Database::Execute($updateSql);
        return $return;
    }

    static function AlertSweep () {
        $alertCategories = ['requires_action'];
        $sql = "SELECT * FROM stripePayments WHERE created >= :startDate AND alerted IS NULL";
        $sql .= " AND stripePaymentIntentStatus = ('" . implode("' OR '",$alertCategories)."')";
        $rlt = static::Query($sql,["startDate" => date('Y-m-d 00:00:00')]);
        foreach ($rlt as $r) {
            $emailMessage = $to = $cc = [];
            $team = new Team($r->teamID);
            $coordinator = $team->getCoordinator();
            if ($coordinator) $cc = [$coordinator->email => $coordinator->__toString()];
            $managers = $team->getTeamManagers();
            $sendTo = ($managers['treasurer'] && $managers['treasurer'] != $managers['captain']) ? $managers['treasurer'] : $managers['captain'];
            $to = [$sendTo->email => $sendTo->__toString()];
            switch ($r->stripePaymentIntentStatus) {
                case 'requires_action': $emailMessage = $r->emailContentRequiresAction($sendTo,$team); break;
            }
            if ($emailMessage && $to) {
                if (Email::Issue($emailMessage['subject'],$emailMessage['message'],$to,$cc,["<EMAIL>" => "Charlotte Waugh"])) {
                    $r->alerted = date('Y-m-d H:i:s');
                    $r->Save();
                }
            } 
        }
    }
}