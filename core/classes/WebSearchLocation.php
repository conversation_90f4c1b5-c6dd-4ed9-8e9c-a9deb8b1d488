<?php

class WebSearchLocation extends Base {

    protected $dbTable = "webSearchLocations";
    protected $dbFields = ["password","activationCode"];
    protected $isAdmin;
    protected $email;

    function __construct(Int $id = null) {
        parent::__contruct($id);
    }
    static function Add (Array $data = []) {
        $sql = null;
        foreach ($data as $k => $v) $sql .= (!$sql) ? "`$k` = :$k" : ", `$k` = :$k";
        $sql = "INSERT INTO `webSearchLocations` SET $sql";
        $db = new Db($sql,$data);
        return $db;
    }
    static function byDate (String $date = null) {
        if (!$date) $date = date('Y-m-d');
        $sql = "SELECT * FROM `webSearchLocations` WHERE `created` >= :startTime AND `created` <= :endTime";
        $db = new Db($sql, ["startTime" => "$date 00:00:00","endTime" => "$date 23:59:59"]);
        if ($db->rows) {
            foreach ($db->rows as $k => $r) {
                if (!$r['lat'] || !$r['lng']) {
                    $searchUrl = "https://maps.googleapis.com/maps/api/geocode/json?address=".urlencode($r['location']).",UK&key=AIzaSyDpeui0fOCNqWx5dDQCtJLJMm0biUUYRsc";
                    $searchRlt = json_decode(file_get_contents($searchUrl),true);
                    if (isset($searchRlt['results'][0]['geometry']['location'])) {
                        $db->rows[$k]['lat'] = $searchRlt['results'][0]['geometry']['location']['lat'];
                        $db->rows[$k]['lng'] = $searchRlt['results'][0]['geometry']['location']['lng'];
                        $sql = "UPDATE `webSearchLocations` SET `lat` = :lat, `lng` = :lng WHERE `id` = :id";
                        new Db($sql,["lat" => $db->rows[$k]['lat'], "lng" => $db->rows[$k]['lng'], "id" => $r['id']]);
                    }
                }
            }
        }
        return $db->rows;
    }
}