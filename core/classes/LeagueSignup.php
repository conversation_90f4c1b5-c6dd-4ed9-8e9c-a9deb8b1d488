<?php

class LeagueSignup extends Base {

    protected $dbTable = "league_signup";
    protected $dbFields = ["captainID","treasurerID","leagueID","teamName","leagueGuide","leagueTerms","leagueTermsAccepted", "stripeSetupIntent"];

    function Process() {

    }

    function CaptainDetails() {
        return <<<HTML
            <form>
                <label for="captain.firstname">Firstname</label>
                <input type="text" name="captain[firstname]" id="captain.firstname" class="form-control" placeholder="Firstname">
                <label for="captain.lastname">Lastname</label>
                <input type="text" name="captain[lastname]" id="captain.lastname" class="form-control" placeholder="Lastname">
                <label for="captain.email">Email</label>
                <input type="email" name="captain[email]" id="captain.email" class="form-control" placeholder="Email">
                <label for="captain.mobile">Mobile</label>
                <input type="tel" name="captain[mobile]" id="captain.mobile" class="form-control" placeholder="Mobile">
                <button class="btn btn-succes mt-2">Save</button>
            </form>
        HTML;
    }
}