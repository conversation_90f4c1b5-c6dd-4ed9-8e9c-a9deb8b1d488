<?php

class IncidentReport extends Base {

    protected $dbTable = "incident_report";

    protected $name;
    protected $dob;
    protected $contact_number;
    protected $date_of_incident;
    protected $time_of_incident;
    protected $venueId;
    protected $leagueId;
    protected $description;
    protected $witness;
    protected $details_location;
    protected $root_cause;
    protected $nature_of_injury;
    protected $treatment;
    protected $match_official_name;
    protected $official_statement_file;
    protected $additional_witness;
    protected $witness_name;
    protected $witness_statement_files;
    protected $additional_info;
    protected $mitigating_circumstances;
    protected $status;
    protected $your_name;
    protected $created_at;
    protected $updated_at;
    protected $update_history;
    protected $is_important;
    protected $no_consent;
    protected $relationship_to_player;
    protected $welfare_check_completed;
    protected $under_18;
    protected $outstanding_info;

    protected $dbFields = [
        "name",
        "dob",
        "contact_number",
        "date_of_incident",
        "time_of_incident",
        "venueId",
        "leagueId",
        "description",
        "witness",
        "details_location",
        "root_cause",
        "nature_of_injury",
        "treatment",
        "match_official_name",
        "official_statement_file",
        "additional_witness",
        "witness_name",
        "witness_statement_files",
        "additional_info",
        "mitigating_circumstances",
        "status",
        "your_name",
        "created_at",
        "updated_at",
        "update_history",
        "is_important",
        "no_consent",
        "relationship_to_player",
        "welfare_check_completed",
        "under_18",
        "outstanding_info",
        "severity",
        "injured_person_type",
        "taken_to_hospital",
        "injury_details",
        "location",
    ];

    function __construct(Int $id = null) {
        parent::__construct($id);
    }


    public function save() {
        // Set the current date and time
        $currentDateTime = date('Y-m-d H:i:s');

        // Check if it's a new record or an existing record
        if ($this->id === null) {
            // New record, set created_at
            $this->created_at = $currentDateTime;
        }

        // Always set updated_at
        $this->updated_at = $currentDateTime;
        if (isset($this->dob)) {
            $this->dob = date('Y-m-d', strtotime($this->dob));
        }
        if (isset($this->date_of_incident)) {
            $this->date_of_incident = date('Y-m-d', strtotime($this->date_of_incident));
        }

        // Call the parent's save method
        return parent::save();
    }


    public function ApiOutput() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "dob" => $this->dob,
            "contact_number" => $this->contact_number,
            "date_of_incident" => $this->date_of_incident,
            "time_of_incident" => $this->time_of_incident,
            "coordinator" => $this->getCoordinatorName(),
            "venue" => $this->getVenueName(),
            "description" => $this->description,
            "witness" => $this->witness,
            "is_important" => $this->is_important,
            "details_location" => $this->details_location,
            "root_cause" => $this->root_cause,
            "nature_of_injury" => $this->nature_of_injury,
            "treatment" => $this->treatment,
            "match_official_name" => $this->match_official_name,
            "official_statement_file" => $this->official_statement_file,
            "witness_statement_files" => $this->witness_statement_files,
            "witness_name" => $this->witness_name,
            "additional_witness" => $this->additional_witness,
            "additional_info" => $this->additional_info,
            "mitigating_circumstances" => $this->mitigating_circumstances,
            "your_name" => $this->your_name,
            "status" => $this->status,
            "severity" => $this->severity,
            "injured_person_type" => $this->injured_person_type,
            "taken_to_hospital" => $this->taken_to_hospital,
            "injury_details" => $this->injury_details,
            "location" => $this->location,
        ];
    }

    function getVenueName() {
        $venue = new Venue((int) $this->venueId);
        return $venue->__toString();
    }

    public function getCoordinatorName() {
        if (isset($this->venueId) && !empty($this->venueId) && $this->getCoordinatorId() != null) {
            $user = new User($this->getCoordinatorId()[0]->coordinator);
            return $user->getFirstname() . ' ' . $user->getLastname();
        }
        return "";
    }
    function getCoordinatorEmail() {
        if (isset($this->venueId) && !empty($this->venueId)) {
            $user = new User($this->getCoordinatorId()[0]->coordinator);
            return $user->getEmail();
        }
        return null;
    }

    static function getAnIncident($id, $coordinatorID, $status = null) {
        $sql = "SELECT * FROM incident_report WHERE id = :id";
        if (isset($status)) {
            $status = str_replace('_', ' ', $status);
            $sql .= " AND status = :status";
        }
        if ((new IncidentReport)->ValidateAdmin($coordinatorID)) {
            if (isset($status)) {
                return static::Query($sql, ["id" => $id, "status" => $status]);
            }
            return static::Query($sql, ["id" => $id]);
        } else {
            $sql .= " AND leagueId IN (SELECT id FROM leagues WHERE coordinator = :coordinatorID)";
            if (isset($status)) {
                return static::Query($sql, ["id" => $id, "status" => $status, "coordinatorID" => $coordinatorID]);
            }
            return static::Query($sql, ["id" => $id,  "coordinatorID" => $coordinatorID]);
        };
    }


    function ValidateAdmin($userId) {
        $user = new User($userId);
        $email = $user->getEmail();
        if ($email === '<EMAIL>' || $email === '<EMAIL>' || $email === '<EMAIL>' || $email === '<EMAIL>') {
            return true;
        }
        return false;
    }
    function getCoordinatorId() {
        if (isset($this->leagueId) && !empty($this->leagueId)) {
            $sql = "SELECT coordinator FROM leagues WHERE id = :leagueId";
            return static::Query($sql, ["leagueId" => $this->leagueId]);
        }
        return null;
    }

    function count($user) {
        $incident_count = [];
        $params = [];
        $whereClause = "";

        // If not admin, show only incidents from leagues they coordinate
        if (!$user->isManager) {
            $whereClause = "WHERE leagueId IN (SELECT id FROM leagues WHERE coordinator = :coordinatorId)";
            $params["coordinatorId"] = $user->id;
        }

        // Total count
        $totalSql = "SELECT COUNT(*) as count FROM incident_report " . $whereClause;
        $totalResult = static::Query($totalSql, $params);
        $incident_count['total'] = $totalResult[0]->count;

        $filterStatus = $user->isManager ? 'pending' : 'open';

        // Pending review incidents
        $pendingSql = "SELECT COUNT(*) as count FROM incident_report ";
        if ($whereClause) {
            $pendingSql .= $whereClause . " AND status = '$filterStatus'";
        } else {
            $pendingSql .= "WHERE status = '$filterStatus'";
        }
        $pendingResult = static::Query($pendingSql, $params);
        $incident_count['pendingReviewCount'] = $pendingResult[0]->count;

        return $incident_count;
    }

    function getCoordinatorEmailFromForm($leagueId) {
        $user = new User($this->getCoordinatorIdFromForm($leagueId)[0]->coordinator);
        return $user->getEmail();
    }
    function getCoordinatorIdFromForm($leagueId) {
        $sql = "SELECT coordinator FROM leagues WHERE id = :leagueId";
        return static::Query($sql, ["leagueId" => $leagueId]);
    }

    static function getVenueIncidentReport($venueId) {
        $sql = "SELECT id, venueId, date_of_incident, name, contact_number, status, leagueId, is_important 
        FROM incident_report 
        WHERE venueId = :venueId
        ORDER BY id DESC";
        $output = [];
        $incident_reports =  static::Query($sql, ["venueId" => $venueId]);
        foreach ($incident_reports as $incident_report) {
            $output[] = $incident_report->ApiOutput();
        }
        return $output;
    }

    function reportOutput() {
        return [
            "id" => $this->id,
            "venue" => $this->getVenueName(),
            "date_of_incident" => $this->date_of_incident,
            "coordinator" => $this->getCoordinatorName(),
        ];
    }
    static function DailyEmailToCoordinator() {
        $sql = "SELECT id, name, venueId, leagueId, date_of_incident, status, created_at FROM incident_report
                WHERE created_at <= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND status IN ('open', 'Coordinator Review')
                ORDER BY created_at ASC";
        $incident_reports = static::Query($sql);

        foreach ($incident_reports as $incident_report) {
            $injuredName = $incident_report->name;
            $venue = $incident_report->getVenueName();
            $date = date('d-m-Y', strtotime($incident_report->date_of_incident));
            $coordinator = [$incident_report->getCoordinatorEmail() => $incident_report->getCoordinatorName()];
            $body = "Incident Report for {$injuredName} at {$venue} is now overdue and needs to be completed from an incident that happened on {$date}.";
            Email::Issue("Incident Report Reminder > 24 hours prior", array($body), $coordinator);
        }
    }
    static function WeeklyEmailToAdmin() {
        $sql = "SELECT id, venueId, date_of_incident, leagueId,  is_important, status FROM incident_report
                WHERE created_at < DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND status = 'open'
                ORDER BY created_at DESC";
        $incident_reports = static::Query($sql);

        $openReports = [];

        foreach ($incident_reports as $incident_report) {
            $openReports[] = $incident_report->reportOutput();
        }

        $sql = "SELECT id, venueId, date_of_incident, leagueId,  is_important, status
                FROM incident_report
                WHERE created_at < DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND is_important = 1 AND status = 'pending'
                ORDER BY created_at DESC";
        $important_report_sql = static::Query($sql);

        $importantReports = [];
        foreach ($important_report_sql as $incident) {
            $importantReports[] = $incident->reportOutput();
        }
        Email::Issue("Latest L4Y Incident Reports", (new IncidentReport)->ReportMailBody($openReports, $importantReports),  $GLOBALS['config']['admin_mail']);
    }
    function ReportMailBody($OpenReports, $importantReports) {
        $openIncidents = "";
        foreach ($OpenReports as $key => $incident) {

            $openIncidents .=   "<tr style='background-color: #ffffff;'>
                                    <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" .  $key + 1 . "</td>
                                    <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" . $incident['venue'] . "</td>
                                    <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" . $incident['coordinator'] . "</td>
                                    <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" . $incident['date_of_incident'] . "</td>
                                </tr>";
        }
        $importantIncidents = "";
        foreach ($importantReports as $key => $incident) {

            $importantIncidents .=  "<tr style='background-color: #ffffff;'>
                                        <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" .  $key + 1 . "</td>
                                        <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" . $incident['venue'] . "</td>
                                        <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" . $incident['coordinator'] . "</td>
                                        <td style='padding: 12px 15px; border: 1px solid #dddddd;'>" . $incident['date_of_incident'] . "</td>
                                    </tr>";
        }
        $mail = "<body style='font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 20px;'>
                    <h1 style='text-align: center; color: #333;'>Weekly Incident Report</h1>
                    
                    <p>
                        Hi there, <br>Incident reports that were created longer than 7 days ago with the status of 'Open' 
                        (meaning the coordinator hasn't reviewed them) can be found below:
                    </p>

                    <table style='width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 3px rgba(0,0,0,0.1);'>
                        <thead style='background-color: #009879; color: #ffffff; text-align: left;'>
                            <tr>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>ID</th>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>Venue</th>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>Coordinator</th>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>Date of Incident</th>
                            </tr>
                        </thead>
                        <tbody>
                            $openIncidents
                        </tbody>
                    </table>
                    <br/>
                    <p>
                        Incident reports with the status 'Pending Review' and marked as 'Important' by the coordinator can be found below:
                    </p>
                                        
                    <table style='width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 3px rgba(0,0,0,0.1);'>
                        <thead style='background-color: #009879; color: #ffffff; text-align: left;'>
                            <tr>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>ID</th>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>Venue</th>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>Coordinator</th>
                                <th style='padding: 12px 15px; border: 1px solid #dddddd; text-transform: uppercase; font-weight: bold;'>Date of Incident</th>
                            </tr>
                        </thead>
                        <tbody>
                            $importantIncidents
                        </tbody>
                    </table>
                    <br/>

                    <p>You can view all incidents here:  <a href='https://hub.leagues4you.co.uk/Coordinator/incidentReports'>Incident Reports</a><br/>Thanks
                </body>";

        return array($mail);
    }

    public static function getFilteredIncidents($filters) {
        $sql = "SELECT * FROM incident_report WHERE 1=1";
        $params = [];

        // Check if user is admin, if not add coordinator filter
        if (isset($filters['user']) && !$filters['user']->isManager) {
            $sql .= " AND leagueId IN (SELECT id FROM leagues WHERE coordinator = :coordinatorId)";
            $params['coordinatorId'] = $filters['user']->id;
        }

        if (!empty($filters['startDate'])) {
            $sql .= " AND DATE(date_of_incident) >= :startDate";
            $params['startDate'] = date('Y-m-d', strtotime($filters['startDate']));
        }

        if (!empty($filters['endDate'])) {
            $sql .= " AND DATE(date_of_incident) <= :endDate";
            $params['endDate'] = date('Y-m-d', strtotime($filters['endDate']));
        }

        if (!empty($filters['venueId'])) {
            $sql .= " AND venueId = :venueId";
            $params['venueId'] = $filters['venueId'];
        }

        if (!empty($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }

        if (!empty($filters['severity']) && is_array($filters['severity'])) {
            // Create placeholders for each severity value
            $placeholders = [];
            foreach ($filters['severity'] as $index => $value) {
                $paramName = "severity{$index}";
                $placeholders[] = ":{$paramName}";
                $params[$paramName] = $value;
            }

            // Join placeholders with commas
            $placeholdersStr = implode(',', $placeholders);
            $sql .= " AND severity IN ($placeholdersStr)";
        }

        // Get total count for pagination
        $countSql = str_replace("SELECT *", "SELECT COUNT(*) as total", $sql);
        $totalCountResult = static::Query($countSql, $params);

        // Check if result exists and has at least one row
        $totalCount = 0;
        if ($totalCountResult && !empty($totalCountResult) && isset($totalCountResult[0]->total)) {
            $totalCount = $totalCountResult[0]->total;
        }

        // Add pagination
        $itemsPerPage = 25;
        $page = isset($filters['page']) ? (int)$filters['page'] : 1;
        $offset = ($page - 1) * $itemsPerPage;
        $sql .= " ORDER BY id DESC LIMIT $itemsPerPage OFFSET $offset";

        // Get pending review count - filtered by coordinator if not admin
        $filterStatus = $filters['user']->isManager ? 'pending' : 'open';
        $pendingReviewSql = "SELECT COUNT(*) as total FROM incident_report WHERE status = '$filterStatus'";
        $pendingReviewParams = [];

        if (isset($filters['user']) && !$filters['user']->isManager) {
            $pendingReviewSql .= " AND leagueId IN (SELECT id FROM leagues WHERE coordinator = :coordinatorId)";
            $pendingReviewParams['coordinatorId'] = $filters['user']->id;
        }

        $pendingReviewResult = static::Query($pendingReviewSql, $pendingReviewParams);
        $pendingReviewCount = 0;
        if ($pendingReviewResult && !empty($pendingReviewResult) && isset($pendingReviewResult[0]->total)) {
            $pendingReviewCount = $pendingReviewResult[0]->total;
        }

        return [
            'incidents' => static::Query($sql, $params),
            'total' => $totalCount,
            'pendingReviewCount' => $pendingReviewCount
        ];
    }

    /**
     * Get incident report by ID with optional status filter
     * 
     * @param int $id The incident report ID
     * @param string|null $status Optional status filter
     * @return IncidentReport|null The incident report object or null if not found
     */
    public static function getIncidentById($id, $status = null, $user = null) {
        $sql = "SELECT * FROM incident_report WHERE id = :id";
        $params = ['id' => $id];

        if (!empty($status)) {
            $status = str_replace('_', ' ', $status);
            $sql .= " AND status = :status";
            $params['status'] = $status;
        }

        // If user is provided and not admin, restrict to their coordinated leagues
        if ($user && !$user->isManager) {
            $sql .= " AND leagueId IN (SELECT id FROM leagues WHERE coordinator = :coordinatorId)";
            $params['coordinatorId'] = $user->id;
        }

        $incidents = static::Query($sql, $params);

        return !empty($incidents) ? $incidents[0] : null;
    }

    public function getStatus() {
        if ($this->status == 'open') return 'Coordinator Review';
        if ($this->status == 'pending') return 'Final Review Required';
        return $this->status;
    }

    public static function seriousInjuriesList(): array {
        return [
            "Bone fracture (excluding finger, thumb, or toe)",
            "Amputation of arm, hand, finger, thumb, leg, foot, or toe",
            "Permanent loss of sight or reduction of sight",
            "Crush injuries leading to brain damage or internal organ damage",
            "Serious burns",
            "Scalping requiring hospital treatment",
            "Loss of consciousness caused by head injury or asphyxia"
        ];
    }
}
