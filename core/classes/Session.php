<?php

class Session {

    function __construct() {
       $config = $GLOBALS['config'];
        // print_r($config);
        $secure = ($config['system']['protocol'] == "https") ? true : false; // if you only want to receive the cookie over HTTPS
        $httponly = false; // prevent JavaScript access to session cookie
        $samesite = 'lax';
        $domain = $config['system']['domain'];
        // $maxlifetime = 86400*7; # 7 days
        $maxlifetime = 0; # 7 days
        // exit("Domain: $domain");
        if(PHP_VERSION_ID < 70300) {
            session_set_cookie_params($maxlifetime, '/; samesite='.$samesite, $domain, $secure, $httponly);
        } else {
            session_set_cookie_params([
                'lifetime' => $maxlifetime,
                'path' => '/',
                'domain' => $domain,
                'secure' => $secure,
                'httponly' => $httponly,
                'samesite' => $samesite
            ]);
        }
        session_start();
    }

}