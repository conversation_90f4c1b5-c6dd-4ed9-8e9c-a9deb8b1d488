<?php

namespace PDF;
use Fpdf\Fpdf;

include ("/var/www/html/core/vendor/fpdf/fpdf/src/Fpdf/makefont/makefont.php");

class SeasonInvoice2 extends Fpdf {

    protected $fontSize = 10, $lineHeight = 6, $margin = 10;
    protected $teamSeason;
    protected $team, $season;
    protected $teamID;
    public $folder, $filename;

    protected $font;
    protected $venue;
    protected $subtotal;

    function __construct(\TeamSeason $teamSeason, String $action = "F") {

        $this->font = "Arial";
        
        parent::__construct();
        
        $this->teamSeason = $teamSeason;
        $this->teamSeason->getSeason();
        $this->teamSeason->getLeague();
        $this->teamSeason->getTeam();
        $this->treasurer = $this->teamSeason->team->getTeamTreasurer();
        
        $this->Build();

        $this->folder = CORE_FOLDER.DIRECTORY_SEPARATOR."invoices".DIRECTORY_SEPARATOR;
        $this->filename = $this->teamSeason->id.".pdf";
        # I = "Inline", D = "Download" or F = "File"
        $this->Output($action,$this->folder.$this->filename);
    }

    function __toString() {
        return "{$this->folder}{$this->filename}";
    }

    function Build() {
        $this->AliasNbPages();
        $this->AddPage();
        $this->SetMargins($this->margin*1.75,$this->margin,$this->margin);
        
        $this->SetY(20);
        $this->SetFont($this->font,'B',$this->fontSize*2);
        $this->SetTextColor(255,255,255);
        $this->MultiCell(100,$this->lineHeight*2,"Invoice to:");
        $this->SetY(35);
        $this->SetFont($this->font,'B',$this->fontSize*1.6);
        $this->MultiCell(100,$this->lineHeight*1.3,$this->treasurer);
        $this->SetFont($this->font,'',$this->fontSize*1.3);
        $address = [];
        if ($this->treasurer->line1) $address[] = $this->treasurer->line1;
        if ($this->treasurer->town) $address[] = $this->treasurer->town;
        if ($this->treasurer->postcode) $address[] = $this->treasurer->postcode;
        $this->MultiCell(100,$this->lineHeight,implode("\n",$address));

        $this->SetY(70);
        $this->MultiCell(100,$this->lineHeight,implode("\n",[$this->teamSeason->team->name,$this->teamSeason->league->name,$this->teamSeason->season->name]));

        $this->SetTextColor(23,30,55);
        $this->SetXY(158, 30);
        $this->SetFont($this->font,'B',$this->fontSize*3);
        $this->MultiCell(100,$this->lineHeight*2,"invoice.");

        $this->SetXY(148, 40);
        $this->SetFont($this->font,'B',$this->fontSize*1.5);
        $this->Cell(50,$this->lineHeight*2,"Invoice ID",null,1,"R");

        $this->SetXY(148, 47);
        $this->SetFont($this->font,'',$this->fontSize*1.5);
        $this->Cell(50,$this->lineHeight*2,"#{$this->teamSeason->id}",null,1,"R");

        $this->SetXY(148, 60);
        $this->SetFont($this->font,'B',$this->fontSize*1.5);
        $this->Cell(50,$this->lineHeight*2,"Invoice Date",null,1,"R");

        $this->SetXY(148, 67);
        $this->SetFont($this->font,'',$this->fontSize*1.5);
        $this->Cell(50,$this->lineHeight*2,date('jS M Y', strtotime($this->teamSeason->season->getLaunchDate())),null,1,"R");

        $this->SetXY(148, 80);
        $this->SetFont($this->font,'B',$this->fontSize*1.5);
        $this->Cell(50,$this->lineHeight*2,"Team ID",null,1,"R");

        $this->SetXY(148, 87);
        $this->SetFont($this->font,'',$this->fontSize*1.5);
        $this->Cell(50,$this->lineHeight*2,"#{$this->teamSeason->teamID}",null,1,"R");

        $this->SetFillColor(23,30,55); $this->SetDrawColor(23,30,55);
        $this->Rect(0, 100, 210, 30,'DF');
        $this->SetTextColor(255,255,255);
        $this->SetXY(10,110);
        $this->SetFont($this->font,'B',$this->fontSize*2);
        $this->SetDrawColor(255,255,255);
        $this->Cell(40,$this->lineHeight*2,"Date",null);
        $this->Cell(110,$this->lineHeight*2,"Fixture",null);
        $this->Cell(40,$this->lineHeight*2,"Amount",null,1,"R");

        $schedules = \Schedule::TeamSeason($this->teamSeason);

        $this->SetTextColor(23,30,55);
        $this->SetFont($this->font,'',$this->fontSize*1.1);
        $this->SetXY(10,135); 
        $this->SetLeftMargin(10);
        foreach ($schedules as $schedule) {
            if (!$this->venue) $this->venue = $schedule->venue;
            $this->Cell(40,$this->lineHeight*1.1,date('d/m/Y',strtotime($schedule->booking->startDate)),null);
            $this->Cell(110,$this->lineHeight*1.1,$schedule->fixture,null);
            $this->Cell(40,$this->lineHeight*1.1,$this->teamSeason->season->fixtureCharge,null,1,"R");
            $this->subtotal += $this->teamSeason->season->fixtureCharge;
        }
    }
    
    function Header() { 
        /* 25% of Screen - Half Blue / Half White */
        $this->SetFillColor(23,30,55);
        $this->Rect(0, 0, 105, 100,'DF');
        $this->Image(CORE_FOLDER. DIRECTORY_SEPARATOR . "assets" . DIRECTORY_SEPARATOR . "l4y_logo.png",135,10);
    }
    function Footer() {
        $footerText = [
            "leagues4you limited",
            "registered company in england and wales",
            "Company number 08049828"
        ];
        $this->SetFillColor(23,30,55);
        $this->SetDrawColor(23,30,55);
        $this->Rect(0, 240, 100, 70,'DF');
        $this->SetTextColor(255,255,255);
        $this->SetFont($this->font,'',$this->fontSize);
        $this->SetXY(10,270);
        $this->MultiCell(70,$this->lineHeight-1,implode("\n",$footerText),null,"L");
        

        $this->SetFont($this->font,'',$this->fontSize);
        $this->SetXY(165,250);
        $this->SetTextColor(23,30,55);
        $this->Cell(30,$this->lineHeight,"All fixtures will take place at {$this->venue}",null,1,"R");

        
        $this->SetTextColor(255,255,255);
        $this->Rect(100, 260, 110, 15,'DF');
        $this->SetFont($this->font,'B',$this->fontSize*1.5);
        $this->SetXY(170,265);
        $this->Cell(30,$this->lineHeight,"Grand Total: " . iconv('utf-8', 'cp1252', "£") . number_format($this->subtotal,2),null,1,"R");

        $this->SetFont($this->font,'',$this->fontSize);
        $this->SetXY(160,280);
        $this->SetTextColor(23,30,55);
        $this->Cell(30,$this->lineHeight,"Payment is due on or before date of fixture.",null,1,"R");
        
    }

}
