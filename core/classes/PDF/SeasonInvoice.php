<?php

namespace PDF;
use Fpdf\Fpdf;

class SeasonInvoice extends Fpdf {

    protected $fontSize = 10, $lineHeight = 6, $margin = 10;
    protected $teamSeason;
    protected $team, $season;
    protected $teamID;
    public $folder, $filename;

    function __construct(\TeamSeason $teamSeason, String $action = "F") {
        /**
            * Actions are
            * I = "Inline" : Send to Browser
            * D = "Download" : Force download
            * F = "File" : As a local file
        */
        parent::__construct();
        
        $this->teamSeason = $teamSeason;
        $this->season = new \Season($this->teamSeason->seasonID);
        if ($this->season->statusID != 1) return;
        $this->divisionCount = ($this->season->getDivisionCount() && $this->season->getDivisionCount() > 1) ? $this->season->getDivisionCount() : 1;
        $this->fixtureCount = (($this->season->teamCount() - 1) / $this->divisionCount) * $this->season->rounds;
        // $this->fixtureCount = ($this->teamSeason->FixtureCount() * $this->season->getRounds());
        if (!$this->fixtureCount) return;
        $this->fixtureCharge = $this->season->getFixtureCharge();
        $this->team = new \Team($teamSeason->teamID);
        $this->league = new \League($this->season->leagueID);
        $this->treasurer = new \User($this->team->treasurerID);
        
        $this->Build();
        $this->folder = APP_ROOT.DIRECTORY_SEPARATOR."invoices".DIRECTORY_SEPARATOR;

        $this->filename = $this->league->id . "_" . $this->season->id . "_" . $this->team->id.".pdf";
        // if (!file_exists($this->folder.$this->filename)) 
        $this->Output($action,$this->folder.$this->filename);
    }
    function __toString() {
        return "{$this->folder}{$this->filename}";
    }
    function Build() {
        $this->AliasNbPages();
        $this->AddPage();
        $this->SetMargins($this->margin,$this->margin,$this->margin);
        $this->SetFont('Arial','',$this->fontSize);
        $this->Ln($this->lineHeight);

        $invoiceOrganisation = [
            "leagues4you Limited",
            "Grove Ln",
            "Westend",
            "Stonehouse",
            "GL10 3SL"
        ];
        
        $y = $this->GetY();
        $this->MultiCell(100,$this->lineHeight,implode("\n",$invoiceOrganisation));

        $this->SetXY(105,$y);
        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"Invoice",null,null,'L');
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(0,$this->lineHeight,"Invoice SI-{$this->league->id}-{$this->season->id}-{$this->team->id}",null,1);

        $this->SetX(105);
        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"Date",null,null,'L');
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(100,$this->lineHeight,date('d/m/Y',strtotime($this->season->getStartDate())),null,1);

        $this->SetX(105);
        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"Team",null,null,'L');
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(100,$this->lineHeight,preg_replace('/[\x00-\x1F\x7F]/u', '', $this->team),null,1);

        $this->SetX(105);
        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"Treasurer",null,null,'L');
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(0,$this->lineHeight,$this->treasurer,null,1);

        $this->SetX(105);
        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"League",null,null,'L');
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(0,$this->lineHeight,$this->league,null,1);

        $this->SetX(105);
        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"Season",null,null,'L');
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(0,$this->lineHeight,$this->season,null,1);
        
        $this->Ln($this->lineHeight);
        $this->Line($this->margin,$this->GetY(),200,$this->GetY());
        $this->Ln($this->lineHeight);

        $this->SetFont('Arial','B',$this->fontSize);
        $this->Cell(30,$this->lineHeight,"Qty");
        $this->Cell(100,$this->lineHeight,"Description");
        $this->Cell(30,$this->lineHeight,"Unit",null,null,"R");
        $this->Cell(30,$this->lineHeight,"Total",null,1,"R");

        // $fixtureCount = $this->fixtureCount/$this->divisionCount;
        $this->SetFont('Arial','',$this->fontSize);
        $this->Cell(30,$this->lineHeight,$this->fixtureCount);
        $this->Cell(100,$this->lineHeight,"Fixtures in the Season");
        $this->Cell(30,$this->lineHeight,$this->fixtureCharge,null,null,"R");
        $this->Cell(30,$this->lineHeight,number_format($this->fixtureCount * $this->fixtureCharge,2),null,1,"R");
        
        $this->SetXY($this->margin,240);
        $this->SetFont('Arial','',$this->fontSize-2);
        $this->SmallPrint();
    }
    function SmallPrint() {
        $wording = [
            "This invoice details the total cost of the season your team has entered", "Payments will be taken in instalments electronically from the card you have registered each week on the day your team has a fixture", 
            "As club captain/treasurer you agree that when you make these payments you are making a commitment to participate for a minimum of ten consecutive weeks in the league, you automatically become our members along with other players in the league who are also our members, and that as part of our wider club or membership all our members have exclusivity on the pitch/court we have hired for the duration of hire and that non members have no right to participate on the pitch during that time"
        ];
        $this->MultiCell(0,($this->lineHeight-1),implode(". ",$wording));
    }
    function Header() { 
        $this->Image(APP_ROOT. DIRECTORY_SEPARATOR . "assets" . DIRECTORY_SEPARATOR . "l4y_logo.png",10,10);
        $this->SetY(30);
    }
    function Footer() {
        $this->SetY(-20);
        $this->SetFont('Arial','I',8);
        $this->Cell(0,$this->lineHeight-1,"Leagues 4 You Ltd. The Old School House, 75a Jacobs Wells Road, Bristol, England, BS8 1DJ. Registered in England and Wales No: 08049828",null,1,"C");
        $this->Cell(0,10,'Page '.$this->PageNo().'/{nb}',0,0,'C');
    }

}