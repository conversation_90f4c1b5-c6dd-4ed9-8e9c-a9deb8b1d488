<?php

class Booking extends Base {

    protected $dbTable = "bookings";

    protected $leagueID;
    protected $venueID;
    protected $pitchCourt;
    protected $startDate;
    protected $startTime, $duration, $endTime;
    protected $cost;
    protected $hourlyRate;
    protected $creditValue;
    protected $weeks;
    protected $processedWeeks;

    public $nextSlot;
    public $usage = [];
    protected $fixtures = [];

    protected $dbFields = ["leagueID", "venueID", "pitchCourt", "startDate","startTime","duration","hourlyRate","creditValue","weeks","processedWeeks","deleted"];

    function __construct(Int $id = null) { parent::__construct($id);}
    function __toString() {
        return substr($this->startTime,0,5)." ".date('d/m/Y',strtotime($this->startDate))." " . $this->pitchCourt . " " . new Venue($this->venueID);
    }
    function Save() {
        // if (!$this->weeks) $this->weeks = 0;
        if (!$this->endTime) $this->endTime = date('H:i:s', strtotime("{+ $this->duration} minutes", strtotime($this->startTime)));
        $this->cost = $this->getCost();
        if (!$this->weeks) $this->weeks = null;
        $rlt = parent::Save();
        if ($this->weeks && !$this->processedWeeks) static::ProcessWeeks($this);
        Logging::Add(__CLASS__ . " {$this} ({$this->id}) saved by " . User::AuthUser());
        return $rlt;
    }
    function ApiOutput() {
        return $this->getNextSlot() . " " . $this->getStartDate('d/m/Y') . " " . new Venue($this->venueID) . ", " . $this->pitchCourt;
    }
    /* Getters */
    function getLeagueID() { return $this->leagueID;}
    function getVenueID() { return $this->venueID;}
    function getVenueName() {
        $venue = new Venue($this->venueID);
        return $venue->__toString();
    }

    function Fixtures () {
        $this->fixtures = Fixture::Booking($this);
        return $this->fixtures;
    }

    function getPitchCourt() { return $this->pitchCourt;}
    function getStartTime() { return substr($this->startTime,0,5);}
    function getStartDate(String $format=null) { return ($format) ? date($format,strtotime($this->startDate)) : $this->startDate;}
    function getWeeks() { return $this->weeks;}
    function getDuration() { return $this->duration;}
    function getHourlyRate() { return $this->hourlyRate;}
    function getCost() { return ($this->hourlyRate && $this->duration) ? $this->hourlyRate * ($this->duration / 60) : 0;}
    function getUsage() {
        if (!$this->usage) $this->usage = Schedule::BookingUsage($this);
        return $this->usage;
    }

    function getNextSlot($fixtureDuration = 40) { 
        $next = Schedule::NextSlot($this->id); if (!$next) $next = $this->startTime;
        if (!$this->endTime) $this->endTime = date('H:i:s', strtotime("{+ $this->duration} minutes", strtotime($this->startTime)));
        // Is there enough time between $next and endTime to allow a fixture?
        // if (strtotime($this->endTime) < strtotime("{+ $fixtureDuration} minutes", strtotime($next))) return;
        return substr($next,0,5);
    }

    /* Setters */
    function setLeagueID(Int $leagueID) { $this->leagueID = $leagueID;}
    function setStartDate($startDate) { $this->startDate = $startDate;}
    /* Base Overides */
    function __clone() {
        parent::__clone();
        $this->weeks = null;
    }

    function getVenue() {
        if (!$this->venue) $this->venue = new Venue($this->venueID);
        return $this->venue;
    }

    function getLeague() {
        return new League($this->leagueID);
    }

    function Map ($segment = 5) {
        $slotBlocks = $this->duration / $segment;
        $slotArray = array_fill(0, $slotBlocks, ["avail" => true, "contigious" => 0]);
        $schedule = Schedule::Slots($this);
        if ($schedule) {
            foreach ($schedule as $s) {
                $startIndex = $s->offset / 5;
                $endIndex = (($s->offset + $s->duration) / 5) - 1;
                for ($i = $startIndex; $i <= $endIndex; $i++) $slotArray[$i]['avail'] = false;
            }
        }
        $startPoint = $this->startTime;
        $contigiousMins = 0;
        $backMarker = 0;
        foreach ($slotArray as $index => $slot) {
            $minutes = 5 * $index;
            $time = date('H:i',strtotime("+ $minutes minutes",strtotime($startPoint)));
            if ($slot['avail'] === true) {
                if (!$slotArray[$index]['contigious']) $slotArray[$index]['contigious'] = 5;
                if (is_numeric($backMarker)) {
                    for($x=$backMarker;$x<$index;$x++) $slotArray[$x]['contigious'] += 5;
                } else $backMarker = $index;
            } else $backMarker = null;
        }
        return $slotArray;
    }

    function Slots() {
        $bookingMap = $this->Map();
        $return = [];
        foreach ($bookingMap as $key => $bm) {
            // if ($bm['contigious']>=30) {
                $return[$key] = [
                    "offset" => $key * 5,
                    "availableMinutes" => $bm['contigious'], 
                    "startTime" => date('H:i',strtotime("+".($key*5)." minutes",strtotime($this->startTime)))];
            // }
        }
        return $return;
    }

    /* Statics */
    static function Season (Season $season) {
        $sql = "SELECT `bookings`.* FROM `bookings` LEFT JOIN `leagues` ON `bookings`.`leagueID` = `leagues`.`id` LEFT JOIN `seasons` ON `leagues`.`id` = `seasons`.`leagueID` WHERE `seasons`.`id` = {$season->id} AND `bookings`.`startDate` >= '" . $season->getStartDate() . "' AND `bookings`.`deleted` IS NULL ORDER BY `bookings`.`startDate`, `bookings`.`startTime`";
        $db = new Db($sql); if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $b = new static();
            $b->Load($r);
           ($return) ? array_push($return,$b) : $return[] = $b;
        }
        return $return;
    }

    static function SeasonAvailability (Season $season) {
        $sql = "SELECT `bookings`.* FROM `bookings` LEFT JOIN `leagues` ON `bookings`.`leagueID` = `leagues`.`id` LEFT JOIN `seasons` ON `leagues`.`id` = `seasons`.`leagueID` WHERE `seasons`.`id` = {$season->id} AND `bookings`.`startDate` >= '".date('Y-m-d')."' AND `bookings`.`deleted` IS NULL ORDER BY `bookings`.`startDate`, `bookings`.`startTime`";
        $db = new Db($sql); if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $b = new static();
            $b->Load($r);
            $b->getUsage();
            // if ($b->usage['available']<$season->duration) continue;
           ($return) ? array_push($return,$b) : $return[] = $b;
        }
        return $return;
    }

    static function SeasonOptions (Season $season) {
        /* Order By startDate important for Fixture date determination*/
        if (!($leagueID = $season->getLeagueID()) || !$season->getStartBookingID()) return "No Season start booking"; # No Season Start Booking
        $startBooking = new static($season->getStartBookingID());
        $startDate = $startBooking->startDate;
        // $sql = "SELECT `id` FROM `bookings` WHERE `leagueID` = $leagueID AND `startDate` >= :startDate AND `deleted` IS NULL ORDER BY `startDate` ASC";
        /*
            - Get Season Divisions
            - Foreach division - check Fixture::getResults (Division $division)
            - if Fixtures exist, Season has already begun
            - StartDate adjusted to Monday following date of last played fixture
        */
        $divisions = Division::forSeason($season); if (!$divisions) return "No Divisions is Season";
        foreach ($divisions as $division) {
            if ($results = Fixture::getResults ($division)) {
                // Some fixtures already played
                foreach ($results as $result) {
                    $compareDate =  date('Y-m-d',strtotime(str_replace("/","-",$result['fixtureDate'])));
                    // $compareDate =  $result['fixtureDate'];
                    // echo "Checking $compareDate against $latestDate<br>";
                    if ($compareDate > $startDate) {
                        $startDate = date('Y-m-d',strtotime("+1 day",strtotime($compareDate)));
                        // Must be a Monday (for a fresh week)
                        while(date('N',strtotime($startDate)) != 1) {
                            $startDate = date('Y-m-d',strtotime("+1 day",strtotime($startDate)));
                        }
                    } 
                }
            }
        }
        $sql = "SELECT `bookings`.* FROM `bookings` WHERE `bookings`.`leagueID` = $leagueID AND `bookings`.`startDate` >= :startDate AND `bookings`.`deleted` IS NULL ORDER BY `bookings`.`startDate` ASC";
        $db = new Db($sql, ["startDate" => $startDate]);
        if (!$db->rows) return $startBooking->id; # No Bookings for that League from that date.
        $return = [];
        $weekCounter = 0;
        $startDate = $endDate = null;
        foreach ($db->rows as $r) {
            $booking = new static();
            $booking->Load($r);
            $booking->getUsage();
            if (!$endDate) {
                $startDate = $booking->startDate;
                $endDate = date('Y-m-d',strtotime("+6 days",strtotime($startDate)));
            }elseif ($booking->startDate > $endDate) {
                $weekCounter ++;
                $startDate = $booking->startDate;
                $endDate = date('Y-m-d',strtotime("+6 days",strtotime($startDate)));
            }
            while(date('N',strtotime($endDate)) != 7) {
                $endDate = date('Y-m-d',strtotime("-1 day",strtotime($endDate)));
            }
            if (!isset($return[$weekCounter])) $return[$weekCounter]['available'] = 0;
            $return[$weekCounter]['available'] += floor($booking->usage['available']/$season->getDuration());
            $return[$weekCounter]['bookings'][] = $booking;
        }
        return $return;
    }

    static function SeasonBookings (Season $season) {
        if (!$season->startBookingID) return "No Season start booking"; # No Season Start Booking
        $startBooking = new static($season->startBookingID);
        $startDate = $startBooking->startDate;
        $sql = "SELECT `bookings`.* FROM `bookings` WHERE `bookings`.`leagueID` = {$season->leagueID} AND `bookings`.`startDate` >= :startDate AND `bookings`.`deleted` IS NULL ORDER BY `bookings`.`startDate` ASC";
        $db = new Db($sql, ["startDate" => $startDate]);
        if (!$db->rows) return $startBooking->id; # No Bookings for that League from that date.
        $return = [];
        foreach ($db->rows as $r) {
            $booking = new static();
            $booking->Load($r);
            $slots = $booking->slots();
            if (!$slots) continue;
            $booking->getUsage();
            $mondayDate = $booking->startDate;
            while(date('N',strtotime($mondayDate)) != 1) {
                $mondayDate = date('Y-m-d',strtotime("-1 day",strtotime($mondayDate)));
            }
            foreach ($slots as $index => $slot) {
                if ($slot['availableMinutes'] < $season->duration) unset($slots[$index]);
            }
            $return[$mondayDate][] = [
                "booking" => $booking,
                "slots" => $slots
            ];
        }   
        return $return;
    }

    static function forLeague (League $league, String $fromDate = null) {
        if (!$league->getID()) return;
        $sql = "SELECT `id` FROM `bookings` WHERE `leagueID` = " . $league->getID() . "  AND `deleted` IS NULL";
        // if ($fromDate) $sql .= " AND `startDate` >= '$fromDate'";
        $sql .= " ORDER BY `startDate`, `startTime`";
        $db = new Db($sql); if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) $return[] = new static($r['id']);
        return $return;
    }

    static function forSeason (Season $season) {
        $sql = "SELECT * FROM `bookings` WHERE `leagueID` = " . $season->leagueID . "  AND `deleted` IS NULL ORDER BY `startDate` ASC";
        return static::Query($sql);
    }
    
    static function Open (Season $season) {
        // $sql = "SELECT bookings.*, COALESCE(SUM(schedule.duration),0) AS used FROM `bookings` LEFT JOIN schedule ON bookings.id = schedule.bookingID WHERE bookings.leagueID = :leagueID AND bookings.deleted IS NULL AND bookings.startDate >= :startDate AND schedule.deleted IS NULL";
        $sql = "SELECT bookings.*, COALESCE(calcSched.used,0) AS used FROM bookings
        LEFT JOIN ( SELECT bookingID, SUM(duration) AS used FROM schedule
                    WHERE schedule.deleted IS NULL
                    GROUP BY schedule.bookingID
                  ) calcSched ON bookings.id = calcSched.bookingID
        WHERE bookings.leagueID = :leagueID
        AND bookings.deleted IS NULL
        AND bookings.startDate >= :startDate
        ORDER BY bookings.startDate DESC";
        $return = [];
        if (($rlt = static::Query($sql,["leagueID" => $season->leagueID,"startDate" => $season->launchDate]))) {
            foreach ($rlt as $r) {
                if ($r->duration > $r->used) $return[] = $r;
            }
        }
        return $return;
    }

    static function ProcessWeeks(Booking $booking) {
        if ($booking->processedWeeks || !$booking->weeks) return Messaging\Add("Can't Process a booking");
        $baselineDate = $booking->getStartDate();
        for ($i = 1; ($i <= $booking->getWeeks()-1); $i++) {
            $b = clone($booking);
            $baselineDate = date('Y-m-d H:i:s',strtotime("+1 week", strtotime($baselineDate)));
            $b->setStartDate($baselineDate);
            // $b->Load($data);
            $b->Save();
        }
        new Db("UPDATE `bookings` SET `processedWeeks` = NOW() WHERE `id` = " . $booking->getID());
    }

    static function Duplicate(Int $id) {
        $new = parent::Duplicate($id);
        $new->startDate = date('Y-m-d',strtotime("+7 days", strtotime($new->startDate)));
        $new->Save();
    }

    static function Status (Season $season) {
        $periods = static::SeasonOptions($season);
        if (!$periods || !is_array($periods)) return "No bookings available to estimate the fixture season";
        $permissibleTeams = $requiredWeeks = $startDate = $endDate = null;
        $possibleOptions = [];
        foreach ($periods as $key => $bookings) {
            $periodFixtures = 0;
            foreach ($bookings['bookings'] as $booking) {
                $bookingFixtures = floor($booking->getDuration() / $season->getDuration());
                $periodFixtures += $bookingFixtures;
            }
            $periodTeams = $periodFixtures * 2;
            for ($i=4;$i<=$periodTeams;$i++) {
                if (!isset($possibleOptions[$i])) $possibleOptions[$i] = 0;
                $possibleOptions[$i]++;
            }
            if (!isset($possibleOptions[$periodTeams])) $possibleOptions[$periodTeams] = 0;
        }
        if (!$possibleOptions) { return "No possible teams / fixtures";}
        krsort($possibleOptions);
        // LeagueNote::Add(new League($season->getLeagueID()), print_r($possibleOptions,true));
        $maxTeams = key($possibleOptions);
        if ($maxTeams < 4) return "Need at least 4 teams to create a Season";
        $fixturesPerRound = $maxTeams -1; 
        $maxWeeks = end($possibleOptions);
        $maxRounds = floor($maxWeeks / $fixturesPerRound);
        // return print_r($max,true);
        return "Target $maxTeams teams playing up to $maxRounds rounds";

        foreach ($possibleOptions as $possibleTeams => $possibleWeeks) {
            $minWeeks = $possibleTeams -1;
            $log[] = ($possibleWeeks >= $possibleTeams ) ? "$possibleTeams team season is possible<br>" :"$possibleTeams team season requires " . ($possibleTeams - $possibleWeeks)." extra bookings weeks<br>";
        }
        return $log;
    }

    static function CostReport (String $startDate, String $endDate) {
        $sql = "SELECT `venueID`, SUM(`duration`/60 * `hourlyRate`) AS `total` FROM `bookings` WHERE `startDate` >= :startDate AND `startDate` <= :endDate GROUP BY `venueID`";
        $db = new Db($sql,["startDate" => $startDate, "endDate" => $endDate]);
        if ($db->rows) return $db->rows;
    }

    static function SeasonCost(Season $season) {
        $sql = "SELECT DISTINCT `bookingID` FROM `schedule` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `divisions`.`seasonID` = {$season->id}";
        $db = new Db($sql); if (!$db->rows) return;
        $totalCost = 0;
        foreach ($db->rows as $r) {
            $b = new static($r['bookingID']);
            $totalCost += $b->getCost();
        }
        return $totalCost;
    }

    static function SeasonCount (Season $season) {
        $endDate = static::SeasonEndDate($season);
        $sql = "SELECT SUM(`duration`) AS `totalMinutes` FROM `bookings` WHERE `leagueID` = {$season->getLeagueID()} AND `deleted` IS NULL AND `startDate` >= '{$season->getStartDate()}' AND `startDate` <= '{$endDate}'";
        $db = new Db($sql);
        return (isset($db->rows[0]['totalMinutes'])) ? $db->rows[0]['totalMinutes'] : null;
    }

    static function CostsBetween (String $startDate, String $endDate, League $league) {
        $sql = "SELECT SUM(`duration`) AS `minutes`, SUM((`duration`)/60 * `hourlyRate`) AS `cost` FROM `bookings` WHERE `leagueID` = :leagueID AND `startDate` >= :startDate AND `startDate` <= :endDate";
        $db = new Db($sql,["leagueID" => $league->id, "startDate" => $startDate, "endDate" => $endDate]);
        return $db->rows[0];
    }

    static function Schedule (Schedule $schedule) {
        $sql = "SELECT * FROM `bookings` WHERE `id` = {$schedule->bookingID}";
        $db = new Db($sql);
        if (!$db->rows) return;
        $b = new static();
        $b->Load($db->rows[0]);
        return $b;
    }

    static function LastScheduledBooking(Season $season) {
        $sql = "SELECT `bookings`.* FROM `schedule` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `divisions`.`seasonID` = {$season->id} ORDER BY `bookings`.`startDate` DESC LIMIT 1";
        $db = new Db($sql);
        if ($db->rows[0]) {
            $booking = new static();
            $booking->Load($db->rows[0]);
            return $booking;
        }
    }

    static function SeasonEndDate (Season $season) {
        $sql = "SELECT `bookings`.`startDate` AS `endDate` FROM `schedule` LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id` LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id` LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id` WHERE `divisions`.`seasonID` = {$season->id} ORDER BY `bookings`.`startDate` DESC LIMIT 1";
        $db = new Db($sql);
        return (isset($db->rows[0]['endDate']) && $db->rows[0]['endDate']) ? $db->rows[0]['endDate'] : null;
    }

    static function Calendar () {
        $sql = "SELECT `bookings`.`id` AS `bookingID`, `bookings`.`pitchCourt`, `venues`.`id`, `venues`.`name`, `venues`.`address1`, `venues`.`postcode`, `venues`.`lat`, `venues`.`lng`, `bookings`.`startDate` AS `bookingStartDate`, `bookings`.`startTime` AS `bookingStartTime`, `schedule`.`fixtureID`, `schedule`.`offset`, `schedule`.`startTime` AS `scheduleStartTime`, `bookings`.`duration` FROM `bookings` LEFT JOIN `venues` ON `bookings`.`venueID` = `venues`.`id` LEFT JOIN `schedule` ON `bookings`.`id` = `schedule`.`bookingID` WHERE `bookings`.`deleted` IS NULL AND `bookings`.`startDate` >= '".date('Y-m-d')."' AND `schedule`.`deleted` IS NULL ORDER BY `bookings`.`startDate` ASC, `bookings`.`startTime` ASC";
        $db = new Db($sql);

        if (!$db->rows) return;
        foreach ($db->rows as $r) {
            $index = $r['bookingID'].date('Ymd',strtotime($r['bookingStartDate']));
            if (isset($fixtures[$index])) continue;
            $start = date("Ymd\THis\Z", strtotime($r['bookingStartDate']." " . $r['bookingStartTime']));
            $end = date("Ymd\THis\Z", strtotime("+ {$r['duration']} minutes", strtotime($start)));
            // $end = date("Ymd\THis\Z", strtotime("+ {$r['duration']} minutes"),strtotime($start));
            $fixtures[$index] = [
                "bookingID" => $r['bookingID'],
                "dtstart" => $start,
                "dtend" => $end,
                "summary" => $r['name'],
                "location" => $r['address1'].", ".$r['postcode'],
                "geo" => "{$r['lat']};{$r['lng']}"
            ];
        }

        $delimiter = "\r\n";
        $output = <<<HTML
        BEGIN:VCALENDAR
        VERSION:2.0
        PRODID:-//a2ztech.co.uk//LEAGUES4YOU //EN"
        CALSCALE:GREGORIAN
        METHOD:PUBLISH
        HTML;
        $stamp = date("Ymd\THis\Z");
        foreach ($fixtures as $detail) {
                extract($detail);
                $output .= <<< HTML

                BEGIN:VEVENT
                UID:$bookingID
                DTSTAMP:$stamp
                DTSTART:$dtstart
                DTEND:$dtend
                SUMMARY:$summary
                DESCRIPTION:$summary
                LOCATION:$location
                GEO:$geo
                SEQUENCE:0
                STATUS:CONFIRMED
                TRANSP:OPAQUE
                END:VEVENT
                HTML;
        }
        $output .= <<< HTML

        END:VCALENDAR
        HTML;
        return $output;
    }

    // OVERRIDE
    static function Archive (Int $id) {
        $booking = new static($id);
        $slots = Schedule::Slots($booking);
        if ($slots) return "Cannot remove that booking - it has Fixtures allocated";
        $sql = "UPDATE `bookings` SET `deleted` = NOW() WHERE `id` = :id";
        $db = new Db($sql,["id" => $id]);
        return ($db->errors) ? implode(". ", $db->errors) : true;
    }

    static function Report (Array $monthYear) {
        $startDate = date('Y-m-d',strtotime("{$monthYear['year']}-{$monthYear['month']}-01"));
        $endDate = date('Y-m-t',strtotime("{$monthYear['year']}-{$monthYear['month']}-01"));
        // $sql = "SELECT `venueID`, SUM(`duration`/60 * `hourlyRate`) AS `total` FROM `bookings` WHERE `startDate` >= '$startDate' AND `startDate` <= '$endDate'  AND `bookings`.`deleted` IS NULL GROUP BY `venueID`";
        $sql = "SELECT `bookings`.`venueID`, SUM((`bookings`.`duration`/60) * `bookings`.`hourlyRate`) AS `total`,  COALESCE(SUM(`creditValue`), 0) AS `creditValue` FROM `bookings` LEFT JOIN `venues` ON `bookings`.`venueID` = `venues`.`id` WHERE `bookings`.`startDate` >= '$startDate' AND `bookings`.`startDate` <= '$endDate' AND `bookings`.`deleted` IS NULL GROUP BY `bookings`.`venueID` ORDER BY `venues`.`name`";
        // echo $sql;
        $rlt = new Db($sql);
        return $rlt->rows;
    }

    static function Orphans () {
        return static::Query("SELECT * FROM `bookings` WHERE `deleted` IS NULL AND `id` NOT IN (SELECT DISTINCT `bookingID` FROM `schedule` WHERE `deleted` IS NULL)ORDER BY `startDate`");
    }

    static function SeasonVenues (Season $season) {
        /* Gets the Venue IDs from the Season's Schedule by way of Divisions linked to Bookings */
        $sql = "SELECT DISTINCT(`venueID`) FROM `bookings` WHERE `leagueID` = {$season->leagueID} AND `startDate` >= (SELECT `startDate` FROM `bookings` WHERE `id` = {$season->startBookingID})";
        $rlt = new Db($sql);
        $return = [];
        if ($rlt->rows) {
            foreach ($rlt->rows as $r) $return[] = $r['venueID'];
        }
        return $return;
    }

    static function VenuePeriod (Venue $venue, Int $year, Int $month) {
        $sql = "SELECT SUM((duration / 60) * hourlyRate) AS totalCost FROM bookings WHERE deleted IS NULL AND venueID = :venueID AND startDate >= :startDate AND startDate <= :endDate";
        $rlt = Database::Execute($sql,[
            'venueID' => $venue->id,
            'startDate' => "{$year}-{$month}-01",
            'endDate' => date('Y-m-t',strtotime("{$year}-{$month}-01"))
        ]);
        return ($rlt['error']) ? $rlt['error']['message'] : (float)$rlt['success']['rows'][0]['totalCost'];
    }

    static function Venues (Int $year, Int $month) {
        $sql = "SELECT bookings.`venueID`, SUM(bookings.`duration`/60 * bookings.`hourlyRate`) AS `total`, venues.name AS venueName, venues.coordinatorID, users.email AS coordinatorEmail, CONCAT_WS(' ',users.firstname,users.lastname) AS coordinatorName FROM `bookings` LEFT JOIN venues ON bookings.venueID = venues.id LEFT JOIN users ON venues.coordinatorID = users.id WHERE bookings.deleted IS NULL AND bookings.`startDate` >= :startDate AND bookings.`startDate` <= :endDate GROUP BY `venueID` ORDER BY venues.name ASC";
        $rlt = Database::Execute($sql,[
            'startDate' => "{$year}-{$month}-01",
            'endDate' => date('Y-m-t',strtotime("{$year}-{$month}-01"))
        ]);
        return $rlt['success']['rows'] ?? [];
    }

}