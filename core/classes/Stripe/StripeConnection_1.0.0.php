<?php

class StripeConnection {

    public $publicKey;
    protected $secretKey;

    protected $mode;
    protected $client;

    protected $setupIntentID, $setupIntent;

    function __construct(Int $mode = 1) {
        $this->mode = $mode;
        $this->Connect();
    }

    function Connect() {
        switch ($this->mode) {
            case 1: # Live
                $this->secretKey =  "***********************************************************************************************************";
                $this->publicKey = "pk_live_51HS2BSLOCeRFt5luhwcpIBSkz2TD2yx7ffCCqxXzZgrAPkHDRtZh3dVMKja5XUkBnYerPM8J7J6zZrKcPraEMmp400pOEmznjM";
                break;
            default: # Test
                $this->secretKey = "sk_test_51HS2BSLOCeRFt5luAhd9SgeUjaWhre6JwnOX1xQRym68XE9LAoFnDJptNf8TKpazfBT19AP5iO4JRWkif9aR4RZp0064LeJz5B";
                $this->publicKey = "pk_test_51HS2BSLOCeRFt5luGoTqd5ssxwJw736SARhvCyuh5reIN19qRIFg2hMEsylHIIlVVVFuFgEORgNU41LYUtA1BiIp00u78WXhM3";
        }
        $this->client = new \Stripe\StripeClient($this->secretKey);        
    }

    function CustomerID (User $user) {
        $sql = "SELECT stripeCustomerID FROM `stripeCustomers` WHERE `userID` = :userID AND `mode` = :mode AND `deleted` IS NULL";
        $rlt = Database::Execute($sql, [
            "userID" => $user->id,
            "mode" => $this->mode
        ]);
        if ($rlt['error']) exit(json_encode($rlt['error']));
        if ($rlt['success']['rows']) return $rlt['success']['rows'][0]['stripeCustomerID'];
        $stripeCustomer = $this->client->customers->create(["email" => $user->email]);
        // $obj->stripeCustomerID = $obj->stripeCustomer->id;
        $sql = "INSERT INTO `stripeCustomers` SET `userID` = :userID, `stripeCustomerID` = :stripeCustomerID, `testAccount` = :testAccount, `mode` = :mode";
        $rlt = Database::Execute($sql, [
            "userID" => $user->id, 
            "stripeCustomerID" => $stripeCustomer->id, 
            "testAccount" => ($mode===1 ? null : 1), 
            "mode" => $mode
        ]);
        return $stripeCustomer->id;
    }

    function PaymentMethod_ApiData (String $stripePaymentMethodID) {
        $paymentMethod = $this->FetchPaymentMethod($stripePaymentMethodID);
        return [
            "paymentMethodID" => $paymentMethod->id,
            "brand" => $paymentMethod->card->brand,
            "exp_month" => $paymentMethod->card->exp_month,
            "exp_year" => $paymentMethod->card->exp_year,
            "funding" => $paymentMethod->card->funding,
            "last4" =>  $paymentMethod->card->last4,
            "name" => ucwords($paymentMethod->card->brand) . " " . ucwords($paymentMethod->card->funding) . " ending {$paymentMethod->card->last4}". " Exp. {$paymentMethod->card->exp_month}/" .substr($paymentMethod->card->exp_year,2)
        ];
    }

    function GetPaymentMethod (String $stripePaymentMethodID) {
        $rlt = Database::Execute("SELECT * FROM `stripePaymentMethods` WHERE `stripePaymentMethodID` = :stripePaymentMethodID",["stripePaymentMethodID" => $stripePaymentMethodID]);
        /* When to re-check status? */
        if ($rlt) return $rlt[0];
        $this->FetchPaymentMethod($stripePaymentMethodID);
    }

    function FetchPaymentMethod (String $stripePaymentMethodID) {
        $paymentMethod = $this->client->paymentMethods->retrieve($stripePaymentMethodID);
        if (!$paymentMethod) return;
        $this->UpdatePaymentMethod($paymentMethod);
        return $paymentMethod;
    }

    function UpdatePaymentMethod ($paymentMethod) {
        $sql = "INSERT INTO `stripePaymentMethods` SET stripePaymentMethodID = :stripePaymentMethodID, stripeCustomerID = :stripeCustomerID, brand = :brand, exp_month = :exp_month, exp_year = :exp_year, funding = :funding, last4 = :last4, postcode = :postcode, mode = :mode ON DUPLICATE KEY UPDATE stripeCustomerID = :stripeCustomerID, brand = :brand, exp_month = :exp_month, exp_year = :exp_year, funding = :funding, last4 = :last4, postcode = :postcode, mode = :mode";
        return Database::Execute($sql,[
            "stripePaymentMethodID" => $paymentMethod->id,
            "stripeCustomerID" => $paymentMethod->customer,
            "brand" => $paymentMethod->card->brand,
            "exp_month" => $paymentMethod->card->exp_month,
            "exp_year" => $paymentMethod->card->exp_year,
            "funding" => $paymentMethod->card->funding,
            "last4" =>  $paymentMethod->card->last4,
            "postcode" =>  $paymentMethod->billing_details->address->postal_code,
            "mode" => ($paymentMethod->livemode == true) ? 1 : 2
        ]);
    }

    function CancelPaymentMethod (String $paymentMethodID) {
        $paymentMethod = $this->client->paymentMethods->detach($paymentMethodID);
        $this->UpdatePaymentMethod($paymentMethod);
        return $paymentMethod;
    }

    function CreateSetupIntent (User $user) {
        $stripeCustomerID = $this->CustomerID($user);
        $setupIntent = $this->client->setupIntents->create([
            "customer" => $stripeCustomerID,
            "payment_method_types" => ['card'],
            "usage" => "off_session"
        ]);
        $this->SaveSetupIntent($setupIntent);
        return $setupIntent;
    }

    function FetchSetupIntent (String $setupIntentID) {
        return ($this->setupIntent = $this->client->setupIntents->retrieve($setupIntentID));
    }

    function SaveSetupIntent ($setupIntent) {
        $sql = "INSERT INTO `stripeSetupIntents` SET `setupIntentID` = :setupIntentID, client_secret = :client_secret, stripeCustomerID = :stripeCustomerID, paymentMethodID = :paymentMethodID, status = :status, testAccount = :testAccount, mode = :mode ON DUPLICATE KEY UPDATE client_secret = :client_secret, stripeCustomerID = :stripeCustomerID, paymentMethodID = :paymentMethodID, status = :status";
        return Database::Execute($sql, [
            "setupIntentID" => $setupIntent->id,
            "client_secret" => $setupIntent->client_secret,
            "stripeCustomerID" => $setupIntent->customer,
            "paymentMethodID" => $setupIntent->payment_method,
            "status" => $setupIntent->status,
            "testAccount" => ($this->mode == 1) ? NULL : 1,
            "mode" => $this->mode,
        ]);
    }

    static function GetSetupIntent (String $setupIntentID) {
        $sql = "SELECT * FROM `stripeSetupIntents` WHERE `setupIntentID` = :setupIntentID";
        $rlt = Database::Execute($sql,["setupIntentID" => $setupIntentID]);
        return ($rlt['success']['rows']) ? $rlt['success']['rows'] : null;
    }

}