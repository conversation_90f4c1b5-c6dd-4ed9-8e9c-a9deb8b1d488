<?php

class StripeSetupIntent extends StripeConnection {

    use GeneralTraits;

    protected $dbTable = 'stripeSetupIntents';
    protected $setupIntentID;
    protected $setupIntent;

    static function FetchFromSetupIntentID (String $setupIntentID) {
        $setupIntentDB = static::GetFromSetupIntentID($setupIntentID);
        static::$client = null;
        static::$mode = $setupIntentDB['mode'];
        static::Connect();
        return static::$client->setupIntents->retrieve($setupIntentID);
    }

    static function GetFromSetupIntentID (String $setupIntentID) {
        $sql = "SELECT * FROM stripeSetupIntents WHERE setupIntentID = :setupIntentID";
        $rlt = Database::Execute($sql,["setupIntentID" => $setupIntentID]);
        return ($rlt['success']['rows']) ? $rlt['success']['rows'][0] : null;
    }

    static function Create (Stripe\Customer $stripeCustomerObj) {
        try {
            $stripeCustomer = StripeCustomer::FromStripeCustomerID ($stripeCustomerObj->id);
            $rlt = static::$client->setupIntents->create([
                "customer" => $stripeCustomerObj->id,
                "payment_method_types" => ['card'],
                "usage" => "off_session"
            ]);
            $sql = "INSERT INTO stripeSetupIntents SET setupIntentID = :setupIntentID, status = :status, testAccount = :testAccount, mode = :mode";
            Database::Execute($sql,[
                "setupIntentID" => $rlt->id, 
                'status' => $rlt->status, 
                'testAccount' => ($stripeCustomer['mode']===1 ? null : 1),
                'mode' => $stripeCustomer['mode']
            ]);
            return $rlt;
        } catch (Exception $e) {
            return (String)$e->getMessage();
        }        
    }

}