<?php

class CardPayment {

    private $stripe;
    private $mode;
    private $secretKey;
    public $publicKey;

    function __construct (Bool $production = true) {
        $this->Connect($production);
    }

    # Connect to Stripe (Live or Test)
    function Connect (Bool $production = true) {
        $this->mode = ($production === true) ? 'Live' : 'Test';
        switch ($this->mode) {
            case 'Live': # Live
                $this->secretKey =  "***********************************************************************************************************";
                $this->publicKey = "pk_live_51HS2BSLOCeRFt5luhwcpIBSkz2TD2yx7ffCCqxXzZgrAPkHDRtZh3dVMKja5XUkBnYerPM8J7J6zZrKcPraEMmp400pOEmznjM";
                break;
            default: # Test
                $this->secretKey = "sk_test_51HS2BSLOCeRFt5luAhd9SgeUjaWhre6JwnOX1xQRym68XE9LAoFnDJptNf8TKpazfBT19AP5iO4JRWkif9aR4RZp0064LeJz5B";
                $this->publicKey = "pk_test_51HS2BSLOCeRFt5luGoTqd5ssxwJw736SARhvCyuh5reIN19qRIFg2hMEsylHIIlVVVFuFgEORgNU41LYUtA1BiIp00u78WXhM3";
        }
        $this->stripe = new \Stripe\StripeClient($this->secretKey);             
    }

    /**
     * CUSTOMERS
    */
    # Fetch Customer
    function Customer_Fetch (String $email) {
        try {
            return ["success" => $this->stripe->customers->all(['email' => $email])->data];
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }
    # Create Customer
    function Customer_Create (String $email) {
        try {
            return ["success" => $this->stripe->customers->create(["email" => $email])];
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }

    /**
     * PAYMENT METHODS
    */
    # Create Payment Method : Done via Front End
    # Get Customer's Payment Methods
    function PaymentMethods_Fetch (String $stripeCustomerID) {
        try {
            $paymentMethods = $this->stripe->customers->allPaymentMethods(
                $stripeCustomerID,
                ['type' => 'card']
            );
            return ["success" => $paymentMethods->data];
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }
    # Get a PaymentMethod
    function PaymentMethod_Fetch (String $stripePaymentMethodID) {
        try {
            return $this->stripe->paymentMethods->retrieve($stripePaymentMethodID);
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }

    /**
     * PAYMENT INTENTS
    */
    # Create Payment Intent
    function PaymentIntent_Create (String $stripeCustomerID, String $stripePaymentMethodID, Float $amount, String $description,Array $metadata = [],String $currency = 'gbp', String $capture_method = 'manual') {
        $stripeData = [
            'amount' => ($amount*100),
            'currency' => $currency,
            'customer' => $stripeCustomerID,
            'description' => $description,
            'capture_method' => $capture_method,
            'payment_method' => $stripePaymentMethodID
        ];
        if ($metadata) $stripeData['metadata'] = $metadata;
        try {
            return $this->stripe->paymentIntents->create($stripeData);
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }
    # Fetch Payment Intent
    function PaymentIntent_Fetch (String $stripePaymentIntentID) {
        try {
            return $this->stripe->paymentIntents->retrieve($stripePaymentIntentID);
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }
    # Confirm Payment Intent
    function PaymentIntent_Confirm (String $stripePaymentIntentID) {
        try {
            return $this->stripe->paymentIntents->confirm($stripePaymentIntentID);
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }
    # Cancel Payment Intent
    function PaymentIntent_Cancel (String $stripePaymentIntentID, String $reason = "duplicate") {
        $allowableReasons = ['duplicate', 'fraudulent', 'requested_by_customer', 'abandoned'];
        $data = ($reason && in_array($reason,$allowableReasons)) ? ["cancellation_reason" => $reason] : [];
        try {
            return ["success" => $this->stripe->client->paymentIntents->cancel($stripePaymentIntentID,$data)];
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }
}