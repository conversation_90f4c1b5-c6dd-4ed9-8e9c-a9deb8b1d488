<?php

class StripeCustomer extends StripeConnection {

    use GeneralTraits;

    protected $dbTable = 'stripeCustomers';
    protected $stripeCustomerID;
    protected $stripeCustomer;

    function __construct(Int $mode = 2) {
        parent::__construct($mode);
    }

    static function Get (User $user, Int $mode = 1) {
        static::$log[] = __METHOD__;
        $obj = new static($mode);
        $sql = "SELECT * FROM `stripeCustomers` WHERE `userID` = {$user->id} AND `mode` = {$mode} AND `deleted` IS NULL";
        $rlt = Database::Execute($sql);
        if ($rlt['success']['rows']) {
            static::$log[] = "Found {$rlt['success']['rows'][0]['stripeCustomerID']}";
            return static::$client->customers->retrieve($rlt['success']['rows'][0]['stripeCustomerID']);
        } 
        return static::Create($user,$mode);
    }

    static function FromStripeCustomerID (String $stripeCustomerID) {
        $sql = "SELECT * FROM `stripeCustomers` WHERE stripeCustomerID = :stripeCustomerID";
        $rlt = Database::Execute($sql,["stripeCustomerID" => $stripeCustomerID]);
        return ($rlt['success']['rows']) ? $rlt['success']['rows'][0] : null;
    }

    static function Create (User $user, Int $mode = 1) {
        static::$log[] = __METHOD__;
        // $obj = new static($mode);
        // $obj->getClient();
        $stripeCustomer = static::$client->customers->create(["email" => $user->email]);
        // $obj->stripeCustomerID = $obj->stripeCustomer->id;
        $sql = "INSERT INTO `stripeCustomers` SET `userID` = :userID, `stripeCustomerID` = :stripeCustomerID, `testAccount` = :testAccount, `mode` = :mode";
        $rlt = Database::Execute($sql, [
            "userID" => $user->id, 
            "stripeCustomerID" => $stripeCustomer->id, 
            "testAccount" => ($mode===1 ? null : 1), 
            "mode" => $mode
        ]);
        return $stripeCustomer;
    }

}