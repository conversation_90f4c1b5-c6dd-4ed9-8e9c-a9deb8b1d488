<?php

class StripePaymentMethod {

    use GeneralTraits;

    protected $dbTable = "stripePaymentMethods";

    protected $client;
    protected $stripePaymentMethodID;
    protected $stripeCustomerID;
    protected $testAccount; # 1 = Live, 2 = Test
    protected $paymentMethod;

    function getClient() {
        if (!$this->client || $this->client->mode != $this->mode) $this->client = StripeConnection::Create($this->mode);
        return $this->client;
    }

    function Fetch () {
        if (!$this->stripePaymentMethodID) return ["error" => "No Stripe Payment Method ID to fetch"];
        $this->getClient();
        try {
            $this->paymentMethod = $this->client->paymentMethods->retrieve($this->stripePaymentMethodID);
            if ($this->paymentMethod) {
                $rlt = $this->UpdateFromObject();
                return ["success" => $this->paymentMethod, "rlt" => $rlt];
            } else {
                return ["error" => "Nothing returned"];
            }
        } catch (Exception $e) {
            return ["error" => $e->getMessage()];
        }
    }

    function UpdateFromObject() {
        $sql = "INSERT INTO stripePaymentMethods SET stripePaymentMethodID = :stripePaymentMethodID, stripeCustomerID = :stripeCustomerID, brand = :brand, exp_month = :exp_month, exp_year = :exp_year, funding = :funding, last4 = :last4, postcode = :postcode, mode = :mode ON DUPLICATE KEY UPDATE stripeCustomerID = :stripeCustomerID, brand = :brand, exp_month = :exp_month, exp_year = :exp_year, funding = :funding, last4 = :last4, postcode = :postcode, mode = :mode";
        return Database::Execute($sql,[
            "stripePaymentMethodID" => $this->paymentMethod->id,
            "stripeCustomerID" => $this->paymentMethod->customer,
            "brand" => $this->paymentMethod->card->brand, 
            "exp_month" => $this->paymentMethod->card->exp_month,
            "exp_year"  => $this->paymentMethod->card->exp_year,
            "funding"  => $this->paymentMethod->card->funding,
            "last4" => $this->paymentMethod->card->last4,
            "postcode" => strtoupper($this->paymentMethod->billing_details->address->postal_code),
            "mode" => $this->mode
        ]);
    }

    function fetchCustomerPaymentMethods (String $stripeCustomerID) {
        $this->getClient(true);
        return $this->client->paymentMethods->all([
            'customer' => $stripeCustomerID,
            'type' => 'card',
            'limit' => 100
          ]);
    }

    static function GetByStripePaymentMethodID (String $stripePaymentMethodID) {
        $rlt = static::Query("SELECT * FROM `stripePaymentMethods` WHERE `stripePaymentMethodID` = :stripePaymentMethodID",["stripePaymentMethodID" => $stripePaymentMethodID]);
        if ($rlt) return $rlt[0];
        
    }

    static function Register (String $stripePaymentMethod) {
        
    }

    static function ResolveUnresolved(Int $limit = 100, Int $mode = 1) {
        /* 100 takes around 30 seconds */
        $sql = "SELECT * FROM stripePaymentMethods WHERE (stripeCustomerID IS NULL OR brand IS NULL OR postcode IS NULL) AND mode = $mode";
        if ($limit) $sql .= " LIMIT $limit";
        $rlt = static::Query($sql);
        if (!$rlt) return [];
        $return = [];
        foreach ($rlt as $p) {
            $rlt = $p->Fetch();
            $return[$p->stripePaymentMethodID] = $rlt;
        } 
        return $return;
    }
    
}