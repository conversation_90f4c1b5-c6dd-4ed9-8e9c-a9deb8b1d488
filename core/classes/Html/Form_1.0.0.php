<?php

class Form {

    public $id, $classes;
    public $style;

    public $action, $target, $method, $enctype;

    protected $children = [];
    protected $output = [];

    function addChild ($child) {
        $this->children[] = $child;
    }

    function __toString() {
        $output = <<<HTML
        <form action="{$this->action}" target="{$this->target}" method="{$this->method}" enctype="{$this->enctype}">
        HTML;
        foreach ($this->children as $child) {
            $output .= <<<HTML
                {$child}
            HTML;
        }
        $output .= '</form>';
        return $output;
    }

}