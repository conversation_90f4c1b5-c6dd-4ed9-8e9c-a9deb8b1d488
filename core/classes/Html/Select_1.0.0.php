<?php

class Select {

    public $id, $classes;
    public $style = "padding: .25em; border: thin solid #eee; border-radius: .25em;";

    public $name;

    public $data = [];
    public $selected;

    function __toString() {
        $output = <<<HTML
            <select name="{$this->name}" id="{$this->id}" class="{$this->classes}" style="{$this->style}">
                <option value=""></option>
        HTML;
        foreach ($this->data as $k => $v) {
            $output .= "<option value=\"$k\"";
            if ($this->selected && $this->selected == $k) $ouput .= " selected";
            $output .= ">$v</option>";
        }
        $output .= "</select>";
        return $output;
    }

}