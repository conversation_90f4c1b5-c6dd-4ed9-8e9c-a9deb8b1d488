<?php

class Environment {
    public const PRODUCTION = 'production';
    public const STAGING = 'staging';
    public const LOCAL = 'local';

    private static $environment = null;

    private static function detectEnvironment(): string {
        $host = $_SERVER['HTTP_HOST'] ?? '';

        if (self::isHostStaging($host)) {
            return self::STAGING;
        }

        if (self::isHostLocal($host)) {
            return self::LOCAL;
        }

        return self::PRODUCTION;
    }

    private static function isHostStaging(string $host): bool {
        return strpos($host, 'staging.') !== false;
    }

    private static function isHostLocal(string $host): bool {
        return strpos($host, 'local.') !== false
            || $host === 'localhost'
            || strpos($host, '127.0.0.1') !== false;
    }

    private static function init(): void {
        if (self::$environment === null) {
            self::$environment = self::detectEnvironment();
        }
    }

    public static function current(): string {
        self::init();
        return self::$environment;
    }

    public static function is(string $env): bool {
        self::init();
        return self::$environment === $env;
    }

    public static function isProduction(): bool {
        self::init();
        return self::$environment === self::PRODUCTION;
    }

    public static function isStaging(): bool {
        self::init();
        return self::$environment === self::STAGING;
    }

    public static function isLocal(): bool {
        self::init();
        return self::$environment === self::LOCAL;
    }
}
