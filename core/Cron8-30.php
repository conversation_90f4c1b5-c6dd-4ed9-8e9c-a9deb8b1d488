<?php

include(__DIR__ . "/init.php");
$start_time = microtime(true); // Record the start time


$stage4_start = microtime(true);
StripePayment::AlertSweep();
\Sentry\captureMessage('Cron8.php - AlertSweep ran in ' . microtime(true) - $stage4_start . ' seconds');

#Venue::CalculateDistances();
#System::BuildLiveLeaguesApi();
$taster_start = microtime(true);
TasterSession::CheckStatuses();
\Sentry\captureMessage('Cron8.php - CheckStatuses ran in ' . microtime(true) - $taster_start . ' seconds');

$trigger_start = microtime(true);
Trigger::Clear();
\Sentry\captureMessage('Cron8.php - Clear ran in ' . microtime(true) - $trigger_start . ' seconds');

$stage5_start = microtime(true);
StripePayment::CancelAllWithStatus('requires_payment_method');
\Sentry\captureMessage('Cron8.php - CancelAllWithStatus ran in ' . microtime(true) - $stage5_start . ' seconds');

$stage6_start = microtime(true);
Stripe::Synchronise();
\Sentry\captureMessage('Cron8.php - Synchronise ran in ' . microtime(true) - $stage6_start . ' seconds');


// Log with Sentry
$execution_time  = microtime(true) - $start_time; // Calculate the execution time
\Sentry\captureMessage('Cron8-30.php ran in ' . $execution_time . ' seconds');
