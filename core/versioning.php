<?php
include 'vendor/autoload.php';
\Sentry\init(['dsn' => 'https://<EMAIL>/****************' ]);

/* New Classes Added/Upgraded Here Are Downloaded from Codeframe Repo - triggered via Author? */
$classList = [
    'Database' => [
        "Database" => '7.4.0',
    ],
    'DocLibrary' => [
        "DocLibraryType" => '1.0.0',
        "DocLibraryVersion" => '1.0.0',
    ],
    'Html' => [
        "Form" => '1.0.0',
        "Input" => '1.0.0',
        "Select" => '1.0.0',
    ],
    'Stripe' => [
        'CardPayment' => '1.0.0',
        'StripeConnection' => '1.0.0',
        'StripeCustomer' => '1.0.0',
        'StripePaymentIntent' => '1.0.0',
        'StripePaymentMethod' => '1.0.0',
        'StripeSetupIntent' => '1.0.0',
    ],
    'PaymentCard' => [
        'PaymentMethod' => '1.0.0'
    ]
];

# Traits included first as invoked in the Classes included below
$traitsFile = CORE_FOLDER."/classes/GeneralTraits.php";
if (file_exists($traitsFile)) include_once($traitsFile);

foreach ($classList as $parentName => $classes) {
    foreach ($classes as $className => $version) include_once(__DIR__."/classes/$parentName/{$className}_{$version}.php");
}

$codebaseClasses = [
    'Base' => [
        'Base' => '7.4.1',
        'Logging' => '1.0.0'
    ],
    'Database' => ['Database' => '7.4.3'],
    'Billing' => [
        'Card' => '1.0.7',
        'Sage' => '1.0.0'
    ]
];

foreach ($codebaseClasses as $parentName => $codebaseClass) {
    foreach ($codebaseClass as $className => $version) {
        require_once(__DIR__."/codebase/$parentName/{$className}_{$version}.php");
    }
    
}