<?php
// $configFile = "/var/www/html/core/config.php";
// include($configFile);

// $autoloadFile = "/var/www/html/core/vendor/autoload.php";
// include($autoloadFile);

// function ClassAutoloader($class) {
//     $file = "/var/www/html/core/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
// }
// spl_autoload_register("ClassAutoloader");

include(__DIR__."/init.php");

// User::ClearReminders();

// Trigger::Run();
#StripePaymentMethod::ResolveUnresolved();