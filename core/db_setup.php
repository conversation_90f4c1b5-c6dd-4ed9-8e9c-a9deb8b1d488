<?php

/*
CREATE TABLE `logging` (
    `id` int(10) unsigned primary key auto_increment, 
    `created` datetime default current_timestamp(), 
    `description` tinytext
);
CREATE TABLE `jwt` (
    `id` int(10) unsigned primary key auto_increment, 
    `created` datetime default current_timestamp(), 
    `updated` datetime default current_timestamp() on update current_timestamp(), 
    `deleted` datetime,
    `expiry` datetime,
    `userID` int unsigned,
    `token` tinytext,
    `ip` tinytext,
    `agent` tinytext,
    `counter` mediumint unsigned
);
CREATE TABLE `users` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `created` datetime DEFAULT current_timestamp(),
  `updated` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted` datetime DEFAULT NULL,
  `isAuthor` tinyint(4) DEFAULT NULL,
  `isAdmin` tinyint(4) DEFAULT NULL,
  `isManager` tinyint(4) DEFAULT NULL,
  `isFinance` tinyint(4) DEFAULT NULL,
  `introducerID` int(10) unsigned DEFAULT NULL,
  `firstname` varchar(100) DEFAULT NULL,
  `lastname` varchar(100) DEFAULT NULL,
  `email` varchar(250) DEFAULT NULL,
  `landline` varchar(20) DEFAULT NULL,
  `extension` mediumint(9) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `line1` text DEFAULT NULL,
  `town` text DEFAULT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `password` varchar(250) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `gender` int(11) DEFAULT NULL,
  `activationCode` varchar(20) DEFAULT NULL,
  `activationExpiry` datetime DEFAULT NULL,
  `activationIP` varchar(20) DEFAULT NULL,
  `activationStamp` datetime DEFAULT NULL,
  `resetIP` varchar(20) DEFAULT NULL,
  `resetSession` varchar(50) DEFAULT NULL,
  `resetCode` varchar(20) DEFAULT NULL,
  `resetExpiry` datetime DEFAULT NULL,
  `source` varchar(50) DEFAULT NULL,
  `clubname` varchar(100) DEFAULT NULL,
  `registrationLeagueID` int(10) unsigned DEFAULT NULL,
  `securityCode` mediumint(8) unsigned DEFAULT NULL,
  `securityCodeExpiry` datetime DEFAULT NULL,
  `profilePictureUrl` tinytext DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `mobile` (`mobile`)
) ENGINE=InnoDB AUTO_INCREMENT=9739 DEFAULT CHARSET=utf8mb4  



 */