<?php

namespace Codebase;

/**
 * @version 1.0.7
 * Added Payout_List() and BalanceTransaction_Payout()
 */
class Card {

    private $secretKey;
    public $publicKey;
    protected $mode = "Test"; # Test mode by default
    private $client; # Used to load the Stripe Client from SDK
    public $errors = [];

    private $stripeCustomers = [];
    private $stripeCustomer;
    private $setupIntent;
    private $paymentIntent;
    private $paymentMethod;
    private $paymentMethods;


    private function __construct(Bool $production = false) {
        global $config;
        if (is_object($config)) {
            $configData = $config;
        } else {
            $configData = json_decode(json_encode($config));
        } 
        // exit(var_dump($configData));
        if ($production === true) {
            # If Production mode - override keys and mode.
            $this->secretKey = $configData->stripe->live->sk;
            $this->publicKey = $configData->stripe->live->pk;
            $this->mode = "Live";
        } else {
            $this->secretKey = $configData->stripe->test->sk;
            $this->publicKey = $configData->stripe->test->pk;
            $this->mode = "Test";
        }
        $this->Connect();
    }

    private function Connect () {
        $this->client = new \Stripe\StripeClient($this->secretKey);
        if (!$this->client) throw new Exception ("Not connected");
    }

    function getMode() {
        return $this->mode;
    }

    # Customer | Lookup / Create
    function Customer_SearchCreate (String $email) {
        $log = __METHOD__ . " for $email";
        $stripeCustomer = $this->Customer_Search($email);
        if (!$stripeCustomer) $stripeCustomer = $this->Customer_Create($email);
        return $stripeCustomer;
    }
    # Customer | Create
    function Customer_Create (String $email) {
        $stripeCustomer = $this->client->customers->create(["email" => $email]);
        $log = __METHOD__ . " $email {$stripeCustomer->id}";
        return $stripeCustomer;
    }
    # Customer | Fetch
    function Customer_Fetch (String $stripeCustomerID) {
        return $this->client->customers->retrieve($stripeCustomerID);
    }
    # Customer | Search
    function Customer_Search (String $email) {
        $log = __METHOD__ . " $email";
        $searchParams = ['query' => "email:'$email'"];
        $search = $this->client->customers->search($searchParams);
        if (isset($search->data[0])) {
            $log .= ". Found {$search->data[0]->id}";
            Logging::Add($log);
            return $search->data[0];
        }
        $log .= ". Not found.";
        Logging::Add($log);
        return;
    }
    # Customer | List
    function Customer_List (Int $limit = 100, String $startFrom = null) {
        $params = ['limit' => $limit];
        if ($startFrom) $params['starting_after'] = $startFrom;
        return $this->client->customers->all($params);
    }

    # Setup Intent | Create
    function SetupIntent_Create (String $stripeCustomerID) {
        return $this->client->setupIntents->create([
            "customer" => $stripeCustomerID,
            "payment_method_types" => ['card'],
            "usage" => "off_session"
        ]);
    }
    # Setup Intent | Process
    function SetupIntent_Process (String $stripeCustomerID) {}
    # Setup Intent | Fetch
    function SetupIntent_Fetch (String $setupIntentID) {
        return $this->setupIntent = $this->client->setupIntents->retrieve($setupIntentID);
    }
    
    # Payment Intent | Create
    function PaymentIntent_Create (String $stripeCustomerID, Float $amount=null, String $description=null, Array $metadata = [], String $stripePaymentMethodID = null, String $currency = 'gbp', String $capture_method = 'manual') {
        $stripeData = [
            'customer' => $stripeCustomerID,
            'currency' => $currency,
            'automatic_payment_methods' => ['enabled' => true],
            'capture_method' => $capture_method,
        ];
        if ($stripePaymentMethodID) $stripeData['payment_method'] = $stripePaymentMethodID;
        if ($amount) $stripeData['amount'] = $amount*100;
        if ($description) $stripeData['description'] = $description;

        if ($metadata) $stripeData['metadata'] = $metadata;
        $this->paymentIntent = $this->client->paymentIntents->create($stripeData);
        return $this->paymentIntent;
    }
    # Payment Intent | Update
    function PaymentIntent_Update (String $stripePaymentIntentID, Float $amount = null, String $description = null, Array $metadata = []) {
        $stripeData = [];
        if ($amount) $stripeData['amount'] = $amount * 100;
        if ($description) $stripeData['description'] = $description;
        if ($metadata) $stripeData['metadata'] = $metadata;
        if (!$stripeData) throw new Exception("No data to update");
        return $this->client->paymentIntents->update($stripePaymentIntentID,$stripeData);
    }
    # Payment Intent | Process
    function PaymentIntent_Process (String $stripeCustomerID) {}
    # Payment Intent | Fetch
    function PaymentIntent_Fetch (String $stripePaymentIntentID) {
        return $this->client->paymentIntents->retrieve($stripePaymentIntentID);
    }
    # Payment Intent | Confirm
    function PaymentIntent_Confirm (String $stripePaymentIntentID) {
        return $this->client->paymentIntents->confirm($stripePaymentIntentID);
    }
    # Payment Intent | Capture
    function PaymentIntent_Capture (String $stripePaymentIntentID) {
        return $this->client->paymentIntents->capture($stripePaymentIntentID);
    }
    # Payment Intent | Cancel
    function PaymentIntent_Cancel (String $stripePaymentIntentID, String $reason = "requested_by_customer") {
        $allowableReasons = ['duplicate', 'fraudulent', 'requested_by_customer', 'abandoned'];
        if (!$reason || !in_array($reason,$allowableReasons)) $reason = "requested_by_customer";
        return $this->client->paymentIntents->cancel(
            $stripePaymentIntentID,
            ['cancellation_reason' => $reason]
          );
    }
    # Payment Intent | Refund
    function PaymentIntent_Refund (String $stripePaymentIntentID, Float $amount = null) {
        $refundData['payment_intent'] = $stripePaymentIntentID;
        if ($amount) $refundData['amount'] = $amount * 100;
        return $this->client->refunds->create($refundData);
    }
    # Payment Intent | List
    function PaymentIntent_List (Int $limit = 100, String $startDate = null, String $endDate = null) {
        $params = ['limit' => $limit];
        if (!$startDate && !$endDate) return $this->client->paymentIntents->all($params);
        $query = null;
        if ($startDate) $query = "created>='".strtotime($startDate . " 00:00:00")."'";
        if ($endDate) {
            $query .= ($query) ? " AND created<='".strtotime($endDate." 23:59:59")."'" : "created<='".strtotime($endDate." 23:59:59")."'";
        }
        return $this->client->paymentIntents->search(['query' => $query,'limit' => $limit]);
    }
    # Payment Intent | By Status
    function PaymentIntent_ByStatus (String $status) {
        return $this->client->paymentIntents->search([
            'query' => "status:'$status'",
        ]);
    }
    # Payment Method | Api
    function PaymentMethod_ApiData (String $stripePaymentMethodID) {
        $this->PaymentMethod_Fetch($stripePaymentMethodID);
        if ($this->paymentMethod) return [
            "paymentMethodID" => $this->paymentMethod->id,
            "brand" => $this->paymentMethod->card->brand,
            "exp_month" => $this->paymentMethod->card->exp_month,
            "exp_year" => $this->paymentMethod->card->exp_year,
            "funding" => $this->paymentMethod->card->funding,
            "last4" =>  $this->paymentMethod->card->last4,
            "name" => ucwords($this->paymentMethod->card->brand) . " " . ucwords($this->paymentMethod->card->funding) . " ending {$this->paymentMethod->card->last4}". " Exp. {$this->paymentMethod->card->exp_month}/" .substr($this->paymentMethod->card->exp_year,2)
        ];
    }
    # Payment Method | Fetch
    function PaymentMethod_Fetch (String $stripePaymentMethodID) {
        return $this->paymentMethod = $this->client->paymentMethods->retrieve($stripePaymentMethodID);
    }
    # Payment Method | Cancel
    function PaymentMethod_Cancel (String $paymentMethodID) {
        return $this->paymentMethod = $this->client->paymentMethods->detach($paymentMethodID);
    }
    
    # Payment Methods | Fetch
    function PaymentMethods_Fetch (String $stripeCustomerID) {
        return $this->paymentMethods = $this->client->customers->allPaymentMethods(
                $stripeCustomerID,
                ['type' => 'card']
            );
    }

    # Payouts | List
    function Payout_List (Int $limit = 7) {
        return $this->client->payouts->all(['limit' => $limit]);
    }
    
    # Balance Transactions | List
    function BalanceTransaction_Payout (String $stripePayoutID) {
        return $this->client->balanceTransactions->all(['payout' => $stripePayoutID, 'limit' => 100]);
    }

    static function Test() {
        try {
            $log = [];
            $card = static::Build();
            $email = "<EMAIL>";
            $stripeCustomer = $card->Customer_SearchCreate($email);
            $log[] = "Stripe Customer: {$stripeCustomer->id}";
            $paymentIntent = $card->PaymentIntent_Create($stripeCustomer->id,17.99,"Test Transaction",["Invoice No" => "ABC001"]);
            $log[] = ($paymentIntent) ? "Payment Intent {$paymentIntent->id} created" : "No PI created";
            if ($log) {
                foreach ($log as $l) echo "$l<br>";
            }
        } catch (Exception $e) {
            echo "Card Failure : " . $e->getMessage()."<br>";
        }
    }

    static function Setup (Bool $production = true) {
        return new static($production);
    }

    static function Process_CaptureConfirmed () {
        $card = static::Setup();
        $capturables = $card->PaymentIntent_ByStatus('requires_capture');
        foreach ($capturables as $capturable) {
            $card->PaymentIntent_Capture($capturable->id);
        }
    }

    /**
     * Card Testing
     * per https://stripe.com/docs/testing
    */
    /**
     * ****************
     * **************** card_declined   generic_decline
     * **************** card_declined   insufficient_funds
     * **************** card_declined   lost_card
     * **************** card_declined   stolen_card
     * **************** expired_card
     * **************** incorrect_cvc
     * **************** processing_error
     * **************** incorrect_number
     * **************** 3D Auth (Unless SetupIntent)
     * **************** 3D Auth (Always)
     * **************** 3D Auth (Fails insufficient_funds)
     */
}