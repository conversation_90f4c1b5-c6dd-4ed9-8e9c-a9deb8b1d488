<?php

/*
Per https://gb-kb.sage.com/portal/app/portlets/results/viewsolution.jsp?solutionid=***************&hypermediatext=null
Transaction Types: BP, BR, CP, CR, VP, VR, JC, JD, SI, SC, SA, SP, PI, PC, PA and PR.
NB: You cannot import SR or PP transactions, instead you should import SA and PA transactions and manually allocate them once imported.
Sage Nominal Codes
0000-0999 fixed assets
1000-1099 stock
1100-1199 debtors
1200-1299 bank and cash
2100-2399 liabilities
3000-3099 capital and reserves
4000-4999 sales and other income
5000-5999 cost of goods
6000-6099 direct labour and direct costs
6100-6999 marketing and miscellaneous
7000 -7999 overheads
8000 - 9997 for end of period adjustments, depreciation, bad debts and other odd expenses
9998 is the suspense account and 9999 is for use by Sage only

Sage Tax Codes
T0 - Zero-rated transactions (eg Food, Children's CLothes, Books/Newspapers, New House construction, passenger transport & medicine)
T1 - Standard rated Goods and Services, 20%
T2 - Exempt Goods and Services, 0% (eg certain of these: land/building sales, finance charges, Education/training, Betting, gaming, Burials, cremation).
Postal services)
T5 - Reduced rate, 5%- T5 (Mobility aids for elderly, nicotine patches/gum, residential utility bills)
T9 - Non-Vatable Items – Wages, Bank/cash transfers, PAYE. NI, Capital Loans, dividends, gifts of money
T14 – Import of goods – No VAT
T15 – Purchase of services from ROW – Reverse charge
T16 – Purchase of services from ROW – No VAT
T17 – Import of goods – Under import reverse charge threshold
T18 – Import of goods – Postponed VAT
T19 – Import of goods – VAT no postponed
T20 - Reverse Charge Items – Wholesale telecom services, Mobile phones (in  certain circumstances), Computer chips (certain circumstances), Emissions allowances 

T32 - For the new rate of 12.5% from October 2021 - March 2022 for on-premise food and non-alcoholic drinks in restaurants, pubs, bars, and cafes, hot takeaway food and hot takeaway non-alcoholic drinks, hotel and holiday accommodation & admission to certain attractions in the UK. This is a suggested new code (by Sage) so any unused woudl do it. 
*/

namespace Codebase;

use \Email;

class Sage {

    protected $folder, $file;

    function __construct() {
        $this->folder = __DIR__ . DIRECTORY_SEPARATOR . "csvfiles" . DIRECTORY_SEPARATOR;
    }

    function getFolder() {
        return $this->folder;
    }

    function TestSalesTransactionCSV() {
        $testData[] = [
            'SI',
            'ABC001',
            '6901',
            null,
            date('d/m/Y'),
            'REF001',
            'DETAIL for REF001',
            199.95,
            'T1',
            39.99
        ];
        echo "Save to {$this->folder}{$this->file}<br>";
        $stream = fopen($this->folder.$this->file,"w");
        foreach ($this->BuildSalesTransactionCSV($testData) as $d) fputcsv($stream,$d);
        fclose($stream);
        // \Tools::Dump($this->BuildSalesTransactionCSV($testData));
    }
 
    /**
     * @var $data ARRAY of PurchaseData
     * @var $includeHeader BOOL Whether to include the Sage File Header Row
     * @var $trunateColumns INT A number to limit the columns of data (typically 10)
     * @return STRING Name of CSV file where data dumped
     */
    function BuildPurchaseTransactionCSV_File (String $importStamp, Array $data, Bool $truncated = true) {
        $filename = $this->folder . str_replace([" ",":","-"],["","",""],$importStamp) . ".csv";
        $data = $this->BuildPurchaseTransaction_Data($data,$truncated);
        $stream = fopen($filename,"w");
        foreach ($data as $d) fputcsv($stream,$d);
        fclose($stream);
        return $filename;
    }

    function PurchaseTransactionFilename (String $importStamp) {
        return $this->folder . str_replace([" ",":","-"],["","",""],$importStamp) . ".csv";
    }

    function SendPurchaseTransactionFile (String $filename, Array $recipient = []) {
        $stamp = $this->ExtractTimestampFromFilename($filename);
        $message[] = "Hi";
        $message[] = "Attached here is filename " . basename($filename);
        $message[] = "This includes the latest unimported transactions up to " . date('H:ia \o\n l jS F Y',strtotime($stamp));
        $message[] = "Thanks";
        if ($recipient) {
            $to = $recipient; 
            $cc = [];
            return Email::Issue("Sage Import File",$message,$recipient,[],["<EMAIL>" => "a2z"],[$filename]);
        } else {
            $to = ["<EMAIL>" => "Alison Udall"];
            $cc = ["<EMAIL>" => "Richard Dakin"];
        }
        return Email::Issue("Sage Import File",$message,$to,$cc,["<EMAIL>" => "a2z"],[$filename]);
        // return Email::Issue("Sage Import File",$message,["<EMAIL>" => "Codeframe"],[],[],[$filename]);
    }

    function ListOfImportFIles() {
        $rlt = scandir($this->folder);
        if (!$rlt) return;
        $return = [];
        foreach ($rlt as $r) {
            if ($r != "." && $r!="..") $return[] = [
                'filename' => $r,
                'timestamp' => $this->ExtractTimestampFromFilename($r)
            ];
        }
        return $return;
    }

    function ExtractTimestampFromFilename (String $filename) {
        $strippedName = basename($filename,".csv");
        return substr($strippedName,0,4)."-".substr($strippedName,4,2)."-".substr($strippedName,6,2)." ".substr($strippedName,8,2).":".substr($strippedName,10,2).":".substr($strippedName,12,2);
    }

    /**
     * @param Array $data The specific finance info to be included in the CSV data
     * @return Array $content Combination of the CSV Header and Data  
     */
    function BuildPurchaseTransaction_Data (Array $data = [], Bool $truncated = false) {
        if ($truncated === false) $content[] = $this->PurchaseTransactionHeadings();
        foreach ($data as $d) $content[] = $d;
        return $content;
    }

    /**
     * @param Array $data The specific finance info to be included in the CSV data
     * @return Array $content Combination of the CSV Header and Data  
     */
    function BuildSalesTransactionData(Array $data = []) {
        $content[] = $this->SalesTransactionHeadings();
        foreach ($data as $d) $content[] = $d;
        return $content;
    }

    function SalesTransactionHeadings() {
        return [
            'TransactionType', # BP, BR, CP, CR, VP, VR, JC, JD, SI, SC, SA, SP, PI, PC, PA, PR.
            'Account', # varchar(8)
            'Nominal Code', # varchar(8) required
            'Department', # optional int(3)
            'Date', # DD/MM/YYYY.
            'Reference', # varchar(30)
            'Details', # varchar(60)
            'net', # FLOAT(10,2)
            'T/C (Tax Code)', # 0 - T99
            'Tax',  # FLOAT(10,2)
            'Exchange Rate', # FLOAT(10,2) think this is multiplier as default = 1
            'Ex.Ref', # varchar(30)
            'User Name', # varchar(32)
            'Project Reference', # varchar(12) no spaces
            'Cost Code Reference', # varchar(8)
            'Country of VAT', # 2 letter code
            'Report Type',
            'Fund',
        ];
    }

    function PurchaseTransactionHeadings() {
        return [
            'TransactionType', # BP, BR, CP, CR, VP, VR, JC, JD, SI, SC, SA, SP, PI, PC, PA, PR.
            'Account', # varchar(8)
            'Nominal Code', # varchar(8) required
            'Department', # optional int(3)
            'Date', # DD/MM/YYYY.
            'Reference', # varchar(30)
            'Details', # varchar(60)
            'Net', # FLOAT(10,2)
            'T/C (Tax Code)', # 0 - T99
            'Tax',  # FLOAT(10,2)
            'Exchange Rate', # FLOAT(10,2) think this is multiplier as default = 1
            'Ex.Ref', # varchar(30)
            'User Name', # varchar(32)
            'Project Reference', # varchar(12) no spaces
            'Cost Code Reference', # varchar(8)
            'Country of VAT', # 2 letter code
            'Report Type',
            'Fund',
        ];
    }

    function FetchFiles() {
        foreach (scandir($this->folder) as $f) {
            if ($f == "." || $f == "..") continue;
            // $stamp = substr($f,0,4) . "-" . substr($f,4,2) . "-" . substr($f,6,2) . " " . substr($f,8,2) . ":" . substr($f,10,2) . ":".substr($f,12,2);
            // $return[$f] = date('H:i:s d/m/Y',strtotime($stamp));
            $return[$f] = date('H:i:s d/m/Y',strtotime(strstr($f,".",true)));
            // $return[$f] = $stamp;
        }
        return $return;
    }
}