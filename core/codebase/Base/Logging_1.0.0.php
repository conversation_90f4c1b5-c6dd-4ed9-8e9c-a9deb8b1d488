<?php

namespace Codebase;

class Logging extends Base {

    static protected $dbTable = "log_table"; 
    public $dbFields = ["description"];

    function ApiData() {
        return [
            'id' => $this->id,
            'created' => $this->created,
            'created_formatted' => date('H:i:s d/m/Y',strtotime($this->created)),
            'description' => $this->description,
        ];
    }

    static function Add (String $text) {
        $sql = "INSERT INTO log_table SET description = :description";
        Database::Execute($sql,["description" => $text]);
    }

    static function Fetch (Int $limit = 10, Int $pageNo = 1) {
        $limit++;
        $sql = "SELECT * FROM log_table ORDER BY created DESC";
        return static::Query($sql);
    }
}