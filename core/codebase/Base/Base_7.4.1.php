<?php

namespace Codebase;

/**
 * @version 7.4.1
 * Changed function signature for Base::Create() 
 * Included Testing options in Base::__construct and Base::Create()
 */
class Base {
    protected $id, $name, $created, $updated, $deleted;
    static protected $dbKey = "id", $dbTable, $dbArrays, $dbOrder;
    
    function __construct (Int $id = null) {
        if ($id) {
            if (isset($_SESSION['testing'])) {
                foreach ($_SESSION['testing'][get_class($this)]['objects'] as $o) {
                    if ($o->id == $id) {
                        #if (get_class($this)=="Role") echo "Matched " . get_class($this) . " ID {$o->id} to $id<br>";
                        foreach ($o as $k => $v) $this->$k = $v;
                        return;
                    } 
                }
            }
            $sql = "SELECT * FROM `".static::$dbTable."` WHERE `".static::$dbKey."` = :id";
            $rlt = Database::Execute($sql,["id" => $id]);
            if (isset($rlt['success']['rows'][0])) {
                if (static::$dbArrays) {
                    foreach (static::$dbArrays as $dbArray) {
                        $rlt['success']['rows'][0][$dbArray] = unserialize($rlt['success']['rows'][0][$dbArray]);
                    }
                }
                $this->Load($rlt['success']['rows'][0]);
            } 
        }
    }
    
    function ApiData() {
        return [
            "id" => $this->id,
            "name" => $this->__toString()
        ];
    }

    function Sql () {
        return [
            "fields" => [], 
            "constraints" => [],
            "relationships" => []
        ];
    }

    function Save () {
        $data = [];
        if ($this->{static::$dbKey}) { // Update
            $sql = "UPDATE `".static::$dbTable."` SET "; $conn = null;
            foreach ($this->dbFields as $f) {
                $sql .= $conn . "`$f` = :$f";
                if (!$this->$f) { $data[$f] = null;
                } elseif (is_array($this->$f)) { $data[$f] = serialize($this->$f);
                } else $data[$f] = $this->$f;
                $conn = ",";
            }
            $sql .= " WHERE `".static::$dbKey."` = :id"; $data['id'] =  $this->{static::$dbKey};
            $rlt = Database::Execute($sql,$data);
        } else { // Insert
            $sql = "INSERT INTO `".static::$dbTable."` SET "; $conn = null;
            foreach ($this->dbFields as $f) {
                $sql .= $conn . "`$f` = :$f";
                if (!$this->$f) { $data[$f] = null;
                } elseif (is_array($this->$f)) { $data[$f] = serialize($this->$f);
                } else $data[$f] = $this->$f;
                $conn = ",";
            } 
            $rlt = Database::Execute($sql,$data);
            if ($rlt['success']) $this->{static::$dbKey} = $rlt['success']['lastInsertID'];
        }
        return $rlt;
    }

    function __toString() {
        return ($this->name) ? "{$this->name}" : get_called_class()." {$this->id}";
    }

    function Load (Array $data = []) {
        foreach ($data as $k => $v) $this->$k = $v;
    }

    function __get ($value) {
        if (isset($this->{$value})) return $this->{$value};
    }

    function __set ($property, $value) {
        $this->{$property} = $value;
    }

    function Delete() {
        $this->deleted = date('Y-m-d H:i:s');
        return Database::Execute("UPDATE " . static::$dbTable . " SET `deleted` = '{$this->deleted}' WHERE `id` = {$this->id}");
    }

    function SqlSetup (Bool $overwrite = false) {
        $schema = $this->Sql();
        if (!$schema['fields']) return;
        $sql = "CREATE TABLE `".static::$dbTable."`("; $conn = null;
        foreach ($schema['fields'] as $fieldName => $fieldProps) {
            $sql .= $conn."`$fieldName` {$fieldProps['type']}";
            if (isset($fieldProps['pk']) && $fieldProps['pk'] === true) $sql .= " primary key auto_increment";
            if (isset($fieldProps['default'])) $sql .= " DEFAULT {$fieldProps['default']}";
            if (isset($fieldProps['extra'])) $sql .= " {$fieldProps['extra']}";
            $conn = ",";
        }
        if ($schema['constraints']) {
            foreach ($schema['constraints'] as $constraintName => $constraintProps) {
                $sql .= $conn . " CONSTRAINT  {$constraintName} {$constraintProps['type']} (" . implode(",",$constraintProps['fields']).")";
            }
        }
        $sql .= ")";
        return Database::Execute($sql);
    }

    function Permitted() {
        return (Auth::AccountID() == $this->accountID) ? true : false;
    }

    static function Listing () {
        $sql = "SELECT * FROM `".static::$dbTable."`";
        if (static::$dbOrder) $sql .= " ORDER BY ".static::$dbOrder;
        return static::Query($sql);
    }

    static function Query (String $sql = null, Array $data = []) {
        if (!$sql) $sql = "SELECT * FROM `" . static::$dbTable . "`";
        $rlt = Database::Execute($sql,$data);
        if ($rlt['error']) Tools::Die("Problem " . $rlt['error']['message']);
        if (!$rlt['success']['rows']) return [];
        $return = [];
        foreach ($rlt['success']['rows'] as $r) {
            $c = new static();
            $c->Load($r);
            $return[] = $c;
        }
        return $return;
    }

    static function Copy ($object) {
        $newObject = clone $object;
        $newObject->id = null;
        $newObject->Save();
        return $newObject;
    }

    static function Between (String $startDate = null, String $endDate = null, String $startDateField = 'created', String $endDateField = 'created') {
        $sql = "SELECT * FROM `" . static::$dbTable . "`";
        $sql .= " WHERE `$startDateField` >= '$startDate'"; 
        if ($endDate) $sql .= " AND `$endDateField` <= '$endDate'";
        $sql .= " AND `deleted` IS NULL";
        return static::Query($sql);
    }

    static function Create (Array $data = []) {
        if (isset($_SESSION['testing'])) $_SESSION['testing']['log'][] = __METHOD__;
        if (!$data) return;
        $obj = new static();
        $obj->Load($data);
        if (isset($_SESSION['testing'])) {
            if (!isset($_SESSION['testing'][get_class($obj)]['lastID'])) $_SESSION['testing'][get_class($obj)]['lastID'] = 0;
            $obj->id = ++$_SESSION['testing'][get_class($obj)]['lastID'];
            if (isset($_SESSION['testing']))  $_SESSION['testing']['log'][] = "Created " . get_class($obj) . " ID " . $_SESSION['testing'][get_class($obj)]['lastID'];
            $_SESSION['testing'][get_class($obj)]['objects'][] = $obj;
         } else $obj->Save();
        return $obj;
    } 

    static function ApiTransform (Array $objects) {
        $result = [];
        foreach ($objects as $object) {
            $result[] = $object->ApiData();
        }
        return $result;
    }

}