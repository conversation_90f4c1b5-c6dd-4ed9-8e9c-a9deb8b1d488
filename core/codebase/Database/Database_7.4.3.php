<?php
namespace Codebase;

class Database {

    private $pdo, $dsn;
    private $statement;
    private $sql, $data;
    public $errors = [];
    public $result;
    public $rows = [];
    public $lastInsertID;
    public $affectedRows;
    public $params;
    protected $backupTables = [];
    public $logs = [];

    private static $instance = null;

    private function __construct($databaseName = null) {
        global $config;
        if (is_object($config)) {
            $configData = $config;
        } else {
            $configData = json_decode(json_encode($config));
        } 
        $dbsetup = $configData->db;
        // exit(var_dump($dbsetup));
        $pdoOptions = [
            \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            \PDO::ATTR_EMULATE_PREPARES => true
        ];
        $this->dsn = "mysql:dbname={$dbsetup->database};host={$dbsetup->hostname}";
        try {
            $this->pdo = new \PDO($this->dsn, $dbsetup->username, $dbsetup->password,$pdoOptions);
        } catch (\Exception $e) {
            exit("Cannot progress " . $e->getMessage()." ".print_r($dbsetup));
        }
    }

    static function TableExists (String $tablename) {
        global $config;
        $dbsetup = $config->db;
        $rlt = static::Execute("SHOW TABLES WHERE Tables_in_{$dbsetup->database} = '$tablename'");
        return ($rlt['success']['rows']) ? true : false;
    }

    static function Describe (String $tableName) {
        $sql = "DESCRIBE `$tableName`";
        $described = static::Execute($sql);
        if (!$described) return;
        $return = [];
        foreach ($described as $d) $return[$d['Field']] = $d;
        return $return;
    }

    static function Upgrade() {
        $sqlRootFolder = APP_ROOT.DIRECTORY_SEPARATOR."Config".DIRECTORY_SEPARATOR."SQL".DIRECTORY_SEPARATOR;
        /* Tables - Return if not found */
        $jsonFile = $sqlRootFolder."sqlTables.json";
        if (!file_exists($jsonFile)) return "No SQL Tables JSON file $jsonFile";
        $data = json_decode(file_get_contents($jsonFile));
        if (!$data) return "No data located in JSON file $jsonFile";
        foreach ($data as $tableName => $d) {
            $existingFields = static::Describe($tableName);
            $sql = "ALTER TABLE `$tableName`"; $conn = null;
            foreach ($d as $fieldName => $field) {
                if (isset($field->key) && $field->key == "PRI") continue;
                if (array_key_exists($fieldName,$existingFields)) {
                    // Modify
                    $sql .= "$conn CHANGE COLUMN $fieldName $fieldName {$field->type}";
                } else {
                    // Add
                    $sql .= "$conn ADD COLUMN IF NOT EXISTS $fieldName {$field->type}";
                }
                if (isset($field->default) && $field->default) $sql .= " DEFAULT {$field->default}";
                if (isset($field->extra) && $field->extra) $sql .= " {$field->extra}";
                if (isset($field->key) && $field->key == "UNI") $sql .= " UNIQUE KEY";
                $conn = ",";
            }
            $rlt = static::Execute($sql);
        }
    }

    static function Backup (String $table = null) {
        $obj = new static();
        global $config;
        $configData = (is_array($config)) ? json_decode(json_encode($config)) : $config;
        $zipFolder = __DIR__;
        $backupFolder = $zipFolder . DIRECTORY_SEPARATOR . "backup";
        $excludeTables = ['docs','jwt','logging','users_old','webSearchLocations'];
        $tables = static::Execute("SHOW TABLES");
        foreach ($tables['success']['rows'] as $tableItem) {
            $table = $tableItem['Tables_in_'.$configData->db->database];
            if (in_array($table,$excludeTables)) continue;
            if (file_exists(($destinationFile = $backupFolder . DIRECTORY_SEPARATOR .  $table . ".sql"))) unlink($destinationFile);
            $mysqlCommand = "(mysqldump --skip-comments --compact -u {$configData->db->username} -p'{$configData->db->password}' {$configData->db->database} $table  > $destinationFile) 2>&1";
            echo $mysqlCommand."<br>";
            exec($mysqlCommand, $output, $result);
        }
        
        $backupFiles = scandir($backupFolder);
        if (!$backupFiles || !is_array($backupFiles) || count($backupFiles)<2) return;
        // exit(var_dump($backupFiles));
        $zipFile = $zipFolder. DIRECTORY_SEPARATOR . "sql_backup.zip";
        if (file_exists($zipFile)) unlink($zipFile);
        echo "ZipFile $zipFile<br>";
        $zip = new \ZipArchive;
        if ($zip->open($zipFile, \ZipArchive::CREATE) === TRUE) {
            // echo "$zipFile opened<br>";
            foreach ($backupFiles as $backupFile) {
                if ($backupFile == "." || $backupFile == "..") continue;
                $zip->addFile($backupFolder. DIRECTORY_SEPARATOR . $backupFile,basename($backupFile.".sql"));
                echo "Adding $backupFile - then remove ".$backupFolder. DIRECTORY_SEPARATOR . $backupFile."<br>";
                // unlink($backupFolder. DIRECTORY_SEPARATOR . $backupFile);
            }
            $zip->close();
            $subject = "Backup ".$configData->system->name;
            $message[] = "Hello";
            $message[] = "Backup file attached here";
            $message[] = "Many thanks";
            $message[] = $configData->system->name;
            \Email::Issue($subject,$message,[$configData->email->mailbox => $configData->system->name],[],[],[$zipFile]);
            // unlink($zipFile);
        }
    }

    static function Schema (String $table = null) {
        global $config;
        if (!isset($config->system->production) || $config->system->production !== true) return;
        $obj = new static();
        $mysqlCommand = "(mysqldump -u{$config->db->username} -p'{$config->db->password}' --no-data --skip-comments {$config->db->database} > {$config->core->root}" . DIRECTORY_SEPARATOR . "core" . DIRECTORY_SEPARATOR . "Sql" . DIRECTORY_SEPARATOR . "schema.sql) 2>&1";
        exec($mysqlCommand, $output, $result);
    }

    static function Restore ($filename = null) {
        if (!$filename)return;
        $command = "mysql -u root -p[Paswordofthedatabase] < $filename"; 
    }

    static function Execute (String $sql, Array $data = [],$databaseName = null) {
        if (!self::$instance) self::$instance = new static($databaseName);
        self::$instance->rows = [];
        self::$instance->affectedRows = self::$instance->lastInsertID = null;
        self::$instance->sql = trim($sql); 
        self::$instance->data = $data;
        $return = ["success" => [], "error" => []];
        try {
            self::$instance->statement = self::$instance->pdo->prepare(self::$instance->sql);
            self::$instance->statement->execute(self::$instance->data);
            switch (strstr(self::$instance->sql," ",true)) {
                case "INSERT": 
                case "LOAD": 
                    $return["success"]["lastInsertID"] = (int)self::$instance->pdo->lastInsertId();
                    break;
                case "UPDATE":
                case "DELETE":
                    $return["success"]["rowCount"] = (int)self::$instance->statement->rowCount();
                    break;
                case "SELECT":
                case "DESCRIBE":
                case "SHOW":
                    $rows = [];
                    while ($row = self::$instance->statement->fetch(\PDO::FETCH_ASSOC)) {
                        $rows[] = $row;
                    }
                    $return["success"]["rows"] = (array)$rows;
                    break;
            }
        } catch (\PDOException $e) {
            $return["error"]["code"] = $e->getCode();
            $return["error"]["message"] = (string)$e->getMessage();
            $return["error"]["trace"] = (array)$e->getTrace();
        }
        return $return;
    }

    static function Check($config = null) {
        try {
            $db = self::$instance = new static($config);
            return true;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    static function TableInit (String $tablename = null) {
        if (!$tablename) return "No table name";
        $sql = "CREATE TABLE `$tablename` (`id` int(10) unsigned primary key auto_increment, `created` datetime default current_timestamp(), `updated` datetime default current_timestamp() on update current_timestamp(), `deleted` datetime)";
        return static::Execute($sql);
    }

    static function DbInit (String $dbName, String $dbUser, String $dbPassword) {
        if (!$tablename) return "No DB name";
        $sql = "CREATE DATABASE `$dbName`";
        static::Execute($sql);
        $sql = "GRANT ALL ON `$dbName`.* TO '$dbUser'@'localhost' IDENTIFIED BY '$dbPassword';";
        static::Execute($sql);
        $sql = "FLUSH PRIVILEGES";
        static::Execute($sql);
    }

}