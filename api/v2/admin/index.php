<?php

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1);

use Stripe_v2 as Stripe;

define("ROOT_FOLDER", "/var/www/html/");
define("APP_FOLDER", ROOT_FOLDER . "core");
define("CORE_FOLDER", ROOT_FOLDER . "core");
include(ROOT_FOLDER . 'hub/app/Helpers/General.php');

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
include($init_file);

// For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) {
    http_response_code(404);
    exit(0);
}

/* If not HTTPS - bin it */
if (!Environment::isLocal() && (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS'] != "on")) {
    http_response_code(403);
    exit(json_encode("HTTPS Required"));
}
$data = explode("/", $_GET['url']);
$action = $data[0];

if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
$_SERVER['REQUEST_METHOD'] = strtoupper($_SERVER['REQUEST_METHOD']);
header('Content-Type: application/json');
// Grab any POST data
$post = json_decode(file_get_contents('php://input'), true);

/* Get Incoming Headers */
$apache_request_headers = apache_request_headers();
/* Ensure Request Headers all have a lowercase key */
foreach ($apache_request_headers as $key => $val) $apache_request_headers[strtolower($key)] = $val;
/* Check for Bearer Token */
$authBearerToken = (isset($apache_request_headers['authorization'])) ? trim(strstr($apache_request_headers['authorization'], " ")) : null;

/* Insert here for API testing without Auth */

if (!$authBearerToken) exit(json_encode(["error" => "Token Failure"]));

$userID = Jwt::Validate($authBearerToken);
if (is_string($userID)) exit(json_encode(["error" => $userID]));

$user = new User($userID);
if (!$user->id) exit(json_encode(["error" => "Invalid User"]));

$user->Login(false);

if ($action == "coordinator" && strtolower($_SERVER['REQUEST_METHOD']) == "get") {
    $leagues = League::Coordinator($user);
    $liveSeasons = Season::AllLive();
    $openSeasons = Season::AllOpen();
    $tasters = TasterSession::Coordinator($user);
    $fixtures = Fixture::Coordinator($user);
    $customers = User::Customers(true, true);
    $teams = Team::Live($user);
    $incidents = (new IncidentReport)->count($user);
    $data = [
        "leagueCount" => count($leagues),
        "liveCount" => count($liveSeasons),
        "nextCount" => count($openSeasons),
        "tasterCount" => count($tasters),
        "fixtureCount" => count($fixtures),
        "customerCount" => $customers,
        "teamCount" => count($teams),
        "incidentCount" => $incidents['total'],
        "openIncidentCount" => $incidents['pendingReviewCount']
    ];
    exit(json_encode($data));
}

if ($action == "users" && strtolower($_SERVER['REQUEST_METHOD']) == "get") {
    memoryLimit('512M');
    extract($_GET);
    if (!isset($maxResults)) $maxResults = 50;
    if (!isset($pageOffset)) $pageOffset = 0;
    if (!isset($filterText)) $filterText = null;
    if (!isset($is_coordinator)) $is_coordinator = false;
    $users = User::Search($filterText, $is_coordinator);
    $maxPages = ceil(count($users['data']) / $maxResults);

    $output = [
        "url" => print_r($_GET, true),
        "users" => [],
        "totalCount" => count($users['data']),
        "pages" => (count($users['data']) > $maxResults) ? $maxPages : 1,
        "requestedPage" => $pageOffset,
        "page" => ($pageOffset && $pageOffset <= $maxPages) ? $pageOffset : 1,
        // "sql" => ($users['sql']) ? $users['sql'] : null,
        "notes" => []
    ];

    $counter = 0;
    $starter = ($output['page'] - 1) * $maxResults;
    // $output['notes'][] = "Starter $starter";
    foreach ($users['data'] as $user) {
        // $output['notes'][] = "{$user->firstname} {$user->lastname} {$user->email}";
        if ($counter < $starter) {
            $counter++;
            continue;
        }
        $output['users'][] = $user->ApiData();
        /* Break if we've reached our limit */
        if (count($output['users']) >= $maxResults) break;
        $counter++;
    }
    exit(json_encode($output));
}

if ($action == "user" && strtolower($_SERVER['REQUEST_METHOD']) == "post") {
    // Logging::Add(print_r($post,true),true);
    $user = (isset($post['id']) && $post['id'] && is_numeric($post['id'])) ? new User($post['id']) : new User();
    // Activation Toggling
    $activation = null;
    if ($post['isActivated'] == 1 && !$user->activationStamp) {
        $activation = true;
    } elseif ((!isset($post['isActivated']) || !$post['isActivated']) && $user->activationStamp) {
        $activation = false;
    }
    $user->Load($post);
    if (isset($post['isAdmin']) && User::isManager()) {
        if ($post['isAdmin'] == 1 && !$user->isAdmin) $user->isAdmin = 1;
        if ($post['isAdmin'] == 0 && $user->isAdmin) $user->isAdmin = 0;
    }
    $user->Save();
    if (isset($post['newpassword']) && $post['newpassword']) $user->setPassword($post['newpassword']);
    if ($activation === true) User::AdminActivation($user->id);
    if ($activation === false) User::AdminDeactivation($user->id);
    exit(json_encode($user->ApiData()));
}

if ($action == "lastest-pi-statuses" && strtolower($_SERVER['REQUEST_METHOD']) == "post") {
    if (isset($post) && $post) {
        $stripe = new Stripe(true);
        foreach ($post as $p) {
            $pi = $stripe->fetchPaymentIntent($p);
            // if ($pi->status == "requires_capture") $pi = $stripe->client->paymentIntents->capture($pi->id);
            $captured = $refunded = 0;
            $refundable = true;
            foreach ($pi->charges->data as $charge) {
                $captured += ($charge->amount_captured / 100);
                $refunded += ($charge->amount_refunded / 100);
            }
            $captured = number_format($captured, 2);
            if ($pi->status == "succeeded") {
                if ($refunded) {
                    $refunded = number_format($refunded, 2);
                    if ($refunded == $captured) {
                        $refundable = false;
                        $status = "Refunded";
                    } else $status = "Refunded £$refunded of £$captured";
                } else $status = "Charged £$captured";
            } elseif ($pi->status == "requires_capture") {
                $status = "Pre-authorised £$captured";
            } elseif ($pi->status == "requires_payment_method") {
                $status = "Incomplete";
                $refundable = false;
            } else {
                $status = "Unpaid";
                $refundable = false;
            }
            $output['success'][] = [
                'stripePaymentIntentID' => $pi->id,
                'stripeCustomerID' => $pi->customer,
                'amount' => $pi->amount,
                'status' => $pi->status,
                'internal_status' => $status,
                'refundable' => $refundable
            ];
        }
    } else $output = ['error' => "No statuses requested"];
    exit(json_encode($output));
}

if ($action == "purchase-transaction") {
    $output = [
        "method" => $_SERVER['REQUEST_METHOD'],
        "success" => [],
        "error" => null,
        "received" => $data
    ];
    if ($_SERVER['REQUEST_METHOD'] == "POST") {
        exit(json_encode($output));
    }

    if ($_SERVER['REQUEST_METHOD'] == "GET") {

        exit(json_encode($output));
    }
    /* METHODS NOW MUST HAVE ID */
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) {
        $output['error'][] = "Missing ID";
    } elseif (!($purchaseTransaction = new PurchaseTransaction($data[1]))) {
        $output['error'][] = "Invalid ID";
    }
    if ($_SERVER['REQUEST_METHOD'] == "PUT") {
        exit(json_encode($output));
    }
    if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
        $log_message = "PI {$data[1]} deleted by {$user}";
        $output['log_message'] = $log_message;
        $purchaseTransaction->Delete($log_message);
        exit(json_encode($output));
    }
}

if ($action == "taster-session-cancel") {
    if (isset($data[1]) && $data[1] && is_numeric($data[1]) && ($tasterSession = new TasterSession($data[1])) && $tasterSession->id) {
        $tasterSession->Cancel();
        exit(json_encode($tasterSession->ApiData()));
    }
}

if ($action == "taster-session-refund") {
    if (isset($data[1]) && $data[1] && is_numeric($data[1]) && ($tasterSession = new TasterSession($data[1])) && $tasterSession->id) {
        $tasterSession->Refund();
        exit(json_encode($tasterSession->ApiData()));
    }
}

if ($action == "billing-log-data") {
    $date = (isset($data[1]) && $data[1]) ? $data[1] : date('Y-m-d');
    $items = Billing_Log::Date($date);
    if (!$items) return [];
    $output = [];
    foreach ($items as $item) $output[] = $item->ApiData();
    exit(json_encode(["success" => $output]));
}

if ($action == "library" && strtoupper($_SERVER['REQUEST_METHOD']) == "POST") {

    $output = [
        "server" => $_SERVER,
        "post" => $post,
        "files" => $_FILES,
        "request" => $_REQUEST,
    ];
    $output['rlt'] = DocLibraryVersion::Upload($post, $_FILES['file']);
    exit(json_encode(["success" => $output]));
}

if ($action == "coordinator-incident-report") {
    exit(getIncidentReports('coordinator', $userID));
}

if ($action == "incident-report-details") {
    $incident_report = IncidentReport::getAnIncident($data[1], $userID)[0];

    exit(json_encode($incident_report->ApiOutput()));
}


if ($action == "extended-incident-questions-save" && strtoupper($_SERVER['REQUEST_METHOD']) == "POST") {

    if (!isset($data[1]) && empty($data[1])) {
        exit(json_encode([
            'success' => 'false',
            'message' => 'incident Id not found'
        ]));
    }
    $incident_report = IncidentReport::getAnIncident($data[1], $userID, isset($_GET['incidentReportStatus']) ? $_GET['incidentReportStatus'] : null)[0];

    if (empty($incident_report)) {
        exit(json_encode([
            'success' => 'false',
            'message' => 'incident not found'
        ]));
    }
    $incident_report = new IncidentReport($incident_report->id);
    $incident_report->Load($_POST);
    if (isset($_FILES['official_statement_file']) && $_FILES['official_statement_file']['error'] == UPLOAD_ERR_OK) {
        $incident_report->official_statement_file = upload_files($_FILES['official_statement_file']);
    }

    foreach ($_FILES['witness_statement']['name'] as $key => $tmpName) {
        $statements[$key]['name'] = $tmpName;
    }
    foreach ($_FILES['witness_statement']['tmp_name'] as $key => $tmpName) {
        $statements[$key]['tmp_name'] = $tmpName;
    }
    foreach ($statements as $key => $statement) {
        $witness_statements[$key] = upload_files($statement);
        $incident_report->witness_statement_files = serialize($witness_statements);
    }
    $incident_report->save();


    $viewUrl = getDomainPrefix() . "hub.leagues4you.co.uk/Coordinator/incidentReportDetails/$incident_report->id";
    $message = array("Please review the following incident <a href='$viewUrl'>click here</a> \n Thanks");


    Email::Issue("A new incident has been reported", $message, $config['admin_mail']);
    exit(json_encode([
        'post' => $_POST,
        'data' => $incident_report->ApiOutput(),
        'save' => $incident_report->save()
    ]));
}
if ($action == "admin-incident-report-details") {

    $incident_report = IncidentReport::getAnIncident($data[1], $userID, $_GET['incidentReportStatus'])[0];

    exit(json_encode($incident_report->ApiOutput()));
}


if ($action == "admin-incident-reports") {
    exit(getIncidentReports('admin'));
}

if ($action == "close-incident-report") {
    $incident_report = new IncidentReport($data[1]);

    $incident_report->status = 'closed';
    $incident_report->save();
    $output = array(
        'success' => true,
        'message' => 'incident closed successfully'
    );
    exit(json_encode($output));
}

if ($action == "get-coordinator-dropdown") {
    // die($authBearerToken);
    $coordinators = User::Coordinators();
    $output = [];
    foreach ($coordinators as $coordinator) {
        $output[] = [
            "id" => $coordinator->id,
            "name" => $coordinator->firstname . ' ' . $coordinator->lastname
        ];
    }

    return exit(json_encode($output));
}
if ($action == "user-table-users" && strtolower($_SERVER['REQUEST_METHOD']) == "get") {
    memoryLimit('512M');
    extract($_GET);
    if (!isset($maxResults)) $maxResults = 50;
    if (!isset($pageOffset)) $pageOffset = 0;
    if (!isset($filterText)) $filterText = null;
    if (!isset($is_coordinator)) $is_coordinator = false;
    $users = User::Search($filterText, $is_coordinator);
    $maxPages = ceil(count($users['data']) / $maxResults);

    $output = [
        "url" => print_r($_GET, true),
        "users" => [],
        "totalCount" => count($users['data']),
        "pages" => (count($users['data']) > $maxResults) ? $maxPages : 1,
        "requestedPage" => $pageOffset,
        "page" => ($pageOffset && $pageOffset <= $maxPages) ? $pageOffset : 1,
        "notes" => [],
    ];
    $counter = 0;
    $starter = ($output['page'] - 1) * $maxResults;
    foreach ($users['data'] as $user) {
        if ($counter < $starter) {
            $counter++;
            continue;
        }
        $output['users'][] = $user->ApiListOutput($user->id);
        if (count($output['users']) >= $maxResults) break;
        $counter++;
    }
    exit(json_encode($output));
}



function getIncidentReports($method, $userID = null) {
    $startDate = isset($_GET['start-date']) && !empty($_GET['start-date']) ? DateTime::createFromFormat('d/m/Y', $_GET['start-date'])->format('Y-m-d') : null;
    $endDate = isset($_GET['end-date']) && !empty($_GET['end-date']) ?  DateTime::createFromFormat('d/m/Y', $_GET['end-date'])->format('Y-m-d') : null;
    $VenueId = $_GET['venues'] ?? null;
    $Status = isset($_GET['status']) && !empty($_GET['status']) ? str_replace('_', ' ', $_GET['status']) : null;
    $is_important = null;
    if (isset($_GET['is_important']) && !empty($_GET['is_important'])) {
        $is_important = $_GET['is_important'] == 'yes' ? 1 : 0;
    }
    $page = isset($_GET['page']) && !empty($_GET['page']) ? $_GET['page'] : 1;
    $itemsPerPage = 25;
    $url = '';
    $coordinatorID =  isset($_GET['coordinatorID']) && !empty($_GET['coordinatorID']) ? $_GET['coordinatorID'] : null;
    if ($method === 'admin') {
        $url = '/Admin/adminIncidentReports';
        $result = IncidentReport::adminIncidentReports(
            $itemsPerPage,
            $startDate,
            $endDate,
            $VenueId,
            $Status,
            $is_important,
            $page,
            $coordinatorID,
        );
    } else if ($method === 'coordinator') {
        $url = '/Admin/coordinatorIncidentReport';
        $result = IncidentReport::CoordinatorIncidentReport(
            $userID,
            $itemsPerPage,
            $startDate,
            $endDate,
            $VenueId,
            $Status,
            $is_important,
            $page
        );
    } else {
        return null;
    }

    $incident_reports = $result['incidents'];
    $total = $result['total'];

    $paginationLinks = generatePaginationLinks($total, $page, $itemsPerPage, $url);

    $output = [];
    $incident_reports_output = [];
    foreach ($incident_reports as $incident_report) {
        if (empty($incident_report)) continue;
        $incident_reports_output[] = $incident_report->ApiOutput();
    }
    $output['incidents'] = $incident_reports_output;
    $output['total'] = $total;
    $output['pagination'] = $paginationLinks;
    return json_encode($output);
}

function generatePaginationLinks($total, $currentPage, $itemsPerPage, $url) {
    $totalPages = ceil($total / $itemsPerPage);
    $paginationLinks = [];

    for ($i = 1; $i <= $totalPages; $i++) {
        $paginationLinks[] = [
            'page' => $i,
            'url' => $url . '?page=' . $i,
            'is_current' => $i == $currentPage
        ];
    }

    return $paginationLinks;
}

function upload_files($file) {
    $uploadDir = getcwd() . '/../public/images/';
    $uploadFile = $uploadDir .  date("h:i:sa") . '-' . basename($file['name']);

    // Ensure upload directory exists
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0775, true);
    }

    if (move_uploaded_file($file['tmp_name'], $uploadFile)) {
        // Save file path to incident report object
        $official_statement_file = getDomainPrefix() . 'public.v2.api.leagues4you.co.uk/images/' .  date("h:i:sa") . '-' . basename($file['name']);
        return $official_statement_file;
    } else {
        exit(json_encode([
            'Post' =>  $uploadDir,
            "file" => $file,
            "bool" => is_dir($uploadDir),
            'success' => 'false',
            'message' => 'File upload failed'
        ]));
    }
}
