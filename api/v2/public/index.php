<?php
/* */
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1);

define("ROOT_FOLDER", "/var/www/html/");
define("APP_FOLDER", ROOT_FOLDER . "core");
define("CORE_FOLDER", ROOT_FOLDER . "core");
include(ROOT_FOLDER . 'hub/app/Helpers/General.php');

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
require_once($init_file);

# For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) {
    http_response_code(404);
    exit(0);
}

function generateError(String $message, Int $code = 401) {
    http_response_code($code);
    exit(json_encode($message));
}

# If not HTTPS - bin it
// if (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS'] != "on") generateError("HTTPS Required", 403);

if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}

# JSON from here on
header('Content-Type: application/json');

# Grab any POST data
$post = json_decode(file_get_contents('php://input'), true);
# Format Request Method
$requestMethod = (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD']) ? strtoupper($_SERVER['REQUEST_METHOD']) : null;
# Explode URL
$data = explode("/", $_GET['url']);

# Get Incoming Headers
$apache_request_headers = apache_request_headers();
# Ensure Request Headers all have a lowercase key
foreach ($apache_request_headers as $key => $val) $apache_request_headers[strtolower($key)] = $val;
# A public API has no need for a Bearer Token ~ Check for Bearer Token
/*
$authBearerToken = (isset($apache_request_headers['authorization'])) ? trim(strstr($apache_request_headers['authorization']," ")) : null;
$userID = ($authBearerToken) ? Jwt::Validate($authBearerToken) : null;
$user = ($userID) ? new User($userID) : null;
*/
if ($data[0] == "sport" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Need a valid Sport ID");
    $sport = new Sport($data[1]);
    if (!$sport->id) generateError("Sport {$data[1]} does not exist");
    exit(json_encode($sport->ApiData()));
}

if ($data[0] == "registration" && $requestMethod == "POST") {
    $rlt = User::Register($post);
    if ($rlt['success']) {
        $user = $rlt['success'];
        $user->setSecurityCode();
        $user->sendSecurityCode();
        exit(json_encode(["success" => "Ok"]));
    } else exit(json_encode(["error" => $rlt['error']]));
}

if ($data[0] == "activation" && $requestMethod == "POST") {
    if (!$post) exit(json_encode(["error" => "No data submitted"]));
    if (!isset($post['email']) || !$post['email'] || !filter_var($post['email'], FILTER_VALIDATE_EMAIL)) exit(json_encode(['error' => "Invalid or missing email"]));
    if (!isset($post['code']) || !$post['code'] || !is_numeric($post['code'])) exit(json_encode(['error' => "Invalid or missing code"]));
    $user = User::EmailLookup($post['email']);
    if (!$user) exit(json_encode(["error" => "Invalid User"]));
    if ($user->checkSecurityCode($post['code']) === true) {
        $returnObject = [
            'token' => Jwt::Create($user, $_SERVER),
            'cookie' => [
                "expires" => date('D, j M Y H:i:s \U\T\C', strtotime("+14 days")),
                "path" => "/",
                "domain" => ".leagues4you.co.uk"
            ]
        ];
        if (isset($post['rememberDevice']) && $post['rememberDevice']) {
            $returnObject['deviceToken'] = Auth::CreateDeviceToken($user->id);
        }
        return exit(json_encode(["success" => $returnObject]));
    } else return exit(json_encode(["error" => "Invalid or Expired Code"]));
}

if ($data[0] == "authenticate") {
    $returnObject = [
        "code" => "",
        "token" => "",
        "message" => "",
        "success" => false
    ];

    if (!isset($post['email']) || !$post['email']) {
        $returnObject['code'] = __LINE__;
        $returnObject['message'] = "Missing or Invalid Email";
    } elseif (!isset($post['password']) || !$post['password']) {
        $returnObject['code'] = __LINE__;
        $returnObject['message'] = "Missing or Invalid Password";
    } else {
        $user = User::EmailLookup($post['email']);
        if (!$user || !$user->id) {
            $returnObject['code'] = __LINE__;
            $returnObject['message'] = "No such user";
        } elseif (($rlt = Auth::CheckPassword($user, $post['password'])) !== true) {
            $returnObject['rlt'] = print_r($rlt, true);
            $returnObject['code'] = __LINE__;
            $returnObject['message'] = "Login Failed";
        } else {
            // Check if user has required role
            if (!$user->hasRequiredRole()) {
                $returnObject['code'] = __LINE__;
                $returnObject['message'] = "Access Denied - Insufficient Permissions";
            } else {
                // Check if device is remembered
                $deviceToken = isset($_COOKIE['device_token']) ? $_COOKIE['device_token'] : null;
                if ($deviceToken && Auth::ValidateDeviceToken($user->id, $deviceToken)) {
                    // Skip 2FA for remembered device
                    $returnObject['success'] = true;
                    $returnObject['token'] = Jwt::Create($user, $_SERVER);
                    $returnObject['cookie'] = [
                        "expires" => date('D, j M Y H:i:s \U\T\C', strtotime("+14 days")),
                        "path" => "/",
                        "domain" => ".leagues4you.co.uk"
                    ];
                } else {
                    // Proceed with 2FA
                    $user->sendAuthCode();
                    $returnObject['success'] = true;
                    $returnObject['requiresAuth'] = true;
                }
            }
        }
    }
    exit(json_encode($returnObject));
}

if ($data[0] == "reminder") {
    // header('Content-Type: application/json');
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "Missing Username"]));
    if (!filter_var($data[1], FILTER_VALIDATE_EMAIL)) exit(json_encode(["error" => "Invalid Username"]));
    $user = User::EmailLookup($data[1]);
    if (!$user) exit(json_encode(["error" => "Unknown User"]));
    $user->setSecurityCode();
    $user->sendSecurityCode();
    exit(json_encode(["success" => "Ok"]));
}

if ($data[0] == "venues-local" && $requestMethod == "GET") {
    // header('Content-Type: application/json');
    /* Expects $data[1] as $lat, $data[2] as $lng */
    /* Accepts $data[3] as $radius, else 100km */
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1]) || $data[1] > 90 || $data[1] < -90) exit(json_encode(["error" => "Latitude must be a numeric value from -90 to 90"]));
    if (!isset($data[2]) || !$data[2] || !is_numeric($data[2]) || $data[2] > 180 || $data[2] < -180) exit(json_encode(["error" => "Longitude must be a numeric value from -180 to 180"]));
    if (!isset($data[3]) || !$data[3] || !is_numeric($data[3])) $data[3] = 25;
    // $venues = Venue::Proximates ($data[1],$data[2],$data[3]);
    // $output = [];
    // if ($venues) {
    //     foreach ($venues as $venue) {
    //         $venue->getLeagues();
    //         $output[] = $venue->ApiData();
    //     } 
    // }
    $rlt = League::SearchRegisterableByLatLng($data[1], $data[2], $data[3]);
    exit(json_encode(["success" => $rlt]));
}

if ($data[0] == "league-search" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || strlen($data[1]) < 3) exit(json_encode(["error" => "Invalid Search Term - Min 3 characters"]));

    // $venues = Venue::liveSeasonLeagueNameVenueSearch($data[1]);
    $venues = Venue::SearchForRegistrationLeagues($data[1]);
    // $output = $venues;
    $output = [];
    if ($venues) {
        foreach ($venues as $venue) {
            $venue->getRegisterableLeagues();
            $output[] = $venue->ApiData();
        }
    }
    exit(json_encode(["success" => $output]));
}

if ($data[0] == "registration-league-search" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || strlen($data[1]) < 3) exit(json_encode(["error" => "Invalid Search Term - Min 3 characters"]));
    $rlt = League::SearchRegisterable($data[1]);
    exit(json_encode(["success" => $rlt]));
}

if ($data[0] == "team-name-checker" && strtoupper($_SERVER['REQUEST_METHOD']) == "POST") {
    /* Receives newTeam data in post variable */
    /* Expects newTeam.league.id for duplication checks */

    $league = (isset($post['league']['id']) && $post['league']['id']) ? new League($post['league']['id']) : null;

    $permitted = Team::Profanity($post['name']);

    $name = strtolower($post['name']);
    if ($permitted === true) {
        if ($league) {
            $teams = Team::LeagueTeams($league);
            $log = [];
            foreach ($teams as $team) {
                $log[] = "Checking if {$team->name} = {$name}";
                if (strtolower(preg_replace("/[^a-zA-Z0-9_]/", "", $team->name)) == strtolower(preg_replace("/[^a-zA-Z0-9_]/", "", $name))) {
                    $permitted = "cannot be used in $league as that team name has already been used";
                    break;
                }
            }
            $output['log'] = $log;
        }
    }
    $output['log'][] = $league;
    $output['log'][] = $post;
    if ($permitted === true) {
        $output["success"] = preg_replace("/[^a-zA-Z0-9_]/", " ", $post['name']);
        $output["success"] = ltrim(preg_replace("/\s+/", " ", $output["success"]));
    } else $output["error"] = "{$post['name']} $permitted";
    exit(json_encode($output));
}

if ($data[0] == "team-search") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "No search term provided"]));
    $pageNo = (isset($data[2]) && $data[2] && is_numeric($data[2])) ? $data[2] : 1;
    $limit = (isset($data[3]) && $data[3] && is_numeric($data[3])) ? $data[3] : 50;
    $teams = Team::Search(urldecode($data[1]), $pageNo, $limit, false);
    $output = [];
    if ($teams) {
        foreach ($teams as $team) {
            $activeSeasons = TeamSeason::Current(new Team($team['teamID']));
            if (!$activeSeasons) continue;
            $output[] = ["teamID" => $team['teamID'], "teamName" => $team['teamName'], "teamDeleted" => $team['teamDeleted'], "leagueID" => $team['leagueID'], "leagueName" => $team['leagueName']];
        }
    }
    exit(json_encode(["success" => $output]));
}

if ($data[0] == "team" && strtoupper($_SERVER['REQUEST_METHOD']) == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id) exit(json_encode(["error" => "Invalid Team"]));
    $league = new League($team->leagueID);

    // 1 - Live , 4 - Completed
    $seasons = Season::Team($team, [1, 4]);
    $teamSeasons = [];
    if ($seasons) {
        foreach ($seasons as $season) $teamSeasons[] = $season->ApiOutput();
    }
    $output = [
        "id" => $team->id,
        "name" => $team->name,
        "league" => $league->ApiBrief(),
        "seasons" => $teamSeasons
    ];
    exit(json_encode(["success" => $output]));
}

if ($data[0] == "season" && strtoupper($_SERVER['REQUEST_METHOD']) == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Season ID"]));
    $season = new Season($data[1]);
    if (!$season || !$season->id) exit(json_encode(["error" => "Invalid Season"]));
    exit(json_encode(["success" => $season->ApiData2()]));
}

if ($data[0] == "league-list" && $requestMethod == "GET") {
    $WebsiteList = Venue::WebsiteList();
    if (is_string($WebsiteList)) exit(json_encode(["error" => "Could not produce League List"]));
    exit(json_encode($WebsiteList));
}

if ($data[0] == "league-info" && $requestMethod == "GET") {
    if (!isset($data[1])) $data[1] = "malvern-netball-league";
    $league = League::fromSlug($data[1]);
    exit(json_encode($league->InfoApi()));
    // exit (json_encode([
    //     'id' => 1,
    //     'data1' => (isset($data[1])) ? $data[1] : null,
    //     'name' => 'Malvern Netball League',
    //     'enterTeamImgUrl' => 'https://leagues4you.co.uk/wp-content/uploads/2022/07/enter_your_team_2.jpg',
    //     'joinTeamImgUrl' => 'https://leagues4you.co.uk/wp-content/uploads/2022/07/join_a_team_2.jpg',
    //     'videoexplainer' => 'https://www.youtube.com/embed/BnTIB2T94Fc',
    //     'registration' => [
    //         'id' => 1,
    //         'name' => 'Autumn 2022',
    //         'day' => 'Monday',
    //         'startTime' => '7pm',
    //         'endTime' => '9pm',
    //         'situated' => 'Outdoor',
    //         'fixtureCharge' => 30,
    //         'launchDate' => '2022-09-01',
    //         'venue' => [
    //             'id' => 1,
    //             'name' => 'MSJ Sports Centre',
    //             'line1' => 'Barnards Green Road',
    //             'town' => 'Malvern',
    //             'postcode' => 'WR14 3LH',
    //         ]
    //     ],
    //     'live' => [
    //         'id' => 2,
    //         'tables' => [
    //             'Division 1' => [
    //                 'Team A' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team B' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team C' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team D' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team E' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team F' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //             ],
    //             'Division 2' => [
    //                 'Team G' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team H' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team I' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team J' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team K' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team L' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //             ],
    //             'Division 3' => [
    //                 'Team M' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team N' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team O' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team P' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team Q' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team R' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //             ],
    //             'Division 4' => [
    //                 'Team S' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team T' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team U' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team V' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team W' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //                 'Team X' => ['w' => 2, 'l' => 3, 'd' => 4, 'f' => 5, 'a' => 6, 'p' => 8, 'b' => 9],
    //             ],
    //         ]
    //     ],
    //     'facebook' => [
    //         'generalGroup' => [
    //             'name' => 'leagues4you', 
    //             'url' => 'https://www.facebook.com/leagues4you/'
    //         ],
    //         'playerRequestGroup' => 'https://www.facebook.com/groups/114666899207651/'
    //     ],
    //     'coordinator' => [
    //         'profileImageUrl' => 'https://leagues4you.co.uk/wp-content/uploads/2022/07/profile-lynsey.jpg',
    //         'firstname' => 'Lynsey',
    //         'email' => '<EMAIL>'
    //     ],
    //     'nearby' => [
    //         ['id' => 1, 'name' => 'League 1'],
    //         ['id' => 2, 'name' => 'League 2'],
    //         ['id' => 3, 'name' => 'League 3'],
    //         ['id' => 4, 'name' => 'League 4'],
    //         ['id' => 5, 'name' => 'League 5'],
    //         ['id' => 6, 'name' => 'League 6'],
    //         ['id' => 7, 'name' => 'League 7'],
    //         ['id' => 8, 'name' => 'League 8'],
    //         ['id' => 9, 'name' => 'League 9'],
    //         ['id' => 10, 'name' => 'League 10'],
    //     ]
    // ]));
}

if ($data[0] == "sync-payment" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "Invalid Payment ID"]));
    $rlt = Stripe::SyncByPaymentIntentID($data[1]);
    Email2::Issue("Update to Payment Intent (Public API)", ["Hi", "Payment Intent {$data[1]} has been updated", "Thanks", "leagues4you"], ["<EMAIL>" => "a2z"]);
    exit("Ok");
}

if ($data[0] == "report-an-incident" && $requestMethod == "POST") {
    if (!isset($_POST['incident_report'])) exit(json_encode([
        "success" => "failed",
        "message" => "No Incident Report"
    ]));

    $incident_report = new IncidentReport();
    $incident_report->Load($_POST);
    $incident_report->time_of_incident = explode(' ', $_POST['time_of_incident'])[0];

    if ($incident_report->save()) {
        if ($incident_report->id == null) {
            exit(json_encode($incident_report->save()));
        }
        $viewUrl = getDomainPrefix() . "hub.leagues4you.co.uk/Coordinator/IncidentReportDetails/$incident_report->id";
        $message = array("Please review the following incident <a href='$viewUrl'>click here</a> <br> Thanks");
        Email::Issue("A new incident has been reported", $message, [$incident_report->getCoordinatorEmailFromForm($_POST['leagueId']) => "leagues4you"]);

        exit(json_encode([
            "success" => "ok",
            "message" => "Incident Reported",
        ]));
    }
    exit(json_encode([
        "success" => $_POST,
        "message" => "Incident Report Failed"
    ]));
}
if ($data[0] == "venues-dropdown" && $requestMethod == "GET") {
    $venues = Venue::ActiveVenues();
    if (!$venues || !is_array($venues) || count($venues) == 0) exit(0);
    $output = [];
    foreach ($venues as $venue) {
        $output[] = [
            "id" => $venue->id,
            "name" => $venue->name
        ];
    }
    exit(json_encode($output));
}


if ($data[0] == "get-leagues-by-venue" && $requestMethod == "GET") {
    $venue = new Venue($data[1]);
    $leagues = $venue->getLeagues();
    $output = [];
    foreach ($leagues as $league) {
        $output[] = [
            "id" => $league->id,
            "name" => $league->name
        ];
    }
    exit(json_encode($output));
}

// This should be removed in the future - Agit
if ($data[0] == "locker-login") {
    $returnObject = [
        "code" => "",
        "token" => "",
        "message" => "",
        "success" => false
    ];
    if (!isset($post['email']) || !$post['email']) {
        $returnObject['code'] = __LINE__;
        $returnObject['message'] = "Missing or Invalid Email";
    } elseif (!isset($post['password']) || !$post['password']) {
        $returnObject['code'] = __LINE__;
        $returnObject['message'] = "Missing or Invalid Password";
    } else {
        $user = User::EmailLookup($post['email']);
        if (!$user || !$user->id) {
            $returnObject['code'] = __LINE__;
            $returnObject['message'] = "No such user";
        } elseif (($rlt = Auth::CheckPassword($user, $post['password'])) !== true) {
            $returnObject['rlt'] = print_r($rlt, true);
            $returnObject['code'] = __LINE__;
            $returnObject['message'] = "Login Failed";
        } else {
            $returnObject['success'] = true;
            $returnObject['token'] = Jwt::Create($user, $_SERVER);
            $returnObject['cookie'] = [
                "expires" => date('D, j M Y H:i:s \U\T\C', strtotime("+14 days")),
                "path" => "/",
                "domain" => ".leagues4you.co.uk"
            ];
        }
    }
    $logging = array_merge($post, $returnObject);
    exit(json_encode($returnObject));
}

generateError("Cannot Help");
