<?php

use Stripe_v2 as Stripe;

define("ROOT_FOLDER", "/var/www/html/");
define("APP_FOLDER", ROOT_FOLDER . "core");
define("CORE_FOLDER", ROOT_FOLDER . "core");

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
include($init_file);

// For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) {
    http_response_code(404);
    exit(0);
}

/* If not HTTPS - bin it */
// if (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS']!="on") {
//     http_response_code(403); exit (json_encode("HTTPS Required"));
// }
$data = explode("/", $_GET['url']);
// $action = $data[0];

if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
$_SERVER['REQUEST_METHOD'] = strtoupper($_SERVER['REQUEST_METHOD']);
header('Content-Type: application/json');

// Grab any POST data
$post = json_decode(file_get_contents('php://input'), true);
// Format Request Method
$requestMethod = (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD']) ? strtoupper($_SERVER['REQUEST_METHOD']) : null;
/* Get Incoming Headers */
$apache_request_headers = apache_request_headers();
/* Ensure Request Headers all have a lowercase key */
foreach ($apache_request_headers as $key => $val) $apache_request_headers[strtolower($key)] = $val;
/* Check for Bearer Token */
$authBearerToken = (isset($apache_request_headers['authorization'])) ? trim(strstr($apache_request_headers['authorization'], " ")) : null;

if ($data[0] == "library" && $requestMethod == "GET") {
    // $listing = DocLibraryType::Listing();
    // $output = [];
    // foreach ($listing as $l) $output[] = $l->ApiData();
    $output = [
        ['name' => 'Terms and Conditions', 'slug' => 'https://cdn.bloomnetball.co.uk/netball-terms-and-conditions'],
        ['name' => 'Policies and Guidelines', 'slug' => 'https://cdn.bloomnetball.co.uk/policies-and-guidelines '],
        ['name' => 'Safeguarding', 'slug' => 'https://cdn.bloomnetball.co.uk/safeguarding'],
    ];

    $playerGuides = Sport::PlayerGuides();
    foreach ($playerGuides as $pg) {
        $output[] = ['name' => "{$pg['name']} Player Guide", 'slug' => $pg['captainsPack']];
    }

    exit(json_encode(["success" => $output]));
}




// $authBearerToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjEzNzUifQ.O0EdnbYvDtvCLd5ThM6ju6Vu9rlSnv7RH0VsnUdAJBE";

/* Insert here for API testing without Auth */
// $user = new User(2);

// $adminOverride = (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR']=="2a00:23c4:ce17:2001:20f1:c5b1:6b48:443") ? true : false;
// $adminOverride = false;

if (!$authBearerToken) exit(json_encode(["error" => "Token Failure", "headers" => $apache_request_headers]));

$userID = Jwt::Validate($authBearerToken);
if (is_string($userID)) exit(json_encode(["error" => $userID]));

// $user = ($userID==2) ? new User(1182) : new User($userID);
// $user = ($userID==2) ? new User(4426) : new User($userID);
// $user = ($userID==2) ? new User(3618) : new User($userID);
// $user = ($userID==2) ? new User(1596) : new User($userID);
// $user = ($userID==2) ? new User(1716) : new User($userID);
// $user = ($userID==2) ? new User(5300) : new User($userID);

// $user = ($userID==2) ? new User(568) : new User($userID);
// $user = ($userID==2) ? new User(1094) : new User($userID);
// $user = ($userID==2) ? new User(7791) : new User($userID);
$user = ($userID == 2) ? new User(2617) : new User($userID);

if (!$user->id) exit(json_encode(["error" => "Invalid User"]));

if ($data[0] == "profile" && $requestMethod == "GET") {
    exit(json_encode(["success" => $user->Profile()]));
}

if ($data[0] == "profile" && $requestMethod == "POST") {
    if (!isset($post) || !isset($post['id']) || !$post['id']) exit(json_encode(["error" => "Missing User"]));
    $user = new User($post['id']);
    $user->Load($post);
    $rlt = $user->Save();
    if (is_string($rlt)) exit(json_encode(["error" => $rlt]));
    exit(json_encode(["success" => $user->Profile()]));
}

if ($data[0] == "league-search" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || strlen($data[1]) < 3) exit(json_encode(["error" => "Invalid Search Term - Min 3 characters"]));
    $leagues = League::Search($data[1]);
    if ($leagues['error']) exit(json_encode(["error" => $leagues['error']]));
}

if ($data[0] == "passchange" && $requestMethod == "POST") {
    if (!isset($post['password1']) || !$post['password1'])  exit(json_encode(["error" => "Passchange Err 1"]));
    if (!isset($post['password2']) || !$post['password2'])  exit(json_encode(["error" => "Passchange Err 2"]));
    if ($post['password1'] != $post['password2'])  exit(json_encode(["error" => "Passwords do not match"]));
    $user->setPassword($post['password1']);
    exit(json_encode(["success" => "Ok"]));
}

if ($data[0] == "team-submit" && $requestMethod == "POST") {
    // Expects teamName, leagueID
    // Accepts stripePaymentMethodID
    $team = new Team();
    $team->name = $post['teamName'];
    $team->leagueID = $post['leagueID'];

    if ($team->DuplicateCheck()) exit(json_encode(["error" => "A team with that name already exists in that League"]));
    if (Team::Profanity($team->name) !== true) exit(json_encode(["error" => "That team name contains profanity that is not permitted"]));
    $team->Save();

    $team->getLeague();
    if (!($season = Season::hasOFR($team->league))) exit(json_encode(["error" => "No Season available to register into"]));

    if (!($division = Division::SeasonDefault($season))) exit(json_encode(["error" => "No Division available to register into"]));

    if (!$division->id) exit(json_encode(["error" => "No Division available to register into"]));

    $stripePaymentIntentID = (isset($post['payment_method']) && $post['payment_method']) ? $post['payment_method'] : null;

    $teamSeasonID = TeamSeason::Add($team->id, $season->id, $division->id);
    TeamFollower::Add($team, $user, $stripePaymentIntentID, true, true);
    $teamSeason = TeamSeason::TeamSeason($team, $season);
    $teamSeason->ConfirmationEmail();
    exit(json_encode(["success" => $team->ApiData()]));
}

if ($data[0] == "team-follow" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id) exit(json_encode(["error" => "Invalid Team"]));
    // $action = Team::Follow($team,$user);
    TeamFollower::Follow($team, $user);
    $action = "Ok";
    exit(json_encode(["success" => $action]));
}

if ($data[0] == "team-unfollow" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id) exit(json_encode(["error" => "Invalid Team"]));
    // $action = Team::Unfollow($team,$user);
    TeamFollower::UnFollow($team, $user);
    $action = "Ok";
    exit(json_encode(["success" => $action]));
}

if ($data[0] == "team-reenter" && $requestMethod == "GET") {
    # Expects $data[1] as teamID
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Team ID"]));
    # Expects $data[2] as seasonID
    if (!isset($data[2]) || !$data[2] || !is_numeric($data[2])) exit(json_encode(["error" => "Invalid Season ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id)  exit(json_encode(["error" => "Invalid Team"]));
    $season = new Season($data[2]);
    if (!$season || !$season->id)  exit(json_encode(["error" => "Invalid Season"]));
    # Confirm that User is a Team Manager
    $teamManagers = $team->getTeamManagers();
    if ($team->captain != $user && $team->treasurer != $user) exit(json_encode(["error" => "Permission Denied"]));
    # Confirm if seasonID is a Next Season
    $league = $season->getLeague();
    $next = Season::Next($league);
    if ($season->id != $next->id) exit(json_encode(["error" => "Cannot resubscribe to a {$league->name} Season ({$season->id}) that isn't Next ({$next->id})"]));
    # Confirm that teamID not already in seasonID
    if (($teamSeasonID = TeamSeason::IsIn($team, $season))) exit(json_encode(["error" => "Already resubscribed under ID $teamSeasonID"]));
    # Enter team into New Season
    $teamSeasonID = TeamSeason::Resubscribe($team, $season);
    $teamSeason = TeamSeason::TeamSeason($team, $season);
    $teamSeason->ConfirmationEmail();
    exit(json_encode(["success" => $teamSeasonID]));
}

if ($data[0] == "team-financials" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team) exit(json_encode(["error" => "Invalid Team"]));
    # Is User a Team Manager?
    $team->getTeamManagers();
    if ($team->captain != $user && $team->treasurer != $user) exit(json_encode(["error" => "Permission Denied"]));
    $output['statement'] = Finance::ApiStatement($team, true);
    $output['balance'] = Finance::Balance($team);
    $output['card'] = $team->PaymentCard();
    $liveSeason = TeamSeason::Live($team);
    $output['invoiceURL']['live'] = ($liveSeason) ? $liveSeason->getInvoiceURL() : null;
    $canReenter = $team->CanReenter();
    if ($canReenter) {
        $league = $team->getLeague();
        $canReenter->getVenue();
        $output['canReenter'] = [
            "team" => ["id" => $team->id, "name" => $team->name],
            "league" => ["id" => $league->id, "name" => $league->name],
            "season" => ["id" => $canReenter->id, "name" => $canReenter->name],
            "venue" => ["id" => $canReenter->venue->id, "name" => $canReenter->venue->name,],
            "day" => $canReenter->FullDayText(),
            "launchDate" => $canReenter->launchDate,
            "fixtureCharge" => (float)$canReenter->fixtureCharge,
            "coordinator" => ["firstname" => $league->getCoordinator()->firstname, "lastname" => $league->getCoordinator()->lastname, "email" => $league->getCoordinator()->email]
        ];
    } else $output['canReenter'] = null;
    $output['paymentOption'] = TeamFollower::PaymentOption($team);
    if (($live = TeamSeason::Live($team))) {
        // $output['fullBalancePayment'] = $live->FullBalancePayment();
        $output['fullBalancePayment'] = $live->FullBalanceToPay();
    }
    ## Payment Requires Action
    // if ($user->id == 4228) {
    if ($team->treasurer == $user) $output['paymentToConfirm'] = $team->PaymentToConfirm();
    // $output['paymentToConfirm'] = $team->PaymentToConfirm();
    // }
    // if ($user->id ==2) {
    //     $paymentIntentID = "pi_3LC54OLOCeRFt5lu0LADH4Ng";
    //     $cardPayment = new CardPayment(false);
    //     $paymentIntent = $cardPayment->PaymentIntent_Fetch($paymentIntentID);
    //     $output['paymentToConfirm'] = [
    //         // "mode" => "Test",
    //         "amount" => $paymentIntent->amount / 100,
    //         "client_secret" => $paymentIntent->client_secret,
    //         'payment_method' => $paymentIntent->payment_method,
    //         "public_key" => $cardPayment->publicKey
    //     ];
    // }

    exit(json_encode(["success" => $output]));
}

if ($data[0] == "setupIntent") {
    $stripe = new StripeConnection();
    $stripe->Connect();
    $setupIntent = $stripe->CreateSetupIntent($user);
    $output = [
        "id" => $setupIntent->id,
        "client_secret" => $setupIntent->client_secret,
        "stripeCustomerID" => $setupIntent->customer,
        "pk" => $stripe->publicKey,
    ];
    exit(json_encode(["success" => $output]));
}

if ($data[0] == "new-team-payment" && $requestMethod == "POST") {
    /* Expects teamID, userID and setupIntent */
    if (!isset($post['teamID']) || !$post['teamID']) exit(json_encode(["error" => "No Team"]));
    if (!isset($post['setupIntentID']) || !$post['setupIntentID']) exit(json_encode(["error" => "No Setup Intent ID"]));
    $team = new Team($post['teamID']);
    if (!$team || !$team->id)  exit(json_encode(["error" => "Invalid Team"]));
    $stripe = new StripeConnection();
    // exit($post['setupIntentID']);
    $setupIntent = $stripe->FetchSetupIntent($post['setupIntentID']);
    $stripe->SaveSetupIntent($setupIntent);
    $rlt = TeamFollower::PaymentMethod($team, $user, $setupIntent->payment_method);
    exit(json_encode(["success" => $team->ApiData()]));
}

if ($data[0] == "cancel-card" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "No Pay ID supplied"]));
    $stripe = new StripeConnection();
    try {
        $paymentMethod = $stripe->CancelPaymentMethod($data[1]);
    } catch (Exception $e) {
        TeamFollower::CancelPaymentMethod($data[1]);
    }
    exit(json_encode(["success" => $paymentMethod]));
}

if ($data[0] == "set-payment-option" && $requestMethod == "GET") {
    // if (!isset($post) || !$post || !isset($post['id']) || !$post['id']) exit(json_encode(["error" => "Invalid Team ID"]));
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1]))  exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id) exit(json_encode(["error" => "Invalid Team"]));
    $team->getTeamManagers();
    if ($team->captain != $user && $team->treasurer != $user) exit(json_encode(["error" => "Permission Denied"]));
    // // $post['payInFull'] = ($post['payInFull'] === true) ? 1 : null;
    // if ( ($post['payInFull'] == 1 && $team->payInFull != 1) || ($post['payInFull'] != 1 && $team->payInFull == 1))  {
    $team->payInFull = TeamFollower::TogglePayInFull($team);
    // } 
    exit(json_encode(["success" => $team->ApiData()]));
    // exit(json_encode(["warning" => $post]));
}

if ($data[0] == "team-statement" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1]))  exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id) exit(json_encode(["error" => "Invalid Team"]));
    $team->getTeamManagers();
    if ($team->captain != $user && $team->treasurer != $user) exit(json_encode(["error" => "Permission Denied"]));
    $statement = Finance::Statement($data[1], true);
    $output = [];
    if ($statement) {
        $output['total'] = 0;
        foreach ($statement as $s) {
            $output['items'][] = [
                "date" => $s->taxDate,
                "description" => $s->description,
                "amount" => $s->total,
            ];
            $output['total'] += $s->total;
        }
    }
    exit(json_encode(["success" => $output]));
}

if ($data[0] == "invite-treasurer" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "Missing Team ID"]));
    if (!is_numeric($data[1]) || !($team = new Team($data[1]))) exit(json_encode(["error" => "Invalid Team"]));
    $team->getTeamManagers();
    if ($team->captain != $user && $team->treasurer != $user) exit(json_encode(["error" => "Permission Denied"]));
    if (!isset($data[2]) || !$data[2] || !filter_var($data[2], FILTER_VALIDATE_EMAIL)) exit(json_encode(["error" => "Missing or Invalid Email"]));
    TeamFollower::InviteTreasurer($data[2], $team, $user);
    exit(json_encode(["success" => "Ok"]));
}

if ($data[0] == "accept-treasurer-invite" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "Missing Invite ID"]));
    $teamFollower = TeamFollower::Invite($data[1]);
    // exit(json_encode(["success" => $teamFollower]));
    $team = new Team($teamFollower['teamID']);
    if (!$team || !$team->id)  exit(json_encode(["error" => "Invalid Team"]));
    $rlt = TeamFollower::AcceptTreasurerInvite($data[1], $user);
    ($rlt === true) ? exit(json_encode(["success" => $team->ApiData()])) : exit(json_encode(["error" => $rlt]));
}

if ($data[0] == "decline-treasurer-invite" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "Missing Team ID"]));
    $rlt = TeamFollower::DeclineTreasurerInvite($data[1], $user);
    ($rlt === true) ? exit(json_encode(["success" => 'Ok'])) : exit(json_encode(["error" => $rlt]));
}

if ($data[0] == "treasurer-remove" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) exit(json_encode(["error" => "Invalid Team ID"]));
    $team = new Team($data[1]);
    if (!$team || !$team->id)  exit(json_encode(["error" => "Invalid Team"]));
    $team->getTeamManagers();
    if ($team->captain->id != $user->id && $team->treasurer->id != $user->id) exit(json_encode(["error" => "Permission Denied"]));
    TeamFollower::TreasurerRemove($team);
    exit(json_encode(["success" => $team->ApiData()]));
}

if ($data[0] == "sync-payment" && $requestMethod == "GET") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode(["error" => "Invalid Payment ID"]));
    $rlt = Stripe::SyncByPaymentIntentID($data[1]);
    Email2::Issue("Update to Payment Intent (User API)", ["Hi", "Payment Intent {$data[1]} has been updated", "Thanks", "leagues4you"], ["<EMAIL>" => "Codeframe"]);
    exit("Ok");
}
