<?php
// session_start();

define ("ROOT_FOLDER",strrev(strstr(strrev(dirname(__FILE__)),DIRECTORY_SEPARATOR)));
define ("APP_FOLDER", ROOT_FOLDER . "core");
define ("CORE_FOLDER", ROOT_FOLDER . "core");

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
include($init_file);

// include(dirname(__FILE__) . DIRECTORY_SEPARATOR . ".." . DIRECTORY_SEPARATOR . "app" . DIRECTORY_SEPARATOR . "init.php");

// Rate Limit??
// header("Access-Control-Allow-Origin: https://leagues4you.co.uk");
// header("Access-Control-Allow-Origin: https://hub.leagues4you.co.uk");
// header('Access-Control-Allow-Credentials: true');
// header('Access-Control-Max-Age: 120');    // cache for 2 minutes
// $allowedOrigins = [
//     "https://leagues4you.co.uk",
//     "https://hub.leagues4you.co.uk",
//     "https://lockerroom.leagues4you.co.uk",
//     "http://localhost:8080",
// ];
// exit($_SERVER['HTTP_ORIGIN']);
// if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'],$allowedOrigins)) {
if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
    header('Access-Control-Allow-Credentials: true');
    header('Access-Control-Max-Age: 86400');    // cache for 1 day
}
    // $log[] = "Origin {$_SERVER['HTTP_ORIGIN']}";
// }
// if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
//     if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
//         header("Access-Control-Allow-Methods: GET, POST");
//     if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
//         header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
//     exit(0);
// }
header('Content-Type: application/json');

// For security, respond 404 for root requests
if (!isset($_SERVER['PATH_INFO']) || !$_SERVER['PATH_INFO']) { http_response_code(404); exit(0);}

// $output['url'] = $_GET['url'];

/* If not HTTPS - bin it */
if (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS']!="on") {
    http_response_code(403); exit (json_encode("HTTPS Required"));
}

$data = explode("/", $_SERVER['PATH_INFO']);
array_shift($data);
$action = $data[0];

$request_method = $_SERVER["REQUEST_METHOD"];
$post = json_decode( file_get_contents('php://input'),true);

/* Admin APIs */
if (User::isAdmin() !== true) {
    http_response_code(401);
    exit(json_encode("Admin Login Required"));
}

exit(json_encode($data));