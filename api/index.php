<?php
// session_start();


define("ROOT_FOLDER", strrev(strstr(strrev(dirname(__FILE__)), DIRECTORY_SEPARATOR)));
define("APP_FOLDER", ROOT_FOLDER . "core");
define("CORE_FOLDER", ROOT_FOLDER . "core");

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
include($init_file);

// include(dirname(__FILE__) . DIRECTORY_SEPARATOR . ".." . DIRECTORY_SEPARATOR . "app" . DIRECTORY_SEPARATOR . "init.php");

// Rate Limit??
// header("Access-Control-Allow-Origin: https://leagues4you.co.uk");
// header("Access-Control-Allow-Origin: https://hub.leagues4you.co.uk");
// header('Access-Control-Allow-Credentials: true');
// header('Access-Control-Max-Age: 120');    // cache for 2 minutes
// $allowedOrigins = [
//     "https://leagues4you.co.uk",
//     "https://hub.leagues4you.co.uk",
//     "https://lockerroom.leagues4you.co.uk",
//     "http://localhost:8080",
// ];
// exit($_SERVER['HTTP_ORIGIN']);
// if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'],$allowedOrigins)) {
if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
}
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');    // cache for 1 day
// $log[] = "Origin {$_SERVER['HTTP_ORIGIN']}";
// }
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
header('Content-Type: application/json');

// For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) {
    http_response_code(404);
    exit(0);
}

// $output['url'] = $_GET['url'];

/* If not HTTPS - bin it */
// if (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS']!="on") {
//     http_response_code(403); exit (json_encode("HTTPS Required"));
// }

$data = explode("/", $_GET['url']);
$action = $data[0];
// exit(print_r($_GET['url']));

$request_method = $_SERVER["REQUEST_METHOD"];
$post = json_decode(file_get_contents('php://input'), true);


if ($action == "liveLeagues") {
    if (isset($_GET['rebuild'])) System::BuildLiveLeaguesApi();
    $apiFile = "liveLeagues.json";
    if (file_exists($apiFile)) exit(file_get_contents($apiFile));
}

if ($action == "leagues") {
    $leagues = League::Listing();
    foreach ($leagues as $league) {
        $output[] = ["id" => $league->id, "name" => $league->name, "url" => $league->getUrl()];
    }
    exit(json_encode($output));
}

if ($action == "venues") {
    $venues = Venue::Listing();
    $output = [];
    if ($venues) {
        foreach ($venues as $venue) $output[] = $venue->name;
    }
    exit(json_encode($output));
}

if ($action == "sports") {
    exit(json_encode(Sport::ApiListing()));
}

if ($action == "venue") {
    if (!isset($data[1]) || !$data[1] || !($venue = new Venue($data[1]))) {
        http_response_code(400);
        exit(json_encode("Missing or Invalid Venue ID"));
    }
    exit(json_encode($venue->ApiOutput()));
}

if ($action == "league") {
    if (!isset($data[1]) || !$data[1]) {
        http_response_code(400);
        exit(json_encode("Missing or Invalid League Info"));
    }
    if (!is_numeric($data[1])) {
        $name = str_replace(["-", "and"], [" ", "&"], $data[1]);
        // exit($name);
        $leagues = League::byName($name);
        if (!$leagues || count($leagues) > 1) {
            http_response_code(400);
            exit(json_encode("Unable to resolve League"));
        }
        $leagueID = $leagues[0]['id'];
    } else $leagueID = $data[1];
    $league = new League($leagueID);
    // exit(json_encode($league->ApiOutput()));
    $output = $league->ApiData(true);
    $nextSeason = Season::Next($league);
    $liveSeason = Season::Live($league);
    if ($nextSeason) {
        $venue = new Venue($nextSeason->venueID);
        $output['venue'] = $venue->ApiData();
    } elseif ($liveSeason) {
        $venue = new Venue($liveSeason->venueID);
        $output['venue'] = $venue->ApiData();
    } else $output['venue'] = [];
    exit(json_encode($output));
}

if ($action == "teamValidate") {
    if (!isset($data[1]) || !$data[1] || !($league = new League($data[1]))) {
        http_response_code(400);
        exit(json_encode("Missing or Invalid League ID"));
    }
    if (!isset($data[2]) || !$data[2]) {
        http_response_code(400);
        exit(json_encode("Missing Team Name"));
    }
    $teamName = urldecode($data[2]);
    $arrayProhibitedWords = [
        "fuck",
        "cunt",
        "wank",
        "bollock",
        "bollox",
        "tits",
        "bullshit"
    ];
    foreach ($arrayProhibitedWords as $word) {
        if (strtolower(substr($teamName, 0, strlen($word))) == $word) {
            http_response_code(400);
            exit(json_encode("prohibited language"));
        }
        if (strtolower(substr($teamName, -strlen($word))) == $word) {
            http_response_code(400);
            exit(json_encode("prohibited language"));
        }
    }
    $sql = "SELECT `name` FROM `teams` WHERE `leagueID` = :leagueID";
    $db = new Db($sql, ["leagueID" => $data[1]]);
    if ($db->rows) {
        foreach ($db->rows as $r) {
            if (strtolower(str_replace([" "], [""], $r['name'])) == strtolower(str_replace([" "], [""], $teamName))) {
                http_response_code(400);
                exit(json_encode("already taken"));
            }
        }
    }
    exit(json_encode("Ok"));
    $sql = "SELECT `id` FROM `teams` WHERE `name` = :teamName AND `leagueID` = :leagueID";
    $db = new Db($sql, ["teamName" => $teamName, "leagueID" => $data[1]]);
    if ($db->rows) {
        http_response_code(400);
        exit(json_encode("already taken"));
    }
    exit(json_encode("Ok"));
}

if ($action == "websearchlocation") {
    $data = [
        "ip" => (isset($post['ip']) && $post['ip']) ? $post['ip'] : null,
        "agent" => (isset($post['agent']) && $post['agent']) ? $post['agent'] : null,
        "location" => (isset($post['location']) && $post['location']) ? $post['location'] : null,
        "sport" => (isset($post['sport']) && $post['sport']) ? $post['sport'] : null,
        "lat" => (isset($post['lat']) && $post['lat']) ? $post['lat'] : null,
        "lng" => (isset($post['lng']) && $post['lng']) ? $post['lng'] : null,
    ];
    $db = WebSearchLocation::Add($data);
    exit(json_encode($db));
}

if ($action == "webContact") {
    $data = [
        "name" => (isset($post['name']) && $post['name']) ? $post['name'] : null,
        "email" => (isset($post['email']) && $post['email']) ? $post['email'] : null,
        "mobile" => (isset($post['mobile']) && $post['mobile']) ? $post['mobile'] : null,
        "message" => (isset($post['message']) && $post['message']) ? $post['message'] : null,
        "ip" => (isset($post['ip']) && $post['ip']) ? $post['ip'] : null,
        "agent" => (isset($post['agent']) && $post['agent']) ? $post['agent'] : null,
    ];
    $db = WebContact::Add($data);
    foreach ($data as $k => $v) $message[] = "$k : $v";
    Email::Issue("Web Contact", $message, ["<EMAIL>" => "bloomnetball"]);
    exit(json_encode($db));
}

if ($action == "tasters") {
    $tasterSessions = TasterSession::Available();
    if (!$tasterSessions) {
        http_response_code(204);
        exit(0);
    }
    $output = null;
    foreach ($tasterSessions as $tasterSession) {
        if (!$tasterSession->coordinatorID) continue;
        if ($tasterSession->deleted) continue;
        if ($tasterSession->cancelled) continue;
        $output[] = $tasterSession->ApiOutput();
        // $output[] = json_decode($tasterSession->toJson(),true);
    }
    exit(json_encode($output));
}

if ($action == "tasters-sport") {

    if (!isset($data[1]) || !$data[1]) {
        http_response_code(401);
        exit(json_encode("Missing or Invalid Sport ID"));
    }

    $sportIDs = explode(",", $data[1]);

    $tasterSessions = TasterSession::Sport($sportIDs);
    if (!$tasterSessions) exit(json_encode([]));
    $output = null;
    foreach ($tasterSessions as $tasterSession) {
        if (!$tasterSession->coordinatorID) continue;
        if ($tasterSession->deleted) continue;
        if ($tasterSession->cancelled) continue;
        $output[] = $tasterSession->ApiOutput();
        // $output[] = json_decode($tasterSession->toJson(),true);
    }
    exit(json_encode($output));
}

if ($action == "wildcard" || $action == "tournament") {
    if (!isset($data[1]) || !$data[1]) {
        http_response_code(401);
        exit(json_encode("Missing or Invalid Sport ID for " . $action));
    }

    $sportIDs = explode(",", $data[1]);
    $tasterSessions = ($action == "wildcard")
        ? TasterSession::WildCard($sportIDs)
        : TasterSession::Tournament($sportIDs);

    if (!$tasterSessions) exit(json_encode([]));

    $output = null;
    foreach ($tasterSessions as $tasterSession) {
        if (!$tasterSession->coordinatorID) continue;
        if ($tasterSession->deleted) continue;
        if ($tasterSession->cancelled) continue;

        $output[] = $tasterSession->ApiOutput();
    }
    exit(json_encode($output));
}

if ($action == "taster") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) {
        http_response_code(400);
        exit(json_encode("Missing or Invalid Taster Session ID"));
    }
    $tasterSession = new TasterSession($data[1]);
    if (!$tasterSession->id) {
        http_response_code(400);
        exit(json_encode("Invalid Taster Session"));
    }
    exit($tasterSession->toJson());
}

if ($action == "getStripeClientSecret") {
    if (!isset($_GET['email']) || !$_GET['email'] || !filter_var($_GET['email'], FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing or Invalid Email Address {$_GET['email']}"]));
    }
    if (!isset($_GET['tasterID']) || !$_GET['tasterID'] || !($taster = new TasterSession($_GET['tasterID']))) {
        http_response_code(400);
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing or Invalid Taster ID {$_GET['tasterID']}"]));
    }

    /* Does Email Address exist already, if not create */
    $user = User::EmailLookup($_GET['email']);
    if (!$user) $user = User::Create($_GET);
    if (!$user) {
        http_response_code(400);
        exit(json_encode(["Error" => __LINE__, "Text" => "We could not create a user account for your email address. You can try again but you may need to contact us to complete this - sorry"]));
    }
    $stripe = new Stripe_v2(false); # Stripe in Test Mode
    /* Does this User exist with Stripe, if not create */
    $stripeCustomer = $stripe->getStripeCustomer($user->id);
    if (!$stripeCustomer) {
        http_response_code(400);
        exit(json_encode(["Error" => __LINE__, "Text" => "We could not create an account for you with our payment provider. They may be experiencing IT issues. You can try again but you may need to contact us to complete this - sorry"]));
    }
    /* Create a StripePaymentIntent for StripeCustomerID and chargeTotal */
    $paymentIntent = $stripe->createPaymentIntent($stripeCustomer->id, $taster->charge, "Couch2Court {$_GET['tasterID']}", false, [], false);
    if ($paymentIntent && $paymentIntent->id) {
        exit(json_encode(["Success" => $paymentIntent->client_secret]));
    }
    http_response_code(400);
    exit(json_encode(["Error" => __LINE__, "Text" => "We could not create a payment request for you with our payment provider. They may be experiencing IT issues. You can try again but you may need to contact us to complete this - sorry"]));
}

if ($action == "taster-registration") {
    /**
     * Expects $_GET for
     * Name, Email, Mobile, tasterID and paymentIntentID
     * Urlencoding for Name, Email, Mobile
     */
    /* Has a firstname been submitted */
    if (!isset($_GET['firstname']) || !$_GET['firstname']) {
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing or Invalid First Name"]));
    } else $firstname = urldecode($_GET['firstname']);
    /* Has a lastname been submitted */
    if (!isset($_GET['lastname']) || !$_GET['lastname']) {
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing or Invalid Last Name"]));
    } else $lastname = urldecode($_GET['lastname']);
    /* Has an Email Address been submitted */
    if (!isset($_GET['email']) || !$_GET['email'] || !filter_var($_GET['email'], FILTER_VALIDATE_EMAIL)) {
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing or Invalid Email Address"]));
    } else $email = urldecode($_GET['email']);
    /* Has a mobile been submitted */
    if (!isset($_GET['mobile']) || !$_GET['mobile']) {
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing or Invalid Mobile Number"]));
    } else $mobile = urldecode($_GET['mobile']);
    /* Has a Taster Session ID been submiited */
    if (!isset($_GET['tasterID']) || !$_GET['tasterID']) {
        exit(json_encode(["Error" => __LINE__, "Text" => "Missing Taster Session ID"]));
    } else $tasterID = $_GET['tasterID'];
    $taster = new TasterSession($tasterID);
    if (!$taster->id) {
        exit(json_encode(["Error" => __LINE__, "Text" => "Invalid Taster Session"]));
    }
    $paymentIntentID = (isset($_GET['paymentIntentID']) && $_GET['paymentIntentID']) ? $_GET['paymentIntentID'] : null;
    $discountCode = (isset($_GET['discountCode']) && $_GET['discountCode']) ? $_GET['discountCode'] : null;
    /* Does User Already Exist */
    if (!($user = User::EmailLookup($email))) {
        /* Create new User */
        $user = User::Create([
            "firstname" => $firstname,
            "lastname" => $lastname,
            "email" => $email,
            "mobile" => $mobile
        ]);
        if (is_string($user)) {
            exit(json_encode(["Error" => __LINE__, "Text" => $user]));
        }
    }
    Logging::Add("C2C Reg: $user $taster $paymentIntentID $discountCode");
    /* Register for Session */
    $ta = TasterAttendee::Create([
        "tasterID" => $taster->id,
        "userID" => $user->id,
        "stripePaymentIntentID" => $paymentIntentID,
        "discountCode" => $discountCode
    ]);
    if (is_string($ta)) {
        exit(json_encode(["Error" => __LINE__, "Text" => $ta]));
    }
    exit(json_encode($ta->id));
}

if ($action == "taster-multibook") {
    // error_reporting(E_ALL);
    // ini_set('display_errors', 1);
    // ini_set('log_errors', 1);
    // ini_set('display_startup_errors', 1); 
    if (isset($_GET['tasterID'])) {
        $c2c_booking = new C2C_Booking();
        $c2c_booking->tasterID = $_GET['tasterID'];
        $c2c_booking->Save();
    } elseif (isset($_GET['bookerTasterID'])) {
        $c2c_booking = new C2C_Booking($_GET['bookerTasterID']);

        // Set basic booking information
        $c2c_booking->firstname = $_GET['firstname'];
        $c2c_booking->lastname = $_GET['lastname'];
        $c2c_booking->email = $_GET['email'];
        $c2c_booking->mobile = $_GET['mobile'];
        $c2c_booking->discountCode = isset($_GET['discountCode']) ? $_GET['discountCode'] : null;

        // Handle team name in data field
        if (!empty($_GET['teamName'])) {
            $data = $c2c_booking->data ? json_decode($c2c_booking->data, true) : [];
            $teamName = htmlspecialchars(trim($_GET['teamName']), ENT_QUOTES, 'UTF-8');
            $teamName = substr($teamName, 0, 150);
            $data['teamName'] = $teamName;
            $c2c_booking->data = json_encode($data);
        }

        $c2c_booking->Save();
    } elseif (isset($_GET['newAttendeeTasterID'])) {
        $c2c_attendee = new C2C_Attendee();
        $c2c_attendee->bookingID = $_GET['newAttendeeTasterID'];
        $c2c_attendee->firstname = $_GET['firstname'];
        $c2c_attendee->lastname = $_GET['lastname'];
        $c2c_attendee->email = $_GET['email'];
        $c2c_attendee->mobile = $_GET['mobile'];
        $c2c_attendee->Save();
        $c2c_booking = new C2C_Booking($_GET['newAttendeeTasterID']);
    } elseif (isset($_GET['removeAttendee']) && isset($_GET['attendeeID'])) {
        $c2c_attendee = new C2C_Attendee($_GET['attendeeID']);
        if ($c2c_attendee->bookingID != $_GET['removeAttendee']) exit(0);
        C2C_Attendee::Archive($_GET['attendeeID']);
        $c2c_booking = new C2C_Booking($_GET['removeAttendee']);
    } elseif ($_GET['bookingID']) {
        $c2c_booking = new C2C_Booking($_GET['bookingID']);
        $c2c_booking->SendConfirmation();
    }
    ($c2c_booking) ? exit(json_encode($c2c_booking->ApiData())) : exit(null);
}

if ($action == "taster-user-validation") {
    $logging = [
        "datetime" => date('Y-m-d H:i:s'),
        "tasterID" => (isset($_GET['tasterID'])) ? $_GET['tasterID'] : null,
        "email" => (isset($_GET['email'])) ? $_GET['email'] : null,
        "ip" => (isset($_SERVER['REMOTE_ADDR'])) ? $_SERVER['REMOTE_ADDR'] : null,
        "agent" => (isset($_SERVER['HTTP_USER_AGENT'])) ? $_SERVER['HTTP_USER_AGENT'] : null
    ];
    $logFile = __DIR__ . DIRECTORY_SEPARATOR . "taster_user_validation.csv";
    $fp = fopen($logFile, 'w');
    fputcsv($fp, $logging);
    // file_put_contents($logFile,implode(",",$logging)."\n",FILE_APPEND);
    if (!isset($_GET['tasterID']) || !$_GET['tasterID'] | !is_numeric($_GET['tasterID'])) exit(json_encode("Invalid Taster Session ID"));
    $taster = new TasterSession($_GET['tasterID']);
    if (!$taster->id) exit(json_encode("Could not find that Taster Session"));
    if (!isset($_GET['email']) || !$_GET['email'] || !filter_var($_GET['email'], FILTER_VALIDATE_EMAIL)) exit(json_encode("Please enter a valid email address"));
    if (!($user = User::EmailLookup($_GET['email']))) exit(json_encode("OK"));
    if (TasterAttendee::Exists($user->id, $_GET['tasterID'])) exit(json_encode("It looks like you're already registered. Registering a friend? Please enter their contact details so we can confirm diretly to them"));
    exit(json_encode("OK"));
}

if ($action == "resolve-stripe-client") {

    global $config;

    if (!isset($_GET['tasterAttendeeID']) || !$_GET['tasterAttendeeID']) exit(json_encode("No Taster Attendee ID"));
    $tasterAttendee = new TasterAttendee($_GET['tasterAttendeeID']);
    if (!$tasterAttendee->id) exit(json_encode("Not a valid Taster Attendee ID"));
    $syncResult = ($tasterAttendee->SynchroniseCustomerData() === true) ? "Yes" : "No";
    exit(json_encode($syncResult));
    // $tasterAttendee->getStripePaymentIntent ();
    // echo "<pre>"; print_r($tasterAttendee); echo "</pre>";
    // exit(0);
    // $stripe = new \Stripe\StripeClient(
    //     '***********************************************************************************************************'
    //   );
    // $pi = $stripe->paymentIntents->retrieve(
    //     'pi_3JWoEZLOCeRFt5lu1hoNG1Kg',
    //     []
    //   );
    // exit(json_encode($pi));
    // $tasterAttendee->getUser ();
    // $localUser = ($tasterAttendee->user && $tasterAttendee->user->email) ? $tasterAttendee->user->email : null;
    // $log[] = "Local user $localUser";
    // $tasterAttendee->getStripePaymentIntent ();
    $tasterAttendee->getStripeCustomer();
    exit(json_encode(print_r($tasterAttendee)));
    // $stripeCustomerEmail = ($tasterAttendee->stripeCustomer) ? $tasterAttendee->stripeCustomer->email : null;
    // exit(json_encode(["Local" => $tasterAttendee->user->email, "stripe" => $stripeCustomerEmail]));
}

if ($action == "taster-session") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) {
        http_response_code(400);
        exit(json_encode("Missing or Invalid Taster Session ID"));
    }
    $tasterSession = new TasterSession($data[1]);
    if (!$tasterSession->id) {
        http_response_code(400);
        exit(json_encode("Invalid Taster Session"));
    }
    exit(json_encode($tasterSession->ApiOutput()));
}

if ($action == "c2cSession") {
    $tasterSessionDetails = TasterSession::Details($data[1]);

    if (!$tasterSessionDetails) exit(json_encode([]));

    $result = array(
        'tasterSession' => array(
            'date' => $tasterSessionDetails->date,
            'time' => $tasterSessionDetails->time,
            'charge' => $tasterSessionDetails->charge,
            'location' => $tasterSessionDetails->location,
            'fb_page' => $tasterSessionDetails->fbPageLink,
            'fullyBooked' => $tasterSessionDetails->fullyBooked,
        ),
        'venue' => array(
            'name' => $tasterSessionDetails->name,
            'address1' => $tasterSessionDetails->address1,
            'town' => $tasterSessionDetails->town,
            'postcode' => $tasterSessionDetails->postcode
        ),
        'coordinator' => array(
            'firstname' => $tasterSessionDetails->firstname,
            'email' => $tasterSessionDetails->email,
            'profilePictureUrl' => $tasterSessionDetails->profilePictureUrl,
            'bio' => $tasterSessionDetails->bio
        ),
        'league' => array(
            'league_fb_group' => $tasterSessionDetails->league_fb_group
        )
    );

    exit(json_encode($result));
}


/* Admin APIs */

if (!isset($_COOKIE['jwt']) || !$_COOKIE['jwt']) {
    exit(json_encode("Admin Login Required. Error " . __LINE__));
}
/* Check for Bearer Token */
$userID = Jwt::Validate($_COOKIE['jwt']);
if (is_string($userID)) exit(json_encode($userID));

$user = ($userID == 2) ? new User(13199) : new User($userID);

if ($user->isAdmin != 1 && $user->isAuthor != 1) exit(json_encode("Admin Login Required. Error " . __LINE__));

// if (($rlt = User::isAdmin()) !== true) {
//     http_response_code(401);
//     exit(json_encode("Admin Login Required " . ));
// } 

if ($action == "toggleWild") {
    $teamSeason = new TeamSeason($data[1]);
    // $old = $teamSeason->ApiData();
    // $teamSeason->wildcard = ($teamSeason->wildcard == 1) ? null : 1;
    // $action = ($teamSeason->wildcard == 1) ? "On" : "Off";
    // $team = new Team($teamSeason->teamID);
    // $season = new Season($teamSeason->seasonID);
    // $league = new League($season->leagueID);
    $newTeamSeason = TeamSeason::ToggleWildcard($teamSeason);
    // $new = $teamSeason->ApiData();
    // $message = "Wilcard status set to $teamSeason->wildcard for $teamSeason->id by " . User::AuthUser();
    // Logging::Add($message);
    // $rlt = $teamSeason->Save();
    exit(json_encode($newTeamSeason->ApiData()));
}

if ($action == "teamSave") {
    exit(json_encode("Ok"));
}

if ($action == "activateUser") {
    if (!isset($_GET['userid']) || !$_GET['userid'] || !($user = new User($_GET['userid']))) {
        http_response_code(400);
        exit(json_encode("Invalid or Missing User Data"));
    }
    $rlt = $user->IssueActivation();
    if ($rlt !== true) {
        http_response_code(400);
        exit(json_encode($rlt));
    }
}

if ($action == "seasonBookings") {
    $seasonBookings = Booking::Season(new Season($data[1]));
    if (!$seasonBookings) {
        http_response_code(204);
        exit(0);
    }
    $output = [];
    foreach ($seasonBookings as $booking) {
        // echo "Booking ID {$booking->id}<br>"; continue;
        $output[] = [
            "id" => $booking->id,
            "pitchCourt" => $booking->getPitchCourt(),
            "venue" => $booking->getVenueName(),
            "startDate" => $booking->getStartDate('d/m/Y'),
            "startTime" => $booking->getStartTime(),
            "name" => $booking->getPitchCourt() . " " . $booking->getVenueName() . " " . $booking->getStartDate('d/m/Y'),
            "usage" => $booking->getUsage()
        ];
    }
    exit(json_encode($output));
}

if ($action == "scheduleAvailability") {
    $seasonBookings = Booking::SeasonAvailability(new Season($data[1]));
    if (!$seasonBookings) {
        http_response_code(204);
        exit(0);
    }
    $output = [];
    foreach ($seasonBookings as $booking) {
        // echo "Booking ID {$booking->id}<br>"; continue;
        $output[] = [
            "id" => $booking->id,
            "pitchCourt" => $booking->getPitchCourt(),
            "venue" => $booking->getVenueName(),
            "startDate" => $booking->getStartDate('d/m/Y'),
            "startTime" => $booking->getStartTime(),
            "name" => $booking->getPitchCourt() . " " . $booking->getVenueName() . " " . $booking->getStartDate('d/m/Y'),
            "usage" => $booking->getUsage()
        ];
    }
    exit(json_encode($output));
}

if ($action == "bookingSlots") {
    // // $slots = Schedule::SlotOptions(new Booking($data[1]));
    // // $output = new Season($data[1]);
    // $slots = Booking::SeasonOptions(new Season($data[1]));
    // // if (!$slots) { http_response_code(204); exit(0);}
    // $output = [];
    // foreach ($slots as $weekNo => $week) {
    //     foreach ($week['bookings'] as $booking) {
    //         $output[$booking->id] = $booking->ApiOutput();    
    //     }
    // }
    // exit(json_encode($output));
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) {
        http_response_code(400);
        exit(json_encode("Missing Booking ID"));
    }
    $booking = new Booking($data[1]);
    if (!$booking || !$booking->id) {
        http_response_code(400);
        exit(json_encode("Invalid Booking ID"));
    }

    if (!isset($data[2]) || !$data[2] || !is_numeric($data[2])) {
        http_response_code(400);
        exit(json_encode("Missing Fixture ID"));
    }
    $fixture = new Fixture($data[2]);
    if (!$fixture || !$fixture->id) {
        http_response_code(400);
        exit(json_encode("Invalid Fixture ID"));
    }

    $slots = Schedule::FixtureOptions($fixture, $booking);
    if (!$slots) {
        http_response_code(404);
        exit(json_encode("No Booking Slots Available"));
    }
    exit(json_encode($slots));

    // $slots = Schedule::SlotOptions(new Booking($data[1]));
    // if (!$slots) { http_response_code(204); exit(0);}
    // $output = [];
    // foreach ($slots as $slot) {
    //     $output[] = strstr($slot," ");
    // }
    // exit(json_encode($output));
}

if ($action == "switchOptions") {
    if (!isset($data[1]) || !$data[1]) exit(json_encode("No schedule specified. Error " . __LINE__));
    $schedule = new Schedule($data[1]);
    if (!$schedule->id) exit(json_encode("Invalid Schedule specified. Error " . __LINE__));
    $switchOptions = Schedule::SwitchOptions($schedule);
    $output = [];
    if ($switchOptions) {
        foreach ($switchOptions as $switchOption) $output[] = [
            'id' => $switchOption->id,
            'name' => $switchOption->__toString(),
        ];
    }
    exit(json_encode($output));
}
