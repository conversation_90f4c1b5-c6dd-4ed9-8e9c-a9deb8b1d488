<?php
include("/var/www/html/app/init.php");

use Models\Season;

$season = new Season(81);
$season->Save();

// $teamCount = (isset($_GET['teams']) && $_GET['teams'] && is_numeric($_GET['teams'])) ? $_GET['teams'] : 10;
// $cycles = (isset($_GET['cycles']) && $_GET['cycles'] && is_numeric($_GET['cycles'])) ? $_GET['cycles'] : 2;
// $alphabet = range("A","Z");
// for ($i = 0; $i < $teamCount; $i++) {
//     $teams[] = "Team " . $alphabet[$i];
// }
// $fixtureWeeks = Fixture::CircularFixtures($teams,$cycles);
// $divisionID = 60;
// $fixtureData = Fixture::Autofixture($divisionID);
// // dump($fixtureData);
// echo "Running Fixture List for Division $divisionID<br />";
// foreach ($fixtureData as $fixtureWeek => $fixtures) {
//     echo "Week " . ($fixtureWeek+1)."<br />";
//     foreach ($fixtures as $key => $fixture) echo ($key + 1) ." : {$fixture[0]} v {$fixture[1]}<br />";
// }

// $fixtures = Fixture::byDivision(60);
// dump($fixtures);

// use Core\Datastore;
// $sqls = [
//     "delete from bookings",
//     "delete from divisions",
//     "delete from fixtures",
//     "delete from logins where username LIKE '%codeframe%'",
//     "delete from schedule",
//     "delete from seasons",
//     "delete from standings",
//     "delete from teams",
//     "delete from transactionMain",
//     "delete from transactionSub"
// ];
// foreach ($sqls as $sql) Datastore::Query($sql);
// use Models\Season;
// $season = new Season(77);
// $season->AutoBill();
// $season->Save();
// use Models\Division;
// use Models\Fixture;
// use Models\Team;
// use Models\Booking;
// use Models\TeamAllocation;
// $team = new Team(75);
// $team->name = "Some Team";
// $team->affiliation["seasonID"] = 53;
// $team->affiliation["divisionID"] = 29;
// $result = $team->Save();
// $result = Season::Allocate(55,53,28);

// if (is_string($result)) exit ($result);
// exit(dump($result));
// echo "Re-created as ID {$result->id}";

// 81 53 29
// $allocation = new TeamAllocation(13);
// $allocation->teamID = 81;
// $allocation->seasonID = 53;
// $allocation->divisionID = 29;
// $allocation->Save();
// dump($allocation);

// $sql = "INSERT INTO `followerLeague` (`leagueID`, `userID`, `isAdmin`) SELECT `id`, 1 as `userID`, 1 as `isAdmin` FROM `leagues`"
