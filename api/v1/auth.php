<?php
namespace v1;

function Login (String $username, String $password) {
    $sql = "SELECT `id`,`password` FROM `users` WHERE `email` = :username";
    $rlt = DbQuery($sql, ["username" => $username]);
    if ($rlt && is_array($rlt)) {
        if (checkPassword($password,$rlt[0]['password'])) {
            session_regenerate_id();
            $_SESSION['user'] = $rlt[0]['id'];    
            return true;
        }
    }
}
function UserID() {
    if (isset($_SESSION['user']) && $_SESSION['user']) return $_SESSION['user'];
}
function setPassword (String $code = null) {
    if (!$code) $code = microtime();
    return password_hash($code, PASSWORD_DEFAULT);
}
function checkPassword (String $code, String $hash) {
    $rlt = (password_verify($code, $hash));
    return ($rlt)  ? true : false;
}
function isLoggedIn() {
    if (isset($_SESSION['user'])) return true;
}
function Logout() {
    unset($_SESSION['user']);
    session_regenerate_id();
}