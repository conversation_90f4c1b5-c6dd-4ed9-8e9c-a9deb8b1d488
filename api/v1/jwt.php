<?php
namespace v1;

define ('JWT_SALT',"S0ph1st1cat10n?!^!D1sc0mb0bulat10n&$%^£");
define("JWT_DAYS",7);

function setJWT (Int $userID) {
    if (!($token=existingJWT($userID))) {
        $token = sha1(md5(time()));
        $expiry = date('Y-m-d H:i:s',strtotime("+{JWT_DAYS} days"));
        $sql = "INSERT INTO `jwt` SET `token` = :token, `userID` = :userID, `expiry` = :expiry";
        $rlt = DbQuery($sql, ["token" => $token, "userID" => $userID, "expiry" => $expiry]);
    } 
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode(['token' => $token,]);

    $base64UrlHeader = base64encode($header);
    $base64UrlPayload = base64encode($payload);

    $signature = hash_hmac('sha256', $base64UrlHeader . "." . $base64UrlPayload, JWT_SALT, true);

    $base64UrlSignature = base64encode($signature);

    $jwt = $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;

    return $jwt;
}
function existingJWT (Int $userID) {
    $sql = "SELECT `token` FROM `jwt` WHERE `userID` = $userID AND `expiry` > NOW() AND `suspended` IS NULL";
    $rlt = DbQuery($sql);
    if ($rlt && isset($rlt[0]['token'])) {
        DbQuery("UPDATE `jwt` SET `expiry` = NOW() + INTERVAL {JWT_DAYS} DAY WHERE `token` = {$rlt[0]['token']}");
        return $rlt[0]['token'];
    } 
}
function base64encode(String $value) {
    return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($value));
}
function base64decode(String $value) {
    return base64_decode($value);
}
function getJWT (String $jwt) {
    list($header, $payload, $signature) = explode(".",$jwt);
    $data = base64decode($payload);
    return $data;
}
function validateJWT (String $jwt, Array $data = []) {
    /* Returns ARRAY on Sucess or String with Error */
    if (!$data || !isset($data['token']) || !$data['token']) return "Invalid";
    $jwtData = getJwtData($data['token']);
    if (!$jwtData) return "Incorrect";
    if ($jwtData['expiry'] && strtotime($jwtData['expiry']) < time()) return "Expired";
    if ($jwtData['suspended']==1) return "Suspended";
    return $jwtData;
}
function getJwtData (String $token) {
    $sql = "SELECT * FROM `jwt` WHERE `token` = '$token'";
    $rlt = dbQuery($sql);
    if ($rlt['result'][0]) return $rlt['result'][0];
}

// CREATE TABLE `jwt` (
//     `id` int(11) NOT NULL,
//     `token` varchar(50) NOT NULL,
//     `userID` int(11) NOT NULL,
//     `expiry` datetime NOT NULL,
//     `suspended` tinyint(4) DEFAULT NULL
//   ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

?>