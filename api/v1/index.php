<?php
// error_reporting(-1);
include("/var/www/html/app/init.php");
use Core\Auth;
use Core\JWT;
use Models\Venue;
use Models\League;
use Models\Sport;
use Models\Season;
use Models\SeasonBooking;
use Models\Division;
use Models\Team;
use Models\TeamAllocation;
use Models\Fixture;
use Models\Booking;
use Models\Session;
use Models\User;

// include("database.php");
// include("auth.php");
// include("jwt.php");
// include("venue.php");

// Rate Limit??
// header("Access-Control-Allow-Origin: https://leagues-4-you.co.uk");
header("Access-Control-Allow-Origin: *");
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 120');    // cache for 2 minutes
$allowedOrigins = [
    "https://leagues-4-you.co.uk",
    "http://localhost:8080"
];
// exit($_SERVER['HTTP_ORIGIN']);
// if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'],$allowedOrigins)) {
//     header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
//     header('Access-Control-Allow-Credentials: true');
//     header('Access-Control-Max-Age: 86400');    // cache for 1 day
//     $log[] = "Origin {$_SERVER['HTTP_ORIGIN']}";
// }
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
header('Content-Type: application/json');

// For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) { http_response_code(404); exit(0);}

// $output['url'] = $_GET['url'];

/* If not HTTPS - bin it */
if (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS']!="on") {
    http_response_code(403); exit (json_encode("HTTPS Required"));
}

$data = explode("/", $_GET['url']);
$action = $data[0];
$request_method = $_SERVER["REQUEST_METHOD"];
/* Login */
if (isset($action) && $action=="login") {
    if (!isset($_GET['username']) || !isset($_GET['password']) || !$_GET['username'] || !$_GET['password']) {
        http_response_code(400); exit(json_encode("Missing username/password"));
    }
    if (($userID = Auth::Login($_GET['username'],$_GET['password']))) {
        /* Issue JWT */
        $jwt = JWT::Set($userID);
        exit (json_encode($jwt));
    } else {
        http_response_code(401); exit (json_encode("Login Incorrect"));}
}
if (isset($action) && $action=="passreset") {
    if (!isset($_GET['username']) || !$_GET['username']) {
        http_response_code(400); exit(json_encode("Missing Email Address"));
    }
    if (($userID = User::fetchByEmail($_GET['username']))) {
        /* Issue Reset Email */
        $user = new User($userID);
        $user->setReset();
        exit (json_encode("Ok"));
    } else {
        http_response_code(401); exit (json_encode("Could not issue reset"));}
}
if (isset($action) && $action=="reset" && $request_method == "POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    if (is_int($userID = User::ProcessReset($postdata))) {
        exit (json_encode(JWT::Set($userID)));
    } else {
        http_response_code(401);
        exit("Err: $userID on ".__LINE__);
    }
}
if (isset($action) && $action=="sports") {
    $items = Sport::Listing();
    if (!$items || !is_array($items) || count($items)==0) exit(0);
    $output = [];
    foreach ($items as $item) $output[] =$item->Output();
    exit (json_encode($output));
}
if (isset($action) && $action=="venues") {
    $venues = Venue::Listing();
    if (!$venues || !is_array($venues) || count($venues)==0) exit(0);
    $output = [];
    foreach ($venues as $venue) $venueOutput[] = $venue->Output();
    exit(json_encode($venueOutput));
}

/* Auth Only Beyond This Point */
/* Check for Auth Bearer */
$requestHeaders = apache_request_headers();
if (!$requestHeaders || !isset($requestHeaders['Authorization']) || substr($requestHeaders['Authorization'],0,6)!="Bearer") {
    http_response_code(401);exit("Error: " . __LINE__ . " Invalid Token");
}
// exit(JWT::Validate(substr($requestHeaders['Authorization'],7)));
if (!is_int($userID=JWT::Validate(substr($requestHeaders['Authorization'],7)))) {
    http_response_code(401);
    header("Error: " . __LINE__ . " $userID");
    exit("Error: " . __LINE__ . " $userID");
}

/* Protected APIs */
if (isset($action) && $action=="leagues" && $request_method=="GET") {
    $leagues = League::AdminListing($userID);
    if (!$leagues || !is_array($leagues)) exit(0);
    $output = [];
    foreach ($leagues as $league){
        $output[] =$league->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="seasons" && $request_method=="GET") {
    $items = Season::Listing();
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

/* Get Bookings this User can see */
if (isset($action) && $action=="bookings" && $request_method=="GET") {
    $items = Booking::AdminListing($userID);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="followers" && $request_method=="GET") {
    $items = User::AdminListing($userID);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="teams" && $request_method=="GET") {
    $items = Team::AdminListing($userID);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="players" && $request_method=="GET") {
    $items = User::PlayerListing($userID);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="allocations" && $request_method=="GET") {
    $items = TeamAllocation::AdminListing($userID);
    // $output = [];
    // if ($items && is_array($items)) {
    //     foreach ($items as $item) $output[] = $item->Output();
    // }
    exit (json_encode($items));
}

if (isset($action) && $action=="divisions" && $request_method=="GET") {
    $items = Division::AdminListing($userID);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="fixtures" && $request_method=="GET") {
    /* Run Auto-fixture where Required */
    // $seasons = Season::AdminListing($userID);
    // foreach ($seasons as $season) {
    //     // dump($season);
    //     if ($season->autoFixture) Season::AutoFixture($season->id);
    // }
    // exit(0);
    $items = Fixture::AdminListing($userID);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="usersearch" && $request_method=="GET") {
    $items = User::Search($data[1]);
    $output = [];
    if ($items && is_array($items)) {
        foreach ($items as $item) $output[] = $item->Output();
    }
    exit (json_encode($output));
}

if (isset($action) && $action=="schedule" && $request_method=="GET") {
    $items = Booking::Schedule($userID);
    // $output = [];
    // if ($items && is_array($items)) {
    //     foreach ($items as $item) $output[] = $item->Output();
    // }
    exit (json_encode($items));
}

if (isset($action) && $action=="divisiontable" && $request_method=="GET" && isset($data[1]) && $data[1]) {
    $tableData = [];
    $tableData = Division::Table($data[1]);
    exit (json_encode($tableData));
}

if (isset($action) && $action=="booking" && $request_method == "POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    $obj = new Booking();
    $obj->Load($postdata);
    $result = $obj->Save();
    if (!is_numeric($result)) {
        http_response_code(401);
        header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
    } else {
        exit(json_encode($obj->id));
    }
}

if (isset($action) && $action=="allocation" && $request_method == "POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    /* Update Team Name 1st */
    $team = new Team();
    $team->Load($postdata);
    $team->Save();
    // exit(dump($postdata));
    // $a = new TeamAllocation();
    // TeamAllocation::Exists($team->id,$postdata['seasonID']);
    // $allocation = (isset($postdata['id']) && $postdata['id'] && is_numeric($postdata['id'])) ? new TeamAllocation($postdata['id']) : new TeamAllocation();
    // // exit(dump($allocation));
    // $allocation->teamID = $team->id;
    // $allocation->seasonID = $postdata['seasonID'];
    // $allocation->divisionID = $postdata['divisionID'];
    // $result = $allocation->Save();
    // exit(dump($allocation));
    // if (!is_numeric($result)) {
    //     http_response_code(401);
    //     header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
    // } else {
    //     exit(json_encode($allocation));
    // }
}

if (isset($action) && $action=="player" && $request_method == "POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    /* Create / Amend User details */
    // $obj = (isset($postdata['id'])) ? new User($postdata['id']) : new User();
    // exit(dump($postdata));
    $obj = new User();
    $obj->Load($postdata);
    $obj->Save();
    if (!$obj->id) {
        http_response_code(401);
        header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
    } else {
        exit(json_encode($obj->Output()));
    }
}

if (isset($action) && $action=="fixture" && $request_method == "POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    $obj = new Fixture();
    $obj->Load($postdata);
    $result = $obj->Save();
    if (!is_numeric($result)) {
        http_response_code(401);
        header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
    } else {
        exit(json_encode($obj->Output()));
    }
}

if (isset($action) && $action=="league" && $request_method=="POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    $obj = new League();
    $obj->Load($postdata);
    $obj->Save();
    if ($obj->error) {
        http_response_code(401);
        header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
    }
    /* Make User a League Admin */
    exit(json_encode($obj->Output()));
}

if (isset($action) && $action=="venue") {
    $post = file_get_contents('php://input');
    if ($post) {
        $postdata = json_decode($post,true);
        $obj = new Venue();
        $obj->Load($postdata);
        $obj->Save();
        if ($obj->error) {
            http_response_code(401);
            header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
        }
        $data[1] = $obj->id;
    }
    if (!isset($data[1]) || !$data[1]) {
        http_response_code(404);header("Err: ".__LINE__); exit();
    }
    if (!isset($obj)) $obj = new Venue($data[1]);
    if (!$obj->id) {
        http_response_code(404);header("Err: ".__LINE__); exit();}
    echo json_encode($obj->output());
}

if (isset($action) && $action=="season" && $request_method=="POST") {
    $post = file_get_contents('php://input');
    if ($post) {
        $postdata = json_decode($post,true);
        $obj = new Season();
        $obj->Load($postdata);
        $result = $obj->Save();
        if (!is_numeric($result)) {
            http_response_code(401);
            header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
        }
        $data[1] = $obj->id;
    }
    if (!isset($data[1]) || !$data[1]) {
        http_response_code(404);header("Err: ".__LINE__); exit();
    }
    if (!isset($obj)) $obj = new Season($data[1]);
    if (!$obj->id) {
        http_response_code(404);header("Err: ".__LINE__); exit();}
    echo json_encode($obj->output());
}

if (isset($action) && $action=="season" && $request_method=="GET" && isset($data[1]) && $data[1]) {
    $season = new Season($data[1]);
    if (!$season->id) {
        http_response_code(404);header("Err: ".__LINE__); exit();}
    echo json_encode($season->output());
}

if (isset($action) && $action=="seasonduplicate" && $request_method=="GET" && isset($data[1]) && $data[1] && is_numeric($data[1])) {
    $newSeason = Season::Duplicate($data[1]);
    if (is_string($newSeason)) {
        http_response_code(401);exit(json_encode($newSeason));
    }
    exit (json_encode($newSeason->Output()));
}

if (isset($action) && $action=="team") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    $team = new Team();
    $team->Load($postdata);
    $team->Save();
    exit(json_encode($team->Output()));
}

if (isset($action) && $action=="division" && $request_method=="POST") {
    $post = file_get_contents('php://input');
    $postdata = json_decode($post,true);
    $obj = new Division();
    $obj->Load($postdata);
    $result = $obj->Save();
    if (!is_numeric($result)) {
        http_response_code(401);
        header("Err: ".$obj->error." on Line ".__LINE__); exit(json_encode($obj->error));
    } else {
        exit(json_encode($obj->Output()));
    }
}
