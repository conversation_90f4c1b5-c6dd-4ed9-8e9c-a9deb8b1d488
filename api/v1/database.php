<?php
namespace v1;

function DbConnect ($hostname="127.0.0.1",$database="leagues4you",$username="leagues4you", $password="jTQZTDFFRwB3b5FC") {
    $dsn = "mysql:host=$hostname;dbname=$database";
    $options = array(\PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8');
    return new \PDO($dsn, $username, $password, $options);
}

function DbQuery (String $sql, Array $data = []) {
    $conn = DbConnect();
    $stmt = $conn->prepare($sql);
    ($data) ? $stmt->execute($data) : $stmt->execute();
    $errors = $stmt->errorInfo();
    if (!$errors[0]) {
        return implode(" ",$errors);
    } else {
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);    
    }
}