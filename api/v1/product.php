<?php
namespace v1;

function ProductListing() {
    $sql = "SELECT * FROM `venues` ORDER BY `name`";
    $rlt = DbQuery($sql);
    return $rlt;
}
function VenueAddress (Array $venue) {
    $return = null;
    if ($venue['address1']) $return .= ($return) ? ", {$venue['address1']}" : $venue['address1'];
    if ($venue['address2']) $return .= ($return) ? ", {$venue['address2']}" : $venue['address2'];
    if ($venue['town']) $return .= ($return) ? ", {$venue['town']}" : $venue['town'];
    if ($venue['county']) $return .= ($return) ? ", {$venue['county']}" : $venue['county'];
    if ($venue['postcode']) $return .= ($return) ? ", {$venue['postcode']}" : $venue['postcode'];
    return $return;
}