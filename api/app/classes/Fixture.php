<?php

class Fixture extends Base {
    protected $dbTable = "fixtures";

    function __construct(Int $id = null) { parent::__construct($id);}
    
    function ApiOutput() {
        return [
            "id" => $this->id,
            ""
        ];
    }

    /* Statics */
    static function forDivision (Division $division) {
        $sql = "SELECT * FROM `fixtures` WHERE `divisionID` = :divisionID ORDER BY weekNo, weekPos";
        $db = new Database($sql,["divisionID" => $division->id]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $f = new static();
            $f->Load($r);
            $return[] = $f->ApiOutput();
        } 
        return $return;
    }
}