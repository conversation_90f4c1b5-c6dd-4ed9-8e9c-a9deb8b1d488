<?php

class WebContact extends Base {

    protected $dbTable = "WebContacts";
    protected $dbFields = ["name","email","mobile","message","ip","agent"];
    protected $name, $email, $mobile, $message;
    protected $ip, $agent;

    function __construct(Int $id = null) {
        parent::__contruct($id);
    }
    static function Add (Array $data = []) {
        $sql = null;
        foreach ($data as $k => $v) $sql .= (!$sql) ? "`$k` = :$k" : ", `$k` = :$k";
        $sql = "INSERT INTO `webContacts` SET $sql";
        $db = new Database($sql,$data);
        return $db;
    }
}