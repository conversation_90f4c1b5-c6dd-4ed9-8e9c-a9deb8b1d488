<?php

class Season extends Base {

    protected $dbTable = "seasons";
    protected $leagueID, $rounds =1;
    protected $fixtureCharge, $officialCharge;
    public $autoFixture, $autoSchedule, $autoBilling;
    protected $name;  
    protected $startBookingID;
    protected $dbFields = ["leagueID", "name","rounds","fixtureCharge","officialCharge","startBookingID","schedulingNotes"];

    public $schedulingNotes;

    public $minProfitability = 60; # Total invoicing / Total Booking cost. 

    function __construct(Int $id = null) { parent::__construct($id);}
    /* Getters */
    function getLeagueID() { return $this->leagueID;}
    function getRounds() { return $this->rounds;}
    function getStartBookingID() {return $this->startBookingID;}
    function getFixtureCharge() { return $this->fixtureCharge;}
    function getOfficialCharge() {return $this->officialCharge;}
    function getAutoFixture() {return $this->autoFixture;}
    function getAutoSchedule() {return $this->autoSchedule;}
    function getAutoBilling() {return $this->autoBilling;}
    function getStatusID() { return $this->status;}
    function getStatusText() { return ($this->status==1) ? "Live" : "Open";}
    function getDuration() {
        return $this->duration;
    }
    function ApiOutput() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "status" => $this->status
        ];
    }
    static function leagueDefaults(League $league) {
        if (!$league->id) return;
        $sql = "SELECT `id` FROM`seasons` WHERE `leagueID` = :leagueID AND `deleted` IS NULL ORDER BY `id` ASC LIMIT 2";
        $db = new Db($sql,["leagueID" => $league->id]);
        if (!$db->rows) return;
        $return["live"] = new static($db->rows[0]['id']);
        // $return["live"] = $live->ApiOutput();
        if (isset($db->rows[1]['id'])) {
            $return["next"] = new static($db->rows[1]['id']);
            // $return["next"] = $next->ApiOutput();
        } 
        return $return;
    }
}