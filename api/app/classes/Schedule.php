<?php

class Schedule extends Base {

    protected $dbTable = "schedule";

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function ApiOutput() {
        $booking = new Booking($this->bookingID);
        $fixture = new Fixture($this->fixtureID);
        $home = new Team($fixture->home);
        $away = new Team($fixture->away);
        $venue = new Venue($booking->venueID);
        return [
            "id" => $this->id,
            "startTime" => $this->startTime,
            "startDate" => $booking->startDate,
            "home" => $home->__toString(),
            "away" => $away->__toString(),
            "pitchCourt" => $booking->pitchCourt,
            "venue" => $venue->__toString()
        ];
    }
    static function BookingUsage (Booking $booking) {
        $sql = "SELECT (`offset` + `duration`) AS `used` FROM `schedule` WHERE `bookingID` = {$booking->id} ORDER BY `offset` DESC LIMIT 1";
        $db = new Db($sql);
        $return = [
            "available" => $booking->getDuration(),
            "used" => 0
        ];
        if (isset($db->rows[0]['used'])) $return['used'] = $db->rows[0]['used'];
        $return['available'] -= $return['used'];
        return $return;
    }
    static function forDivision (Division $division) {
        $sql = "SELECT `schedule`.* FROM `schedule`
            LEFT JOIN `bookings` ON `schedule`.`bookingID` = `bookings`.`id`
            LEFT JOIN `fixtures` ON `schedule`.`fixtureID` = `fixtures`.`id`
            LEFT JOIN `divisions` ON `fixtures`.`divisionID` = `divisions`.`id`
            LEFT JOIN `seasons` ON `divisions`.`seasonID` = `seasons`.`id`
            WHERE `fixtures`.`divisionID` = :divisionID
            AND `seasons`.`status` = 1
            ORDER BY `bookings`.`startDate`, `schedule`.`startTime`";
        $db = new Db($sql,["divisionID" => $division->id]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s->ApiOutput();
        } 
        return $return;
    }
}