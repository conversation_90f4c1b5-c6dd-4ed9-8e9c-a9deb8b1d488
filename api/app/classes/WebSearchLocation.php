<?php

class WebSearchLocation extends Base {

    protected $dbTable = "webSearchLocations";
    protected $dbFields = ["password","activationCode"];
    protected $isAdmin;
    protected $email;

    function __construct(Int $id = null) {
        parent::__contruct($id);
    }
    static function Add (Array $data = []) {
        // $sql = "INSERT INTO `WebSearchLocations` SET ";
        $sql = null;
        foreach ($data as $k => $v) $sql .= (!$sql) ? "`$k` = :$k" : ", `$k` = :$k";
        $sql = "INSERT INTO `webSearchLocations` SET $sql";
        $db = new Database($sql,$data);
        return $db;
    }

}