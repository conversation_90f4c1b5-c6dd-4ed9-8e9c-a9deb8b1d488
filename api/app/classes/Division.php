<?php

class Division extends Base {

    protected $dbTable = "divisions";
    protected $seasonID;
    protected $name;  
    protected $dbFields = ["seasonID", "name"];

    public $table;

    function __construct(Int $id = null) { parent::__construct($id);}

    function ApiOutput() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "schedule" => Schedule::forDivision($this)
        ];
    }

    static function forSeason(Season $season) {
        if (!$season->id) return;
        $sql = "SELECT `id` FROM `divisions` WHERE `seasonID` = :seasonID AND `deleted` IS NULL ORDER BY `name`";
        $db = new Database($sql,["seasonID" => $season->id]);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $d =  new static($r['id']);
            $return[] = $d->ApiOutput();
        } 
        return $return;
    }
}