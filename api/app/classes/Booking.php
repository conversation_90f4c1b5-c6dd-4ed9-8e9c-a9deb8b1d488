<?php

class Booking extends Base {

    protected $dbTable = "bookings";

    function __construct(Int $id = null) { parent::__construct($id);}
    /* Getters */
    function getLeagueID() { return $this->leagueID;}
    function getVenueID() { return $this->venueID;}
    function getVenueName() {
        $venue = new Venue($this->venueID);
        return $venue->__toString();
    }
    function getPitchCourt() { return $this->pitchCourt;}
    function getStartTime() { return substr($this->startTime,0,5);}
    function getStartDate(String $format=null) { return ($format) ? date($format,strtotime($this->startDate)) : $this->startDate;}
    function getWeeks() { return $this->weeks;}
    function getDuration() { return $this->duration;}
    function getHourlyRate() { return $this->hourlyRate;}
    function getCost() { return ($this->hourlyRate && $this->duration) ? $this->hourlyRate * ($this->duration / 60) : 0;}
    function getUsage() { $this->usage = Schedule::BookingUsage($this);}
    function getNextSlot($fixtureDuration = 40) { 
        $next = Schedule::NextSlot($this->id); if (!$next) $next = $this->startTime;
        if (!$this->endTime) $this->endTime = date('H:i:s', strtotime("{+ $this->duration} minutes", strtotime($this->startTime)));
        // Is there enough time between $next and endTime to allow a fixture?
        // if (strtotime($this->endTime) < strtotime("{+ $fixtureDuration} minutes", strtotime($next))) return;
        return substr($next,0,5);
    }
    static function SeasonOptions (Season $season) {
        // return null;
        /* Order By startDate important for Fixture date determination*/
        if (!($leagueID = $season->getLeagueID()) || !$season->getStartBookingID()) return "No Season start booking";
        $startBooking = new static($season->getStartBookingID());
        // return "Start Booking ID {$startBooking->startDate}";
        $sql = "SELECT `id` FROM `bookings` WHERE `leagueID` = $leagueID AND `startDate` >= :startDate AND `deleted` IS NULL ORDER BY `startDate` ASC";
        $db = new Db($sql, ["startDate" => $startBooking->startDate]);
        if (!$db->rows) return $startBooking->id;
        $return = [];
        $weekCounter = 0;
        $startDate = $endDate = null;
        foreach ($db->rows as $r) {
            $booking = new static($r['id']);
            $booking->getUsage();
            if (!$endDate) {
                $startDate = $booking->startDate;
                // echo "Set Start Date as $startDate<br />";
                $endDate = date('Y-m-d',strtotime("+6 days",strtotime($startDate)));
                // echo "No end date - set it to $endDate<br />";
            }elseif ($booking->startDate > $endDate) {
                // echo "Booking date {$booking->startDate}  beyond current enddate $endDate - rolling forward<br />";
                $weekCounter ++;
                $startDate = $booking->startDate;
                $endDate = date('Y-m-d',strtotime("+6 days",strtotime($startDate)));
            }
            while(date('N',strtotime($endDate)) != 7) {
                // echo "End Date $endDate - not a Sunday - rewinding it to ";
                $endDate = date('Y-m-d',strtotime("-1 day",strtotime($endDate)));
                // echo $endDate . "<br />";
            }
            if (!isset($return[$weekCounter])) $return[$weekCounter]['available'] = 0;
            $return[$weekCounter]['available'] += floor($booking->usage['available']/$season->getDuration());
            $return[$weekCounter]['bookings'][] = $booking;
        }
        return $return;
    }
}