<?php

class League extends Base {

    protected $dbTable = "leagues";
    protected $defaultVenueID,$defaultDay;
    protected $launchDate, $launchTime, $playingSurface, $totalPrice;
    protected $sportID;
    protected $status, $statusText;

    function __construct(Int $id = null) {
        parent::__construct($id);
        switch ($this->status) {
            case 1: $this->statusText = "Join Waiting List"; break;
            case 2: $this->statusText = "Closed for Registration"; break;
            default: $this->statusText = "Open for Registration";
        }
    }
    function __toString() {
        return "{$this->name}";
    }
    function getDefaultVenueID() { return $this->defaultVenueID;}
    function getDefaultDay() { return $this->defaultDay;}
    function getPlayingSurface() { return $this->playingSurface;}
    function getSportID() { return $this->sportID;}
    function ApiOutput() {
        $venueID = $this->getDefaultVenueID(); 
        if ($venueID) {
            $venue = new Venue($venueID);
            $venueOutput = $venue->ApiOutput();
        } else $venueOutput = $this->getDefaultVenueID();
        switch ($this->defaultDay) {
            case "mo": $day = "Monday"; break;
            case "tu": $day = "Tuesday"; break;
            case "we": $day = "Wednesday"; break;
            case "th": $day = "Thursday"; break;
            case "fr": $day = "Friday"; break;
            case "sa": $day = "Saturday"; break;
            case "su": $day = "Sunday"; break;
        }
        $launch = null;
        if ($this->launchDate && $this->launchTime) {
            $launch = $this->launchDate." ". $this->launchTime;
        }
        $sport = new Sport($this->sportID);
        $seasonData = Season::leagueDefaults($this);
        $divisions = ($seasonData["live"]) ? Division::forSeason($seasonData["live"]) : [];
        $return = [
            "id" => $this->id,
            "name" => $this->name,
            "sport" => [
                "name" => $sport->getName(),
                "img" => $sport->getWebImage(),
            ],
            "status" => $this->status,
            "statusText" => $this->statusText,
            "venue" => $venueOutput,
            "day" => $day,
            "seasons" => [
                "live" => $seasonData['live']->ApiOutput()
            ],
            "divisions" => $divisions,
            "days" => [
                    "mo" => ($this->defaultDay=="mo") ? true : false,
                    "tu" => ($this->defaultDay=="tu") ? true : false,
                    "we" => ($this->defaultDay=="we") ? true : false,
                    "th" => ($this->defaultDay=="th") ? true : false,
                    "fr" => ($this->defaultDay=="fr") ? true : false,
                    "sa" => ($this->defaultDay=="sa") ? true : false,
                    "su" => ($this->defaultDay=="su") ? true : false
            ],
            "startTime" => ($this->launchTime) ? substr($this->launchTime,0,5) : "TBA",
            "playingSurface" => ($this->playingSurface) ? static::Surfaces()[$this->playingSurface]['name'] : "TBA",
            "duration" => $seasonData["live"]->getDuration(),
            "fixtureCharge" => $this->totalPrice,
            "nearby" => $this->fetchNearby(),
            "launch" => $launch
        ];
        return $return;
    }
    static function DefaultVenue(Int $venueID, $liveOnly = true) {
        $sql = "SELECT * FROM `leagues` WHERE `defaultVenueID` = :venueID";
        if ($liveOnly === true) $sql .= " AND `visible` = 1";
        $rlt = new Database($sql,["venueID" => $venueID]);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $league = new static();
            $league->Load($r);
            ($return) ? $return[] = $league : array_push($return,$league); 
        }
        return $return;
    }
    function fetchNearby() {
        $sql = "SELECT `id`,`name`,`defaultVenueID` FROM `leagues` WHERE `visible` = 1 AND `defaultVenueID` IN (SELECT `venue2` FROM `venueDistances` WHERE `venue1` = {$this->defaultVenueID} AND `distance` <= 50)";
        $rlt = new Database($sql);
        if (!$rlt->rows) return;
        $return = [];
        foreach ($rlt->rows as $r) {
            $venue = new Venue($r['defaultVenueID']);
            $return[] = [
                "id" => $r['id'],
                "name" => $r['name'],
                "venue" => $venue->getName()
            ];
        }
        return $return;
    }
    static function Surfaces() {
        return [
            1 => ["id" => 1, "name" => "Indoor"],
            2 => ["id" => 2, "name" => "Outdoor"],
            3 => ["id" => 3, "name" => "Indoor/Outdoor"]
        ];
    }
}