<?php

class User extends Base {

    protected $dbTable = "users";
    protected $dbFields = ["password","activationCode"];
    protected $isAdmin;
    protected $email;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function Save() {
        return parent::Save();
    }
    /* Getters */
    function getEmail() { return $this->email;}
    function getActivationCode() { return $this->activationCode;}
    /* Setters */

    /* Checkers */
    static function isLoggedIn() {
        if (isset($_SESSION['userID']) && $_SESSION['userID']) return true;
    }
    /* Doers */
    function IssueActivation(String $invitationEmail = null) {
        global $config;
        $this->setPassword();
        $this->setActivationCode();
        $rlt = $this->Save();
        if (!is_numeric($rlt)) return $rlt;
        $subject = "Registration at " . $config['system']['name'];
        $message[] = "Hello";
        $message[] = ($invitationEmail) ? "You have been invited to join us at {$config['system']['name']} by $invitationEmail" : "Thanks for registering with us at " . $config['system']['name'];
        $message[] = "To activate your account with us, please click <a href=\"" . $config['system']['url'] . "/Home/Activate/" . $this->getEmail() . "/" . $this->getActivationCode() . "\">here</a>";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to = [$this->getEmail() => $this->getName()];
        $cc = $bcc = [];
        $attachments = [];
        Email::Issue ($subject, $message, $to, $cc, $bcc, $attachments);
        return true;
    }
    function setPassword(String $password=null) { 
        if (!$password) $password = md5(time());
        $this->password = password_hash($password, PASSWORD_DEFAULT);
    }
    function setActivationCode() { $this->activationCode = substr(md5(time()),0,10);}
    static function isAdmin() {
        $authUser = static::AuthUser();
        if (!$authUser) return "Not Logged In";
        if ($authUser->isAdmin == 1) return true;
        return "Not an Admin";
       
    }
    static function AuthUser() {
        return (isset($_SESSION['userID']) && $_SESSION['userID']) ? new static($_SESSION['userID']) : false;
    }
}
