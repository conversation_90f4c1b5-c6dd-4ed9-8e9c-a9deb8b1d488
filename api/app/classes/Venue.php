<?php

class Venue extends Base {

    protected $dbTable = "venues";
    protected $lat, $lng;
    protected $name, $address1, $town, $postcode;
    protected $days;

    protected $facility_freeParking, $facility_paidParking;
    protected $facility_changingRooms,$facility_showers;
    protected $facility_cafe, $facility_bar;

    protected $defaultLeagues; # Populated by getDefaultLeagues();

    protected $nearby = []; # Populated by fetchNearby()

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function getFaciities() {
        $return = [];
        if ($this->facility_freeParking==1) $return[] = ["name" => "Free Parking", "icon" => "fas fa-parking"];
        if ($this->facility_paidParking==1) $return[] = ["name" => "Paid Parking", "icon" => "fas fa-parking"];
        if ($this->facility_changingRooms==1) $return[] = ["name" => "Changing Rooms", "icon" => "fas fa-person-booth"];
        if ($this->facility_showers==1) $return[] = ["name" => "Showers", "icon" => "fas fa-shower"];
        if ($this->facility_cafe==1) $return[] = ["name" => "Free Parking", "icon" => "fas fa-coffee"];
        if ($this->facility_bar==1) $return[] = ["name" => "Free Parking", "icon" => "fas fa-glass-cheers"];
        return $return;
    }
    function __toString() {
        return "{$this->name}";
    }
    function getDefaultLeagues($liveOnly = true) {
        $this->defaultLeagues = League::DefaultVenue($this->id,$liveOnly);
    }
    function ApiOutput() {
        global $config;
        $return = [
            "id" => $this->id,
            "name" => $this->name,
            "address1" => $this->address1,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "coordinates" => ["lat" => $this->lat, "lng" => $this->lng],
            "facilities" => $this->getFaciities(),
            "leagues" => []
        ];
        if ($this->defaultLeagues) {
            foreach ($this->defaultLeagues as $league) {
                // $sport = new Sport($league->getSportID());
                // if (!isset($return['sports'][$sport->id])) $return['sports'][$sport->id] = $sport->ApiOutput();
                // $return['sports'][$sport->id]["leagues"][$league->id] = $league->ApiOutput();
                $return["leagues"][$league->id] = $league->ApiOutput();
                // $days = [
                //     "mo" => false,
                //     "tu" => false,
                //     "we" => false,
                //     "th" => false,
                //     "fr" => false,
                //     "sa" => false,
                //     "su" => false
                // ];
                // $days[$league->getDefaultDay()] = true;

                // $return["leagues"][$league->id] = [
                //     "id" => $league->id,
                //     "name" => $league->name,
                //     "sport" => [
                //         "name" => "Netball",
                //         "img" => "https://leagues4you.co.uk/wp-content/themes/leagues4you1/img/sport-netball.png",
                //     ],
                //     "days" => $days,
                //     "surface" => "Indoor",
                //     "duration" => 40,
                //     "fixtureCharge" => 27.5,
                //     "seasonStartDate" => "2020-10-02 18:00"
                // ];
            }
        }
        return $return;
    }
    function fetchNearby($kilometres = 30) {
        $this->nearby = [];
        $sql = "SELECT `venue1`,`venue2`,`distance` FROM `venueDistances` WHERE (`venue1` = {$this->id} OR `venue2` = {$this->id}) AND `distance` <= $kilometres ORDER BY `distance` ASC";
        $rlt = new Db($sql);
        if ($rlt->rows) {
            foreach ($rlt->rows as $r) {
                if ($r['venue1']!=$this->id) {
                    $this->nearby[$r['venue1']] = $r['distance'];
                } 
                if ($r['venue2']!=$this->id) {
                    $this->nearby[$r['venue2']] = $r['distance'];
                } 
            }
        }
    }
    static function Api() {
        $db = static::Listing();
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $row) {
            $venue = new static();
            $venue->Load($row);
            $venue->getDefaultLeagues(true);
            if (!$venue->defaultLeagues) continue;
            $return[] = $venue->ApiOutput();
        }
        return $return;
    }
}