<?php

class Sport extends Base {

    protected $dbTable = "sports";
    protected $dbFields = ["name"];
    protected $name, $webImage, $duration;

    function __construct(Int $id = null) {
        parent::__construct($id);
    }
    function getName() { return $this->name;}
    function getWebImage() { return $this->webImage;}
    function getDuration() { return $this->duration;}
    function ApiOutput() {
        return [
            "id" => $this->id,
            "name" => $this->name,
            "img" => $this->webImage
        ];
    }
}