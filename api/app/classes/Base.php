<?php

class Base {

    protected $dbTable, $dbKey = "id", $dbOrder = ["name" => "ASC"];
    protected $created, $updated, $deleted;
    public $id;
    protected $name;
    protected $dbFields = [];
    protected $dbOrderBy = [];
    protected $minData = [];

    function __construct($id = null) {
        if ($id && $this->dbTable && $this->dbKey) {
            $sql = "SELECT * FROM `$this->dbTable` WHERE `$this->dbKey` = $id";
            $db = new Db($sql);
            // $db->Query();
            if ($db->errors) return implode(". ", $db->errors);
            if (!$db->rows) return "No record found";
            $this->Load($db->rows[0]);
        } 
    }
    function Load(Array $data) {
        foreach ($data as $k => $v) $this->{$k} = $v;
    }
    function Save() {
        // echo __METHOD__."<br>";
        if (!$this->dbFields) return "No database fields setup for "  . get_called_class();
        $sql = null; $sqlData = [];
        foreach ($this->dbFields as $field) {
            if ($sql) $sql .=",";
            $sql .= "`$field` = :$field";
            $sqlData[$field] = $this->$field;
        }
        if (!$this->{$this->dbKey}) {
            $sql = "INSERT INTO `$this->dbTable` SET $sql";
            $db = new Db($sql, $sqlData);
            if ($db->errors) return implode(". ",$db->errors);
            if (!$db->lastInsertID) return "No new ID from Save operation";
            return ($this->{$this->dbKey} = $db->lastInsertID);
        } else {
            $sql = "UPDATE `$this->dbTable` SET $sql WHERE `$this->dbKey` = :$this->dbKey";
            $sqlData[$this->dbKey] = $this->{$this->dbKey};
            $db = new Db($sql, $sqlData);
            if ($db->errors) return implode(". ",$db->errors);
            return $db->affectedRows;
        }
    }
    function __clone() { $this->id = null;}
    function getID() { return $this->id;}
    function getName() { return $this->name;}
    static function Fetch() {
        $o = new static();
        $orderBy = ($o->dbOrder) ? "ORDER BY " . key($o->dbOrder) ." " . current($o->dbOrder) : null;
        $db = new Database(($sql="SELECT * FROM `$o->dbTable` $orderBy"));
        if (!$db->errors) return implode(". ",$db->errors);
        $return = [];
        foreach ($db->rows as $r) {
            $s = new static();
            $s->Load($r);
            $return[] = $s;
        }
        return $return;
    }
    static function Archive (Int $id) {
        // Messaging\Add("Deleting $id");
        $obj = new static($id);
        $obj->Load(["deleted" => date('Y-m-d H:i:s')]);
        $obj->Save();
    }
    static function Duplicate(Int $id) {
        $orig = new static($id);
        $new = clone($orig);
        $new->Save();
        return $new;
    }
    static function Listing() {
        $obj = new static();
        $sql = "SELECT * FROM {$obj->dbTable}";
        if ($obj->dbOrderBy) {
            $sql .=  " ORDER BY";
            foreach ($obj->dbOrderBy as $field => $sortOrder) {
                $sql .=  "$field $sortOrder";
            }
        }
        return new Database($sql);
    }
    static function Lookup (Array $fields = [], Array $columns = []) {
        if (!$fields) return;
        $obj = new static();
        $sql = "SELECT ";
        $sql .= ($columns) ? implode(", ",$columns) : "*";
        $sql .= " FROM {$obj->dbTable}";
        $conn = " WHERE ";
        foreach ($fields as $field => $value) {
            $sql .= $conn ." `$field` = :$field";
            $conn = " AND ";
        } 
        return new Database($sql, $fields);
    }
}