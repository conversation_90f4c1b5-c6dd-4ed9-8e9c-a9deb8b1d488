<?php
# Development Error Setting
error_reporting(E_ALL); 
ini_set('display_errors', 1); 
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1); 

# Production Error Setting
// error_reporting(0);
// ini_set('display_errors', 0);
// ini_set('log_errors', 1);
// ini_set('display_startup_errors', 0);

include("config.php");
global $config;
#( int $lifetime [, string $path [, string $domain [, bool $secure = FALSE [, bool $httponly = FALSE ]]]] ) 
$cookieLifetime = 4; # 4 hours;
$pathOnDomain = "/"; # All paths
$cookieDomain = '.'.$config['system']['parentDomain'];
$cookieSecure = true; # https only?
$httpOnly = false; # Only send via HTTP (eg not JS) 
session_set_cookie_params(60*60*($cookieLifetime), $pathOnDomain, $cookieDomain,$cookieSecure,$httpOnly);
if(!isset($_SESSION)) session_start();

// Class Autoloading;
if (file_exists("/var/www/html/core/vendor/autoload.php")) include("/var/www/html/core/vendor/autoload.php");
// function ClassAutoloader($class) {
//     global $config;
//     $file = $config['core']['path']."/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
//     // exit("Failed on $file");
//     $file = $config['system']['path']."/app/classes/$class.php";
//     if (file_exists($file))return (include_once ($file));
    
// }
function ClassAutoloader($class) {
    global $config;
    $file = $config['core']['path']."/classes/$class.php";
    if (file_exists($file))return (include_once ($file));
    $file = $config['system']['path']."/app/classes/$class.php";
    if (file_exists($file))return (include_once ($file));
    $file = $config['system']['path']."/app/Controllers/$class.php";
    if (file_exists($file))return (include_once ($file));
    exit("Failed on $file");
}
spl_autoload_register("ClassAutoloader");