<?php

error_reporting(E_ALL); 
ini_set('display_errors', 1); 
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1); 

use Codebase\Card;

define ("ROOT_FOLDER",__DIR__ . DIRECTORY_SEPARATOR . ".."  . DIRECTORY_SEPARATOR. ".."  . DIRECTORY_SEPARATOR. ".."  . DIRECTORY_SEPARATOR);
define ("APP_FOLDER", ROOT_FOLDER . "core");
define ("CORE_FOLDER", ROOT_FOLDER . "core");

function generateError (String $message, Int $statusCode = 403) {
    http_response_code($statusCode);
    exit(json_encode($message));
}

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
if (!file_exists($init_file)) generateError("Could not initialise");
include($init_file);

// For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) generateError("Nothing requested");
// No Request Method? Just die
if (!isset($_SERVER['REQUEST_METHOD']) || !$_SERVER['REQUEST_METHOD']) generateError("No Request Method");
$request_method = $_SERVER['REQUEST_METHOD'] = strtoupper($_SERVER['REQUEST_METHOD']);

/* If not HTTPS - bin it */
// if (!isset($_SERVER) || !isset($_SERVER['HTTPS']) || !$_SERVER['HTTPS'] || $_SERVER['HTTPS']!="on") {
//     http_response_code(403); exit (json_encode("HTTPS Required"));
// }
$data = explode("/", $_GET['url']);
$action = $api_action = array_shift($data);

if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) {
    header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
}
// if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");

header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
$_SERVER['REQUEST_METHOD'] = strtoupper($_SERVER['REQUEST_METHOD']);
header('Content-Type: application/json');
// Grab any POST data
$post = json_decode( file_get_contents('php://input'),true);
// $_POST = $Post ?? $_POST;

// exit(json_encode("Hello"));

if ($api_action == "registration" && $request_method=="POST") {
    if (!isset($post['email']) || !$post['email'] || !filter_var($post['email'],FILTER_VALIDATE_EMAIL)) generateError("Missing or Invalid Email");
    if (User::Exists($post['email'])) generateError("User already exists");
    $user = User::Setup(["email" => $post['email']]);
    exit(json_encode($user->ApiData()));
}

// if ($api_action == "login" && $request_method=="POST") {
//     if (!isset($post['email']) || !$post['email'] || !filter_var($post['email'],FILTER_VALIDATE_EMAIL)) generateError("Missing or Invalid Email");
//     if (!isset($post['password']) || !$post['password']) generateError("Missing or Empty Password");
//     if (!($user=User::Exists($post['email']))) generateError("No such user");
//     if (!Auth::CheckPassword($user,$post['password'])) generateError("Login failed");
//     $jwt = JWT::Setup($user,$_SERVER);
//     exit(json_encode([
//         'server' => $_SERVER,
//         'jwt' => $jwt
//     ]));
// }

if ($api_action == "activation" && $request_method=="POST") {
    if (!isset($post['email']) || !$post['email'] || !filter_var($post['email'],FILTER_VALIDATE_EMAIL)) generateError("Missing or Invalid Email");
    if (!isset($post['code']) || !$post['code'] || !is_numeric($post['code'])) generateError("Missing, Empty or Invalid Code");
    if (!($user=User::Exists($post['email']))) generateError("No such user");
    if (!$user->checkSecurityCode($post['code'])) generateError("Activation failed");
    $jwt = JWT::Setup($user,$_SERVER);
    exit(json_encode([
        "token" => $jwt,
        // "expires" => date('D, j M Y H:i:s \U\T\C',strtotime("+14 days")),
        "expires" => date('D, d-M-Y H:i:s T',strtotime("+14 days")),
        "path" => "/",
        // "domain" => ".{$configData->system->domain}"
    ]));
}

if ($action=="reminder" && $_SERVER['REQUEST_METHOD']=="POST") {
    if (!isset($post['email']) || !$post['email'] || !filter_var($post['email'],FILTER_VALIDATE_EMAIL)) generateError("Invalid Username");
    $user = User::EmailLookup($post['email']);
    if (!$user || !$user->id) generateError("Unknown User");
    $user->setSecurityCode();
    $user->sendSecurityCode();
    exit(json_encode("Ok"));
}

if ($action=="login" && $_SERVER['REQUEST_METHOD']=="POST") {
    if (!isset($post['email']) || !$post['email'] || !filter_var($post['email'],FILTER_VALIDATE_EMAIL)) generateError("Invalid or Missing Username");
    if (!isset($post['password']) || !$post['password']) generateError("Invalid or Missing Password");
    $user = User::EmailLookup($post['email']);
    if (!$user || !$user->id) generateError("Unknown User");
    if (Auth::CheckPassword($user,$post['password'])!==true) generateError("Login Failed");
    global $config;
    $configData = (is_array($config)) ? json_decode(json_encode($config)) : $config;
    exit(json_encode([
        "token" =>Jwt::Create($user,$_SERVER),
        "expires" => date('D, j M Y H:i:s \U\T\C',strtotime("+14 days")),
        "path" => "/",
        "domain" => ".{$_SERVER['HTTP_HOST']}"
    ]));
}

if ($action=="taster-sessions" && $_SERVER['REQUEST_METHOD']=="GET") {
    if (!isset($data[0]) || !$data[0] || !is_numeric($data[0])) generateError("Missing or Bad Taster Reference");
    exit(file_get_contents("https://api.leagues4you.co.uk/tasters-sport/{$data[0]}"));
}

# Create the required Stripe PI
if ($action="taster-payment" && $_SERVER['REQUEST_METHOD']=="POST") {
    try {
        if (!isset($post['email'])) generateError("No Email Address Supplied");
        if (!filter_var($post['email'],FILTER_VALIDATE_EMAIL)) generateError("Invalid Email Address Format");
        if (!isset($post['amount'])) generateError("No Amount Supplied");
        if (!isset($post['tasterID'])) generateError("No Taster Session Supplied");
        $tasterSession = new TasterSession($post['tasterID']);
        if (!$tasterSession->id) generateError("Invalid taster session {$post['tasterID']}");
        $user = User::SearchCreate($post);
        $card = Card::Setup(false);
        $stripeCustomer = $card->Customer_SearchCreate($post['email']);
        $booking = (isset($post['bookingID']) && $post['bookingID']) ? new C2C_Booking($post['bookingID']) : new C2C_Booking();
        $booking->firstname = $post['firstname'] ;
        $booking->lastname =$post['lastname'] ;
        $booking->email =$post['email'] ;
        $booking->mobile =$post['mobile'] ;
        $booking->discountCode = (isset($post['discountCode']) && $post['discountCode']) ? $post['discountCode'] : null ;
        $bookingRlt = $booking->Save();
        $stripePaymentIntent = $card->PaymentIntent_Create($stripeCustomer->id, $post['amount'], $tasterSession->__toString(),['bookingID' => $booking->id]);
        exit(json_encode([
            'id' => $stripePaymentIntent->id,
            'client_secret' => $stripePaymentIntent->client_secret,
            'pk' => $card->publicKey,
            'currencySymbol' => '£',
            'amount' => (float)($stripePaymentIntent->amount / 100),
            'status' => $stripePaymentIntent->status,
            'bookingID' => (int)$booking->id,
        ]));
    } catch (Exception $e) {
        generateError($e->getMessage());
    }
}
# Update the required Stripe PI
if ($action="taster-payment" && $_SERVER['REQUEST_METHOD']=="PATCH") {
    if (!isset($post['stripePaymentIntentID'])) generateError("No Stripe Payment ID");
    $card = Card::Setup(false);
    // $stripePaymentIntent = $card->PaymentIntent_Fetch($post['stripePaymentIntentID']);
    // if (!$post['amount'] != $stripePaymentIntent->amount / 100) {
    $stripePaymentIntent = $card->PaymentIntent_Update($post['stripePaymentIntentID'],$post['amount']);
    // }
    try {
        exit(json_encode([
            'id' => $stripePaymentIntent->id,
            'client_secret' => $stripePaymentIntent->client_secret,
            'pk' => $card->publicKey,
            'currencySymbol' => '£',
            'amount' => ($stripePaymentIntent->amount / 100),
            'status' => $stripePaymentIntent->status,
            'bookingID' => $stripePaymentIntent->metadata->bookingID
        ]));
    } catch (Exception $e) {
        generateError($e->getMessage());
    }
}
# Fetch Latest Data for Stripe ID
if ($action=="taster-payment" && $_SERVER['REQUEST_METHOD']=="GET") {

}

generateError("Cannot Help");