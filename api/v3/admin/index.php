<?php

error_reporting(E_ALL); 
ini_set('display_errors', 1); 
ini_set('log_errors', 0);
ini_set('display_startup_errors', 1); 

define ("ROOT_FOLDER",__DIR__ . DIRECTORY_SEPARATOR . ".."  . DIRECTORY_SEPARATOR. ".."  . DIRECTORY_SEPARATOR. ".."  . DIRECTORY_SEPARATOR);
define ("APP_FOLDER", ROOT_FOLDER . "core");
define ("CORE_FOLDER", ROOT_FOLDER . "core");

function generateError (String $message, Int $statusCode = 403) {
    http_response_code($statusCode);
    exit(json_encode($message));
}

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
if (!file_exists($init_file)) generateError("Coudl not initialise");
include($init_file);

use Stripe_v2 as Stripe;
use Codebase\Sage;

// For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) { http_response_code(404); exit(0);}

# Permitted Origins
if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");

$data = explode("/", $_GET['url']);
$action = $data[0];

header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
$request_method = $_SERVER['REQUEST_METHOD'] = strtoupper($_SERVER['REQUEST_METHOD']);

// Grab any POST data
$post = json_decode( file_get_contents('php://input'),true);

/* Get Incoming Headers */
$apache_request_headers = apache_request_headers();
/* Ensure Request Headers all have a lowercase key */
foreach($apache_request_headers as $key => $val) $apache_request_headers[strtolower($key)] = $val;
/* Check for Bearer Token */
$authBearerToken = (isset($apache_request_headers['authorization'])) ? trim(strstr($apache_request_headers['authorization']," ")) : null;

/* Insert here for API testing without Auth */

if (!$authBearerToken) exit(json_encode(["error" => "Token Failure"]));

$userID = Jwt::Validate($authBearerToken);
if (is_string($userID)) exit(json_encode(["error" => $userID]));

$user = new User($userID);
if (!$user->id) exit(json_encode(["error" => "Invalid User"]));

$user->Login(false);



if ($action=="coordinator" && strtolower($_SERVER['REQUEST_METHOD'])=="get") {
    $leagues = League::Coordinator($user);
    $liveSeasons = Season::AllLive();
    $openSeasons = Season::AllOpen();
    $tasters = TasterSession::Coordinator($user);
    $fixtures = Fixture::Coordinator($user);
    $customers = User::Customers();
    $teams = Team::Live($user);
    $data = [
        "leagueCount" => count($leagues),
        "liveCount" => count($liveSeasons),
        "nextCount" => count($openSeasons),
        "tasterCount" => count($tasters),
        "fixtureCount" => count($fixtures),
        "customerCount" => count($customers),
        "teamCount" => count($teams),
    ];
    exit(json_encode($data));
}

if ($action=="coordinators" && $request_method=="GET") {
    $coordinators = User::Coordinators();
    $output = [];
    foreach ($coordinators as $coordinator) {
        $output[] = $coordinator->ProfileBasic();
    }
    exit(json_encode($output));
}

if ($action=="users" && strtolower($_SERVER['REQUEST_METHOD'])=="get") {
    extract($_GET);
    if (!isset($maxResults)) $maxResults = 50;
    if (!isset($pageOffset)) $pageOffset = 0;
    if (!isset($filterText)) $filterText = null;
    $users = User::Search($filterText);
    $maxPages = ceil(count($users['data']) / $maxResults);
    $output = [
        "url" => print_r($_GET,true),
        "users" => [],
        "totalCount" => count($users['data']),
        "pages" => (count($users['data']) > $maxResults) ? $maxPages : 1,
        "requestedPage" => $pageOffset,
        "page" => ($pageOffset && $pageOffset <= $maxPages) ? $pageOffset : 1,
        // "sql" => ($users['sql']) ? $users['sql'] : null,
        "notes" => []
    ];

    $counter = 0;
    $starter = ($output['page'] - 1) * $maxResults;
    // $output['notes'][] = "Starter $starter";
    foreach ($users['data'] as $user) {
        // $output['notes'][] = "{$user->firstname} {$user->lastname} {$user->email}";
        if ($counter < $starter) {
            $counter++;
            continue;
        }
        $output['users'][] = $user->ApiData();
        /* Break if we've reached our limit */
        if (count($output['users']) >= $maxResults) break;
        $counter++;
    } 
    exit(json_encode($output));
}

if ($action=="user" && strtolower($_SERVER['REQUEST_METHOD'])=="post") {
    // Logging::Add(print_r($post,true),true);
    $user = (isset($post['id']) && $post['id'] && is_numeric($post['id'])) ? new User($post['id']) : new User();
    // Activation Toggling
    $activation = null;
    if ($post['isActivated'] ==1 && !$user->activationStamp) {
        $activation = true;
    } elseif ((!isset($post['isActivated']) || !$post['isActivated']) && $user->activationStamp) {
        $activation = false;
    }
    $user->Load($post);
    if (!isset($post['isAdmin']) || $post['isAdmin'] != 1) $user->isAdmin = null;
    $user->Save();
    if (isset($post['newpassword']) && $post['newpassword']) $user->setPassword($post['newpassword']);
    if ($activation === true) User::AdminActivation($user->id);
    if ($activation === false) User::AdminDeactivation($user->id);
    exit(json_encode($user->ApiData()));
}

if ($action == "lastest-pi-statuses" && strtolower($_SERVER['REQUEST_METHOD'])=="post") {
    if (isset($post) && $post) {
        $stripe = new Stripe(true);
        foreach ($post as $p) {
            $pi = $stripe->fetchPaymentIntent($p);
            // if ($pi->status == "requires_capture") $pi = $stripe->client->paymentIntents->capture($pi->id);
            $captured = $refunded = null;
            $refundable = true;
            foreach ($pi->charges->data as $charge) {
                $captured += ($charge->amount_captured/100);
                $refunded += ($charge->amount_refunded/100);
            }
            $captured = number_format($captured,2);
            if ($pi->status == "succeeded") {
                if ($refunded) {
                    $refunded = number_format($refunded,2);
                    if ($refunded == $captured) {
                        $refundable = false;
                        $status = "Refunded";
                    } else $status = "Refunded £$refunded of £$captured";
                } else $status = "Charged £$captured";
            } elseif ($pi->status == "requires_capture") {
                $status = "Pre-authorised £$captured";
            } elseif ($pi->status == "requires_payment_method") {
                $status = "Incomplete";
                $refundable = false;
            } else {
                $status = "Unpaid";
                $refundable = false;
            } 
            $output['success'][] = [
                'stripePaymentIntentID' => $pi->id,
                'stripeCustomerID' => $pi->customer,
                'amount' => $pi->amount,
                'status' => $pi->status,
                'internal_status' => $status, 
                'refundable' => $refundable 
            ];
        }
    } else $output = ['error' => "No statuses requested"];
    exit(json_encode($output));
}

if ($action=="purchase-transaction") {
    $output = [
        "method" => $_SERVER['REQUEST_METHOD'],
        "success" => [],
        "error" => null,
        "received" => $data];
    if ($_SERVER['REQUEST_METHOD'] == "POST") {
        exit(json_encode($output));
    }

    if ($_SERVER['REQUEST_METHOD'] == "GET") {
        
        exit(json_encode($output));
    }
    /* METHODS NOW MUST HAVE ID */
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) {
        $output['error'][] = "Missing ID";
    } elseif (!($purchaseTransaction = new PurchaseTransaction($data[1]))) {
        $output['error'][] = "Invalid ID";
    } 
    if ($_SERVER['REQUEST_METHOD'] == "PUT") {
        exit(json_encode($output));
    } 
    if ($_SERVER['REQUEST_METHOD'] == "DELETE") {
        $log_message = "PI {$data[1]} deleted by {$user}";
        $output['log_message'] = $log_message;
        $purchaseTransaction->Delete($log_message);
        exit(json_encode($output));
    } 
}

if ($action=="taster-session-cancel") {
    if (isset($data[1]) && $data[1] && is_numeric($data[1]) && ($tasterSession = new TasterSession($data[1])) && $tasterSession->id) {
        $tasterSession->Cancel();
        exit(json_encode($tasterSession->ApiData()));
    }
}

if ($action=="taster-session-refund") {
    if (isset($data[1]) && $data[1] && is_numeric($data[1]) && ($tasterSession = new TasterSession($data[1])) && $tasterSession->id) {
        $tasterSession->Refund();
        exit(json_encode($tasterSession->ApiData()));
    }
}

if ($action=="billing-log-data") {
    $date = (isset($data[1]) && $data[1]) ? $data[1] : date('Y-m-d');
    $items = Billing_Log::Date($date);
    if (!$items) return [];
    $output = [];
    foreach ($items as $item) $output[] = $item->ApiData();
    exit(json_encode(["success" => $output]));
}

if ($action=="library" && strtoupper($_SERVER['REQUEST_METHOD']) == "POST") {

    $output = [
        "server" => $_SERVER,
        "post" => $post,
        "files" => $_FILES,
        "request" => $_REQUEST,
    ];
    $output['rlt'] = DocLibraryVersion::Upload($post,$_FILES['file']);
    exit(json_encode(["success" => $output]));
}

if ($action=="profile" && $_SERVER['REQUEST_METHOD']=="GET") {
    exit(json_encode($user->ProfileBasic()));
}

if ($action=="venue-monthly-accrual" && $request_method=="GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Invalid VenueID");
    $venue = new Venue($data[1]);
    if (!$venue || !$venue->id) generateError("Invalid Venue");
    if (!isset($data[2]) || !$data[2] || !is_numeric($data[2]) || $data[2] < 2016 || $data[2] > date('Y')) generateError("Invalid Year");
    if (!isset($data[3]) || !$data[3] || !is_numeric($data[3]) || $data[3] < 1 || $data[3] > 12) generateError("Invalid Month");
    $bookingCost = Booking::VenuePeriod($venue,$data[2],$data[3]);
    if (!is_float($bookingCost)) generateError($bookingCost);
    $purchaseReceived = PurchaseTransactionItem::VenuePeriodTotal($venue,$data[1],$data[2]);
    if (!is_float($purchaseReceived)) generateError($purchaseReceived);
    exit(json_encode([
        'coordinatorID' => (int)$venue->coordinatorID,
        'venueID' => (int)$venue->id,
        'booking' => (float)$bookingCost,
        'purchased' => (float)$purchaseReceived,
        'subTotal' => (float)($bookingCost - $purchaseReceived)
    ]));
}

/*
@var $data[1] YEAR
@var $data[2] MONTH
*/
if ($action=="venues-monthly-accrual" && $request_method=="GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1]) || $data[1] < 2016 || $data[1] > (date('Y')+1)) generateError("Invalid Year");
    if (!isset($data[2]) || !$data[2] || !is_numeric($data[2]) || $data[2] < 1 || $data[2] > 12) generateError("Invalid Month");
    $bookings = Booking::Venues($data[1],$data[2]);
    $output = [];
    foreach ($bookings as $booking) {
        $output[$booking['venueID']] = [
            'coordinatorID' => (int)$booking['coordinatorID'],
            'coordinator' => [
                'name' => $booking['coordinatorName'],
                'email' => $booking['coordinatorEmail'],
            ],
            "venueName" => (string)$booking['venueName'],
            "venueID" => (int)$booking['venueID'],
            "bookings" => (isset($booking['total']) && is_numeric($booking['total'])) ? (float)round($booking['total'],2) : 0,
            "transactions" => 0,
        ];
    }
    // $transactions = PurchaseTransaction::VenueTotals($data[1],$data[2]);
    $transactions = PurchaseTransaction::VenueSubTotals($data[1],$data[2]);
    foreach ($transactions as $transaction) {
        if (!isset($output[$transaction['venueID']]['bookings'])) $output[$transaction['venueID']]['bookings'] = 0;
        $output[$transaction['venueID']]['venueName'] = $transaction['venueName'];
        $output[$transaction['venueID']]['transactions'] = (float)$transaction['total'];
        $output[$transaction['venueID']]['coordinator'] = [
            'name' => $transaction['coordinatorName'],
            'email' => $transaction['coordinatorEmail'],
        ];
    }
    foreach ($output as $k => $v) {
        $bookingVal = (isset($v['bookings']) && is_numeric($v['bookings'])) ? (float)$v['bookings'] : 0;
        $transactionVal = (float)$v['transactions'] ?? (float)0;
        $output[$k]['balance'] = (float)round($bookingVal - $transactionVal,2);
    }
    array_multisort($output);
    // $output = usort($output,function($a,$b) {
    //     return strcmp($a['venueName'],$b['venueName']);
    // });
    exit(json_encode($output));
}

if ($action=="download-venue-transaction-document" && $request_method=="GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("No Transaction ID");
    $purchaseTransaction = new PurchaseTransaction($data[1]);
    if (!$purchaseTransaction || !$purchaseTransaction->id) generateError("Invalid Transaction");
    if (!isset($data[2]) || !$data[2]) generateError("No file specified");
    if (!in_array($data[2],$purchaseTransaction->filestoreFiles())) generateError("{$data[2]} not found in File Stroe for Purchase Transaction {$data[1]}");
    $file = $purchaseTransaction->filestoreFolder() . DIRECTORY_SEPARATOR . $data[2];
    if (file_exists($file)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="'.basename($file).'"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    }
}

if ($action=="venue-prepay-transactions" && $request_method=="GET") {
    $transactions = PurchaseTransaction::PrePays();
    exit(json_encode($transactions));
}

if ($action=="transaction-import-files" && $request_method=="GET") {
    $sage = new Sage();
    $files = $sage->ListOfImportFIles();
    exit(json_encode($files));
}

if ($action=="resend-transaction-import-file" && $request_method=="GET") {
    if (!isset($data[1]) || !$data[1]) generateError("Missing filename");
    try {
        $sage = new Sage();
        $send = $sage->SendPurchaseTransactionFile($sage->getFolder().$data[1],["<EMAIL>" => "Richard Dakin"]);
        ($send===true) ? exit("Ok") : generateError($send);
    } catch (\Throwable $t) { generateError($t->getMessage()); }
}