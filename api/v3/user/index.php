<?php

// error_reporting(E_ALL); 
// ini_set('display_errors', 1); 
// ini_set('log_errors', 0);
// ini_set('display_startup_errors', 1); 

// function shutDownFunction() {
//     $error = error_get_last();
//      // Fatal error, E_ERROR === 1
//     if (isset($error['type']) && $error['type'] === E_ERROR) {
//         generateError($error["message"]);
//     }
// }
// register_shutdown_function('shutDownFunction');

// define ("ROOT_FOLDER",__DIR__ . DIRECTORY_SEPARATOR . ".."  . DIRECTORY_SEPARATOR. ".."  . DIRECTORY_SEPARATOR. ".."  . DIRECTORY_SEPARATOR);
define("ROOT_FOLDER", dirname(__DIR__, 3) . DIRECTORY_SEPARATOR);
define("APP_FOLDER", ROOT_FOLDER . "core");
define("CORE_FOLDER", ROOT_FOLDER . "core");
// exit(json_encode(ROOT_FOLDER));


function generateError(String $message, Int $statusCode = 403) {
    header("Err: $message");
    http_response_code($statusCode);
    exit(json_encode($message));
}

$init_file = APP_FOLDER . DIRECTORY_SEPARATOR . "init.php";
if (!file_exists($init_file)) generateError("Could not initialise with $init_file");
include($init_file);

# For security, respond 404 for root requests
if (!$_GET || !$_GET['url']) {
    http_response_code(404);
    exit(0);
}

# Permitted Origins
$permitted_origins = [
    'https://hub.leagues4you.co.uk',
    'https://dashboard.leagues4you.co.uk',
];
if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN'] && in_array($_SERVER['HTTP_ORIGIN'], $permitted_origins)) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");
// if (isset($_SERVER['HTTP_ORIGIN']) && $_SERVER['HTTP_ORIGIN']) header("Access-Control-Allow-Origin: {$_SERVER['HTTP_ORIGIN']}");

# Breakdiwn the URL
$data = explode("/", $_GET['url']);
$action = $data[0];

# Catch the Pre-flight
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Max-Age: 86400');
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD']))
        header("Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE");
    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']))
        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
    exit(0);
}
$request_method = $_SERVER['REQUEST_METHOD'] = strtoupper($_SERVER['REQUEST_METHOD']);

# Grab any POST data
$post = json_decode(file_get_contents('php://input'), true);
if (!$post) $post = $_POST;
// if (!$_POST && $post) $_POST = $post;
// $_POST = ($post) ? $post : $_POST;

if ($action == "test") exit(json_encode("Ok"));

# Get Incoming Headers
$apache_request_headers = apache_request_headers();
# Ensure Request Headers all have a lowercase key
foreach ($apache_request_headers as $key => $val) $apache_request_headers[strtolower($key)] = $val;
# Check for Bearer Token
$authBearerToken = (isset($apache_request_headers['authorization'])) ? trim(strstr($apache_request_headers['authorization'], " ")) : null;

if (!$authBearerToken) generateError("Token Missing");
$userID = Jwt::Validate($authBearerToken);
if (is_string($userID)) generateError("Token Invalid $userID");

$user = new User($userID);
if (!$user->id) generateError("Invalid User");

$user->Login(false);

if ($action == "logout" && $request_method == "GET") {
    JWT::Logout($user->id);
    exit(json_encode("Ok"));
}

if ($action == "password" && $request_method == "PATCH") {
    /* TODO */
    $log = "Password Change. {$user} ({$user->id}).";
    if (!isset($post['password1']) || !isset($post['password2']) || !$post['password1'] || !$post['password2'] || $post['password1'] != $post['password2']) {
        $error = "Missing or non-matching passwords";
        Logging::Add("$log $error");
        generateError($error, 403);
    }
    $user->setPassword($post['password1']);
    $user->Save();
    Logging::Add("$log Ok");
    exit(json_encode($user->ApiData()));
}

if ($action == "profile" && $request_method == "GET") {
    exit(json_encode($user->Profile()));
}

if ($action == "profile" && $request_method == "PATCH") {
    $user->Load($post);
    $rlt = $user->Save();
    if (is_string($rlt)) generateError($rlt);
    exit(json_encode($user->Profile()));
}

if ($action == "reference" && $request_method == "GET") {
    /* TODO */
}

if ($action == "venues" && $request_method == "GET") {
    $venues = Venue::Listing();
    $output = [];
    foreach ($venues as $venue) $output[] = $venue->ApiBasic();
    exit(json_encode($output));
}

if ($action == "venue" && $request_method == "PATCH") {
    if (!isset($post['id']) || !$post['id'] || !is_numeric($post['id'])) generateError("No valid Venue ID");
    $venue = new Venue($post['id']);
    if (!$venue || !$venue->id) generateError("Not a valid Venue");
    $venue->Load($post);
    $venue->status =  (int) $post['status'];
    $venue->Save();
    exit(json_encode($venue->ApiBasic()));
}

if ($action == "venue" && $request_method == "POST") {
    $venue = new Venue();
    $venue->Load($post);
    $venue->Save();
    exit(json_encode($venue->ApiBasic()));
}

if ($action == "teams" && $request_method == "GET") {
    $limit = (isset($data[1]) && $data[1] && is_numeric($data[1])) ? $data[1] : 100;
    $start = (isset($data[2]) && $data[2] && is_numeric($data[2])) ? $data[2] * $limit : null;
    $total = Database::Execute("SELECT COUNT(id) AS total FROM teams WHERE deleted IS NULL");
    $teams = Team::Listing($limit, $start);
    $output = [
        'total' => $total['success']['rows'][0]['total'] ?? 0,
        'count' => count($teams) ?? 0,
    ];
    foreach ($teams as $team) $output['data'][] = $team->ApiBasic();
    exit(json_encode($output));
}

if ($action == "team" && $request_method == "PATCH") {
    if (!isset($post['id']) || !$post['id'] || !is_numeric($post['id'])) generateError("No valid Team ID");
    $team = new Team($post['id']);
    if (!$team || !$team->id) generateError("Not a valid Team");
    $team->Load($post);
    $team->Save();
    exit(json_encode($team->ApiBasic()));
}

if ($action == "team" && $request_method == "POST") {
    $team = new Team();
    $team->Load($post);
    $team->Save();
    exit(json_encode($team->ApiBasic()));
}

if ($action == "team-search" && $request_method == "POST") {
    if (!isset($post['searchText']) || !$post['searchText']) generateError("No search text supplied");
    $limit = (isset($data[1]) && $data[1] && is_numeric($data[1])) ? $data[1] : 100;
    $start = (isset($data[2]) && $data[2] && is_numeric($data[2])) ? $data[2] * $limit : null;
    $where = " FROM teams LEFT JOIN leagues ON teams.leagueID = leagues.id WHERE teams.name LIKE :name";
    $total = Database::Execute("SELECT COUNT(id) AS total $where", ["name" => "{$post['searchText']}%"]);
    $where .= " ORDER BY name";
    if ($limit) {
        $where .= ($start) ? " LIMIT $start,$limit" : " LIMIT $limit";
    }
    $teams = Team::Query("SELECT teams.*, leagues.name AS leagueName $where", ["name" => "{$post['searchText']}%"]);
    $output = [
        'total' => $total['success']['rows'][0]['total'] ?? 0,
        'count' => count($teams) ?? 0,
    ];
    foreach ($teams as $team) $output['data'][] = $team->ApiBasic();
    exit(json_encode($output));
}

if ($action == "team-management" && $request_method == "POST") {
    /*
        Only doing Capt - Treasurer must be done via LockerRoom
        1 = Captain & Treasurer
        2 = Captain
        3 = Treasurer
    */
    if (!isset($post['management']['teamID']) || !$post['management']['teamID'] || !is_numeric($post['management']['teamID'])) generateError("Missing or Non-numeric Team ID");
    $team = new Team($post['management']['teamID']);
    if (!$team || !$team->id) generateError("Invalid Team");

    if (!isset($post['management']['userID']) || !$post['management']['userID'] || !is_numeric($post['management']['userID'])) generateError("Missing or Non-numeric User ID");
    $user = new User($post['management']['userID']);
    if (!$user || !$user->id) generateError("Invalid User");

    if (!isset($post['management']['appointID']) || !$post['management']['appointID'] || !is_numeric($post['management']['appointID']) || $post['management']['appointID'] < 1 || $post['management']['appointID'] > 3) generateError("Appointment Type can only be 1, 2 or 3");

    $supression = (isset($post['management']['isSuppressed']) && $post['management']['isSuppressed']) ? true : false;
    try {
        $rlt = TeamFollower::InviteCaptain2($team, $user, $supression);
        exit(json_encode("Ok"));
    } catch (Exception $e) {
        generateError($e->getMessage());
    }
    // $rlt = TeamFollower::Invitation($team,$user,$post['management']['appointID'],$supression);
    // exit(json_encode($rlt));
}

if ($action == "leagues" && $request_method == "GET") {
    $leagues = League::Listing();
    $output = [];
    foreach ($leagues as $league) $output[] = $league->ApiBasic();
    exit(json_encode($output));
}

if ($action == "league" && $request_method == "PATCH") {
    if (!isset($post['id']) || !$post['id'] || !is_numeric($post['id'])) generateError("No valid League ID");
    $league = new League($post['id']);
    if (!$league || !$league->id) generateError("Not a valid League");
    $league->Load($post);
    $league->Save();
    exit(json_encode($league->ApiBasic()));
}

if ($action == "league" && $request_method == "POST") {
    $team = new Venue();
    $team->Load($post);
    $team->Save();
    exit(json_encode($team->ApiBasic()));
}

if ($action == "users" && $request_method == "GET") {
    $limit = (isset($data[1]) && $data[1] && is_numeric($data[1])) ? $data[1] : 100;
    $start = (isset($data[2]) && $data[2] && is_numeric($data[2])) ? $data[2] * $limit : null;
    $total = Database::Execute("SELECT COUNT(id) AS total FROM users WHERE deleted IS NULL");
    $users = User::Listing($limit, $start);
    $output = [
        'total' => $total['success']['rows'][0]['total'] ?? 0,
        'count' => count($users) ?? 0,
    ];
    foreach ($users as $user) $output['data'][] = $user->ApiBasic();
    exit(json_encode($output));
}

if ($action == "user-search" && $request_method == "POST") {
    if (!isset($post['searchText']) || !$post['searchText']) generateError("No search text supplied");
    $limit = (isset($data[1]) && $data[1] && is_numeric($data[1])) ? $data[1] : 100;
    $start = (isset($data[2]) && $data[2] && is_numeric($data[2])) ? $data[2] * $limit : null;
    $where = " FROM users WHERE email LIKE :name OR firstname LIKE :name OR lastname LIKE :name";
    $total = Database::Execute("SELECT COUNT(id) AS total $where", ["name" => "{$post['searchText']}%"]);
    $where .= " ORDER BY lastname, firstname, email";
    if ($limit) {
        $where .= ($start) ? " LIMIT $start,$limit" : " LIMIT $limit";
    }
    $users = User::Query(($sql = "SELECT * $where"), ["name" => "{$post['searchText']}%"]);
    $output = [
        'total' => $total['success']['rows'][0]['total'] ?? 0,
        'count' => count($users) ?? 0,
        // 'sql' => $sql,
        'limit' => $limit,
        'start' => $start,
        // '1' => $data[1],
        // '2' => $data[2],
    ];
    foreach ($users as $user) $output['data'][] = $user->ApiData();
    exit(json_encode($output));
}

if ($action == "purchase-transaction" && $request_method == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Missing or Non-numeric ID");
    $purchaseTransaction = new PurchaseTransaction($data[1]);
    if (!$purchaseTransaction || !$purchaseTransaction->id) generateError("Invalid Transaction");
    exit(json_encode($purchaseTransaction->ApiFull()));
}

if ($action == "purchase-transaction" && $request_method == "POST") {
    $post['purchaseInvoice'] = json_decode($post['purchaseInvoice'], true);
    if (!isset($post['purchaseInvoice']['venueID']) || !$post['purchaseInvoice']['venueID'] || !is_numeric($post['purchaseInvoice']['venueID'])) generateError("Missing or Non-numeric Venue ID");
    $venue = new Venue($post['purchaseInvoice']['venueID']);
    if (!$venue || !$venue->id) generateError("Invalid Venue");

    if (!isset($post['purchaseInvoice']['typeID']) || !$post['purchaseInvoice']['typeID'] || !is_numeric($post['purchaseInvoice']['typeID'])) generateError("Missing or Non-numeric Type ID");
    $transactionType = new TransactionType($post['purchaseInvoice']['typeID']);
    if (!$transactionType || !$transactionType->id) generateError("Invalid Transaction Type");

    if (!isset($post['purchaseInvoice']['reference']) || !$post['purchaseInvoice']['reference']) generateError("Missing Reference");
    if (!isset($post['purchaseInvoice']['taxDate']) || !$post['purchaseInvoice']['taxDate']) generateError("Missing Tax Date");

    $purchaseInvoiceID = (isset($post['purchaseInvoice']['id']) && $post['purchaseInvoice']['id'] && is_numeric($post['purchaseInvoice']['id'])) ? $post['purchaseInvoice']['id'] : null;
    $purchaseInvoice = new PurchaseTransaction($purchaseInvoiceID);

    $purchaseInvoice->Load($post['purchaseInvoice']);
    $rlt = $purchaseInvoice->Save();

    exit(json_encode($purchaseInvoice->ApiBasic()));
}

if ($action == "purchase-transaction-document" && $request_method == "POST") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Missing or Invalid Purchase Transaction ID");
    $purchaseTransaction = new PurchaseTransaction($data[1]);
    if (!$purchaseTransaction || !$purchaseTransaction->id) generateError("Invalid Purchase Transaction");
    if (!isset($_FILES['purchaseTransaction']) || !$_FILES['purchaseTransaction']) generateError("Missing File");

    $destinationFolder =  $purchaseTransaction->filestoreFolder();

    if (!file_exists($destinationFolder)) {
        $oldmask = umask(0);
        mkdir($destinationFolder, 0777);
        umask($oldmask);
    }

    $destinationFile = $purchaseTransaction->filestoreFile(pathinfo($_FILES['purchaseTransaction']['name'])['extension']);

    if (move_uploaded_file($_FILES['purchaseTransaction']['tmp_name'], $destinationFolder . DIRECTORY_SEPARATOR . $destinationFile)) {
        exit(json_encode($purchaseTransaction->ApiBasic()));
    } else generateError("Could not uload file");
}

if ($action == "purchase-transactions" && $request_method == "GET") {
    $output = [];
    $sql = "SELECT pi.*, items.itemsTotal, venues.name AS venueName, CONCAT_WS(' ', users.firstname, users.lastname) AS coordinatorName FROM pi LEFT JOIN (SELECT mainID, SUM(total) AS itemsTotal FROM pi_items WHERE deleted IS NULL GROUP BY mainID) items ON pi.id = items.mainID LEFT JOIN venues ON pi.venueID = venues.id LEFT JOIN users ON venues.coordinatorID = users.id WHERE pi.deleted IS NULL ORDER BY taxDate DESC";
    $purchaseInvoices = PurchaseTransaction::Query($sql);
    foreach ($purchaseInvoices as $purchaseInvoice) $output[] = $purchaseInvoice->ApiBasic();
    exit(json_encode($output));
}

if ($action == "purchase-transaction-post" && $request_method == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Missing or Non-numeric ID");
    $purchaseTransaction = new PurchaseTransaction($data[1]);
    if (!$purchaseTransaction || !$purchaseTransaction->id) generateError("Invalid Transaction");
    $purchaseTransaction->setPosted();
    exit(json_encode($purchaseTransaction->ApiBasic()));
}

if ($action == "purchase-transaction-unpost" && $request_method == "GET") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Missing or Non-numeric ID");
    $purchaseTransaction = new PurchaseTransaction($data[1]);
    if (!$purchaseTransaction || !$purchaseTransaction->id) generateError("Invalid Transaction");
    $purchaseTransaction->setUnposted();
    exit(json_encode($purchaseTransaction->ApiBasic()));
}

if ($action == "purchase-transaction-item" && $request_method == "POST") {
    if (!isset($post['mainID']) || !$post['mainID'] || !is_numeric($post['mainID'])) generateError("Missing or Non-numeric Transaction ID");
    $purchaseTransaction = new PurchaseTransaction($post['mainID']);
    if (!$purchaseTransaction || !$purchaseTransaction->id) generateError("Invalid Transaction");
    if (!isset($post['month']) || !$post['month'] || !is_numeric($post['month']) || $post['month'] < 1 || $post['month'] > 12) generateError("Missing or Invalid Month");
    if (!isset($post['year']) || !$post['year'] || !is_numeric($post['year']) || $post['year'] < 2016 || $post['month'] > date('Y')) generateError("Missing or Invalid Year");
    if (!isset($post['vatCode']) || !$post['vatCode']) generateError("Missing VAT Code");
    $purchaseTransactionItem = new PurchaseTransactionItem();
    $purchaseTransactionItem->Load($post);
    $purchaseTransactionItem->Save();
    exit(json_encode($purchaseTransaction->ApiFull()));
}

if ($action == "purchase-transaction-item" && $request_method == "DELETE") {
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Missing or Non-numeric ID");
    $purchaseTransactionItem = new PurchaseTransactionItem($data[1]);
    if (!$purchaseTransactionItem || !$purchaseTransactionItem->id) generateError("Invalid Transaction Item");
    $purchaseTransaction = $purchaseTransactionItem->getPurchaseTransaction();
    $purchaseTransactionItem->Delete();
    exit(json_encode($purchaseTransaction->ApiFull()));
}

if ($action == "purchase-transaction-venue-subtotals" && $request_method == "GET") {
    # venueID
    # month
    # year
    if (!isset($data[1]) || !$data[1] || !is_numeric($data[1])) generateError("Missing Venue ID");
    if (!isset($data[2]) || !$data[2] || !is_numeric($data[2])) generateError("Missing Year");
    if (!isset($data[3]) || !$data[3] || !is_numeric($data[3])) generateError("Missing Month");
}

generateError("Cannot Help with $request_method to $action");
