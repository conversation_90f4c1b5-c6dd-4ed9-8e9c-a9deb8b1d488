<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      html,
      body {
        height: 100%;
      }
      body {
        font-family: Arial, sans-serif;
        background-color: #f3f4f6;
        text-align: center;
        background: url("ela.jpg") no-repeat center center fixed;
        background-size: cover;
        min-height: 100vh;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }
      h1 {
        font-size: 3em;
        color: #ff4081;
      }
      p {
        font-size: 1.2em;
        color: #333;
        padding: 0 20px;
      }
      .audio-player {
        margin-top: 20px;
      }
      button {
        background-color: #ff4081;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 10px;
        font-size: 1em;
        cursor: pointer;
        border-radius: 5px;
        transition: background-color 0.3s;
      }
      button.active {
        background-color: #e0336d;
      }
      button:hover {
        background-color: #d02c5f;
      }
      #current-track {
        font-size: 1.2em;
        margin-top: 20px;
        color: #ff4081;
      }
      #lyrics {
        margin-top: 20px;
        font-size: 1em;
        color: #333;
        white-space: pre-wrap; /* Satır sonlarını düzgün göstermek için */
      }
      /* Seviyorum Seni animasyonu ve tasarımı */
      #love-message {
        position: absolute;
        bottom: 0;
        font-size: 1.4em;
        color: #ff4081;
        font-weight: bold;
        /* opacity: 0; */
        /* animation: fadeInOut 5s ease-in-out infinite; */
      }
      /* Fade in-out animasyonu */
      @keyframes fadeInOut {
        0% {
          opacity: 0;
          transform: scale(0.5);
        }
        50% {
          opacity: 1;
          transform: scale(1.2);
        }
        100% {
          opacity: 0;
          transform: scale(0.5);
        }
      }
      @media (max-width: 768px) {
        body {
          margin-top: -38px;
        }
      }
    </style>
  </head>
  <body>
    <h1>İyi ki Doğdun, Güzelim Benim</h1>
    <p>
      Güzelim benim, bu seninle ilk doğum günümüz, her ne kadar yanında olamasam
      da önümüzdeki senelerde telafisini yaparız, doğum günün kutlu olsun cennet
      yüzlüm benim. Nice beraber sağlıklı, mutlu, huzurlu yıllara bebis. Sana
      yaptığım gizli işleri gönderiyorum.
    </p>
    <br />
    <div class="voice-buttons-container">
      <button id="join-btn" onclick="joinVoice()">Arama Baslat</button>
      <button id="leave-btn" onclick="leaveVoice()" disabled>
        Arama Bitir
      </button>
    </div>
    <div id="voice-status" style="margin-top: 10px; color: #555"></div>

    <p>
      <a
        href="https://wa.me/447340933924?text=Beyefendi"
        target="_blank"
        style="text-decoration: none"
      >
        <b>Sag alt koseye tikla yaz oradan yada +44 734 093 39 24 ten yaz</b>
      </a>
    </p>
    <div class="audio-player">
      <p>Senin için yaptığım özel şarkıları dinleyebilirsin hanfendi</p>
      <button id="btn1" onclick="playAudio(1)">Şarkı 1</button>
      <button id="btn2" onclick="playAudio(2)">Şarkı 2</button>
      <button id="btn3" onclick="playAudio(3)">Şarkı 3</button>
      <button id="btn4" onclick="playAudio(4)">Şarkı 4</button>
      <button id="btn5" onclick="playAudio(5)">Şarkı 5</button>
      <button id="btn6" onclick="playAudio(6)">Şarkı 6</button>
      <button id="btn7" onclick="playAudio(7)">Şarkı 7</button>
      <button id="btn8" onclick="playAudio(8)">Şarkı 8</button>
    </div>
    <div id="current-track">Şu an çalan: Yok</div>
    <!-- <div id="lyrics">Şarkı sözleri burada görünecek...</div> -->
    <!-- Animasyonlu Seviyorum Seni Mesajı -->
    <div id="love-message">Seviyorum seni tombik ayakli</div>
    <audio id="audio-player" controls style="display: none">
      <source id="audio-source" src="" type="audio/mpeg" />
      Tarayıcınız ses oynatmayı desteklemiyor.
    </audio>
    <script>
      let currentButton = null;
      // Şarkı sözlerini dış dosyadan yükleyen fonksiyon
      function loadLyrics(trackNumber) {
        const lyricsDiv = document.getElementById("lyrics");
        // İlgili şarkı sözlerinin bulunduğu dosyayı yükleme
        fetch("lyrics/" + trackNumber + ".txt")
          .then((response) => response.text())
          .then((text) => {
            lyricsDiv.textContent = text;
          })
          .catch((err) => {
            lyricsDiv.textContent = "Şarkı sözleri yüklenemedi.";
          });
      }
      function playAudio(trackNumber) {
        const audioPlayer = document.getElementById("audio-player");
        const audioSource = document.getElementById("audio-source");
        const currentTrack = document.getElementById("current-track");
        // Yeni şarkının yolunu ayarlayalım
        audioSource.src = trackNumber + ".mp3";
        audioPlayer.load();
        audioPlayer.play();
        // Şu anki çalınan şarkıyı gösterelim
        currentTrack.textContent = "Şu an çalan: Şarkı " + trackNumber;
        // Şarkı sözlerini yükleyelim
        loadLyrics(trackNumber);
        // Eski aktif butonu sıfırla
        if (currentButton) {
          currentButton.classList.remove("active");
        }
        // Şu an tıklanan butonu aktif yap
        currentButton = document.getElementById("btn" + trackNumber);
        currentButton.classList.add("active");
        // Şarkı bittiğinde butonun durumunu sıfırla ve sözleri kaldır
        audioPlayer.onended = function () {
          currentButton.classList.remove("active");
          currentTrack.textContent = "Şu an çalan: Yok";
          document.getElementById("lyrics").textContent =
            "Şarkı sözleri burada görünecek...";
        };
      }
    </script>
  </body>
</html>

<!--Start of Tawk.to Script-->
<script type="text/javascript">
  var Tawk_API = Tawk_API || {},
    Tawk_LoadStart = new Date();
  (function () {
    var s1 = document.createElement("script"),
      s0 = document.getElementsByTagName("script")[0];
    s1.async = true;
    s1.src = "https://embed.tawk.to/6823cf0736f29c190d2128fc/1ir5u1dfb";
    s1.charset = "UTF-8";
    s1.setAttribute("crossorigin", "*");
    s0.parentNode.insertBefore(s1, s0);
  })();
</script>
<!--End of Tawk.to Script-->
<script src="https://download.agora.io/sdk/release/AgoraRTC_N.js"></script>

<script src="main.js"></script>

<style>
  .voice-buttons-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  .voice-buttons-container button {
    flex: 1 1 auto;
    min-width: 140px;
  }
</style>
